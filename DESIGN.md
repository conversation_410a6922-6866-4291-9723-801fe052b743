# PSFSMON-L 系统设计文档（双Fanotify Group架构）

## 1. 系统概述

PSFSMON-L 是一个Linux系统安全监控程序，采用**双Fanotify Group + 双线程监控策略**实现跨内核版本的兼容性。系统主要由文件监控、进程监控、网络监控三大核心模块组成，并提供RESTful API接口。

### 1.1 核心架构特点
- **双Fanotify Group策略**: 分离受保护路径权限检查和重点进程文件监控
- **双线程处理**: 独立线程处理不同监控任务，避免事件冲突
- **基础Fanotify机制**: 仅使用4.19+内核支持的基础特性，确保兼容性
- **完全移除Inotify**: 不再依赖Inotify机制，简化架构复杂度

## 2. 双Fanotify Group监控架构

### 2.1 架构总览

```
┌─────────────────────────────────────────────────────────────────┐
│                        PSFSMON-L 双Group架构                     │
├─────────────────────────────────┬───────────────────────────────┤
│     受保护路径监控Group         │        重点进程监控Group       │
│                                 │                               │
│  ┌─────────────────────────┐   │   ┌─────────────────────────┐ │
│  │  protected_fanotify_fd  │   │   │   focus_fanotify_fd     │ │
│  │                         │   │   │                         │ │
│  │  • FAN_CLASS_CONTENT    │   │   │  • FAN_CLASS_NOTIF      │ │
│  │  • FAN_OPEN_PERM        │   │   │  • 基础事件监控          │ │
│  │  • 具体路径标记          │   │   │  • 全局文件系统监控      │ │
│  └─────────────────────────┘   │   └─────────────────────────┘ │
│              │                  │              │                │
│  ┌─────────────────────────┐   │   ┌─────────────────────────┐ │
│  │ protected_monitor_thread │   │   │  focus_monitor_thread   │ │
│  │                         │   │   │                         │ │
│  │  • 权限检查             │   │   │  • 重点进程过滤          │ │
│  │  • KILL/LOG模式         │   │   │  • 熵值计算             │ │
│  │  • 违规记录             │   │   │  • 文件操作记录          │ │
│  └─────────────────────────┘   │   └─────────────────────────┘ │
└─────────────────────────────────┴───────────────────────────────┘
```

### 2.2 双Group策略详解

#### 2.2.1 受保护路径监控Group
```cpp
// 初始化
protected_fanotify_fd_ = fanotify_init(FAN_CLASS_CONTENT | FAN_CLOEXEC, O_RDONLY);

// 标记受保护路径
for (const auto& path : protected_paths) {
    fanotify_mark(protected_fanotify_fd_, FAN_MARK_ADD,
                  FAN_OPEN_PERM, AT_FDCWD, path.c_str());
}

// 专用线程处理
void FileMonitor::processProtectedPathEvents() {
    // 1. 读取FAN_OPEN_PERM事件
    // 2. 检查进程是否在排除列表
    // 3. 验证路径是否为受保护路径
    // 4. 根据配置决定FAN_ALLOW或FAN_DENY
    // 5. 记录违规行为
}
```

#### 2.2.2 重点进程监控Group
```cpp
// 初始化
focus_fanotify_fd_ = fanotify_init(FAN_CLASS_NOTIF | FAN_CLOEXEC, O_RDONLY);

// 全局监控策略
uint64_t basic_mask = FAN_OPEN | FAN_CLOSE_WRITE | FAN_MODIFY | FAN_ACCESS;

// 4.20+内核使用文件系统级监控
if (kernel >= 4.20) {
    fanotify_mark(focus_fanotify_fd_, FAN_MARK_ADD | FAN_MARK_FILESYSTEM,
                  basic_mask, AT_FDCWD, "/");
} else {
    // 4.19内核使用挂载点监控
    for (const auto& mountpoint : mountpoints) {
        fanotify_mark(focus_fanotify_fd_, FAN_MARK_ADD | FAN_MARK_MOUNT,
                      basic_mask, AT_FDCWD, mountpoint.c_str());
    }
}

// 专用线程处理
void FileMonitor::processFocusProcessEvents() {
    // 1. 读取基础文件事件
    // 2. 快速检查是否为重点进程（非重点进程直接跳过）
    // 3. 构建FileEvent对象
    // 4. 计算文件熵值（读前/写后）
    // 5. 记录文件操作历史
}
```

### 2.3 内核版本兼容策略

| 内核版本 | 受保护路径策略 | 重点进程监控策略 | 关键特性 |
|---------|---------------|----------------|----------|
| 4.19 | FAN_CLASS_NOTIF<br/>记录违规 | FAN_MARK_MOUNT<br/>挂载点监控 | 基础通知模式 |
| 4.20+ | FAN_CLASS_CONTENT<br/>预先拦截 | FAN_MARK_FILESYSTEM<br/>文件系统监控 | 权限检查 + 优化监控 |

## 3. 核心模块设计

### 3.1 FileMonitor模块

#### 3.1.1 类架构
```cpp
class FileMonitor {
private:
    // 双fanotify描述符 - 需求13核心实现
    int protected_fanotify_fd_;          // 受保护路径fanotify文件描述符
    int focus_fanotify_fd_;              // 重点进程fanotify文件描述符
    int fanotify_fd_;                    // 主描述符（兼容性保留）
    
    // 双监控线程 - 需求13核心实现
    std::thread protected_monitor_thread_;   // 受保护路径监控线程
    std::thread focus_monitor_thread_;       // 重点进程监控线程
    
    // 内核兼容性
    KernelCompat kernel_compat_;         // 内核版本检测和特性支持
    
    // 基础Fanotify初始化
    bool initializeBasicFanotify();
    bool setupBasicFanotifyMonitoring();
    
    // 双线程事件处理 - 需求13核心方法
    void processProtectedPathEvents();   // 受保护路径专用线程
    void processFocusProcessEvents();    // 重点进程专用线程
    bool processProtectedPathEvent(struct fanotify_event_metadata* metadata);
    void processFocusProcessEvent(struct fanotify_event_metadata* metadata);
    
    // 需求12熵值计算方法
    void calculateReadEntropy(FileEvent& event);
    void calculateWriteEntropy(FileEvent& event);
};
```

#### 3.1.2 初始化流程
```
FileMonitor::initialize()
         │
         ├─── 内核版本检测 (KernelCompat)
         │
         ├─── 双Fanotify Group初始化
         │    ├─── initializeBasicFanotify()
         │    │    ├─── 受保护路径Group: FAN_CLASS_CONTENT
         │    │    └─── 重点进程Group: FAN_CLASS_NOTIF
         │    │
         │    └─── setupBasicFanotifyMonitoring()
         │         ├─── 受保护路径具体标记
         │         └─── 重点进程全局监控
         │
         └─── 双线程启动
              ├─── protected_monitor_thread_
              └─── focus_monitor_thread_
```

### 3.2 重点进程文件监控（需求12核心实现）

#### 3.2.1 数据结构设计
```cpp
struct FocusProcessFileInfo {
    // 熵值统计 - 需求12关键数据
    double total_read_entropy;      // 读操作累计熵值
    double total_write_entropy;     // 写操作累计熵值
    uint64_t read_entropy_count;    // 读操作计数
    uint64_t write_entropy_count;   // 写操作计数
    
    // 文件操作统计 - 需求12要求
    uint64_t delete_count;          // 删除文件数量
    uint64_t rename_count;          // 重命名文件数量
    std::vector<std::string> rename_extensions;  // 扩展名变化列表
    std::vector<std::string> path_history;       // 文件路径历史
    
    mutable std::mutex mutex;       // 线程安全
    
    // 核心方法
    void addReadEntropy(double entropy);        // 累加读熵值
    void addWriteEntropy(double entropy);       // 累加写熵值
    void recordDelete(const std::string& path); // 记录删除操作
    void recordRename(const std::string& old_path, const std::string& new_path);
    double getAverageReadEntropy() const;       // 平均读熵值
    double getAverageWriteEntropy() const;      // 平均写熵值
    void cleanup();                             // 清理过期记录
};
```

#### 3.2.2 熵值计算流程（需求12核心算法）
```
重点进程文件事件
         │
    ┌────┴─────┐
    │ 事件类型  │
    └────┬─────┘
         │
    ┌────▼─────┐        ┌─────────────────┐
    │ 读操作   │────────│ 读操作前计算熵值 │
    └──────────┘        └─────────────────┘
         │                       │
    ┌────▼─────┐        ┌─────────▼─────────┐
    │ 写操作   │────────│ 写操作后计算熵值 │
    └──────────┘        └─────────────────────┘
         │                       │
    ┌────▼─────┐        ┌─────────▼─────────┐
    │ 其他操作 │────────│ 记录操作历史     │
    └──────────┘        └─────────────────────┘
         │                       │
         └───────┬───────────────┘
                 │
        ┌────────▼────────┐
        │ 进程级数据累积   │
        │ • total_*_entropy │
        │ • *_entropy_count │
        │ • operation_count │
        └─────────────────┘
```

### 3.3 受保护路径防护机制

#### 3.3.1 权限检查流程
```cpp
bool FileMonitor::processProtectedPathEvent(metadata) {
    // 1. 排除检查
    if (ProcessExclusionManager::isExcluded(metadata->pid)) {
        return true;  // 忽略进程，允许访问
    }
    
    // 2. 路径验证
    std::string file_path = getPathFromFd(metadata->fd);
    if (!ProtectedPathManager::isProtectedPath(file_path)) {
        return true;  // 非受保护路径，允许访问
    }
    
    // 3. 违规记录
    ProtectedPathManager::handleViolation(file_path, metadata->pid, process_name);
    
    // 4. 策略执行
    if (config->terminate_violating_processes) {
        return false;  // KILL模式：拒绝访问
    } else {
        return true;   // LOG模式：记录但允许访问
    }
}
```

#### 3.3.2 不同内核版本的防护效果
| 内核版本 | 防护模式 | 拦截时机 | 效果 |
|---------|---------|---------|------|
| 4.19 | LOG模式强制 | 事件发生后 | 记录违规，无法阻止 |
| 5.4+ | KILL/LOG可选 | 权限检查前 | 可预先阻止写操作 |

## 4. API接口架构

### 4.1 RESTful API设计

#### 4.1.1 重点进程管理API（需求12核心接口）
```
GET  /api/focus-processes              # 获取重点进程列表
POST /api/focus-processes              # 添加重点进程
     Body: {"pid": 1234} 或 {"name": "process_name"}
     
DELETE /api/focus-processes           # 删除重点进程
       Query: ?pid=1234 或 ?name=process_name

GET /api/focus-process/file-info      # 获取重点进程文件信息（核心API）
    Query: ?pid=1234
    Response: {
        "pid": 1234,
        "process_name": "target_process",
        "file_info": {
            "read_entropy_count": 150,
            "write_entropy_count": 89,
            "total_read_entropy": 1247.56,
            "total_write_entropy": 892.34,
            "average_read_entropy": 8.32,
            "average_write_entropy": 10.03,
            "delete_count": 5,
            "rename_count": 3,
            "path_history_count": 234,
            "rename_extensions": [".tmp", ".bak", ".log"]
        }
    }
```

#### 4.1.2 受保护路径管理API（需求10核心接口）
```
GET  /api/protected-paths              # 获取受保护路径列表
POST /api/protected-paths              # 添加受保护路径
     Body: {"path": "/etc/passwd", "mode": "KILL"}
     
DELETE /api/protected-paths           # 删除受保护路径
       Query: ?path=/etc/passwd
```

## 5. 线程安全和性能优化

### 5.1 线程安全策略

#### 5.1.1 双线程隔离
- 受保护路径监控线程和重点进程监控线程完全独立
- 各自处理独立的fanotify描述符，避免竞争条件
- 共享数据结构使用细粒度锁保护

#### 5.1.2 锁设计
```cpp
// 进程信息保护
mutable std::mutex processes_mutex_;

// 重点进程文件信息保护
mutable std::mutex FocusProcessFileInfo::mutex;

// 受保护路径管理保护
mutable std::mutex ProtectedPathManager::mutex_;

// 统计信息保护（使用原子操作）
std::atomic<uint64_t> total_events_;
std::atomic<uint64_t> focus_process_events_;
std::atomic<uint64_t> protected_path_violations_;
std::atomic<uint64_t> entropy_calculations_;
```

### 5.2 性能优化策略

#### 5.2.1 事件过滤优化
```cpp
void FileMonitor::processFocusProcessEvent(metadata) {
    // 快速过滤：非重点进程直接跳过，避免不必要的处理
    if (!FocusProcessManager::isFocusProcess(metadata->pid)) {
        return;  // 性能优化：快速返回
    }
    
    // 只有重点进程才进行详细处理
    // ...
}
```

## 6. 部署和配置

### 6.1 配置文件结构
```ini
[monitoring]
enable_process_monitoring=true
enable_file_monitoring=true
enable_network_monitoring=true
enable_focus_process_monitoring=true
enable_protected_path_monitoring=true

[focus_processes]
# 重点进程配置（逗号分隔）
process_list=nginx,apache2,mysql

[protected_paths]
# 受保护路径配置（逗号分隔）
path_list=/etc/passwd,/etc/shadow,/etc/hosts
terminate_violating_processes=true

[performance]
process_scan_interval_ms=5000
update_interval_ms=1000
calculate_file_entropy=true
```

## 7. 项目验收标准

### 7.1 架构合规性
- ✅ 双Fanotify Group策略正确实现
- ✅ 双线程监控机制稳定运行
- ✅ 完全移除Inotify机制
- ✅ 仅使用基础Fanotify特性

### 7.2 功能完整性
- ✅ 需求12：重点进程文件监控和熵值计算完整实现
- ✅ 需求13：双Fanotify Group基础监控策略正确工作
- ✅ 所有19项需求均正确实现
- ✅ API接口完整且功能正常

**当前状态：双Fanotify Group架构已完整实现，符合所有设计要求** 