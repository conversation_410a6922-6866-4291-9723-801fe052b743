# 进程清理逻辑改进

## 概述

根据用户要求，对进程清理逻辑进行了重大改进，主要解决了之前过度保护的问题。现在的实现遵循以下原则：

1. **重点进程在没有被手动移除前，不会被判定为死进程**
2. **普通进程可以正常被清理**
3. **在重点进程被移出时，进行一次判断，如果它是死进程，则进行清理**
4. **死进程的判定采用更全面的、灵活的机制，避免误判**

## 主要修改

### 1. 增强的进程存在检查机制

在 `src/main_monitor.cpp` 中：

- **改进 `Utils::processExists()`**: 使用三种方法综合判断进程是否存在
  - `kill(pid, 0)` 检查
  - `/proc/[pid]` 目录存在检查
  - `/proc/[pid]/stat` 文件存在检查

- **新增 `Utils::processExistsStrict()`**: 更严格的进程存在检查
  - 在基本检查基础上，还验证进程状态
  - 排除僵尸进程（状态为 'Z' 的进程）

### 2. 修改 `scanProcesses()` 函数

在 `src/process_monitor.cpp` 中：

- **移除过度保护逻辑**：不再在有重点进程时完全跳过死进程检查
- **精准保护**：只保护重点进程，允许其他进程正常清理
- **使用严格验证**：采用 `Utils::processExistsStrict()` 进行二次验证

### 3. 修改 `cleanupDeadProcesses()` 函数

- **同样移除过度保护逻辑**
- **保持重点进程保护**：重点进程不会被定时清理
- **优化清理流程**：使用更严格的进程存在检查

### 4. 增强手动移除重点进程的清理

在 `setFocusProcess()` 函数中：

- **按 PID 移除时**：立即检查该进程是否已死亡，如果是则清理
- **按进程名移除时**：批量检查所有被移除的重点进程，清理死进程
- **使用严格检查**：确保只清理确实已死亡的进程

## 代码结构

```cpp
// 增强的进程存在检查
bool Utils::processExists(pid_t pid) {
    // 三重检查机制
    bool kill_check = (kill(pid, 0) == 0);
    bool proc_check = fileExists("/proc/" + std::to_string(pid));
    bool stat_check = fileExists("/proc/" + std::to_string(pid) + "/stat");
    return kill_check && proc_check && stat_check;
}

bool Utils::processExistsStrict(pid_t pid) {
    // 基本检查 + 状态验证
    if (!processExists(pid)) return false;
    
    // 排除僵尸进程
    std::string stat_content = readFileContent("/proc/" + std::to_string(pid) + "/stat");
    // 解析状态，如果是 'Z' 则认为已死
    return !isZombieProcess(stat_content);
}
```

## 测试

创建了专门的测试程序 `test/test_process_cleanup.cpp`：

- **测试场景**：创建重点进程和普通进程，验证清理逻辑
- **验证重点进程保护**：确保重点进程不会被自动清理
- **验证普通进程清理**：确保普通进程正常被清理
- **验证手动移除清理**：确保手动移除重点进程时的死进程清理

## 使用方法

### 编译
```bash
cd test
make test_process_cleanup
```

### 运行测试
```bash
# 确保 PSFSMON-L 正在运行
sudo ../bin/psfsmon --api-port 8080

# 运行测试
./test_process_cleanup
```

## 预期效果

1. **提高清理效率**：普通进程可以及时被清理，不受重点进程影响
2. **保护重点进程**：重点进程在手动移除前不会被误清理
3. **减少误判**：使用更严格的进程存在检查，避免误删活跃进程
4. **及时清理死进程**：手动移除重点进程时，立即清理已死亡的进程

## 注意事项

1. **向后兼容**：保持了原有的 API 接口不变
2. **线程安全**：所有修改都保持了线程安全
3. **性能优化**：严格检查只在必要时使用，避免性能影响
4. **日志记录**：增加了详细的日志记录，便于调试和监控

## 测试建议

建议使用以下测试命令验证修改效果：

```bash
# 基本功能测试
./test_process_cleanup

# 压力测试（可选）
./test_process_cleanup --stress

# 结合现有测试
make -C test entropy_test_program
./entropy_test_program --self-register
```

这些改进确保了进程清理逻辑既高效又可靠，满足了用户的所有要求。 