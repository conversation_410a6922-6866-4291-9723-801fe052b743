# PSFSMON-L 功能需求文档 (最终版本 - 双Fanotify Group策略)

## 1. 项目概述

### 1.1 项目名称
Linux进程安全文件系统监控程序 (Process Security File System Monitor for Linux - PSFSMON-L)

### 1.2 项目目标
PSFSMON-L 是一个Linux系统安全监控程序，提供实时的进程、文件系统和网络监控功能。该程序主要功能是监控系统中所有运行的进程并收集它们的状态信息，**核心特色是基于双Fanotify Group策略的重点进程文件操作行为分析和熵值计算**。

### 1.3 应用场景
- 系统安全监控
- 恶意软件检测
- 重点进程文件访问审计
- 进程行为分析
- 合规性监控
- 受保护路径防护

## 2. 技术规格要求

### 2.1 开发语言和标准
- **主要语言**: C++11标准（确保在较老系统上编译通过）
- **底层库**: 可使用C语言库
- **编译器兼容性**: 支持GCC 4.8+、Clang 3.3+等C++11兼容编译器

### 2.2 平台支持
- **操作系统**: Linux内核版本 >= 4.19
- **CPU架构**: 
  - x64/amd64
  - aarch64/arm64
  - 其他Linux支持的架构
- **编译方式**: 本地编译（不支持交叉编译）

### 2.3 系统依赖
- **最低内核版本**: 4.19
- **必需特性**: 
  - **基础Fanotify机制**：仅使用基础Fanotify特性，支持4.19+内核
  - /proc文件系统
  - /sys文件系统
- **权限要求**: root权限（用于系统级监控）
- **不得使用的机制**: eBPF（因操作系统限制）

## 3. 核心功能需求（19项完整需求）

### 3.1 基础系统要求 (需求1-9)

#### 需求1: Linux平台安全监控程序
- **描述**: 这是一个运行在Linux平台下面的安全监控程序

#### 需求2: 实时进程监控和状态收集
- **描述**: 程序的主要功能是实时监控所有系统中所运行的进程并收集它们的状态，并具备汇报这些信息的能力

#### 需求3: C++11标准开发
- **描述**: 程序需要以C++语言进行编写，低层的库可以使用C语言。C++语言的标准，由于系统编译器的版本问题，要控制在可以支持C++11标准的状态

#### 需求4: 多CPU架构支持
- **描述**: 程序可能需要在不同的CPU上运行，例如x64/amd64, aarch64/arm64等等，但是不会跨平台编译，也就是说，它一定会在所在的平台下，用本地编译器进行编译

#### 需求5: 启动时进程信息收集
- **描述**: 程序要在启动时收集所有的已有进程的信息，管理它们，具备形成进程树的能力

#### 需求6: 进程和线程管理
- **描述**: 针对每个进程，要可以管理它创建、clone、fork的所有进程、线程

#### 需求7: 进程级信息汇总
- **描述**: 收集的信息要归属并管理在进程下面，要在进程这一层次有一个汇总

#### 需求8: 双类型信息收集
- **描述**: 收集的线程的信息主要包括2类：
  1. 进程的基本信息例如名称、CPU使用状态、内存使用状态、执行文件路径等等
  2. 进程的基本网络操作信息，例如打开了多少连接，连接了什么IP地址，每个IP收发的基本数据量是多少、整体收发数据量等等

#### 需求9: 禁用eBPF机制
- **描述**: 因为OS的限制，我们不能使用eBPF机制来收集这些信息；这个要求非常重要

### 3.2 核心监控功能 (需求10-13)

#### 需求10: 受保护路径概念
- **描述**: 引入受保护路径概念。支持受保护路径设置，被设置为受保护路径的，在发生针对受保护路径的写操作时，包括删除和重命名，相关的进程会按要求被中止，或者将相关记录在日志中，即受保护路径支持KILL模式和LOG模式2种处理方式，可以自定义

#### 需求11: 忽略进程概念
- **描述**: 引入忽略进程概念。支持忽略进程设置，被设置为忽略进程的，不再监控其相关信息（所有信息，包括文件操作信息），忽略进程可以安全地访问受保护路径而不被KILL掉

#### 需求12: 重点进程概念 (核心功能)
- **描述**: 引入重点进程概念。支持重点进程设置，被设置为重点进程的，除进程基本信息外，还要依靠和使用Fanotify机制，尽可能多的监控其文件操作相关信息，也只有被设置为重点进程，才会最大化地监控文件操作信息（对其它进程只监控有没有侵入/写操作到上面提到的受保护路径即可）
- **关键要求**:
  - 监控重点是读、写操作
  - **要在读操作前，计算文件熵值**
  - **要在写操作后，再次计算文件熵值**
  - 记录的文件熵值，按读/写区分，统一累加记录到进程级别进行汇总
  - 获取删除的文件数量、重命名的文件数量、重命名的扩展名列表、操作文件路径历史记录等信息
  - 记录到重点进程的相关数据结构中供查询
  - 提供接口API支持这一功能
  - **该功能是本项目最大的需求，要尽全力做好**

#### 需求13: 双Fanotify Group基础监控策略 (关键架构需求)
- **描述**: **我们要使用Fanotify机制来进行文件信息的获取，不再整合Inotify机制到本程序中，应该去除所有已经整合的Inotify机制代码**
- **核心策略**:
  - **仅使用最基本的Fanotify支持机制**，不使用文档中标注的(since Linux x.xx)的特性
  - **例外允许**: 如果OS > 4.20，可以使用FAN_MARK_FILESYSTEM来避免逐个标记挂载点
  - **完全移除**: FAN_REPORT_TID、FAN_REPORT_PIDFD、FAN_REPORT_FID等复杂mask
- **双Group架构**:
  1. **受保护路径监控**: 使用fanotify_init建立独立的fanotify group，使用FAN_CLASS_CONTENT flag，使用FAN_OPEN_PERM对具体受保护路径进行标记，实现权限检查和路径保护
  2. **重点进程监控**: 使用fanotify_init建立独立的fanotify group，使用FAN_CLASS_NOTIF flag，使用FAN_MARK_MOUNT或FAN_MARK_FILESYSTEM进行全局监控，过滤重点进程事件
- **双线程处理**: 分别启动独立的监控线程处理两个fanotify group的事件

### 3.3 系统架构要求 (需求14-19)

#### 需求14: 避免大规模底层依赖
- **描述**: 对于进程信息、网络通信信息，要避免依赖其它大规模的底层依赖库，因为我们的操作系统版本并不高，而且可能是多个CPU类型的，它的基础版本只有4.19，可能很多功能都不具备

#### 需求15: 双执行模式支持
- **描述**: 程序可以直接执行，以直观的方式进行输出，也可以以服务的方式进行安装和执行，通过对外接口形式进行输出

#### 需求16: 灵活对外接口
- **描述**: 程序对外的接口，可以向调用者灵活的提供所输出的信息，例如进程树、每个进程的信息汇总，每个进程的详细信息例如它所管理的每个线程的所有的文件的操作序列字符串等等

#### 需求17: 双编译方式支持
- **描述**: 程序要支持CMake和标准Makefile这2种编译方式

#### 需求18: 代码可读性控制
- **描述**: 保持程序的可读性，如果单个源代码文件过大，例如超过了1500行，则可以在逻辑上拆分它，并在设计文件中给出简单的说明；如果项目中存在不在本文档中提到的功能实现，应该为遗留代码，删除它们以避免误解

#### 需求19: 文档和测试整理
- **描述**: 同步更改相关的需求文档、测试代码和脚本，项目根目录下只保留1个说明文档，1个需求文件，1个设计文档，不保留其它任何文档；test目录下，只保留1个shell脚本(test_api_with_curl.sh)，1个测试项目(含源代码文件和Makefile文件)，用来整体测试所有API调用

## 4. 核心数据结构设计

### 4.1 重点进程文件监控信息
```cpp
struct FocusProcessFileInfo {
    // 文件熵值信息
    double total_read_entropy;      // 读操作累计文件熵值
    double total_write_entropy;     // 写操作累计文件熵值
    uint64_t read_entropy_count;    // 读操作熵值计数
    uint64_t write_entropy_count;   // 写操作熵值计数
    
    // 文件操作统计
    uint64_t delete_count;          // 删除文件数量
    uint64_t rename_count;          // 重命名文件数量
    std::vector<std::string> rename_extensions;  // 重命名扩展名变化列表
    std::vector<std::string> path_history;       // 操作文件路径历史记录
    
    mutable std::mutex mutex;       // 线程安全锁
    
    // 方法
    void addReadEntropy(double entropy);
    void addWriteEntropy(double entropy);
    void recordDelete(const std::string& path);
    void recordRename(const std::string& old_path, const std::string& new_path);
    double getAverageReadEntropy() const;
    double getAverageWriteEntropy() const;
    void cleanup();
};
```

### 4.2 双Fanotify Group架构
```cpp
class FileMonitor {
private:
    // 双fanotify描述符
    int protected_fanotify_fd_;      // 受保护路径fanotify文件描述符
    int focus_fanotify_fd_;          // 重点进程fanotify文件描述符
    
    // 双监控线程
    std::thread protected_monitor_thread_;  // 受保护路径监控线程
    std::thread focus_monitor_thread_;      // 重点进程监控线程
    
    // 双线程事件处理方法
    void processProtectedPathEvents();      // 受保护路径监控线程
    void processFocusProcessEvents();       // 重点进程监控线程
    bool processProtectedPathEvent(struct fanotify_event_metadata* metadata);
    void processFocusProcessEvent(struct fanotify_event_metadata* metadata);
    
    // 熵值计算方法
    void calculateReadEntropy(FileEvent& event);
    void calculateWriteEntropy(FileEvent& event);
};
```

## 5. API接口设计

### 5.1 重点进程管理API
- `GET /api/focus-processes` - 获取重点进程列表
- `POST /api/focus-processes` - 添加重点进程
- `DELETE /api/focus-processes` - 删除重点进程
- `GET /api/focus-process/file-info?pid=xxx` - 获取重点进程文件信息和熵值

### 5.2 受保护路径管理API
- `GET /api/protected-paths` - 获取受保护路径列表
- `POST /api/protected-paths` - 添加受保护路径
- `DELETE /api/protected-paths` - 删除受保护路径

### 5.3 进程监控API
- `GET /api/processes` - 获取进程列表
- `GET /api/processes/tree` - 获取进程树
- `GET /api/processes/{pid}` - 获取进程详情

### 5.4 系统监控API
- `GET /api/system/stats` - 获取系统统计
- `GET /api/monitor/status` - 获取监控状态

## 6. 关键技术实现

### 6.1 基础Fanotify机制
- 使用基础事件：FAN_OPEN、FAN_CLOSE_WRITE、FAN_MODIFY、FAN_ACCESS
- 避免复杂特性：不使用since Linux x.xx的新特性
- 双group策略：受保护路径权限检查 + 重点进程全局监控

### 6.2 熵值计算
- Shannon熵值算法实现
- 读操作前计算文件熵值
- 写操作后计算文件熵值
- 进程级熵值累积和统计

### 6.3 线程安全
- 双线程独立处理不同fanotify group事件
- 所有共享数据结构使用互斥锁保护
- 原子操作用于统计计数器

## 7. 性能和兼容性

### 7.1 内核兼容性
- 最低支持4.19内核
- 4.20+内核使用FAN_MARK_FILESYSTEM优化
- 基础Fanotify特性确保跨版本兼容

### 7.2 性能优化
- 双线程处理避免事件处理冲突
- 重点进程快速过滤，非重点进程直接跳过
- 熵值计算仅针对重点进程
- 进程排除机制减少不必要的监控开销

## 8. 安全特性

### 8.1 受保护路径防护
- 5.4+内核：预先拦截写操作（FAN_OPEN_PERM）
- 4.19内核：实时监控和记录违规
- KILL/LOG模式可配置

### 8.2 重点进程行为分析
- 文件熵值变化监控
- 文件操作模式分析
- 异常行为检测基础数据收集 