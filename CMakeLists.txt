cmake_minimum_required(VERSION 3.5)
project(psfsmon VERSION 1.0.0 LANGUAGES C CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 如果没有指定构建类型，默认为Debug
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Debug CACHE STRING "Choose the type of build" FORCE)
    message(STATUS "No build type specified, defaulting to Debug")
endif()

# 基础编译选项
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -Wpedantic")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -Wextra -Wpedantic")

# 调试模式配置
set(CMAKE_CXX_FLAGS_DEBUG "-g3 -O0 -DDEBUG -fno-omit-frame-pointer")
set(CMAKE_C_FLAGS_DEBUG "-g3 -O0 -DDEBUG -fno-omit-frame-pointer")

# 发布模式配置
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG -fomit-frame-pointer")
set(CMAKE_C_FLAGS_RELEASE "-O3 -DNDEBUG -fomit-frame-pointer")

# RelWithDebInfo模式配置
set(CMAKE_CXX_FLAGS_RELWITHDEBINFO "-O2 -g -DNDEBUG")
set(CMAKE_C_FLAGS_RELWITHDEBINFO "-O2 -g -DNDEBUG")

# MinSizeRel模式配置
set(CMAKE_CXX_FLAGS_MINSIZEREL "-Os -DNDEBUG")
set(CMAKE_C_FLAGS_MINSIZEREL "-Os -DNDEBUG")

# 调试选项
option(ENABLE_SANITIZERS "Enable Address and Undefined Behavior Sanitizers" OFF)
option(ENABLE_VALGRIND_SUPPORT "Enable Valgrind support" OFF)
option(ENABLE_DEBUG_SYMBOLS "Enable debug symbols even in release mode" OFF)
option(ENABLE_PROFILING "Enable profiling support" OFF)

# Sanitizers支持（仅在Debug模式下默认启用）
if(CMAKE_BUILD_TYPE STREQUAL "Debug" AND ENABLE_SANITIZERS)
    set(SANITIZER_FLAGS "-fsanitize=address -fsanitize=undefined -fsanitize=leak")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} ${SANITIZER_FLAGS}")
    set(CMAKE_C_FLAGS_DEBUG "${CMAKE_C_FLAGS_DEBUG} ${SANITIZER_FLAGS}")
    set(CMAKE_EXE_LINKER_FLAGS_DEBUG "${CMAKE_EXE_LINKER_FLAGS_DEBUG} ${SANITIZER_FLAGS}")
    message(STATUS "Sanitizers enabled for debug build")
endif()

# Valgrind支持
if(ENABLE_VALGRIND_SUPPORT)
    add_definitions(-DVALGRIND_SUPPORT)
    message(STATUS "Valgrind support enabled")
endif()

# 调试符号支持
if(ENABLE_DEBUG_SYMBOLS)
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -g")
    set(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} -g")
    message(STATUS "Debug symbols enabled for release build")
endif()

# Profiling支持
if(ENABLE_PROFILING)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -pg")
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -pg")
    set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -pg")
    message(STATUS "Profiling support enabled")
endif()

# 包含目录
include_directories(include)

# 查找所需的系统库
find_package(Threads REQUIRED)

# 检查系统头文件
include(CheckIncludeFile)
check_include_file("sys/fanotify.h" HAVE_FANOTIFY)
# 移除inotify检查，仅使用fanotify

if(NOT HAVE_FANOTIFY)
    message(WARNING "fanotify.h not found, file monitoring may be limited")
endif()

# inotify不再需要

# 源文件 - 使用通配符保持与Makefile一致
file(GLOB CORE_SOURCES "src/*.cpp")
list(REMOVE_ITEM CORE_SOURCES "${CMAKE_CURRENT_SOURCE_DIR}/src/main.cpp")

# 明确列出以便IDE识别
set(CORE_SOURCES_EXPLICIT
    src/psfsmon_impl.cpp
    src/process_monitor.cpp
    src/file_monitor.cpp
    src/network_monitor.cpp
    src/main_monitor.cpp
    src/api_server.cpp
    src/http_server.cpp
    src/process_exclusion.cpp
    src/protected_path_manager.cpp
    src/focus_process_manager.cpp
    src/api_handlers_basic.cpp
    src/api_json_builder.cpp
    src/rule_match_info.cpp
)

# 主程序源文件
set(MAIN_SOURCES
    src/main.cpp
    ${CORE_SOURCES}
)

# 创建可执行文件
add_executable(psfsmon ${MAIN_SOURCES})

# 调试版本特定配置
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(psfsmon PRIVATE
        DEBUG_MODE=1
        VERBOSE_DEBUG=1
        ENABLE_TRACE_LOGGING=1
    )
    message(STATUS "Debug definitions added")
endif()

# 链接库
target_link_libraries(psfsmon
    ${CMAKE_THREAD_LIBS_INIT}
    rt        # for clock_gettime
    dl        # for dlopen
)

# 创建调试版本的单独目标
add_executable(psfsmon-debug ${MAIN_SOURCES})
target_compile_definitions(psfsmon-debug PRIVATE
    DEBUG_MODE=1
    VERBOSE_DEBUG=1
    ENABLE_TRACE_LOGGING=1
    ENABLE_MEMORY_DEBUG=1
)
set_target_properties(psfsmon-debug PROPERTIES
    COMPILE_FLAGS "-g3 -O0 -DDEBUG -fno-omit-frame-pointer"
    OUTPUT_NAME "psfsmon-debug"
)
target_link_libraries(psfsmon-debug
    ${CMAKE_THREAD_LIBS_INIT}
    rt
    dl
)

# 安装规则
install(TARGETS psfsmon
    RUNTIME DESTINATION bin
)

# 创建配置文件安装
install(FILES config/psfsmon.conf
    DESTINATION /etc/psfsmon
    OPTIONAL
)

# 创建systemd服务文件安装 (可选)
if(EXISTS "/lib/systemd/system")
    install(FILES scripts/psfsmon.service
        DESTINATION /lib/systemd/system
        OPTIONAL
    )
endif()

# 创建配置目录
install(DIRECTORY DESTINATION /etc/psfsmon)
install(DIRECTORY DESTINATION /var/log/psfsmon)

# 调试助手目标
add_custom_target(debug-info
    COMMAND echo "=== Debug Information ==="
    COMMAND echo "Build type: ${CMAKE_BUILD_TYPE}"
    COMMAND echo "CXX Compiler: ${CMAKE_CXX_COMPILER}"
    COMMAND echo "CXX Flags: ${CMAKE_CXX_FLAGS}"
    COMMAND echo "Debug CXX Flags: ${CMAKE_CXX_FLAGS_DEBUG}"
    COMMAND echo "Sanitizers: ${ENABLE_SANITIZERS}"
    COMMAND echo "Debug executable: psfsmon-debug"
    COMMAND echo "=========================="
    COMMENT "Displaying debug configuration information"
)

# GDB调试助手
add_custom_target(gdb
    COMMAND gdb --args $<TARGET_FILE:psfsmon-debug>
    DEPENDS psfsmon-debug
    COMMENT "Starting GDB debugger with psfsmon-debug"
)

# Valgrind内存检查
add_custom_target(valgrind
    COMMAND valgrind --tool=memcheck --leak-check=full --show-leak-kinds=all --track-origins=yes $<TARGET_FILE:psfsmon-debug>
    DEPENDS psfsmon-debug
    COMMENT "Running Valgrind memory check on psfsmon-debug"
)

# 测试 (可选)
option(BUILD_TESTS "Build tests" OFF)
if(BUILD_TESTS)
    enable_testing()
    
    # 如果test目录存在CMakeLists.txt，则使用它
    if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/test/CMakeLists.txt")
        add_subdirectory(test)
    else()
        # 否则创建基本的测试支持
        message(STATUS "Creating basic test support")
        
        # 查找test目录中的所有cpp文件
        file(GLOB TEST_SOURCES "test/*.cpp")
        
        # 为每个测试文件创建可执行文件
        foreach(TEST_SOURCE ${TEST_SOURCES})
            get_filename_component(TEST_NAME ${TEST_SOURCE} NAME_WE)
            
            # 创建测试可执行文件
            add_executable(${TEST_NAME} ${TEST_SOURCE} ${CORE_SOURCES})
            
            # 设置包含目录
            target_include_directories(${TEST_NAME} PRIVATE include)
            
            # 调试配置
            target_compile_definitions(${TEST_NAME} PRIVATE
                DEBUG_MODE=1
                TEST_MODE=1
            )
            
            # 链接库
            target_link_libraries(${TEST_NAME}
                ${CMAKE_THREAD_LIBS_INIT}
                rt
                dl
            )
            
            # 设置输出目录
            set_target_properties(${TEST_NAME} PROPERTIES
                RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/test"
            )
            
            message(STATUS "Added test: ${TEST_NAME}")
        endforeach()
    endif()
    
    # 自定义测试目标
    add_custom_target(test-all
        COMMAND echo "Running all tests..."
        WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
        COMMENT "Running all available tests"
    )
    
    # 低版本内核测试目标
    if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/test/test_low_kernel_monitoring.cpp")
        add_custom_target(test-low-kernel
            COMMAND echo "Running low kernel version tests..."
            COMMAND ${CMAKE_BINARY_DIR}/test/test_low_kernel_monitoring || echo "Low kernel test completed"
            WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
            DEPENDS test_low_kernel_monitoring
            COMMENT "Running low kernel version monitoring tests"
        )
    endif()
endif()

# 调试信息
message(STATUS "=== Build Configuration ===")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ compiler: ${CMAKE_CXX_COMPILER}")
message(STATUS "C++ flags: ${CMAKE_CXX_FLAGS}")
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    message(STATUS "Debug CXX flags: ${CMAKE_CXX_FLAGS_DEBUG}")
endif()
message(STATUS "Install prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "Sanitizers: ${ENABLE_SANITIZERS}")
message(STATUS "Valgrind support: ${ENABLE_VALGRIND_SUPPORT}")
message(STATUS "Debug symbols: ${ENABLE_DEBUG_SYMBOLS}")
message(STATUS "Profiling: ${ENABLE_PROFILING}")

# 架构检测
if(CMAKE_SIZEOF_VOID_P EQUAL 8)
    message(STATUS "Building for 64-bit architecture")
else()
    message(STATUS "Building for 32-bit architecture")
endif()

# 内核版本检查提示
message(STATUS "NOTE: This program requires Linux kernel >= 4.19.0")
message(STATUS "NOTE: Fanotify support required for file monitoring")
message(STATUS "===============================")
