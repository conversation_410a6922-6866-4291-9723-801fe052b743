# PSFSMON-L - Linux安全监控程序

PSFSMON-L 是一个高性能的Linux系统安全监控程序，提供实时的文件、网络和进程监控功能。

## 主要特性

- **混合文件监控策略**: 主要使用Fanotify，Inotify作为关键补充（特别是4.19内核）
- **内核版本智能适配**: 根据不同内核版本自动选择最优监控策略
- **重点进程监控**: 对重点进程进行文件操作熵值计算和详细监控
- **受保护路径防护**: 多重防护机制，适应不同内核版本能力
  - **5.4+内核**: Fanotify预先拦截机制
  - **4.19内核**: Inotify事后检测 + 快速响应机制
- **网络连接监控**: 监控TCP/UDP连接和网络统计  
- **进程监控**: 跟踪进程创建、退出和资源使用
- **RESTful API**: 提供完整的HTTP API接口
- **高性能**: 多线程架构，低资源占用
- **灵活配置**: 支持配置文件和命令行参数
- **进程排除**: 支持忽略指定进程的监控

## 核心监控策略

### 混合监控机制
项目采用智能混合监控策略，根据内核版本采用不同的监控和防护方案：

- **主监控**: Fanotify负责基础文件系统事件监控
- **辅助监控**: Inotify在4.19内核中提供关键的扩展事件支持
- **受保护路径防护**:
  - 5.4+内核：使用FAN_CLASS_CONTENT模式实现预先拦截
  - 4.19内核：通过Inotify事后检测 + 快速响应机制（<100ms）

### 内核版本兼容性

| 内核版本 | 监控策略 | 防护机制 | 特殊功能 |
|---------|---------|---------|----------|
| 4.19及以下 | Fanotify + Inotify混合 | 后防护（快速响应） | 快速进程查找与中止 |
| 5.4 | 主要Fanotify | 预防护 + 后防护 | 扩展事件支持 |
| 5.4以上 | 完全Fanotify | 完整预防护 | FAN_RENAME支持 |
| 6.6+ | 完整Fanotify | 完整预防护 | 所有高级特性 |

## 系统要求

- **Linux内核版本**: >= 4.19 
- **Root权限**: 必需
- **支持fanotify的文件系统**: 必需
- **C++11编译器**: GCC 4.8+ 或 Clang 3.3+

## 快速开始

### 编译

```bash
git clone <repository-url>
cd psfsmonL

# 使用Makefile编译
make

# 或使用CMake编译
mkdir build && cd build
cmake ..
make
```

### 配置热重载

PSFSMON-L 支持运行时配置热重载，无需重启程序即可应用新的配置：

```bash
# 方法1: 使用重载工具
./scripts/psfsmon-reload

# 方法2: 直接发送SIGHUP信号
kill -HUP $(cat /var/run/psfsmon.pid)

# 方法3: 使用测试脚本验证功能
./test/test_config_reload.sh
```

**重载工具选项:**
```bash
./scripts/psfsmon-reload -h                    # 显示帮助
./scripts/psfsmon-reload -p /tmp/psfsmon.pid   # 指定PID文件
./scripts/psfsmon-reload -s SIGUSR1            # 使用不同信号
./scripts/psfsmon-reload -v                    # 详细输出
```

**支持热重载的配置项:**
- 文件监控开关 (`enable_file_monitoring`)
- 网络监控开关 (`enable_network_monitoring`) 
- 进程监控开关 (`enable_process_monitoring`)
- 受保护路径监控开关 (`enable_protected_path_monitoring`)
- 终止违规进程开关 (`terminate_violating_processes`)
- 重点进程监控配置
- 受保护路径列表
- 日志级别设置
- API服务器配置

**注意事项:**
- 热重载会重新初始化相关监控模块
- 如果重载失败，程序会自动回滚到原配置
- 重载过程会记录详细日志
- 建议在修改配置前备份原配置文件

### 基本使用

```bash
# 使用默认配置运行
sudo ./psfsmon

# 使用指定配置文件
sudo ./psfsmon -c config/psfsmon.conf

# 后台服务模式
sudo ./psfsmon -d

# 查看帮助
./psfsmon -h
```

### 重点进程管理

```bash
# 设置重点进程（通过API）
curl -X POST http://localhost:8080/api/focus-processes \
     -d '{"pid": 1234}' \
     -H "Content-Type: application/json"

# 查看重点进程文件操作信息
curl http://localhost:8080/api/focus-process/file-info?pid=1234
```

### 受保护路径管理

```bash
# 添加受保护路径
curl -X POST http://localhost:8080/api/protected-paths \
     -d '{"path": "/etc/passwd"}' \
     -H "Content-Type: application/json"

# 查看受保护路径列表  
curl http://localhost:8080/api/protected-paths
```

## API接口

### 核心监控API
- `GET /api/health` - 健康检查
- `GET /api/stats` - 获取统计信息
- `GET /api/monitor/strategy` - 获取当前监控策略
- `GET /api/monitor/kernel-compat` - 获取内核兼容性信息

### 进程管理API
- `GET /api/processes` - 获取所有进程信息
- `GET /api/process-tree` - 获取进程树
- `GET /api/focus-processes` - 管理重点进程
- `GET /api/excluded-processes` - 管理排除进程

### 路径防护API
- `GET /api/protected-paths` - 管理受保护路径

### 网络监控API
- `GET /api/network` - 获取网络连接信息

## 4.19内核特殊功能

### 快速响应机制
在4.19内核中，由于Fanotify无法提供预先拦截能力，项目实现了创新的快速响应机制：

1. **事后检测**: Inotify监控受保护路径变化
2. **快速查找**: 通过/proc文件系统快速定位违规进程
3. **及时中止**: SIGTERM/SIGKILL信号快速中止违规进程
4. **文件锁定**: 临时锁定受保护文件，防止进一步操作
5. **响应优化**: 整个过程控制在100ms以内

### 监控策略查询
```bash
# 查看当前监控策略
curl http://localhost:8080/api/monitor/strategy

# 查看快速响应状态（仅4.19内核）
curl http://localhost:8080/api/monitor/quick-response
```

## 重点进程文件监控

### 熵值计算
对重点进程的文件操作进行Shannon熵值计算：
- **读操作前**: 计算原始文件熵值
- **写操作后**: 计算修改后文件熵值
- **统计汇总**: 按进程级别累加并提供平均值

### 文件操作统计
- 删除文件数量统计
- 重命名文件数量和扩展名变化记录
- 文件操作路径历史记录

## 配置文件

配置文件位于 `config/psfsmon.conf`：

```ini
[monitoring]
enable_file_monitoring=true
enable_network_monitoring=true
enable_process_monitoring=true
enable_protected_path_monitoring=true

[api]
listen_port=8080
enable_api_server=true

[focus_process]
enable_entropy_calculation=true
max_path_history=1000

[quick_response]
enable_for_4_19_kernel=true
response_timeout_ms=100
```

## 服务安装

```bash
# 复制服务文件
sudo cp scripts/psfsmon.service /etc/systemd/system/

# 启用服务
sudo systemctl enable psfsmon
sudo systemctl start psfsmon

# 查看状态
sudo systemctl status psfsmon
```

## 测试

```bash
# 运行API测试
cd test
./test_api_with_curl.sh

# 编译并运行综合测试
make
./test_api_comprehensive
```

## 架构设计

### 模块组成
- **FileMonitor**: 混合文件监控模块（Fanotify + Inotify）
- **ProcessMonitor**: 进程监控模块
- **NetworkMonitor**: 网络监控模块
- **FocusProcessManager**: 重点进程管理
- **ProtectedPathManager**: 受保护路径管理
- **KernelCompat**: 内核兼容性检测
- **APIServer**: REST API服务器

### 线程模型
- 主监控线程（Fanotify事件处理）
- Inotify辅助监控线程（4.19内核）
- 进程监控线程
- 网络监控线程
- API服务器线程池

## 故障排除

### 常见问题

1. **权限不足**
   ```bash
   # 确保以root权限运行
   sudo ./psfsmon
   ```

2. **Fanotify不支持**
   ```bash
   # 检查内核配置
   cat /proc/config.gz | gunzip | grep FANOTIFY
   ```

3. **4.19内核快速响应延迟**
   ```bash
   # 检查快速响应配置
   curl http://localhost:8080/api/monitor/quick-response
   ```

### 日志查看
```bash
# 查看系统日志
sudo journalctl -u psfsmon -f

# 查看应用日志
tail -f /var/log/psfsmon.log
```

## 开发说明

### 构建要求
- C++11标准兼容编译器
- Linux内核开发头文件
- pthread库
- 标准系统库

### 代码结构
```
psfsmonL/
├── src/           # 源代码
├── include/       # 头文件
├── config/        # 配置文件
├── test/          # 测试代码
├── scripts/       # 部署脚本
└── docs/          # 文档
```

## 贡献

欢迎提交Issue和Pull Request。请确保：
- 代码符合C++11标准
- 包含适当的测试
- 更新相关文档
- 考虑内核兼容性

## 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者

---

**注意**: 此项目专门设计用于Linux环境，不支持跨平台使用。混合监控策略确保在4.19等低版本内核中也能提供完整的受保护路径防护能力。 