#include <iostream>
#include <fstream>
#include <string>
#include <unistd.h>
#include <signal.h>
#include <sys/wait.h>
#include <cstdlib>
#include <thread>
#include <chrono>
#include <vector>

// 测试路径去重功能
class PathDeduplicationTest {
private:
    std::vector<std::string> test_files;
    pid_t child_pid;
    bool should_exit;

public:
    PathDeduplicationTest() : child_pid(0), should_exit(false) {
        // 准备测试文件列表
        test_files = {
            "./test_dedup_file1.txt",
            "./test_dedup_file2.txt", 
            "./test_dedup_file3.txt"
        };
    }

    void setupSignalHandler() {
        signal(SIGUSR1, [](int) {
            std::cout << "[Child] 收到退出信号，准备退出..." << std::endl;
            exit(0);
        });
    }

    void performRepeatedFileOperations() {
        std::cout << "[Child] 开始重复文件操作测试..." << std::endl;
        
        // 第一轮：创建和操作文件
        for (int round = 1; round <= 3; round++) {
            std::cout << "[Child] === 第 " << round << " 轮操作 ===" << std::endl;
            
            for (const auto& file_path : test_files) {
                // 1. 写入文件（每次都操作相同的文件）
                std::ofstream file(file_path);
                std::string content = "Round " + std::to_string(round) + " content for " + file_path;
                file << content;
                file.close();
                std::cout << "[Child] 写入文件: " << file_path << std::endl;
                
                // 2. 读取文件
                std::ifstream read_file(file_path);
                if (read_file.is_open()) {
                    std::string read_content;
                    std::getline(read_file, read_content);
                    read_file.close();
                    std::cout << "[Child] 读取文件: " << file_path << " (" << read_content.length() << " 字节)" << std::endl;
                }
                
                // 短暂等待
                std::this_thread::sleep_for(std::chrono::milliseconds(200));
            }
            
            // 每轮之间稍作等待
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }
        
        // 第二轮：重命名操作（测试扩展名去重）
        std::cout << "[Child] === 重命名操作测试 ===" << std::endl;
        
        // 重复进行相同的重命名操作
        for (int i = 0; i < 3; i++) {
            std::cout << "[Child] 重命名操作轮次: " << (i + 1) << std::endl;
            
            // 重命名 .txt -> .bak
            for (size_t j = 0; j < test_files.size(); j++) {
                std::string old_name = test_files[j];
                std::string new_name = old_name.substr(0, old_name.find_last_of('.')) + ".bak";
                
                if (rename(old_name.c_str(), new_name.c_str()) == 0) {
                    std::cout << "[Child] 重命名: " << old_name << " -> " << new_name << std::endl;
                }
                
                std::this_thread::sleep_for(std::chrono::milliseconds(200));
                
                // 再重命名回来 .bak -> .txt
                if (rename(new_name.c_str(), old_name.c_str()) == 0) {
                    std::cout << "[Child] 重命名: " << new_name << " -> " << old_name << std::endl;
                }
                
                std::this_thread::sleep_for(std::chrono::milliseconds(200));
            }
        }
        
        std::cout << "[Child] 文件操作完成，等待父进程收集数据..." << std::endl;
        
        // 等待父进程信号
        while (!should_exit) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }

    void runChildProcess() {
        setupSignalHandler();
        performRepeatedFileOperations();
    }

    void queryProcessData() {
        std::cout << "[Parent] 查询进程数据..." << std::endl;
        
        // 查询重点进程详细信息
        std::string curl_cmd = "curl -s 'http://localhost:8080/api/focus-processes/" + std::to_string(child_pid) + "'";
        std::cout << "[Parent] 执行命令: " << curl_cmd << std::endl;
        
        int result = system(curl_cmd.c_str());
        if (result != 0) {
            std::cout << "[Parent] API查询失败，返回码: " << result << std::endl;
        }
        std::cout << std::endl;
        
        // 查询熵值信息
        std::string entropy_cmd = "curl -s 'http://localhost:8080/api/entropy?pid=" + std::to_string(child_pid) + "'";
        std::cout << "[Parent] 执行命令: " << entropy_cmd << std::endl;
        
        result = system(entropy_cmd.c_str());
        if (result != 0) {
            std::cout << "[Parent] 熵值API查询失败，返回码: " << result << std::endl;
        }
        std::cout << std::endl;
    }

    void cleanupAndExit() {
        std::cout << "[Parent] 清理测试进程和文件..." << std::endl;
        
        // 发送退出信号给子进程
        if (child_pid > 0) {
            kill(child_pid, SIGUSR1);
            
            // 等待子进程退出
            int status;
            waitpid(child_pid, &status, 0);
            std::cout << "[Parent] 子进程已退出，状态: " << status << std::endl;
        }
        
        // 清理测试文件
        for (const auto& file_path : test_files) {
            if (unlink(file_path.c_str()) == 0) {
                std::cout << "[Parent] 删除测试文件: " << file_path << std::endl;
            }
            
            // 也尝试删除可能的 .bak 文件
            std::string bak_file = file_path.substr(0, file_path.find_last_of('.')) + ".bak";
            unlink(bak_file.c_str());
        }
    }

    void runTest() {
        std::cout << "=== 路径去重功能测试 ===" << std::endl;
        std::cout << "[Parent] 启动测试进程..." << std::endl;
        
        child_pid = fork();
        if (child_pid == 0) {
            // 子进程
            runChildProcess();
            exit(0);
        } else if (child_pid > 0) {
            // 父进程
            std::cout << "[Parent] 子进程PID: " << child_pid << std::endl;
            
            // 等待子进程完成文件操作
            std::this_thread::sleep_for(std::chrono::seconds(8));
            
            // 查询进程数据
            queryProcessData();
            
            // 清理并退出
            cleanupAndExit();
            
            std::cout << "[Parent] 测试完成" << std::endl;
            std::cout << "预期结果：" << std::endl;
            std::cout << "1. path_history 中每个文件路径只出现一次（即使操作了多次）" << std::endl;
            std::cout << "2. rename_extensions 中每种扩展名变化只出现一次（如 txt->bak）" << std::endl;
        } else {
            std::cerr << "[Parent] fork失败" << std::endl;
            exit(1);
        }
    }
};

int main() {
    std::cout << "路径去重功能测试程序" << std::endl;
    std::cout << "测试目标：验证重复的文件路径和扩展名变化不会被重复记录" << std::endl;
    std::cout << "注意：需要PSFSMON-L服务运行并且当前进程被设置为重点进程" << std::endl;
    std::cout << std::endl;
    
    PathDeduplicationTest test;
    test.runTest();
    
    return 0;
}
