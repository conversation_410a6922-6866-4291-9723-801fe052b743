# PSFSMON-L测试Makefile
# 
# 此Makefile用于编译和运行PSFSMON-L项目的测试程序

# 编译器和编译选项
CXX = g++
CXXFLAGS = -std=c++11 -Wall -Wextra -Wpedantic -O2 -g
INCLUDES = -I../include

# 库依赖
# 使用pkg-config来查找libcurl和jsoncpp库
CURL_CFLAGS = $(shell pkg-config --cflags libcurl 2>/dev/null || echo "")
CURL_LIBS = $(shell pkg-config --libs libcurl 2>/dev/null || echo "-lcurl")

JSONCPP_CFLAGS = $(shell pkg-config --cflags jsoncpp 2>/dev/null || echo "")
JSONCPP_LIBS = $(shell pkg-config --libs jsoncpp 2>/dev/null || echo "-ljsoncpp")

# 如果pkg-config不可用，尝试使用默认的库路径
ifeq ($(CURL_CFLAGS),)
    CURL_CFLAGS = 
endif

ifeq ($(JSONCPP_CFLAGS),)
    JSONCPP_CFLAGS = 
endif

# 合并所有编译标志
ALL_CFLAGS = $(CXXFLAGS) $(INCLUDES) $(CURL_CFLAGS) $(JSONCPP_CFLAGS)
ALL_LIBS = $(CURL_LIBS) $(JSONCPP_LIBS)

# 目标程序
TEST_API_COMPREHENSIVE = test_api_comprehensive
PROTECTED_PATH_VIOLATOR = protected_path_violator
ENTROPY_TEST_PROGRAM = entropy_test_program
ERROR_RECOVERY_TEST = test_error_recovery
PROCESS_CLEANUP_TEST = test_process_cleanup
FOCUS_STABILITY_TEST = test_focus_process_stability

# 源文件
COMPREHENSIVE_SRC = test_api_comprehensive.cpp
VIOLATOR_SRC = protected_path_violator.cpp
ENTROPY_SRC = entropy_test_program.cpp
ERROR_RECOVERY_SRC = test_error_recovery.cpp
PROCESS_CLEANUP_SRC = test_process_cleanup.cpp
FOCUS_STABILITY_SRC = test_focus_process_stability.cpp

# 脚本文件
TEST_SCRIPT = test_api_with_curl.sh

# 所有目标程序
TARGETS = $(TEST_API_COMPREHENSIVE) $(PROTECTED_PATH_VIOLATOR) $(ENTROPY_TEST_PROGRAM) $(ERROR_RECOVERY_TEST) $(PROCESS_CLEANUP_TEST) $(FOCUS_STABILITY_TEST)

# 默认目标
.PHONY: all clean test test-entropy test-comprehensive test-shell test-quick test-stress test-security test-process-cleanup test-focus-stability help check-deps install-deps debug

all: check-deps $(TARGETS)

# 编译主要的综合测试程序（需要网络库）
$(TEST_API_COMPREHENSIVE): $(COMPREHENSIVE_SRC)
	@echo "编译 $(TEST_API_COMPREHENSIVE)..."
	$(CXX) $(ALL_CFLAGS) -o $@ $< $(ALL_LIBS)
	@echo "编译完成: $(TEST_API_COMPREHENSIVE)"



# 编译受保护路径违规测试程序（现在需要curl库）
$(PROTECTED_PATH_VIOLATOR): $(VIOLATOR_SRC)
	@echo "编译 $(PROTECTED_PATH_VIOLATOR)..."
	$(CXX) $(ALL_CFLAGS) -o $@ $< $(CURL_LIBS)
	@echo "编译完成: $(PROTECTED_PATH_VIOLATOR)"

# 编译熵值测试程序（现在需要curl库）
$(ENTROPY_TEST_PROGRAM): $(ENTROPY_SRC)
	@echo "编译 $(ENTROPY_TEST_PROGRAM)..."
	$(CXX) $(ALL_CFLAGS) -o $@ $< $(CURL_LIBS)
	@echo "编译完成: $(ENTROPY_TEST_PROGRAM)"

# 编译错误恢复测试程序
$(ERROR_RECOVERY_TEST): $(ERROR_RECOVERY_SRC)
	@echo "编译 $(ERROR_RECOVERY_TEST)..."
	$(CXX) $(ALL_CFLAGS) -o $@ $< $(CURL_LIBS)
	@echo "编译完成: $(ERROR_RECOVERY_TEST)"

# 编译进程清理测试程序（需要curl库）
$(PROCESS_CLEANUP_TEST): $(PROCESS_CLEANUP_SRC)
	@echo "编译 $(PROCESS_CLEANUP_TEST)..."
	$(CXX) $(ALL_CFLAGS) -o $@ $< $(CURL_LIBS)
	@echo "编译完成: $(PROCESS_CLEANUP_TEST)"

# 编译重点进程状态稳定性测试程序（需要curl库）
$(FOCUS_STABILITY_TEST): $(FOCUS_STABILITY_SRC)
	@echo "编译 $(FOCUS_STABILITY_TEST)..."
	$(CXX) $(ALL_CFLAGS) -o $@ $< $(CURL_LIBS)
	@echo "编译完成: $(FOCUS_STABILITY_TEST)"

# 检查依赖库
check-deps:
	@echo "检查依赖库..."
	@echo -n "检查 libcurl: "
	@if pkg-config --exists libcurl; then \
	        echo "✓ 已安装 (版本: $$(pkg-config --modversion libcurl))"; \
	elif echo '#include <curl/curl.h>' | $(CXX) -E -x c++ - >/dev/null 2>&1; then \
	        echo "✓ 已安装 (通过默认路径)"; \
	else \
	        echo "✗ 未安装"; \
	        echo "请安装 libcurl-dev 或 curl-devel 包"; \
	        exit 1; \
	fi
	@echo -n "检查 jsoncpp: "
	@if pkg-config --exists jsoncpp; then \
	        echo "✓ 已安装 (版本: $$(pkg-config --modversion jsoncpp))"; \
	elif echo '#include <jsoncpp/json/json.h>' | $(CXX) -E -x c++ - >/dev/null 2>&1; then \
	        echo "✓ 已安装 (通过默认路径)"; \
	elif echo '#include <json/json.h>' | $(CXX) -E -x c++ - >/dev/null 2>&1; then \
	        echo "✓ 已安装 (通过默认路径)"; \
	else \
	        echo "✗ 未安装"; \
	        echo "请安装 libjsoncpp-dev 或 jsoncpp-devel 包"; \
	        exit 1; \
	fi
	@echo "所有依赖库检查完成"

# 安装依赖库 (Ubuntu/Debian)
install-deps:
	@echo "正在安装依赖库..."
	@if command -v apt-get >/dev/null 2>&1; then \
	        echo "使用 apt-get 安装依赖..."; \
	        sudo apt-get update && sudo apt-get install -y libcurl4-openssl-dev libjsoncpp-dev; \
	elif command -v yum >/dev/null 2>&1; then \
	        echo "使用 yum 安装依赖..."; \
	        sudo yum install -y libcurl-devel jsoncpp-devel; \
	elif command -v dnf >/dev/null 2>&1; then \
	        echo "使用 dnf 安装依赖..."; \
	        sudo dnf install -y libcurl-devel jsoncpp-devel; \
	elif command -v pacman >/dev/null 2>&1; then \
	        echo "使用 pacman 安装依赖..."; \
	        sudo pacman -S curl jsoncpp; \
	else \
	        echo "无法自动安装依赖库，请手动安装 libcurl 和 jsoncpp 开发包"; \
	        exit 1; \
	fi

# 运行完整的综合测试
test: all
	@echo "=== 运行PSFSMON-L 完整API测试 ==="
	@echo ""
	@echo "注意: 请确保PSFSMON-L服务正在运行 (默认端口8080)"
	@echo "启动命令: sudo ../bin/psfsmon --api-port 8080"
	@echo ""
	@read -p "按Enter键继续，或Ctrl+C取消..." dummy
	@echo ""
	@echo "--- 运行综合API测试程序 ---"
	@if [ -x "./$(TEST_API_COMPREHENSIVE)" ]; then \
	        ./$(TEST_API_COMPREHENSIVE) --test complete-behavior,entropy-test,focus-process; \
	else \
	        echo "错误: $(TEST_API_COMPREHENSIVE) 不存在或不可执行"; \
	        exit 1; \
	fi

# 运行专门的熵值测试
test-entropy: all
	@echo "=== 运行PSFSMON-L 熵值专项测试 ==="
	@echo ""
	@echo "注意: 请确保PSFSMON-L服务正在运行 (默认端口8080)"
	@echo "启动命令: sudo ../bin/psfsmon --api-port 8080"
	@echo ""
	@read -p "按Enter键继续，或Ctrl+C取消..." dummy
	@echo ""
	@echo "--- 运行熵值专项测试 ---"
	@if [ -x "./$(TEST_API_COMPREHENSIVE)" ]; then \
	        ./$(TEST_API_COMPREHENSIVE) --test entropy-test; \
	else \
	        echo "错误: $(TEST_API_COMPREHENSIVE) 不存在或不可执行"; \
	        exit 1; \
	fi

# 运行内部熵值API测试
test-entropy-api-internal: all
	@echo "=== 运行PSFSMON-L 内部熵值API完整测试 ==="
	@echo ""
	@echo "注意: 请确保PSFSMON-L服务正在运行 (默认端口8080)"
	@echo "启动命令: sudo ../bin/psfsmon --api-port 8080"
	@echo ""
	@read -p "按Enter键继续，或Ctrl+C取消..." dummy
	@echo ""
	@echo "--- 运行内部熵值API测试 ---"
	@if [ -x "./$(TEST_API_COMPREHENSIVE)" ]; then \
	        ./$(TEST_API_COMPREHENSIVE) --test entropy-api-internal; \
	else \
	        echo "错误: $(TEST_API_COMPREHENSIVE) 不存在或不可执行"; \
	        exit 1; \
	fi

# 运行全面的综合测试
test-comprehensive: all
	@echo "=== 运行PSFSMON-L 全面综合测试 ==="
	@echo ""
	@echo "注意: 请确保PSFSMON-L服务正在运行 (默认端口8080)"
	@echo "启动命令: sudo ../bin/psfsmon --api-port 8080"
	@echo ""
	@read -p "按Enter键继续，或Ctrl+C取消..." dummy
	@echo ""
	@echo "--- 运行全面API测试程序 ---"
	@if [ -x "./$(TEST_API_COMPREHENSIVE)" ]; then \
	        ./$(TEST_API_COMPREHENSIVE) --all; \
	else \
	        echo "错误: $(TEST_API_COMPREHENSIVE) 不存在或不可执行"; \
	        exit 1; \
	fi

# 运行基础功能测试
test-basic: all
	@echo "=== 运行PSFSMON-L 基础功能测试 ==="
	@echo ""
	@echo "注意: 请确保PSFSMON-L服务正在运行 (默认端口8080)"
	@echo "启动命令: sudo ../bin/psfsmon --api-port 8080"
	@echo ""
	@read -p "按Enter键继续，或Ctrl+C取消..." dummy
	@echo ""
	@echo "--- 运行基础API测试 ---"
	@if [ -x "./$(TEST_API_COMPREHENSIVE)" ]; then \
	        ./$(TEST_API_COMPREHENSIVE) --test health,process,network,stats,config; \
	else \
	        echo "错误: $(TEST_API_COMPREHENSIVE) 不存在或不可执行"; \
	        exit 1; \
	fi

# 运行重点进程专项测试
test-focus-process: all
	@echo "=== 运行PSFSMON-L 重点进程专项测试 ==="
	@echo ""
	@echo "注意: 请确保PSFSMON-L服务正在运行 (默认端口8080)"
	@echo "启动命令: sudo ../bin/psfsmon --api-port 8080"
	@echo ""
	@read -p "按Enter键继续，或Ctrl+C取消..." dummy
	@echo ""
	@echo "--- 运行重点进程专项测试 ---"
	@if [ -x "./$(TEST_API_COMPREHENSIVE)" ]; then \
	        ./$(TEST_API_COMPREHENSIVE) --test focus-process,entropy-test,complete-behavior; \
	else \
	        echo "错误: $(TEST_API_COMPREHENSIVE) 不存在或不可执行"; \
	        exit 1; \
	fi

# 运行受保护路径测试
test-protected-path: all
	@echo "=== 运行PSFSMON-L 受保护路径测试 ==="
	@echo ""
	@echo "注意: 请确保PSFSMON-L服务正在运行 (默认端口8080)"
	@echo "启动命令: sudo ../bin/psfsmon --api-port 8080"
	@echo ""
	@read -p "按Enter键继续，或Ctrl+C取消..." dummy
	@echo ""
	@echo "--- 运行受保护路径违规测试 ---"
	@if [ -x "./$(TEST_API_COMPREHENSIVE)" ]; then \
	        ./$(TEST_API_COMPREHENSIVE) --test protected-path; \
	else \
	        echo "错误: $(TEST_API_COMPREHENSIVE) 不存在或不可执行"; \
	        exit 1; \
	fi

# 运行进程清理测试
test-process-cleanup: all
	@echo "=== 运行PSFSMON-L 进程清理测试 ==="
	@echo ""
	@echo "注意: 请确保PSFSMON-L服务正在运行 (默认端口8080)"
	@echo "启动命令: sudo ../bin/psfsmon --api-port 8080"
	@echo ""
	@read -p "按Enter键继续，或Ctrl+C取消..." dummy
	@echo ""
	@echo "--- 运行进程清理测试 ---"
	@if [ -x "./$(PROCESS_CLEANUP_TEST)" ]; then \
	        ./$(PROCESS_CLEANUP_TEST); \
	else \
	        echo "错误: $(PROCESS_CLEANUP_TEST) 不存在或不可执行"; \
	        exit 1; \
	fi

# 运行重点进程状态稳定性测试
test-focus-stability: all
	@echo "=== 运行PSFSMON-L 重点进程状态稳定性测试 ==="
	@echo ""
	@echo "注意: 请确保PSFSMON-L服务正在运行 (默认端口8080)"
	@echo "启动命令: sudo ../bin/psfsmon --api-port 8080"
	@echo ""
	@read -p "按Enter键继续，或Ctrl+C取消..." dummy
	@echo ""
	@echo "--- 运行重点进程状态稳定性测试 ---"
	@if [ -x "./$(FOCUS_STABILITY_TEST)" ]; then \
	        ./$(FOCUS_STABILITY_TEST); \
	else \
	        echo "错误: $(FOCUS_STABILITY_TEST) 不存在或不可执行"; \
	        exit 1; \
	fi

# 运行shell脚本测试
test-shell:
	@echo "=== 运行Shell脚本测试 ==="
	@echo ""
	@echo "注意: 请确保PSFSMON-L服务正在运行 (默认端口8080)"
	@echo "启动命令: sudo ../bin/psfsmon --api-port 8080"
	@echo ""
	@read -p "按Enter键继续，或Ctrl+C取消..." dummy
	@chmod +x $(TEST_SCRIPT)
	@./$(TEST_SCRIPT)

# 快速测试（无交互）
test-quick: all
	@echo "=== 快速API测试 (无交互模式) ==="
	@if [ -x "./$(TEST_API_COMPREHENSIVE)" ]; then \
	        timeout 60s ./$(TEST_API_COMPREHENSIVE) --test health,stats --no-start 2>/dev/null || true; \
	else \
	        echo "错误: $(TEST_API_COMPREHENSIVE) 不存在或不可执行"; \
	fi

# 压力测试
test-stress: all
	@echo "=== 运行PSFSMON-L 压力测试 ==="
	@echo ""
	@echo "注意: 请确保PSFSMON-L服务正在运行 (默认端口8080)"
	@echo "启动命令: sudo ../bin/psfsmon --api-port 8080"
	@echo ""
	@read -p "按Enter键继续，或Ctrl+C取消..." dummy
	@echo ""
	@echo "--- 运行压力测试 ---"
	@if [ -x "./psfsmon_comprehensive_test.sh" ]; then \
	        ./psfsmon_comprehensive_test.sh stress; \
	else \
	        echo "错误: psfsmon_comprehensive_test.sh 不存在或不可执行"; \
	        exit 1; \
	fi

# 安全测试
test-security: all
	@echo "=== 运行PSFSMON-L 安全测试 ==="
	@echo ""
	@echo "注意: 请确保PSFSMON-L服务正在运行 (默认端口8080)"
	@echo "启动命令: sudo ../bin/psfsmon --api-port 8080"
	@echo ""
	@read -p "按Enter键继续，或Ctrl+C取消..." dummy
	@echo ""
	@echo "--- 运行安全测试 ---"
	@if [ -x "./psfsmon_comprehensive_test.sh" ]; then \
	        ./psfsmon_comprehensive_test.sh security; \
	else \
	        echo "错误: psfsmon_comprehensive_test.sh 不存在或不可执行"; \
	        exit 1; \
	fi

# 清理
clean:
	@echo "清理测试文件..."
	rm -f $(TARGETS)
	rm -f *.o
	@echo "清理完成"

# 显示帮助
help:
	@echo "PSFSMON-L测试Makefile帮助"
	@echo ""
	@echo "可用目标:"
	@echo "  all                   - 编译所有测试程序 (默认)"
	@echo "  check-deps            - 检查编译依赖库"
	@echo "  install-deps          - 自动安装依赖库"
	@echo ""
	@echo "测试目标:"
	@echo "  test                     - 运行完整综合测试 (重点进程+熵值测试)"
	@echo "  test-entropy             - 运行熵值专项测试"
	@echo "  test-entropy-api-internal - 运行内部熵值API完整测试"
	@echo "  test-comprehensive       - 运行全面综合测试 (所有API)"
	@echo "  test-basic               - 运行基础功能测试"
	@echo "  test-focus-process       - 运行重点进程专项测试"
	@echo "  test-protected-path      - 运行受保护路径测试"
	@echo "  test-process-cleanup     - 运行进程清理测试"
	@echo "  test-focus-stability     - 运行重点进程状态稳定性测试"
	@echo "  test-shell            - 运行Shell脚本测试"
	@echo "  test-quick            - 快速测试 (无交互，60秒超时)"
	@echo "  test-stress           - 压力测试 (高并发、大数据量)"
	@echo "  test-security         - 安全测试 (边界条件、恶意请求)"
	@echo ""
	@echo "管理目标:"
	@echo "  clean                 - 清理编译文件"
	@echo "  help                  - 显示此帮助信息"
	@echo "  debug                 - 显示编译调试信息"
	@echo ""
	@echo "编译的测试程序:"
	@echo "  $(TEST_API_COMPREHENSIVE)        - 主要综合测试程序"
	@echo "  $(PROTECTED_PATH_VIOLATOR)      - 受保护路径违规测试程序"
	@echo "  $(ENTROPY_TEST_PROGRAM)        - 熵值测试程序"
	@echo "  $(PROCESS_CLEANUP_TEST)        - 进程清理测试程序"
	@echo "  $(FOCUS_STABILITY_TEST)        - 重点进程状态稳定性测试程序"
	@echo ""
	@echo "使用示例:"
	@echo "  make                              # 编译所有测试程序"
	@echo "  make install-deps                 # 安装依赖库"
	@echo "  make test                         # 运行完整测试"
	@echo "  make test-entropy                 # 熵值专项测试"
	@echo "  make test-entropy-api-internal    # 内部熵值API完整测试"
	@echo "  make test-focus-process           # 重点进程专项测试"
	@echo "  make test-process-cleanup         # 进程清理测试"
	@echo "  make test-focus-stability         # 重点进程状态稳定性测试"
	@echo "  make test-quick                   # 快速测试"
	@echo "  make clean                        # 清理文件"
	@echo ""
	@echo "注意事项:"
	@echo "  - 运行测试前请确保PSFSMON-L服务已启动"
	@echo "  - 默认API端口为8080，可通过环境变量API_PORT修改"
	@echo "  - 测试需要root权限来启动PSFSMON-L服务"
	@echo "  - 熵值测试会创建临时文件，请确保/tmp目录有足够空间"

# 调试信息
debug:
	@echo "=== 编译调试信息 ==="
	@echo "CXX: $(CXX)"
	@echo "CXXFLAGS: $(CXXFLAGS)"
	@echo "INCLUDES: $(INCLUDES)"
	@echo "CURL_CFLAGS: $(CURL_CFLAGS)"
	@echo "CURL_LIBS: $(CURL_LIBS)"
	@echo "JSONCPP_CFLAGS: $(JSONCPP_CFLAGS)"
	@echo "JSONCPP_LIBS: $(JSONCPP_LIBS)"
	@echo "ALL_CFLAGS: $(ALL_CFLAGS)"
	@echo "ALL_LIBS: $(ALL_LIBS)"
	@echo ""
	@echo "=== 目标程序 ==="
	@echo "TARGETS: $(TARGETS)"
	@echo ""
	@echo "=== 源文件 ==="
	@echo "COMPREHENSIVE_SRC: $(COMPREHENSIVE_SRC)"
	@echo "VIOLATOR_SRC: $(VIOLATOR_SRC)"
	@echo "ENTROPY_SRC: $(ENTROPY_SRC)"
	@echo "ERROR_RECOVERY_SRC: $(ERROR_RECOVERY_SRC)"
	@echo "PROCESS_CLEANUP_SRC: $(PROCESS_CLEANUP_SRC)"
	@echo "FOCUS_STABILITY_SRC: $(FOCUS_STABILITY_SRC)" 