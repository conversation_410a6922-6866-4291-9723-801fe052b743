# PSFSMON-L 测试套件

## 概述

本目录包含PSFSMON-L项目的完整测试套件，已整合为单一的综合测试脚本。

## 文件说明

### 主要文件
- **`psfsmon_comprehensive_test.sh`** - 统一的综合测试脚本（唯一的shell脚本）
- **`entropy_test_program.cpp`** - 重点进程熵值测试程序源码
- **`protected_path_violator.cpp`** - 受保护路径违规测试程序源码
- **`test_api_comprehensive.cpp`** - API综合测试程序源码（可选）
- **`test_error_recovery.cpp`** - 错误恢复和边界条件测试程序源码
- **`Makefile`** - 编译测试程序的Makefile

### 编译产物
- **`entropy_test_program`** - 重点进程熵值测试可执行文件
- **`protected_path_violator`** - 受保护路径违规测试可执行文件
- **`test_api_comprehensive`** - API综合测试可执行文件
- **`test_error_recovery`** - 错误恢复测试可执行文件

## 快速开始

### 1. 编译测试程序
```bash
make -C test
```

### 2. 启动PSFSMON服务
```bash
sudo ./bin/psfsmon --api-port 8080
```

### 3. 运行测试
```bash
# 运行所有测试
./test/psfsmon_comprehensive_test.sh

# 运行特定测试
./test/psfsmon_comprehensive_test.sh api-basic
./test/psfsmon_comprehensive_test.sh entropy-quick
./test/psfsmon_comprehensive_test.sh protected-path
```

## 测试类别详解

### API测试
- **`api-basic`** - 基础API测试（健康检查、状态、统计）
- **`api-process`** - 进程相关API测试
- **`api-network`** - 网络相关API测试
- **`api-management`** - 管理API测试（重点进程、排除进程、受保护路径）
- **`api`** - 运行所有API测试

### 功能测试
- **`entropy-quick`** - 快速熵值测试（小文件，约30秒）
- **`entropy-complete`** - 完整熵值测试（大文件，约2分钟）
- **`entropy`** - 默认完整熵值测试
- **`protected-path`** - 受保护路径功能测试
- **`comprehensive`** - 综合功能测试（包含熵值+受保护路径+API）

### 专项测试
- **`deadlock-check`** - API死锁检测测试
- **`performance`** - 性能测试
- **`stress`** - 压力测试（高并发、大数据量）
- **`security`** - 安全测试（边界条件、恶意请求）
- **`all`** - 运行所有测试（默认）

## 高级用法

### 自定义配置
```bash
# 使用不同的API URL
./test/psfsmon_comprehensive_test.sh --url http://localhost:9090 api

# 设置超时时间
./test/psfsmon_comprehensive_test.sh --timeout 30 all

# 详细输出模式
./test/psfsmon_comprehensive_test.sh --verbose comprehensive

# 自定义测试目录
./test/psfsmon_comprehensive_test.sh --test-dir /tmp/my_test entropy
```

### 组合测试
```bash
# 运行多个测试类别
./test/psfsmon_comprehensive_test.sh api-basic entropy-quick performance
```

## 测试程序说明

### entropy_test_program
重点进程熵值测试程序，用于验证文件熵值计算功能。

**特性：**
- 支持自动注册为重点进程
- 创建不同大小的测试文件（10MB-200MB）
- 执行读取、写入、修改操作
- 支持快速模式和完整模式

**用法：**
```bash
./test/entropy_test_program --quick --self-register
./test/entropy_test_program --complete --test-dir /tmp/my_test
```

### protected_path_violator
受保护路径违规测试程序，用于验证受保护路径监控功能。

**特性：**
- 支持自动注册受保护路径
- 执行多种违规操作（创建、修改、读取、删除文件）
- 等待监控系统响应
- 检测是否被正确终止

**用法：**
```bash
./test/protected_path_violator /tmp/protected --self-register
```

## 测试原理

### 进程隔离
- 所有测试程序使用fork()启动，避免shell进程被监控
- 确保测试的是真实的子进程，而不是shell脚本进程

### 生命周期管理
- 自动注册和清理重点进程/受保护路径
- 智能等待机制，确保监控系统有足够时间处理事件
- 完善的清理机制，避免测试残留

### 错误处理
- 全面的错误检测和报告
- 超时检测，防止死锁
- 优雅的失败处理

## 故障排除

### 常见问题

1. **测试程序编译失败**
   ```bash
   # 检查依赖库
   sudo apt-get install libcurl4-openssl-dev libjsoncpp-dev
   make -C test
   ```

2. **API连接失败**
   ```bash
   # 检查服务状态
   curl http://127.0.0.1:8080/api/health
   
   # 检查端口占用
   netstat -tlnp | grep 8080
   ```

3. **权限问题**
   ```bash
   # 确保以root权限运行psfsmon
   sudo ./bin/psfsmon --api-port 8080
   ```

4. **测试超时**
   ```bash
   # 增加超时时间
   ./test/psfsmon_comprehensive_test.sh --timeout 30 all
   ```

### 调试模式
```bash
# 启用详细输出
./test/psfsmon_comprehensive_test.sh --verbose all

# 检查测试程序日志
./test/entropy_test_program --help
./test/protected_path_violator --help
```

## 测试结果解读

### 成功标准
- ✅ 通过：功能正常工作
- ❌ 失败：功能存在问题
- ⏭️ 跳过：API未实现或不可用

### 关键指标
- **API响应时间** < 1秒
- **熵值计算** 能检测到数据
- **受保护路径** 能正确终止违规进程
- **无死锁** 所有API正常响应

## 开发指南

### 添加新测试
1. 在脚本中添加新的测试函数
2. 在主函数的case语句中添加新的测试类别
3. 更新帮助信息

### 修改测试程序
1. 编辑对应的.cpp文件
2. 运行`make -C test`重新编译
3. 测试新功能

## 注意事项

- 测试可能会创建临时文件，请在安全环境中运行
- 某些测试需要root权限
- 受保护路径测试可能会终止违规进程
- 建议在测试环境而非生产环境运行

## 联系支持

如有问题，请查看：
1. 项目主README.md
2. 架构分析文档：PROJECT_ARCHITECTURE_ANALYSIS.md
3. 开发指南：DEVELOPER_HANDOVER_GUIDE.md
