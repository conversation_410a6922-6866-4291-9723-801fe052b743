#include <iostream>
#include <fstream>
#include <string>
#include <unistd.h>
#include <signal.h>
#include <sys/wait.h>
#include <cstdlib>
#include <thread>
#include <chrono>

// 测试文件写入大小记录功能
class FileSizeTest {
private:
    std::string test_file_path;
    pid_t child_pid;
    bool should_exit;

public:
    FileSizeTest() : test_file_path("./test_writesize_file.txt"), child_pid(0), should_exit(false) {}

    void setupSignalHandler() {
        signal(SIGUSR1, [](int) {
            // 收到父进程信号，准备退出
            std::cout << "[Child] 收到退出信号，准备退出..." << std::endl;
            exit(0);
        });
    }

    void performFileOperations() {
        std::cout << "[Child] 开始文件操作测试..." << std::endl;
        
        // 1. 读取原始文件（如果存在）
        std::ifstream read_file(test_file_path);
        if (read_file.is_open()) {
            std::string content;
            std::getline(read_file, content);
            std::cout << "[Child] 读取原始文件内容: " << content.length() << " 字节" << std::endl;
            read_file.close();
        }
        
        // 2. 写入小文件
        std::ofstream small_file(test_file_path);
        std::string small_content = "Small test content for writesize test.";
        small_file << small_content;
        small_file.close();
        std::cout << "[Child] 写入小文件: " << small_content.length() << " 字节" << std::endl;
        
        // 短暂等待
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
        
        // 3. 写入大文件
        std::ofstream large_file(test_file_path);
        std::string large_content;
        for (int i = 0; i < 1000; i++) {
            large_content += "This is line " + std::to_string(i) + " of the large test file for writesize testing.\n";
        }
        large_file << large_content;
        large_file.close();
        std::cout << "[Child] 写入大文件: " << large_content.length() << " 字节" << std::endl;
        
        // 4. 追加写入
        std::ofstream append_file(test_file_path, std::ios::app);
        std::string append_content = "\nAppended content for additional writesize testing.";
        append_file << append_content;
        append_file.close();
        std::cout << "[Child] 追加写入: " << append_content.length() << " 字节" << std::endl;
        
        std::cout << "[Child] 文件操作完成，等待父进程收集数据..." << std::endl;
        
        // 等待父进程信号
        while (!should_exit) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }

    void runChildProcess() {
        setupSignalHandler();
        performFileOperations();
    }

    void queryEntropyData() {
        std::cout << "[Parent] 查询熵值数据..." << std::endl;
        
        // 使用curl查询API
        std::string curl_cmd = "curl -s 'http://localhost:8080/api/entropy?pid=" + std::to_string(child_pid) + "'";
        std::cout << "[Parent] 执行命令: " << curl_cmd << std::endl;
        
        int result = system(curl_cmd.c_str());
        if (result != 0) {
            std::cout << "[Parent] API查询失败，返回码: " << result << std::endl;
        }
        std::cout << std::endl;
    }

    void cleanupAndExit() {
        std::cout << "[Parent] 清理测试进程..." << std::endl;
        
        // 发送退出信号给子进程
        if (child_pid > 0) {
            kill(child_pid, SIGUSR1);
            
            // 等待子进程退出
            int status;
            waitpid(child_pid, &status, 0);
            std::cout << "[Parent] 子进程已退出，状态: " << status << std::endl;
        }
        
        // 清理测试文件
        if (unlink(test_file_path.c_str()) == 0) {
            std::cout << "[Parent] 测试文件已删除" << std::endl;
        }
    }

    void runTest() {
        std::cout << "=== 文件写入大小记录测试 ===" << std::endl;
        std::cout << "[Parent] 启动测试进程..." << std::endl;
        
        child_pid = fork();
        if (child_pid == 0) {
            // 子进程
            runChildProcess();
            exit(0);
        } else if (child_pid > 0) {
            // 父进程
            std::cout << "[Parent] 子进程PID: " << child_pid << std::endl;
            
            // 等待子进程完成文件操作
            std::this_thread::sleep_for(std::chrono::seconds(3));
            
            // 查询熵值数据
            queryEntropyData();
            
            // 清理并退出
            cleanupAndExit();
            
            std::cout << "[Parent] 测试完成" << std::endl;
        } else {
            std::cerr << "[Parent] fork失败" << std::endl;
            exit(1);
        }
    }
};

int main() {
    std::cout << "文件写入大小记录功能测试程序" << std::endl;
    std::cout << "测试目标：验证file_writesize字段是否正确记录和输出" << std::endl;
    std::cout << "注意：需要PSFSMON-L服务运行并且当前进程被设置为重点进程" << std::endl;
    std::cout << std::endl;
    
    FileSizeTest test;
    test.runTest();
    
    return 0;
}
