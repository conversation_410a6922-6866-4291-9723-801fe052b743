/*
 * 重点进程熵值测试程序
 * 
 * 本程序用于测试psfsmon项目的重点进程文件熵值计算功能
 * 模拟真实的文件操作场景，包括：
 * 1. 读取不同大小的文件（小文件、中等文件、大文件、超大文件）
 * 2. 写入和修改文件
 * 3. 输出详细的操作日志
 * 4. 支持命令行参数控制测试行为
 */

#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <chrono>
#include <thread>
#include <cstring>
#include <cstdlib>
#include <ctime>
#include <unistd.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <fcntl.h>
#include <sys/wait.h>
#include <errno.h>
#include <curl/curl.h>
#include <sstream>
#include <signal.h>
#include <atomic>
#include <iomanip> // 用于格式化输出

// 全局变量用于信号处理
static std::atomic<bool> g_graceful_shutdown(false);

class EntropyTestProgram {
private:
    std::string test_dir_;
    std::vector<std::string> test_files_;
    bool verbose_;
    int delay_ms_;
    std::string api_base_url_;
    bool self_register_;
    
    // 测试文件配置
    struct TestFile {
        std::string name;
        size_t size_mb;
        std::string description;
    };
    
    std::vector<TestFile> test_configs_ = {
        {"small_file.bin", 10, "小文件（10MB）"},     // 缩小文件大小，加快测试
        {"medium_file.bin", 50, "中等文件（50MB）"},
        {"large_file.bin", 100, "大文件（100MB）"},
        {"huge_file.bin", 200, "超大文件（200MB）"}   // 降低资源消耗
    };

    // HTTP回调函数
    static size_t WriteCallback(void* contents, size_t size, size_t nmemb, std::string* userp) {
        userp->append((char*)contents, size * nmemb);
        return size * nmemb;
    }

public:
    EntropyTestProgram(const std::string& test_dir = "/tmp/entropy_test", 
                      bool verbose = true, int delay_ms = 100,
                      const std::string& api_url = "http://127.0.0.1:8080",
                      bool self_register = false)
        : test_dir_(test_dir), verbose_(verbose), delay_ms_(delay_ms), 
          api_base_url_(api_url), self_register_(self_register) {
        
        // 初始化CURL
        if (self_register_) {
            curl_global_init(CURL_GLOBAL_DEFAULT);
        }
        
        // 创建测试目录
        if (mkdir(test_dir_.c_str(), 0755) != 0 && errno != EEXIST) {
            std::cerr << "错误: 无法创建测试目录 " << test_dir_ << ": " << strerror(errno) << std::endl;
            exit(1);
        }
        
        // 初始化测试文件路径
        for (const auto& config : test_configs_) {
            test_files_.push_back(test_dir_ + "/" + config.name);
        }
    }
    
    ~EntropyTestProgram() {
        // 清理测试文件
        if (!verbose_) {  // 只有在非详细模式下才清理
            cleanup();
        }
        
        // 清理CURL
        if (self_register_) {
            curl_global_cleanup();
        }
    }
    
    void log(const std::string& message) {
        if (verbose_) {
            auto now = std::chrono::system_clock::now();
            auto time_val = std::chrono::system_clock::to_time_t(now);
            auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                now.time_since_epoch()) % 1000;
            
            std::cout << "[" << std::ctime(&time_val) << " " << ms.count() << "ms] "
                      << "PID=" << getpid() << " " << message << std::endl;
        }
    }
    
    void log_operation(const std::string& operation, const std::string& filename, size_t size_mb = 0) {
        std::string message = operation + ": " + filename;
        if (size_mb > 0) {
            message += " (" + std::to_string(size_mb) + "MB)";
        }
        log(message);
    }

    // 优雅退出处理方法
    void handleGracefulShutdown() {
        log("收到优雅退出信号，准备退出...");

        // 注意：不在这里清理重点进程注册！
        // 清理工作应该由主测试程序在收集完数据后执行
        if (self_register_) {
            log("保持重点进程注册状态，由主测试程序负责清理");
        }

        log("优雅退出流程完成，程序即将退出");
        log("注意：重点进程注册等清理工作由主测试程序负责");
    }
    
    // HTTP请求方法
    bool makeHttpRequest(const std::string& method, const std::string& endpoint, const std::string& data = "") {
        if (!self_register_) return false;
        
        CURL* curl = curl_easy_init();
        if (!curl) {
            log("CURL初始化失败");
            return false;
        }
        
        std::string url = api_base_url_ + endpoint;
        std::string response;
        
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 5L);
        
        // 设置HTTP方法
        if (method == "POST") {
            curl_easy_setopt(curl, CURLOPT_POST, 1L);
            if (!data.empty()) {
                curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data.c_str());
            }
        } else if (method == "DELETE") {
            curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "DELETE");
        }
        
        // 设置HTTP头
        struct curl_slist* headers = nullptr;
        if (!data.empty()) {
            headers = curl_slist_append(headers, "Content-Type: application/json");
            curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        }
        
        // 执行请求
        CURLcode res = curl_easy_perform(curl);
        long response_code = 0;
        curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &response_code);
        
        curl_easy_cleanup(curl);
        if (headers) curl_slist_free_all(headers);
        
        if (res != CURLE_OK) {
            log("HTTP请求失败: " + std::string(curl_easy_strerror(res)));
            return false;
        }
        
        if (response_code >= 200 && response_code < 300) {
            log("HTTP请求成功: " + method + " " + endpoint + " -> " + std::to_string(response_code));
            return true;
        } else {
            log("HTTP请求失败: " + method + " " + endpoint + " -> " + std::to_string(response_code) + " " + response);
            return false;
        }
    }
    
    // 将自己注册为重点进程
    bool registerAsFocusProcess() {
        if (!self_register_) return true;

        log("开始将自己注册为重点进程...");

        // 方式1：优先通过PID注册（更可靠）
        pid_t my_pid = getpid();
        std::string data = R"({"type": "pid", "value": ")" + std::to_string(my_pid) + R"("})";
        if (makeHttpRequest("POST", "/api/focus-processes", data)) {
            log("成功通过PID注册为重点进程: " + std::to_string(my_pid));
            return true;
        }

        // 方式2：备选通过进程名注册
        data = R"({"type": "name", "value": "entropy_test_program"})";
        if (makeHttpRequest("POST", "/api/focus-processes", data)) {
            log("成功通过进程名注册为重点进程");
            return true;
        }

        log("注册重点进程失败，可能服务未启动或API不可用");
        return false;
    }
    
    // 从重点进程列表中移除自己
    bool unregisterFromFocusProcess() {
        if (!self_register_) return true;
        
        log("开始从重点进程列表中移除自己...");
        
        // 方式1：通过进程名移除
        if (makeHttpRequest("DELETE", "/api/focus-processes?type=name&value=entropy_test_program")) {
            log("成功从重点进程列表中移除");
            return true;
        }
        
        // 方式2：通过PID移除
        pid_t my_pid = getpid();
        if (makeHttpRequest("DELETE", "/api/focus-processes?type=pid&value=" + std::to_string(my_pid))) {
            log("成功通过PID从重点进程列表中移除: " + std::to_string(my_pid));
            return true;
        }
        
        log("移除重点进程失败");
        return false;
    }

    // 查询并显示熵值信息
    void query_and_display_entropy_info(const std::string& operation_type, const std::string& filepath = "") {
        if (!self_register_) {
            return; // 如果没有自注册，就不查询熵值
        }

        log("🔍 查询熵值信息 - " + operation_type);

        // 查询当前进程的熵值信息
        pid_t current_pid = getpid();
        std::string entropy_url = "/api/focus-processes/entropy?pid=" + std::to_string(current_pid);

        CURL* curl = curl_easy_init();
        if (!curl) {
            log("❌ 无法初始化CURL进行熵值查询");
            return;
        }

        std::string url = api_base_url_ + entropy_url;
        std::string response;

        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 5L);

        CURLcode res = curl_easy_perform(curl);
        long response_code = 0;
        curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &response_code);
        curl_easy_cleanup(curl);

        if (res == CURLE_OK && response_code == 200) {
            parse_and_display_entropy_response(response, operation_type, filepath);
        } else {
            log("⚠️ 熵值查询失败: HTTP " + std::to_string(response_code));
        }

        // 同时查询详细统计信息
        query_entropy_stats();
    }

    // 解析并显示熵值响应（格式化显示）
    void parse_and_display_entropy_response(const std::string& response, const std::string& operation_type, const std::string& filepath) {
        log("📊 " + operation_type + " 熵值分析结果 (格式化显示):");

        // 简单的JSON解析（查找关键字段）
        size_t pos = 0;

        // 查找总原始熵值
        pos = response.find("\"total_original_entropy\":");
        if (pos != std::string::npos) {
            pos += 25; // 跳过字段名
            size_t end_pos = response.find_first_of(",}", pos);
            if (end_pos != std::string::npos) {
                std::string value_str = response.substr(pos, end_pos - pos);
                try {
                    double value = std::stod(value_str);
                    log(format_entropy_value(value, "📈 总原始熵值"));
                } catch (const std::exception&) {
                    log("  📈 总原始熵值: " + value_str + " (解析错误)");
                }
            }
        }

        // 查找总最终熵值
        pos = response.find("\"total_final_entropy\":");
        if (pos != std::string::npos) {
            pos += 22; // 跳过字段名
            size_t end_pos = response.find_first_of(",}", pos);
            if (end_pos != std::string::npos) {
                std::string value_str = response.substr(pos, end_pos - pos);
                try {
                    double value = std::stod(value_str);
                    log(format_entropy_value(value, "📉 总最终熵值"));
                } catch (const std::exception&) {
                    log("  📉 总最终熵值: " + value_str + " (解析错误)");
                }
            }
        }

        // 查找熵值变化量
        pos = response.find("\"total_entropy_change\":");
        if (pos != std::string::npos) {
            pos += 23; // 跳过字段名
            size_t end_pos = response.find_first_of(",}", pos);
            if (end_pos != std::string::npos) {
                std::string value_str = response.substr(pos, end_pos - pos);
                try {
                    double change = std::stod(value_str);
                    std::string trend = (change > 0) ? "📈 增加" : (change < 0) ? "📉 减少" : "➡️ 无变化";
                    log(format_entropy_value(change, "🔄 熵值变化") + " (" + trend + ")");
                } catch (const std::exception&) {
                    log("  🔄 熵值变化: " + value_str + " (解析错误)");
                }
            }
        }

        // 查找文件数量
        pos = response.find("\"file_count\":");
        if (pos != std::string::npos) {
            pos += 13; // 跳过字段名
            size_t end_pos = response.find_first_of(",}", pos);
            if (end_pos != std::string::npos) {
                std::string value = response.substr(pos, end_pos - pos);
                log("  📁 监控文件数量: " + value);
            }
        }

        // 如果指定了文件路径，尝试查找该文件的具体信息
        if (!filepath.empty()) {
            display_file_specific_entropy(response, filepath);
        }
    }

    // 显示特定文件的熵值信息
    void display_file_specific_entropy(const std::string& response, const std::string& filepath) {
        log("🔍 文件 " + filepath + " 的详细熵值信息:");

        // 查找files数组
        size_t files_pos = response.find("\"files\":[");
        if (files_pos == std::string::npos) {
            log("  ⚠️ 未找到文件详细信息");
            return;
        }

        // 简单查找包含文件路径的条目
        size_t file_entry_pos = response.find(filepath, files_pos);
        if (file_entry_pos == std::string::npos) {
            log("  ⚠️ 未找到该文件的熵值记录");
            return;
        }

        // 向前查找到对象开始
        size_t obj_start = response.rfind("{", file_entry_pos);
        if (obj_start == std::string::npos) return;

        // 向后查找到对象结束
        size_t obj_end = response.find("}", file_entry_pos);
        if (obj_end == std::string::npos) return;

        std::string file_obj = response.substr(obj_start, obj_end - obj_start + 1);

        // 解析文件对象中的熵值信息
        parse_file_entropy_object(file_obj);
    }

    // 解析文件熵值对象（格式化显示）
    void parse_file_entropy_object(const std::string& file_obj) {
        // 查找原始熵值
        size_t pos = file_obj.find("\"original_entropy\":");
        if (pos != std::string::npos) {
            pos += 19;
            size_t end_pos = file_obj.find_first_of(",}", pos);
            if (end_pos != std::string::npos) {
                std::string value_str = file_obj.substr(pos, end_pos - pos);
                try {
                    double value = std::stod(value_str);
                    log("  " + format_entropy_value(value, "🔢 原始熵值").substr(2)); // 去掉前面的"  "
                } catch (const std::exception&) {
                    log("    🔢 原始熵值: " + value_str + " (解析错误)");
                }
            }
        }

        // 查找最终熵值
        pos = file_obj.find("\"final_entropy\":");
        if (pos != std::string::npos) {
            pos += 16;
            size_t end_pos = file_obj.find_first_of(",}", pos);
            if (end_pos != std::string::npos) {
                std::string value_str = file_obj.substr(pos, end_pos - pos);
                try {
                    double value = std::stod(value_str);
                    log("  " + format_entropy_value(value, "🔢 最终熵值").substr(2)); // 去掉前面的"  "
                } catch (const std::exception&) {
                    log("    🔢 最终熵值: " + value_str + " (解析错误)");
                }
            }
        }

        // 查找熵值变化
        pos = file_obj.find("\"entropy_change\":");
        if (pos != std::string::npos) {
            pos += 17;
            size_t end_pos = file_obj.find_first_of(",}", pos);
            if (end_pos != std::string::npos) {
                std::string value_str = file_obj.substr(pos, end_pos - pos);
                try {
                    double value = std::stod(value_str);
                    log("  " + format_entropy_value(value, "🔄 熵值变化").substr(2)); // 去掉前面的"  "
                } catch (const std::exception&) {
                    log("    🔄 熵值变化: " + value_str + " (解析错误)");
                }
            }
        }

        // 查找文件大小
        pos = file_obj.find("\"file_size\":");
        if (pos != std::string::npos) {
            pos += 12;
            size_t end_pos = file_obj.find_first_of(",}", pos);
            if (end_pos != std::string::npos) {
                std::string value = file_obj.substr(pos, end_pos - pos);
                try {
                    long size_bytes = std::stol(value);
                    double size_mb = static_cast<double>(size_bytes) / (1024.0 * 1024.0);
                    log("    📏 文件大小: " + std::to_string(size_mb) + " MB");
                } catch (const std::exception&) {
                    log("    📏 文件大小: " + value + " 字节 (解析错误)");
                }
            }
        }
    }

    // 查询熵值统计信息
    void query_entropy_stats() {
        std::string stats_url = "/api/focus-processes/entropy-stats";

        CURL* curl = curl_easy_init();
        if (!curl) return;

        std::string url = api_base_url_ + stats_url;
        std::string response;

        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 5L);

        CURLcode res = curl_easy_perform(curl);
        long response_code = 0;
        curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &response_code);
        curl_easy_cleanup(curl);

        if (res == CURLE_OK && response_code == 200) {
            display_entropy_stats(response);
        }
    }

    // 显示熵值统计信息
    void display_entropy_stats(const std::string& response) {
        log("📊 全局熵值统计信息:");

        // 查找熵值计算次数
        size_t pos = response.find("\"entropy_calculations\":");
        if (pos != std::string::npos) {
            pos += 23;
            size_t end_pos = response.find_first_of(",}", pos);
            if (end_pos != std::string::npos) {
                std::string value = response.substr(pos, end_pos - pos);
                log("  🔢 总熵值计算次数: " + value);
            }
        }

        // 查找读取熵值计算次数
        pos = response.find("\"read_entropy_calculations\":");
        if (pos != std::string::npos) {
            pos += 28;
            size_t end_pos = response.find_first_of(",}", pos);
            if (end_pos != std::string::npos) {
                std::string value = response.substr(pos, end_pos - pos);
                log("  📖 读取熵值计算次数: " + value);
            }
        }

        // 查找写入熵值计算次数
        pos = response.find("\"write_entropy_calculations\":");
        if (pos != std::string::npos) {
            pos += 29;
            size_t end_pos = response.find_first_of(",}", pos);
            if (end_pos != std::string::npos) {
                std::string value = response.substr(pos, end_pos - pos);
                log("  ✏️ 写入熵值计算次数: " + value);
            }
        }

        // 查找重点进程事件数
        pos = response.find("\"focus_process_events\":");
        if (pos != std::string::npos) {
            pos += 23;
            size_t end_pos = response.find_first_of(",}", pos);
            if (end_pos != std::string::npos) {
                std::string value = response.substr(pos, end_pos - pos);
                log("  🎯 重点进程事件数: " + value);
            }
        }
    }

    // 格式化熵值显示（将大数值转换为更直观的格式）
    std::string format_entropy_value(double entropy_value, const std::string& label) {
        std::ostringstream oss;
        oss << "  " << label << ": ";
        
        if (entropy_value == 0) {
            oss << "0.0";
        } else if (entropy_value < 1000) {
            oss << std::fixed << std::setprecision(2) << entropy_value;
        } else if (entropy_value < 1000000) {
            // 显示为千位单位
            oss << std::fixed << std::setprecision(2) << (entropy_value / 1000.0) << "K";
        } else if (entropy_value < 1000000000) {
            // 显示为百万位单位
            oss << std::fixed << std::setprecision(2) << (entropy_value / 1000000.0) << "M";
        } else {
            // 显示为十亿位单位
            oss << std::fixed << std::setprecision(2) << (entropy_value / 1000000000.0) << "G";
        }
        
        // 注意：内部已经完成标准化（每MB熵值），所以这里直接显示单位
        oss << " (每MB熵值: " << std::fixed << std::setprecision(3) << entropy_value << ")";
        
        return oss.str();
    }

    // 解析并显示格式化的熵值统计
    void display_formatted_entropy_stats(const std::string& response) {
        log("📊 重点进程熵值统计信息 (格式化显示):");
        
        // 查找并格式化各种熵值
        std::vector<std::pair<std::string, std::string>> entropy_fields = {
            {"\"total_entropy_calculations\":", "总熵值计算次数"},
            {"\"total_read_entropy\":", "读取操作总熵值"},
            {"\"total_write_entropy\":", "写入操作总熵值"},
            {"\"total_final_entropy\":", "最终总熵值"},
            {"\"total_original_entropy\":", "原始总熵值"},
            {"\"total_entropy_change\":", "熵值变化量"},
            {"\"average_entropy\":", "平均熵值"},
            {"\"read_entropy_calculations\":", "读取熵值计算次数"},
            {"\"write_entropy_calculations\":", "写入熵值计算次数"},
            {"\"focus_process_events\":", "重点进程事件数"}
        };
        
        for (const auto& field : entropy_fields) {
            size_t pos = response.find(field.first);
            if (pos != std::string::npos) {
                size_t start = pos + field.first.length();
                size_t end = response.find_first_of(",}", start);
                if (end != std::string::npos) {
                    std::string value_str = response.substr(start, end - start);
                    try {
                        double value = std::stod(value_str);
                        
                        // 对于计数类型的字段，不需要格式化
                        if (field.first.find("calculations") != std::string::npos || 
                            field.first.find("events") != std::string::npos) {
                            log("  " + field.second + ": " + std::to_string(static_cast<long long>(value)));
                        } else {
                            // 对于熵值类型的字段，使用格式化显示
                            log(format_entropy_value(value, field.second));
                        }
                    } catch (const std::exception& e) {
                        log("  " + field.second + ": " + value_str + " (解析错误)");
                    }
                }
            }
        }
        
        // 计算并显示一些有用的派生指标
        double total_final = 0, total_original = 0, total_change = 0;
        
        auto extract_value = [&](const std::string& field) -> double {
            size_t pos = response.find(field);
            if (pos != std::string::npos) {
                size_t start = pos + field.length();
                size_t end = response.find_first_of(",}", start);
                if (end != std::string::npos) {
                    try {
                        return std::stod(response.substr(start, end - start));
                    } catch (...) {}
                }
            }
            return 0.0;
        };
        
        total_final = extract_value("\"total_final_entropy\":");
        total_original = extract_value("\"total_original_entropy\":");
        total_change = extract_value("\"total_entropy_change\":");
        
        if (total_final > 0 || total_original > 0) {
            log("\n📈 派生指标:");
            
            if (total_original > 0) {
                double change_percentage = (total_change / total_original) * 100;
                log("  熵值变化百分比: " + std::to_string(change_percentage) + "%");
            }
            
            if (total_final > 0 && total_original > 0) {
                double efficiency = total_final / total_original;
                log("  熵值效率比: " + std::to_string(efficiency));
            }
        }
    }

    // 查询并显示所有重点进程的熵值信息
    void queryAndDisplayAllEntropyData() {
        if (!self_register_) {
            return; // 如果没有自注册，就不查询熵值
        }

        log("🔍 查询整体重点进程熵值信息");

        // 查询所有重点进程的熵值信息
        std::string all_entropy_url = "/api/focus-processes/entropy";
        
        CURL* curl = curl_easy_init();
        if (!curl) {
            log("CURL初始化失败");
            return;
        }
        
        std::string url = api_base_url_ + all_entropy_url;
        std::string response;
        
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
        
        CURLcode res = curl_easy_perform(curl);
        long response_code = 0;
        curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &response_code);
        
        curl_easy_cleanup(curl);
        
        if (res == CURLE_OK && response_code == 200) {
            log("✅ 成功获取重点进程熵值信息");
            parse_and_display_entropy_response(response, "整体查询", "");
        } else {
            log("❌ 获取重点进程熵值信息失败: " + std::string(curl_easy_strerror(res)) + " (HTTP " + std::to_string(response_code) + ")");
        }
        
        // 查询详细统计信息
        std::string stats_url = "/api/focus-processes/entropy-stats";
        
        curl = curl_easy_init();
        if (!curl) {
            return;
        }
        
        url = api_base_url_ + stats_url;
        response.clear();
        
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
        
        res = curl_easy_perform(curl);
        response_code = 0;
        curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &response_code);
        
        curl_easy_cleanup(curl);
        
        if (res == CURLE_OK && response_code == 200) {
            log("✅ 成功获取重点进程统计信息");
            display_formatted_entropy_stats(response);
        } else {
            log("❌ 获取重点进程统计信息失败: " + std::string(curl_easy_strerror(res)) + " (HTTP " + std::to_string(response_code) + ")");
        }
    }

    // 创建测试文件
    void create_test_files() {
        log("开始创建测试文件...");
        
        for (size_t i = 0; i < test_configs_.size(); i++) {
            const auto& config = test_configs_[i];
            const std::string& filepath = test_files_[i];
            
            log_operation("创建文件", filepath, config.size_mb);
            
            // 创建具有初始熵值的文件（非全0文件）
            log("📝 创建具有低初始熵值的文件作为测试起点，系统将自动标准化为每MB熵值");
            log("🔍 预期后续修改操作将使标准化熵值发生可观察的变化");
            
            // 根据文件大小选择不同的初始熵值策略
            if (config.size_mb <= 50) {
                // 小文件和中等文件：使用文本内容（中等熵值）
                createTextFile(filepath, config.size_mb);
            } else {
                // 大文件：使用混合模式（低-中等熵值）
                createMixedFile(filepath, config.size_mb);
            }
            
            // 验证文件大小
            struct stat st;
            if (stat(filepath.c_str(), &st) == 0) {
                size_t actual_size_mb = static_cast<size_t>(st.st_size) / (1024 * 1024);
                log("文件创建成功: " + filepath + " (实际大小: " + 
                    std::to_string(actual_size_mb) + "MB)");
            }
            
            // 延迟
            if (delay_ms_ > 0) {
                std::this_thread::sleep_for(std::chrono::milliseconds(delay_ms_));
            }
        }
        
        log("所有测试文件创建完成");
    }
    
    // 创建文本文件（低熵值）
    void createTextFile(const std::string& filepath, size_t size_mb) {
        log("创建低熵值文本文件 (预期初始熵值: 1.0-2.5)");
        
        std::ofstream file(filepath, std::ios::binary);
        if (!file.is_open()) {
            std::cerr << "错误: 无法创建文件 " << filepath << std::endl;
            return;
        }
        
        // 使用重复性高的内容创建低熵值文件
        std::string low_entropy_pattern = "AAAABBBBCCCCDDDDEEEEFFFFGGGGHHHHIIIIJJJJKKKKLLLLMMMMNNNNOOOOPPPPQQQQRRRRSSSSTTTTUUUUVVVVWWWWXXXXYYYYZZZZ";
        
        size_t total_written = 0;
        size_t target_size = size_mb * 1024 * 1024;
        
        while (total_written < target_size) {
            size_t write_size = std::min(low_entropy_pattern.length(), target_size - total_written);
            file.write(low_entropy_pattern.c_str(), write_size);
            total_written += write_size;
        }
        
        file.close();
        log("低熵值文本文件创建完成，系统将自动标准化为每MB熵值");
        
        // 查询文件创建后的熵值信息
        query_and_display_entropy_info("文件创建", filepath);
    }
    
    // 创建混合模式文件（低熵值）
    void createMixedFile(const std::string& filepath, size_t size_mb) {
        log("创建低熵值混合文件，系统将自动标准化为每MB熵值");
        
        std::ofstream file(filepath, std::ios::binary);
        if (!file.is_open()) {
            std::cerr << "错误: 无法创建文件 " << filepath << std::endl;
            return;
        }
        
        size_t total_written = 0;
        size_t target_size = size_mb * 1024 * 1024;
        const size_t chunk_size = 1024 * 1024; // 1MB chunks
        
        while (total_written < target_size) {
            size_t current_chunk_size = std::min(chunk_size, target_size - total_written);
            std::vector<char> chunk(current_chunk_size);
            
            // 低熵值模式：90%重复模式 + 10%变化
            for (size_t i = 0; i < current_chunk_size; i++) {
                if (i % 10 < 9) {
                    // 90%：使用重复模式（非常低熵值）
                    chunk[i] = static_cast<char>('A' + (i % 5)); // 只使用A-E 5个字符
                } else {
                    // 10%：使用稍微变化的内容
                    chunk[i] = static_cast<char>('0' + (i % 10)); // 使用0-9数字
                }
            }
            
            file.write(chunk.data(), current_chunk_size);
            total_written += current_chunk_size;
        }
        
        file.close();
        log("低熵值混合文件创建完成，系统将自动标准化为每MB熵值");
        
        // 查询文件创建后的熵值信息
        query_and_display_entropy_info("文件创建", filepath);
    }
    
    // 创建压力测试文件（简化版本，快速创建）
    void createStressFile(const std::string& filepath, size_t size_mb) {
        std::ofstream file(filepath, std::ios::binary);
        if (!file.is_open()) {
            std::cerr << "错误: 无法创建压力测试文件 " << filepath << std::endl;
            return;
        }
        
        // 使用简单模式：重复文本内容（系统将自动标准化为每MB熵值）
        const std::string pattern = "Stress Test Content " + std::to_string(rand() % 1000) + " ";
        
        size_t total_written = 0;
        size_t target_size = size_mb * 1024 * 1024;
        
        while (total_written < target_size) {
            size_t write_size = std::min(pattern.length(), target_size - total_written);
            file.write(pattern.c_str(), write_size);
            total_written += write_size;
        }
        
        file.close();
    }
    
    // 读取文件（使用C++原生I/O，确保被监控系统正确计入）
    void read_file_cat(const std::string& filepath, size_t size_mb) {
        log_operation("读取文件(C++原生)", filepath, size_mb);

        // 记录操作前的时间戳
        auto start_time = std::chrono::steady_clock::now();

        // 使用C++原生文件I/O进行读取，确保操作被计入当前进程
        std::ifstream file(filepath, std::ios::binary);
        if (!file.is_open()) {
            std::cerr << "错误: 无法打开文件 " << filepath << std::endl;
            return;
        }

        // 读取文件内容（分块读取以提高效率）
        const size_t buffer_size = 1024 * 1024; // 1MB缓冲区
        std::vector<char> buffer(buffer_size);
        size_t total_read = 0;

        while (file.read(buffer.data(), buffer_size) || file.gcount() > 0) {
            total_read += file.gcount();
        }
        file.close();

        auto end_time = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

        log("文件读取完成: " + filepath + " (总读取: " +
            std::to_string(total_read / (1024 * 1024)) + "MB, 耗时: " +
            std::to_string(duration.count()) + "ms)");

        // 等待一段时间让监控系统处理事件
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }
    
    // 读取文件（使用C++文件流）
    void read_file_cpp(const std::string& filepath, size_t size_mb) {
        log_operation("读取文件(C++)", filepath, size_mb);
        
        std::ifstream file(filepath, std::ios::binary);
        if (!file.is_open()) {
            std::cerr << "错误: 无法打开文件 " << filepath << std::endl;
            return;
        }
        
        // 读取文件内容（分块读取）
        const size_t buffer_size = 1024 * 1024;  // 1MB缓冲区
        std::vector<char> buffer(buffer_size);
        size_t total_read = 0;
        
        while (file.read(buffer.data(), buffer_size)) {
            total_read += static_cast<size_t>(file.gcount());

            // 每读取10MB输出一次进度
            if (total_read % (10 * 1024 * 1024) == 0) {
                log("读取进度: " + filepath + " - " +
                    std::to_string(total_read / (1024 * 1024)) + "MB");
            }
        }

        // 读取最后一块
        if (file.gcount() > 0) {
            total_read += static_cast<size_t>(file.gcount());
        }
        
        file.close();
        log("文件读取完成: " + filepath + " (总读取: " + 
            std::to_string(total_read / (1024 * 1024)) + "MB)");
    }
    
    // 写入文件 - 修复版本：创建低熵值数据以产生明显变化
    void write_file(const std::string& filepath, size_t size_mb) {
        log_operation("写入文件", filepath, size_mb);

        // 记录操作前的时间戳
        auto start_time = std::chrono::steady_clock::now();

        std::ofstream file(filepath, std::ios::binary);
        if (!file.is_open()) {
            std::cerr << "错误: 无法创建文件 " << filepath << std::endl;
            return;
        }

        // 写入中等熵值数据（文本内容）以产生明显的熵值变化
        const size_t buffer_size = 1024 * 1024;  // 1MB缓冲区
        std::vector<char> buffer(buffer_size);

        // 填充中等熵值的文本数据（模拟普通文档内容）
        const std::string text_content =
            "This is a sample text file with moderate entropy. "
            "It contains various characters, numbers 123456789, "
            "and symbols !@#$%^&*() that create medium-level randomness. "
            "The content is structured but not completely predictable. "
            "Lorem ipsum dolor sit amet, consectetur adipiscing elit. "
            "Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. ";

        for (size_t i = 0; i < buffer_size; i++) {
            buffer[i] = text_content[i % text_content.length()];
        }

        log("📝 写入中等熵值文本数据，系统将自动标准化熵值");

        size_t total_written = 0;
        for (size_t i = 0; i < size_mb; i++) {
            file.write(buffer.data(), buffer_size);
            total_written += buffer_size;

            // 每写入10MB输出一次进度
            if ((i + 1) % 10 == 0) {
                log("写入进度: " + filepath + " - " +
                    std::to_string(i + 1) + "MB");
            }
        }

        file.flush();  // 强制刷新缓冲区
        file.close();
        sync();  // 强制同步到磁盘

        auto end_time = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

        log("文件写入完成: " + filepath + " (总写入: " +
            std::to_string(total_written / (1024 * 1024)) + "MB, 耗时: " +
            std::to_string(duration.count()) + "ms)");

        // 等待一段时间让监控系统处理事件
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }
    
    // 修改文件 - 修复版本：追加高熵值随机数据
    void modify_file(const std::string& filepath) {
        log_operation("修改文件", filepath);

        // 记录操作前的时间戳
        auto start_time = std::chrono::steady_clock::now();

        // 查询修改前的熵值信息
        query_and_display_entropy_info("修改前", filepath);

        std::ofstream file(filepath, std::ios::app | std::ios::binary);
        if (!file.is_open()) {
            std::cerr << "错误: 无法打开文件进行修改 " << filepath << std::endl;
            return;
        }

        // 设置随机数种子
        static int modification_counter = 0;
        modification_counter++;
        srand(static_cast<unsigned int>(time(nullptr)) + modification_counter);

        // 渐进式增加熵值：每次修改增加更多随机性
        const size_t append_size = 512 * 1024; // 512KB每次修改
        std::vector<char> mixed_data(append_size);
        
        // 根据修改次数调整随机性比例
        int random_percentage = std::min(20 + (modification_counter * 15), 90); // 从20%逐渐增加到90%
        
        log("📝 第" + std::to_string(modification_counter) + "次修改，随机性比例: " + std::to_string(random_percentage) + "%");

        for (size_t i = 0; i < append_size; i++) {
            if ((rand() % 100) < random_percentage) {
                // 随机数据（高熵值）
                mixed_data[i] = static_cast<char>(rand() % 256);
            } else {
                // 重复模式（低熵值）
                mixed_data[i] = static_cast<char>('A' + (i % 26));
            }
        }

        log("📝 追加混合数据（随机性" + std::to_string(random_percentage) + "%），系统将自动标准化熵值");
        file.write(mixed_data.data(), append_size);
        file.flush();  // 强制刷新缓冲区
        file.close();
        sync();  // 强制同步到磁盘

        auto end_time = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

        log("文件修改完成: " + filepath + " (追加512KB混合数据, 耗时: " + std::to_string(duration.count()) + "ms)");

        // 等待一段时间让监控系统处理事件
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
        
        // 查询修改后的熵值信息
        query_and_display_entropy_info("修改后", filepath);
    }
    
    // 执行完整的测试序列
    void run_complete_test() {
        log("=== 开始完整熵值测试序列 ===");
        log("当前进程PID: " + std::to_string(getpid()));
        log("测试目录: " + test_dir_);
        log("自注册模式: " + std::string(self_register_ ? "启用" : "禁用"));

		// 阶段1: 创建测试文件
        log("\n--- 阶段1: 创建测试文件 ---");
        create_test_files();
        
        // 等待文件系统同步
        log("等待文件系统同步...");
        std::this_thread::sleep_for(std::chrono::seconds(2));
		
        // 如果启用自注册，先注册为重点进程
        if (self_register_) {
            if (!registerAsFocusProcess()) {
                log("警告: 注册重点进程失败，继续执行测试");
            }
            // 等待注册生效
            std::this_thread::sleep_for(std::chrono::seconds(2));
        }

        // 阶段2: 读取文件测试
        log("\n--- 阶段2: 读取文件测试 ---");
        for (size_t i = 0; i < test_configs_.size(); i++) {
            const auto& config = test_configs_[i];
            const std::string& filepath = test_files_[i];
            
            // 使用cat命令读取
            read_file_cat(filepath, config.size_mb);
            
            // 延迟
            if (delay_ms_ > 0) {
                std::this_thread::sleep_for(std::chrono::milliseconds(delay_ms_));
            }
        }
        
        // 等待事件处理
        log("等待文件读取事件处理...");
        std::this_thread::sleep_for(std::chrono::seconds(3));
        
        // 阶段3: 使用C++文件流读取
        log("\n--- 阶段3: C++文件流读取测试 ---");
        for (size_t i = 0; i < test_configs_.size(); i++) {
            const auto& config = test_configs_[i];
            const std::string& filepath = test_files_[i];
            
            read_file_cpp(filepath, config.size_mb);
            
            // 延迟
            if (delay_ms_ > 0) {
                std::this_thread::sleep_for(std::chrono::milliseconds(delay_ms_));
            }
        }
        
        // 等待事件处理
        log("等待文件读取事件处理...");
        std::this_thread::sleep_for(std::chrono::seconds(3));
        
        // 阶段4: 写入文件测试
        log("\n--- 阶段4: 写入文件测试 ---");
        for (size_t i = 0; i < test_configs_.size(); i++) {
            const auto& config = test_configs_[i];
            const std::string& filepath = test_files_[i];
            
            write_file(filepath, config.size_mb);
            
            // 延迟
            if (delay_ms_ > 0) {
                std::this_thread::sleep_for(std::chrono::milliseconds(delay_ms_));
            }
        }
        
        // 等待事件处理
        log("等待文件写入事件处理...");
        std::this_thread::sleep_for(std::chrono::seconds(3));
        
        // 阶段5: 多次修改文件测试（展示熵值渐进变化）
        log("\n--- 阶段5: 多次修改文件测试 ---");
        log("🔄 对每个文件进行3次修改，展示熵值的渐进变化");
        
        for (const auto& filepath : test_files_) {
            log("\n🔄 开始对文件进行多次修改: " + filepath);
            
            // 对每个文件进行3次修改
            for (int modify_round = 1; modify_round <= 3; modify_round++) {
                log("\n--- 第" + std::to_string(modify_round) + "次修改 ---");
                modify_file(filepath);
                
                // 延迟让系统处理事件
                if (delay_ms_ > 0) {
                    std::this_thread::sleep_for(std::chrono::milliseconds(delay_ms_));
                }
                
                // 等待事件处理
                log("等待文件修改事件处理...");
                std::this_thread::sleep_for(std::chrono::seconds(2));
                
                // 查询整体熵值信息
                queryAndDisplayAllEntropyData();
            }
        }
        
        // 等待最终事件处理
        log("等待所有文件修改事件处理...");
        std::this_thread::sleep_for(std::chrono::seconds(5));
        
        // 阶段6: 最终读取测试
        log("\n--- 阶段6: 最终读取测试 ---");
        for (size_t i = 0; i < test_configs_.size(); i++) {
            const auto& config = test_configs_[i];
            const std::string& filepath = test_files_[i];
            
            read_file_cat(filepath, config.size_mb);
            
            // 延迟
            if (delay_ms_ > 0) {
                std::this_thread::sleep_for(std::chrono::milliseconds(delay_ms_));
            }
        }
        
        log("\n=== 完整熵值测试序列完成 ===");
        log("测试总结:");
        log("- 创建了 " + std::to_string(test_configs_.size()) + " 个不同大小的测试文件");
        log("- 执行了多次文件读取操作（cat和C++文件流）");
        log("- 执行了文件写入和修改操作");
        log("- 所有操作都应该被psfsmon监控并计算熵值");
        log("- 大文件（>100MB）应该只计算前100MB的熵值");
        log("- 分块读取应该避免内存不足问题");

        // 等待熵值计算完成
        log("文件操作完成，等待熵值计算...");
        // 等待更长时间让系统处理文件事件和熵值计算
        // 特别是FAN_CLOSE_WRITE事件的处理可能需要更多时间
        std::this_thread::sleep_for(std::chrono::seconds(10));
        log("完整测试序列完成");

        // 创建完成标志文件，通知主测试程序
        std::string flag_file = "/tmp/psfsmon_entropy_test_done.flag";
        std::ofstream flag_stream(flag_file);
        if (flag_stream.is_open()) {
            flag_stream << "DONE:" << getpid() << ":" << time(nullptr) << std::endl;
            flag_stream.close();
            log("✅ 创建完成标志文件: " + flag_file);
        } else {
            log("⚠️ 无法创建完成标志文件: " + flag_file);
        }

        // 注意：不再自动移除重点进程注册，由主测试程序负责清理
        // 这样确保测试程序能够找到并读取统计信息
        if (self_register_) {
            log("保持重点进程注册状态，由主测试程序负责清理");
            log("程序将持续运行，等待主测试程序完成数据获取后终止...");

            // 等待主测试程序发送优雅退出信号，期间定期显示熵值信息
            while (!g_graceful_shutdown.load()) {
                std::this_thread::sleep_for(std::chrono::seconds(5));
                static int heartbeat_counter = 0;
                heartbeat_counter++;

                // 每60秒输出一次心跳
                if (heartbeat_counter % 12 == 0) { // 每60秒
                    log("等待主测试程序完成数据获取... (已等待 " + std::to_string(heartbeat_counter * 5) + " 秒)");
                }
            }

            log("收到优雅退出信号，开始清理流程...");
            handleGracefulShutdown();
        }
        
        if (verbose_) {
            log("测试文件保留在: " + test_dir_);
            log("请手动清理测试文件或使用 --cleanup 选项");
        }
    }
    
    // 执行快速测试（只测试小文件）
    void run_quick_test() {
        log("=== 开始快速熵值测试 ===");
        log("当前进程PID: " + std::to_string(getpid()));
        log("自注册模式: " + std::string(self_register_ ? "启用" : "禁用"));

		// 只测试小文件
        std::string small_file = test_dir_ + "/quick_test.bin";
        
        // 创建小文件（使用初始熵值数据作为起点）
        log("创建快速测试文件: " + small_file);
        log("📝 创建具有初始熵值的文件作为测试起点，预期后续操作将使熵值发生变化");
        createTextFile(small_file, 10);  // 使用文本文件创建方法
		
        // 如果启用自注册，先注册为重点进程
        if (self_register_) {
            if (!registerAsFocusProcess()) {
                log("警告: 注册重点进程失败，继续执行测试");
            }
            // 等待注册生效
            std::this_thread::sleep_for(std::chrono::seconds(2));
        }
        
        // 读取文件
        log("读取快速测试文件");
        read_file_cat(small_file, 10);
        
        // 写入文件
        log("写入快速测试文件");
        write_file(small_file, 10);
        
        // 多次修改文件展示熵值渐进变化
        log("多次修改快速测试文件，展示熵值渐进变化");
        for (int modify_round = 1; modify_round <= 3; modify_round++) {
            log("\n--- 第" + std::to_string(modify_round) + "次修改 ---");
            modify_file(small_file);
            
            // 等待事件处理并查询熵值
            std::this_thread::sleep_for(std::chrono::seconds(2));
            queryAndDisplayAllEntropyData();
        }

        // 等待熵值计算完成
        log("文件操作完成，等待熵值计算...");
        // 等待更长时间让系统处理文件事件和熵值计算
        // 特别是FAN_CLOSE_WRITE事件的处理可能需要更多时间
        std::this_thread::sleep_for(std::chrono::seconds(10));
        log("文件修改完成");

        // 创建完成标志文件，通知主测试程序
        std::string flag_file = "/tmp/psfsmon_entropy_test_done.flag";
        std::ofstream flag_stream(flag_file);
        if (flag_stream.is_open()) {
            flag_stream << "DONE:" << getpid() << ":" << time(nullptr) << std::endl;
            flag_stream.close();
            log("✅ 创建完成标志文件: " + flag_file);
        } else {
            log("⚠️ 无法创建完成标志文件: " + flag_file);
        }

        // 注意：不再自动移除重点进程注册，由主测试程序负责清理
        if (self_register_) {
            log("保持重点进程注册状态，由主测试程序负责清理");
            log("程序将持续运行，等待主测试程序完成数据获取后终止...");

            // 等待主测试程序发送优雅退出信号，期间定期显示熵值信息
            while (!g_graceful_shutdown.load()) {
                std::this_thread::sleep_for(std::chrono::seconds(5));
                static int heartbeat_counter = 0;
                heartbeat_counter++;

                // 每60秒输出一次心跳
                if (heartbeat_counter % 12 == 0) { // 每60秒
                    log("等待主测试程序完成数据获取... (已等待 " + std::to_string(heartbeat_counter * 5) + " 秒)");
                }
            }

            log("收到优雅退出信号，开始清理流程...");
            handleGracefulShutdown();
        }
        
        // 最终熵值总结
        log("=== 快速熵值测试完成 ===");
        log("📋 子测试程序完成所有文件操作，等待主测试程序查询熵值数据");
    }

    // 显示最终熵值总结
    void display_final_entropy_summary() {
        if (!self_register_) {
            log("📋 测试总结: 未启用自注册模式，无法获取熵值统计");
            return;
        }

        log("📋 === 最终熵值测试总结 ===");

        // 查询最终的熵值信息
        pid_t current_pid = getpid();
        std::string entropy_url = "/api/focus-processes/entropy?pid=" + std::to_string(current_pid);

        CURL* curl = curl_easy_init();
        if (!curl) {
            log("❌ 无法获取最终熵值统计");
            return;
        }

        std::string url = api_base_url_ + entropy_url;
        std::string response;

        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 5L);

        CURLcode res = curl_easy_perform(curl);
        long response_code = 0;
        curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &response_code);
        curl_easy_cleanup(curl);

        if (res == CURLE_OK && response_code == 200) {
            log("🎯 重点进程 PID " + std::to_string(current_pid) + " 最终熵值统计:");
            parse_and_display_entropy_response(response, "最终统计", "");

            // 分析熵值变化趋势
            analyze_entropy_trends(response);
        } else {
            log("⚠️ 无法获取最终熵值统计: HTTP " + std::to_string(response_code));
        }

        // 显示测试建议
        display_test_recommendations();
    }

    // 分析熵值变化趋势
    void analyze_entropy_trends(const std::string& response) {
        log("🔍 === 熵值变化趋势分析 ===");

        // 查找熵值变化量
        size_t pos = response.find("\"total_entropy_change\":");
        if (pos != std::string::npos) {
            pos += 23;
            size_t end_pos = response.find_first_of(",}", pos);
            if (end_pos != std::string::npos) {
                std::string value = response.substr(pos, end_pos - pos);
                double change = std::stod(value);

                if (change > 0) {
                    log("📈 熵值总体呈上升趋势 (+" + std::to_string(change) + ")");
                    log("   💡 可能原因: 文件内容变得更加随机化或复杂化");
                    log("   🔍 建议: 检查是否有加密、压缩或随机数据写入操作");
                } else if (change < 0) {
                    log("📉 熵值总体呈下降趋势 (" + std::to_string(change) + ")");
                    log("   💡 可能原因: 文件内容变得更加规律化或简单化");
                    log("   🔍 建议: 检查是否有解密、解压或规律数据写入操作");
                } else {
                    log("➡️ 熵值保持稳定 (无显著变化)");
                    log("   💡 说明: 文件操作未显著改变数据的随机性");
                }
            }
        }

        // 查找文件数量
        pos = response.find("\"file_count\":");
        if (pos != std::string::npos) {
            pos += 13;
            size_t end_pos = response.find_first_of(",}", pos);
            if (end_pos != std::string::npos) {
                std::string value = response.substr(pos, end_pos - pos);
                int file_count = std::stoi(value);

                log("📁 监控文件统计: " + std::to_string(file_count) + " 个文件");
                if (file_count > 0) {
                    log("   ✅ 成功监控到文件操作");
                } else {
                    log("   ⚠️ 未检测到文件操作，可能存在配置问题");
                }
            }
        }
    }

    // 显示测试建议
    void display_test_recommendations() {
        log("💡 === 测试建议和说明 ===");
        log("🔬 熵值计算原理:");
        log("   • 熵值范围: 0-8 (0=完全规律, 8=完全随机)");
        log("   • 高熵值: 加密文件、压缩文件、随机数据");
        log("   • 低熵值: 文本文件、重复数据、空文件");
        log("   • 熵值变化: 反映文件内容复杂度的变化");

        log("🎯 监控意义:");
        log("   • 检测恶意软件加密行为");
        log("   • 识别数据泄露和异常文件操作");
        log("   • 分析文件内容变化模式");
        log("   • 评估数据安全风险");

        log("📊 如何解读结果:");
        log("   • 熵值突然大幅上升: 可能是勒索软件加密");
        log("   • 熵值持续下降: 可能是数据清理或格式化");
        log("   • 熵值频繁波动: 可能是正常的编辑操作");
        log("   • 无熵值变化: 可能是只读操作或监控未生效");

        log("📝 测试注意事项:");
        log("- 本测试专门设计了低初始熵值文件，以便观察熵值变化");
        log("- 大文件（>100MB）只计算前100MB的熵值，但已完全标准化为每MB熵值");
        log("- 系统内部自动标准化：总熵值 = 每字节熵值 × 计算字节数 ÷ (1024×1024)");
        log("- JSON输出中的熵值数据已经标准化，可直接比较不同大小文件的熵值密度");
        log("- 测试将通过多次修改逐步增加文件的随机性，观察熵值的梯度变化");
    }

    // 压力测试模式
    void run_stress_test() {
        log("=== 开始压力测试 ===");
        log("当前进程PID: " + std::to_string(getpid()));

		// 创建大量小文件进行并发操作
        std::vector<std::string> stress_files;
        int file_count = 100;

        log("创建 " + std::to_string(file_count) + " 个测试文件...");
        log("📝 创建具有初始熵值的文件，提供更真实的测试环境");
        
        for (int i = 0; i < file_count; i++) {
            std::string filename = test_dir_ + "/stress_test_" + std::to_string(i) + ".bin";
            stress_files.push_back(filename);

            // 使用文本文件创建方法，确保有初始熵值
            createStressFile(filename, 1);  // 1MB文件
        }

        // 如果启用自注册，先注册为重点进程
        if (self_register_) {
            if (!registerAsFocusProcess()) {
                log("警告: 注册重点进程失败，继续执行测试");
            }
            std::this_thread::sleep_for(std::chrono::seconds(2));
        }

        log("开始并发文件操作...");
        auto start_time = std::chrono::steady_clock::now();

        // 并发读取所有文件
        for (const auto& filename : stress_files) {
            read_file_cat(filename, 1);
            if (delay_ms_ > 0) {
                std::this_thread::sleep_for(std::chrono::milliseconds(delay_ms_ / 10)); // 减少延迟
            }
        }

        // 并发写入所有文件
        for (const auto& filename : stress_files) {
            write_file(filename, 1);
            if (delay_ms_ > 0) {
                std::this_thread::sleep_for(std::chrono::milliseconds(delay_ms_ / 10));
            }
        }

        // 并发修改所有文件
        for (const auto& filename : stress_files) {
            modify_file(filename);
            if (delay_ms_ > 0) {
                std::this_thread::sleep_for(std::chrono::milliseconds(delay_ms_ / 10));
            }
        }

        auto end_time = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(end_time - start_time);

        log("压力测试完成，耗时: " + std::to_string(duration.count()) + " 秒");
        log("总文件操作次数: " + std::to_string(file_count * 3) + " (读取+写入+修改)");

        // 等待熵值计算完成 - 必须在删除文件之前完成
        log("文件操作完成，等待熵值计算...");
        // 【关键修复】：在删除文件之前等待足够长的时间，确保所有FAN_CLOSE_WRITE事件被处理
        // 这样可以避免在熵值计算时文件已经被删除的问题
        std::this_thread::sleep_for(std::chrono::seconds(15));

        // 清理测试文件
        log("清理压力测试文件...");
        for (const auto& filename : stress_files) {
            std::remove(filename.c_str());
        }
        log("压力测试完成");

        // 创建完成标志文件，通知主测试程序
        std::string flag_file = "/tmp/psfsmon_entropy_test_done.flag";
        std::ofstream flag_stream(flag_file);
        if (flag_stream.is_open()) {
            flag_stream << "DONE:" << getpid() << ":" << time(nullptr) << std::endl;
            flag_stream.close();
            log("✅ 创建完成标志文件: " + flag_file);
        } else {
            log("⚠️ 无法创建完成标志文件: " + flag_file);
        }

        if (self_register_) {
            log("保持重点进程注册状态，由主测试程序负责清理");
            log("程序将持续运行，等待主测试程序完成数据获取后终止...");

            // 等待主测试程序发送优雅退出信号，期间定期显示熵值信息
            while (!g_graceful_shutdown.load()) {
                std::this_thread::sleep_for(std::chrono::seconds(5));
                static int heartbeat_counter = 0;
                heartbeat_counter++;

                // 每60秒输出一次心跳
                if (heartbeat_counter % 12 == 0) { // 每60秒
                    log("等待主测试程序完成数据获取... (已等待 " + std::to_string(heartbeat_counter * 5) + " 秒)");
                }
            }

            log("收到优雅退出信号，开始清理流程...");
            handleGracefulShutdown();
        }

        log("=== 压力测试完成 ===");
    }
    
    // 清理测试文件
    void cleanup() {
        log("清理测试文件...");
        
        for (const auto& filepath : test_files_) {
            if (std::remove(filepath.c_str()) == 0) {
                log("删除文件: " + filepath);
            }
        }
        
        // 删除测试目录
        if (rmdir(test_dir_.c_str()) == 0) {
            log("删除测试目录: " + test_dir_);
        }
        
        log("清理完成");
    }
    
    // 显示帮助信息
    static void show_help(const char* program_name) {
        std::cout << "用法: " << program_name << " [选项]" << std::endl;
        std::cout << "选项:" << std::endl;
        std::cout << "  --test-dir <目录>    测试目录 (默认: /tmp/entropy_test)" << std::endl;
        std::cout << "  --quick              快速测试模式 (只测试小文件)" << std::endl;
        std::cout << "  --complete           完整测试模式 (默认)" << std::endl;
        std::cout << "  --delay <毫秒>       操作间延迟 (默认: 100ms)" << std::endl;
        std::cout << "  --no-delay           无延迟模式" << std::endl;
        std::cout << "  --quiet              静默模式 (不输出详细日志)" << std::endl;
        std::cout << "  --cleanup            测试完成后清理文件" << std::endl;
        std::cout << "  --api-url <URL>      API服务器地址 (默认: http://127.0.0.1:8080)" << std::endl;
        std::cout << "  --self-register      启动时自动注册为重点进程" << std::endl;
        std::cout << "  --help               显示此帮助信息" << std::endl;
        std::cout << std::endl;
        std::cout << "测试说明:" << std::endl;
        std::cout << "  本程序用于测试psfsmon的重点进程文件熵值计算功能" << std::endl;
        std::cout << "  程序会创建不同大小的文件并执行读写操作" << std::endl;
        std::cout << "  所有操作都应该被psfsmon监控并计算熵值" << std::endl;
        std::cout << std::endl;
        std::cout << "示例:" << std::endl;
        std::cout << "  " << program_name << " --quick                    # 快速测试" << std::endl;
        std::cout << "  " << program_name << " --complete --delay 500     # 完整测试，500ms延迟" << std::endl;
        std::cout << "  " << program_name << " --test-dir /tmp/my_test    # 自定义测试目录" << std::endl;
        std::cout << "  " << program_name << " --self-register            # 自动注册为重点进程" << std::endl;
        std::cout << "  " << program_name << " --cleanup                  # 测试后清理" << std::endl;
        std::cout << "  " << program_name << " --stress                   # 压力测试模式" << std::endl;
    }
};

// 全局实例指针用于信号处理
static EntropyTestProgram* g_test_program_instance = nullptr;

// 信号处理函数
static void signal_handler(int signal) {
    std::cout << "\n[熵值测试程序] 收到信号 " << signal << " (";
    switch(signal) {
        case SIGUSR1:
            std::cout << "SIGUSR1 - 优雅退出请求";
            g_graceful_shutdown.store(true);
            break;
        case SIGTERM:
            std::cout << "SIGTERM";
            g_graceful_shutdown.store(true);
            break;
        case SIGINT:
            std::cout << "SIGINT";
            g_graceful_shutdown.store(true);
            break;
        default:
            std::cout << "未知信号";
            break;
    }
    std::cout << "), 程序将优雅退出" << std::endl;

    // 如果有测试程序实例，触发清理操作
    if (g_test_program_instance) {
        g_test_program_instance->handleGracefulShutdown();
    }
}

int main(int argc, char* argv[]) {
    std::string test_dir = "/tmp/entropy_test";
    bool quick_mode = false;
    bool complete_mode = true;
    bool stress_mode = false;
    bool cleanup_mode = false;
    bool verbose = true;
    int delay_ms = 100;
    std::string api_url = "http://127.0.0.1:8080";
    bool self_register = false;
    
    // 解析命令行参数
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];
        
        if (arg == "--test-dir" && i + 1 < argc) {
            test_dir = argv[++i];
        } else if (arg == "--quick") {
            quick_mode = true;
            complete_mode = false;
            stress_mode = false;
        } else if (arg == "--complete") {
            complete_mode = true;
            quick_mode = false;
            stress_mode = false;
        } else if (arg == "--stress") {
            stress_mode = true;
            quick_mode = false;
            complete_mode = false;
        } else if (arg == "--delay" && i + 1 < argc) {
            delay_ms = std::atoi(argv[++i]);
        } else if (arg == "--no-delay") {
            delay_ms = 0;
        } else if (arg == "--quiet") {
            verbose = false;
        } else if (arg == "--cleanup") {
            cleanup_mode = true;
        } else if (arg == "--api-url" && i + 1 < argc) {
            api_url = argv[++i];
        } else if (arg == "--self-register") {
            self_register = true;
        } else if (arg == "--help") {
            EntropyTestProgram::show_help(argv[0]);
            return 0;
        } else {
            std::cerr << "未知选项: " << arg << std::endl;
            std::cerr << "使用 --help 查看帮助信息" << std::endl;
            return 1;
        }
    }
    
    try {
        EntropyTestProgram test_program(test_dir, verbose, delay_ms, api_url, self_register);

        // 设置全局实例指针，用于信号处理
        g_test_program_instance = &test_program;

        // 设置信号处理器
        signal(SIGUSR1, signal_handler);  // 优雅退出信号
        signal(SIGTERM, signal_handler);  // 终止信号
        signal(SIGINT, signal_handler);   // 中断信号

        if (quick_mode) {
            test_program.run_quick_test();
        } else if (stress_mode) {
            test_program.run_stress_test();
        } else if (complete_mode) {
            test_program.run_complete_test();
        }

        if (cleanup_mode) {
            test_program.cleanup();
        }

        // 清理全局实例指针
        g_test_program_instance = nullptr;

    } catch (const std::exception& e) {
        std::cerr << "程序异常: " << e.what() << std::endl;
        g_test_program_instance = nullptr;
        return 1;
    }
    
    return 0;
} 