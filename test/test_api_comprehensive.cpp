/*
 * PSFSMON-L API综合测试程序 (优化版)
 * 
 * 本程序用于全面测试psfsmon项目对外提供的所有API接口，特别优化了：
 * 1. API接口的精确对应关系测试
 * 2. 细粒度的测试控制和验证
 * 3. 重点进程熵值功能的专项测试集成
 * 4. 详细的测试报告和诊断信息
 * 
 * 测试分类：
 * === 基础API测试 ===
 * 1. 健康检查API - /api/health
 * 2. 系统统计API - /api/stats, /api/monitor/status
 * 3. 配置管理API - /api/config (GET/PUT)
 * 
 * === 进程相关API测试 ===
 * 4. 进程查询API - /api/processes, /api/processes-list, /api/process-tree
 * 5. 单进程信息API - /api/process?pid=<pid>
 * 6. 进程排除管理API - /api/exclusions (GET/POST/DELETE)
 * 
 * === 重点进程API测试 (核心功能) ===
 * 7. 重点进程管理API - /api/focus-processes (GET/POST/DELETE)
 * 8. 重点进程熵值API - /api/focus-processes/entropy?pid=<pid>
 * 9. 重点进程详细统计API - /api/focus-processes/entropy-stats?pid=<pid>
 * 10. 重点进程文件信息API - /api/focus-process/file-info?pid=<pid>
 * 
 * === 文件监控API测试 ===
 * 11. 文件事件API - /api/file/events
 * 12. 文件统计API - /api/file/stats (包含详细熵值统计)
 * 
 * === 网络监控API测试 ===
 * 13. 网络连接API - /api/network
 * 14. 网络统计API - /api/network/stats
 * 15. 监听端口API - /api/network/listening
 * 
 * === 路径管理API测试 ===
 * 16. 受保护路径管理API - /api/protected-paths (GET/POST/DELETE)
 * 
 * === 实时数据API测试 ===
 * 17. 实时数据API - /api/realtime
 * 
 * === 专项功能测试 ===
 * 18. 重点进程文件监控专项测试 (实际文件操作验证)
 * 19. 重点进程熵值计算专项测试 (集成entropy_test_program)
 * 20. 受保护路径违规测试 (集成protected_path_violator)
 */

#include <iostream>
#include <string>
#include <vector>
#include <map>
#include <chrono>
#include <thread>
#include <atomic>
#include <mutex>
#include <fstream>
#include <sstream>
#include <functional>
#include <algorithm>
#include <curl/curl.h>
#include <signal.h>
#include <sys/wait.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/wait.h>
#include <unistd.h>
#include <signal.h>
#include <errno.h>
#include <cstring>
#include <jsoncpp/json/json.h>
#include <iomanip> // 用于格式化输出

// 进程管理辅助类
class ProcessManager {
public:
    static pid_t startPsfsmonService(const std::string& binary_path) {
        pid_t pid = fork();
        if (pid == 0) {
            // 子进程：启动psfsmon服务
            execl(binary_path.c_str(), "psfsmon", "--port", "8080",
                  "--daemon", nullptr);
            exit(1); // 如果exec失败
        } else if (pid > 0) {
            // 父进程：等待服务启动
            std::this_thread::sleep_for(std::chrono::seconds(3));
            return pid;
        } else {
            return -1; // fork失败
        }
    }

    // 优雅终止测试进程（使用SIGUSR1信号）
    static bool gracefulStopTestProcess(pid_t pid, const std::string& process_name = "") {
        if (pid <= 0) return false;

        std::cout << "发送优雅退出信号 (SIGUSR1) 给进程";
        if (!process_name.empty()) {
            std::cout << " " << process_name;
        }
        std::cout << " PID: " << pid << std::endl;

        // 发送SIGUSR1信号请求优雅退出
        if (kill(pid, SIGUSR1) == 0) {
            // 等待进程结束
            int status;
            for (int i = 0; i < 15; ++i) { // 最多等待15秒，给足够时间进行清理
                if (waitpid(pid, &status, WNOHANG) == pid) {
                    std::cout << "✓ 进程优雅退出成功" << std::endl;
                    return true;
                }
                std::this_thread::sleep_for(std::chrono::seconds(1));

                // 每5秒输出一次等待状态
                if (i % 5 == 4) {
                    std::cout << "等待进程优雅退出... (" << (i+1) << "/15 秒)" << std::endl;
                }
            }

            // 如果优雅退出超时，发送SIGTERM
            std::cout << "⚠️ 优雅退出超时，发送SIGTERM信号..." << std::endl;
            if (kill(pid, SIGTERM) == 0) {
                for (int i = 0; i < 5; ++i) { // 再等待5秒
                    if (waitpid(pid, &status, WNOHANG) == pid) {
                        std::cout << "✓ 进程通过SIGTERM终止" << std::endl;
                        return true;
                    }
                    std::this_thread::sleep_for(std::chrono::seconds(1));
                }

                // 最后发送SIGKILL强制终止
                std::cout << "⚠️ SIGTERM超时，强制终止进程..." << std::endl;
                kill(pid, SIGKILL);
                waitpid(pid, &status, 0);
            }
        } else {
            std::cout << "⚠️ 无法发送SIGUSR1信号，进程可能已经结束" << std::endl;
        }

        return true;
    }

    static bool stopProcess(pid_t pid) {
        if (pid <= 0) return false;

        // 发送SIGTERM
        if (kill(pid, SIGTERM) == 0) {
            // 等待进程结束
            int status;
            for (int i = 0; i < 10; ++i) { // 最多等待10秒
                if (waitpid(pid, &status, WNOHANG) == pid) {
                    return true;
                }
                std::this_thread::sleep_for(std::chrono::seconds(1));
            }

            // 如果进程仍未结束，发送SIGKILL
            kill(pid, SIGKILL);
            waitpid(pid, &status, 0);
        }

        return true;
    }

    static bool isProcessRunning(pid_t pid) {
        return kill(pid, 0) == 0;
    }
};

class ApiTestFramework {
private:
    std::string base_url_;
    std::atomic<bool> test_running_;
    std::mutex results_mutex_;
    std::string entropy_test_mode_;  // 熵值测试模式
    
    // 详细测试统计结构
    struct DetailedTestStats {
        // 基础统计
        int total_tests = 0;
        int passed_tests = 0;
        int failed_tests = 0;
        int skipped_tests = 0;
        
        // 分类统计
        std::map<std::string, int> category_total;
        std::map<std::string, int> category_passed;
        std::map<std::string, int> category_failed;
        
        // API端点统计
        std::map<std::string, bool> api_endpoint_results;
        
        // 详细结果记录
        std::vector<std::string> failed_test_names;
        std::vector<std::string> error_messages;
        std::vector<std::string> skipped_reasons;
        
        // 性能统计
        std::vector<double> response_times;
        double total_test_time = 0.0;
    } stats_;
    
    // HTTP响应结构
    struct HttpResponse {
        long response_code;
        std::string headers;
        std::string body;
        double total_time;
        bool is_json_valid;
        Json::Value json_data;
        
        HttpResponse() : response_code(0), total_time(0.0), is_json_valid(false) {}
    };
    
    // API端点定义结构
    struct ApiEndpoint {
        std::string method;
        std::string path;
        std::string category;
        std::string description;
        bool requires_data;
        std::function<std::string()> get_test_data;
        std::function<bool(const HttpResponse&)> validator;
    };
    
    // 预定义的API端点列表
    std::vector<ApiEndpoint> api_endpoints_;
    
    // CURL回调函数
    static size_t WriteCallback(void* contents, size_t size, size_t nmemb, std::string* userp) {
        userp->append((char*)contents, size * nmemb);
        return size * nmemb;
    }
    
    static size_t HeaderCallback(void* contents, size_t size, size_t nmemb, std::string* userp) {
        userp->append((char*)contents, size * nmemb);
        return size * nmemb;
    }

    // JSON格式化输出辅助函数
    std::string formatJsonOutput(const std::string& json_str) {
        Json::Value root;
        Json::Reader reader;
        if (reader.parse(json_str, root)) {
            Json::StreamWriterBuilder builder;
            builder["indentation"] = "  ";
            builder["commentStyle"] = "None";
            builder["enableYAMLCompatibility"] = false;
            builder["dropNullPlaceholders"] = false;
            builder["useSpecialFloats"] = false;
            builder["precision"] = 6;
            
            std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());
            std::ostringstream formatted;
            writer->write(root, &formatted);
            return formatted.str();
        } else {
            return json_str;  // 如果JSON解析失败，返回原始字符串
        }
    }
    
    // API端点定义初始化
    void initializeApiEndpoints() {
        api_endpoints_.clear();
        
        // === 健康检查和状态API ===
        api_endpoints_.push_back({
            "GET", "/api/health", "health", "系统健康检查",
            false, nullptr,
            [](const HttpResponse& resp) {
                return resp.is_json_valid && 
                       resp.json_data.isObject() &&
                       resp.json_data.get("status", "").asString() == "ok" &&
                       resp.json_data.isMember("service") &&
                       resp.json_data.isMember("timestamp");
            }
        });
        
        api_endpoints_.push_back({
            "GET", "/api/stats", "stats", "系统统计信息",
            false, nullptr,
            [](const HttpResponse& resp) {
                return resp.is_json_valid && 
                       resp.json_data.isObject() &&
                       resp.json_data.isMember("total_processes") &&
                       resp.json_data.isMember("total_file_events") &&
                       resp.json_data.isMember("uptime_seconds");
            }
        });
        
        api_endpoints_.push_back({
            "GET", "/api/monitor/status", "stats", "监控状态信息",
            false, nullptr,
            [](const HttpResponse& resp) {
                return resp.is_json_valid && 
                       resp.json_data.isObject() &&
                       resp.json_data.isMember("running") &&
                       resp.json_data.get("running", false).asBool() == true;
            }
        });
        
        // === 进程相关API ===
        api_endpoints_.push_back({
            "GET", "/api/processes", "process", "获取所有进程信息",
            false, nullptr,
            [](const HttpResponse& resp) {
                return resp.is_json_valid && 
                       resp.json_data.isObject() &&
                       resp.json_data.isMember("count") &&
                       resp.json_data.isMember("processes") &&
                       resp.json_data["processes"].isArray();
            }
        });
        
        api_endpoints_.push_back({
            "GET", "/api/processes-list", "process", "获取进程列表",
            false, nullptr,
            [](const HttpResponse& resp) {
                return resp.is_json_valid && 
                       resp.json_data.isObject() &&
                       resp.json_data.isMember("processes") &&
                       resp.json_data["processes"].isArray();
            }
        });
        
        api_endpoints_.push_back({
            "GET", "/api/process-tree", "process", "获取进程树",
            false, nullptr,
            [](const HttpResponse& resp) {
                return resp.is_json_valid && 
                       resp.json_data.isObject() &&
                       (resp.json_data.isMember("pid") || resp.json_data.isMember("error"));
            }
        });
        
        // 注意：特定进程信息测试在实际功能测试中进行，这里不做伪测试
        
        // === 网络相关API ===
        api_endpoints_.push_back({
            "GET", "/api/network", "network", "获取网络连接信息",
            false, nullptr,
            [](const HttpResponse& resp) {
                return resp.is_json_valid && 
                       resp.json_data.isObject() &&
                       resp.json_data.isMember("count") &&
                       resp.json_data.isMember("connections") &&
                       resp.json_data["connections"].isArray();
            }
        });
        
        api_endpoints_.push_back({
            "GET", "/api/network/stats", "network", "获取网络统计信息",
            false, nullptr,
            [](const HttpResponse& resp) {
                return resp.is_json_valid && 
                       resp.json_data.isObject() &&
                       (resp.json_data.isMember("total_connections") || resp.json_data.isMember("error"));
            }
        });
        
        api_endpoints_.push_back({
            "GET", "/api/network/listening", "network", "获取监听端口信息",
            false, nullptr,
            [](const HttpResponse& resp) {
                return resp.is_json_valid && 
                       resp.json_data.isObject() &&
                       (resp.json_data.isMember("listening_ports") || resp.json_data.isMember("error"));
            }
        });
        
        // === 配置管理API ===
        api_endpoints_.push_back({
            "GET", "/api/config", "config", "获取配置信息",
            false, nullptr,
            [](const HttpResponse& resp) {
                return resp.is_json_valid && 
                       resp.json_data.isObject() &&
                       (resp.json_data.isMember("enable_file_monitoring") || resp.json_data.isMember("error"));
            }
        });
        
        api_endpoints_.push_back({
            "PUT", "/api/config", "config", "更新配置信息",
            true, 
            []() { return R"({"log_level": "INFO", "enable_file_monitoring": true})"; },
            [](const HttpResponse& resp) {
                return resp.is_json_valid && 
                       resp.json_data.isObject() &&
                       (resp.json_data.isMember("message") || resp.json_data.isMember("error"));
            }
        });
        
        // === 进程管理API ===
        api_endpoints_.push_back({
            "GET", "/api/exclusions", "process-mgmt", "获取排除进程列表",
            false, nullptr,
            [](const HttpResponse& resp) {
                return resp.is_json_valid && 
                       resp.json_data.isObject() &&
                       resp.json_data.isMember("total_exclusions") &&
                       resp.json_data.isMember("excluded_names");
            }
        });
        
        api_endpoints_.push_back({
            "POST", "/api/exclusions", "process-mgmt", "添加排除进程规则",
            true, 
            []() { return R"({"type": "name", "value": "test_exclusion_process"})"; },
            [](const HttpResponse& resp) {
                return resp.is_json_valid && 
                       resp.json_data.isObject() &&
                       (resp.json_data.isMember("status") || resp.json_data.isMember("message"));
            }
        });
        
        api_endpoints_.push_back({
            "DELETE", "/api/exclusions?type=name&value=test_exclusion_process", "process-mgmt", "删除排除进程规则",
            false, nullptr,
            [](const HttpResponse& resp) {
                return resp.is_json_valid && 
                       resp.json_data.isObject() &&
                       (resp.json_data.isMember("status") || resp.json_data.isMember("message"));
            }
        });
        
        api_endpoints_.push_back({
            "GET", "/api/focus-processes", "focus-process", "获取重点进程列表",
            false, nullptr,
            [](const HttpResponse& resp) {
                return resp.is_json_valid && 
                       resp.json_data.isObject() &&
                       resp.json_data.isMember("focus_processes") &&
                       resp.json_data["focus_processes"].isArray();
            }
        });
        
        api_endpoints_.push_back({
            "POST", "/api/focus-processes", "focus-process", "添加重点进程",
            true, 
            []() { return R"({"type": "name", "value": "test_focus_process"})"; },
            [](const HttpResponse& resp) {
                return resp.is_json_valid && 
                       resp.json_data.isObject() &&
                       (resp.json_data.isMember("status") || resp.json_data.isMember("message"));
            }
        });
        
        api_endpoints_.push_back({
            "DELETE", "/api/focus-processes?type=name&value=test_focus_process", "focus-process", "删除重点进程",
            false, nullptr,
            [](const HttpResponse& resp) {
                return resp.is_json_valid && 
                       resp.json_data.isObject() &&
                       (resp.json_data.isMember("status") || resp.json_data.isMember("message"));
            }
        });
        
        // === 重点进程熵值API ===
        api_endpoints_.push_back({
            "GET", "/api/focus-processes/entropy", "focus-process-entropy", "获取重点进程熵值信息(无参数)",
            false, nullptr,
            [](const HttpResponse& resp) {
                return resp.is_json_valid && 
                       resp.json_data.isObject() &&
                       (resp.json_data.isMember("focus_process_count") || resp.json_data.isMember("error"));
            }
        });
        
        api_endpoints_.push_back({
            "GET", "/api/focus-processes/entropy-stats", "focus-process-entropy", "获取重点进程详细熵值统计(无参数)",
            false, nullptr,
            [](const HttpResponse& resp) {
                return resp.is_json_valid && 
                       resp.json_data.isObject() &&
                       (resp.json_data.isMember("entropy_stats") || resp.json_data.isMember("error"));
            }
        });
        
        // 注意：特定PID的熵值测试在实际功能测试中进行，使用真实的测试程序PID
        
        // === 文件监控API ===
        api_endpoints_.push_back({
            "GET", "/api/file/events", "file-monitor", "获取文件事件",
            false, nullptr,
            [](const HttpResponse& resp) {
                return resp.is_json_valid && 
                       resp.json_data.isObject() &&
                       (resp.json_data.isMember("events") || resp.json_data.isMember("error"));
            }
        });
        
        api_endpoints_.push_back({
            "GET", "/api/file/stats", "file-monitor", "获取文件统计信息",
            false, nullptr,
            [](const HttpResponse& resp) {
                return resp.is_json_valid && 
                       resp.json_data.isObject() &&
                       resp.json_data.isMember("total_events") &&
                       resp.json_data.isMember("entropy_calculations");
            }
        });
        
        // === 路径管理API ===
        api_endpoints_.push_back({
            "GET", "/api/protected-paths", "path-mgmt", "获取受保护路径列表",
            false, nullptr,
            [](const HttpResponse& resp) {
                return resp.is_json_valid && 
                       resp.json_data.isObject() &&
                       resp.json_data.isMember("protected_paths") &&
                       resp.json_data["protected_paths"].isArray();
            }
        });
        
        api_endpoints_.push_back({
            "POST", "/api/protected-paths", "path-mgmt", "添加受保护路径",
            true, 
            []() { return R"({"path": "/tmp/test_protected_path"})"; },
            [](const HttpResponse& resp) {
                return resp.is_json_valid && 
                       resp.json_data.isObject() &&
                       (resp.json_data.isMember("status") || resp.json_data.isMember("message"));
            }
        });
        
        api_endpoints_.push_back({
            "DELETE", "/api/protected-paths?path=/tmp/test_protected_path", "path-mgmt", "删除受保护路径",
            false, nullptr,
            [](const HttpResponse& resp) {
                return resp.is_json_valid && 
                       resp.json_data.isObject() &&
                       (resp.json_data.isMember("status") || resp.json_data.isMember("message"));
            }
        });
        
        // === 实时数据API ===
        api_endpoints_.push_back({
            "GET", "/api/realtime", "realtime", "获取实时数据",
            false, nullptr,
            [](const HttpResponse& resp) {
                return resp.is_json_valid && 
                       resp.json_data.isObject() &&
                       (resp.json_data.isMember("timestamp") || resp.json_data.isMember("error"));
            }
        });
    }

public:
    ApiTestFramework(const std::string& base_url, const std::string& entropy_mode = "quick") 
        : base_url_(base_url), test_running_(true), entropy_test_mode_(entropy_mode) {
        curl_global_init(CURL_GLOBAL_DEFAULT);
        initializeApiEndpoints();
    }
    
    ~ApiTestFramework() {
        curl_global_cleanup();
    }
    
    // 主测试入口 - 运行所有预定义的API测试
    bool runAllApiTests() {
        std::cout << "=== PSFSMON-L API综合测试程序 (优化版) ===" << std::endl;
        std::cout << "测试基础URL: " << base_url_ << std::endl;
        std::cout << "预定义API端点数量: " << api_endpoints_.size() << std::endl;
        std::cout << std::endl;
        
        auto start_time = std::chrono::steady_clock::now();
        
        // 预检查
        if (!preflightCheck()) {
            std::cout << "预检查失败，测试终止" << std::endl;
            return false;
        }
        
        // 运行所有预定义的API测试
        runPredefinedApiTests();
        
        auto end_time = std::chrono::steady_clock::now();
        stats_.total_test_time = std::chrono::duration<double>(end_time - start_time).count();
        
        // 生成最终报告
        generateDetailedReport();
        
        return stats_.failed_tests == 0;
    }
    
    // 运行预定义的API测试
    void runPredefinedApiTests() {
        std::cout << "=== 运行预定义API端点测试 ===" << std::endl;
        
        // 预处理：创建API测试需要的测试路径
        std::string test_protected_path = "/tmp/test_protected_path";
        bool need_cleanup_path = false;
        
        std::cout << "预处理：创建API测试需要的测试路径..." << std::endl;
        if (system(("mkdir -p " + test_protected_path).c_str()) == 0) {
            need_cleanup_path = true;
            std::cout << "✓ 测试路径创建成功: " << test_protected_path << std::endl;
        } else {
            std::cout << "⚠️ 测试路径创建失败: " << test_protected_path << std::endl;
        }
        
        for (const auto& endpoint : api_endpoints_) {
            runSingleApiTest(endpoint);
        }
        
        // 后处理：清理测试路径
        if (need_cleanup_path) {
            std::cout << "后处理：清理测试路径..." << std::endl;
            
            /*
            // 先尝试删除受保护路径注册（如果存在）
            try {
                auto response = makeHttpRequest("DELETE", "/api/protected-paths?path=" + test_protected_path, "");
                if (response.response_code == 200) {
                    std::cout << "✓ 受保护路径注册已删除" << std::endl;
                }
            } catch (...) {
                // 忽略删除失败的情况
            }
            */
            
            // 删除测试路径
            if (system(("rm -rf " + test_protected_path).c_str()) == 0) {
                std::cout << "✓ 测试路径清理成功: " << test_protected_path << std::endl;
            } else {
                std::cout << "⚠️ 测试路径清理失败: " << test_protected_path << std::endl;
            }
        }
    }
    
    // 运行分类测试
    bool runCategoryTests(const std::vector<std::string>& categories) {
        std::cout << "=== PSFSMON-L API分类测试程序 ===" << std::endl;
        std::cout << "测试基础URL: " << base_url_ << std::endl;
        std::cout << "执行的测试类别: ";
        for (const auto& category : categories) {
            std::cout << category << " ";
        }
        std::cout << std::endl << std::endl;
        
        auto start_time = std::chrono::steady_clock::now();
        
        // 预检查
        if (!preflightCheck()) {
            std::cout << "预检查失败，测试终止" << std::endl;
            return false;
        }
        
        // 运行分类测试
        for (const auto& category : categories) {
            if (category == "api-all") {
                runPredefinedApiTests();
            } else {
                // 运行特定分类的API测试
                runCategoryApiTests(category);
            }
        }
        
        auto end_time = std::chrono::steady_clock::now();
        stats_.total_test_time = std::chrono::duration<double>(end_time - start_time).count();
        
        // 生成最终报告
        generateDetailedReport();
        
        return stats_.failed_tests == 0;
    }

private:
    // 运行单个API测试 (优化版)
    void runSingleApiTest(const ApiEndpoint& endpoint) {
        std::lock_guard<std::mutex> lock(results_mutex_);
        
        stats_.total_tests++;
        stats_.category_total[endpoint.category]++;
        
        std::string test_name = endpoint.method + " " + endpoint.path + " (" + endpoint.description + ")";
        std::cout << "\n测试: " << test_name << std::endl;
        
        try {
            std::string test_data;
            if (endpoint.requires_data && endpoint.get_test_data) {
                test_data = endpoint.get_test_data();
                std::cout << "  请求数据: " << formatJsonOutput(test_data) << std::endl;
            }
            
            auto response = makeEnhancedHttpRequest(endpoint.method, endpoint.path, test_data);
            double response_time = response.total_time;  // 保存响应时间用于错误处理
            
            std::cout << "  响应状态: HTTP " << response.response_code 
                      << " (耗时: " << response_time << "s)" << std::endl;
            
            if (response.is_json_valid) {
                std::cout << "  响应内容: " << formatJsonOutput(response.body) << std::endl;
            } else {
                std::cout << "  响应内容: " << response.body << std::endl;
            }
            
            // 记录性能数据
            stats_.response_times.push_back(response.total_time);
            
            // 检查HTTP状态码
            if (response.response_code < 200 || response.response_code >= 300) {
                if (response.response_code == 404 || response.response_code == 501) {
                    std::cout << "  结果: 跳过 (API未实现)" << std::endl;
                    stats_.skipped_tests++;
                    stats_.skipped_reasons.push_back("API未实现: " + endpoint.path);
                    return;
                } else {
                    throw std::runtime_error("HTTP错误: " + std::to_string(response.response_code));
                }
            }
            
            // 执行自定义验证
            if (endpoint.validator && endpoint.validator(response)) {
                std::cout << "  结果: ✅ 通过" << std::endl;
                stats_.passed_tests++;
                stats_.category_passed[endpoint.category]++;
                stats_.api_endpoint_results[endpoint.path] = true;
            } else {
                throw std::runtime_error("响应验证失败");
            }
            
        } catch (const std::exception& e) {
            std::string error_msg = e.what();
            
            // 检查是否是超时错误（可能的死锁）
            if (error_msg.find("Timeout was reached") != std::string::npos || 
                error_msg.find("timeout") != std::string::npos) {  // 超时错误
                std::cout << "  结果: ⚠️ 疑似死锁 - 超时 (" << error_msg << ")" << std::endl;
                std::cout << "  🚨 警告：API " << endpoint.path << " 可能导致服务死锁！" << std::endl;
                std::cout << "  建议检查服务日志: /var/log/psfsmon/" << std::endl;
                
                // 检查服务是否还活着
                std::cout << "  检查服务状态..." << std::endl;
                try {
                    auto health_response = makeHttpRequest("GET", "/api/health", "");
                    if (health_response.response_code != 200) {
                        std::cout << "  ❌ 服务已死机，健康检查失败！" << std::endl;
                        std::cout << "  死锁原因API: " << endpoint.path << std::endl;
                        exit(1);  // 立即退出，报告死锁
                    } else {
                        std::cout << "  ⚠️ 服务响应缓慢但仍可访问" << std::endl;
                    }
                } catch (const std::exception& health_e) {
                    std::cout << "  ❌ 服务已死机，无法访问健康检查接口！" << std::endl;
                    std::cout << "  死锁原因API: " << endpoint.path << std::endl;
                    exit(1);  // 立即退出，报告死锁
                }
                
                stats_.failed_test_names.push_back(test_name + " [疑似死锁]");
                stats_.error_messages.push_back("疑似死锁超时: " + error_msg);
            } else {
                std::cout << "  结果: ❌ 失败 (" << error_msg << ")" << std::endl;
                stats_.failed_test_names.push_back(test_name);
                stats_.error_messages.push_back(error_msg);
            }
            
            stats_.failed_tests++;
            stats_.category_failed[endpoint.category]++;
            stats_.api_endpoint_results[endpoint.path] = false;
        }
    }
    
    // 执行HTTP请求并解析JSON (增强版 - 死锁检测)
    HttpResponse makeEnhancedHttpRequest(const std::string& method, const std::string& endpoint, const std::string& data) {
        HttpResponse response;
        CURL* curl = curl_easy_init();
        
        if (!curl) {
            throw std::runtime_error("CURL初始化失败");
        }
        
        std::string url = base_url_ + endpoint;
        
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response.body);
        curl_easy_setopt(curl, CURLOPT_HEADERFUNCTION, HeaderCallback);
        curl_easy_setopt(curl, CURLOPT_HEADERDATA, &response.headers);
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);        // 死锁检测：10秒超时
        curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L);  // 连接超时5秒
        curl_easy_setopt(curl, CURLOPT_VERBOSE, 0L);
        curl_easy_setopt(curl, CURLOPT_NOPROGRESS, 1L);
        
        // 设置HTTP方法
        if (method == "POST") {
            curl_easy_setopt(curl, CURLOPT_POST, 1L);
            if (!data.empty()) {
                curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data.c_str());
            }
        } else if (method == "PUT") {
            curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "PUT");
            if (!data.empty()) {
                curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data.c_str());
            }
        } else if (method == "DELETE") {
            curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "DELETE");
            if (!data.empty()) {
                curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data.c_str());
            }
        }
        
        // 设置HTTP头
        struct curl_slist* headers = nullptr;
        if (!data.empty()) {
            headers = curl_slist_append(headers, "Content-Type: application/json");
            curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        }
        
        // 执行请求
        CURLcode res = curl_easy_perform(curl);
        
        if (res != CURLE_OK) {
            curl_easy_cleanup(curl);
            if (headers) curl_slist_free_all(headers);
            throw std::runtime_error("CURL请求失败: " + std::string(curl_easy_strerror(res)));
        }
        
        // 获取响应信息
        curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &response.response_code);
        curl_easy_getinfo(curl, CURLINFO_TOTAL_TIME, &response.total_time);
        
        curl_easy_cleanup(curl);
        if (headers) curl_slist_free_all(headers);
        
        // 尝试解析JSON
        Json::Reader reader;
        if (reader.parse(response.body, response.json_data)) {
            response.is_json_valid = true;
        }
        
        return response;
    }
    
    // 运行特定分类的API测试
    void runCategoryApiTests(const std::string& category) {
        std::cout << "\n--- 分类测试: " << category << " ---" << std::endl;
        
        // 处理特殊的测试分类
        if (category == "entropy-test") {
            runSpecialEntropyTest();
            return;
        } else if (category == "complete-behavior") {
            runCompleteBehaviorTest();
            return;
        } else if (category == "protected-path") {
            runProtectedPathTest();
            return;
        } else if (category == "entropy-api-internal") {
            runInternalEntropyApiTest();
            return;
        } else if (category == "focus-process-entropy") {
            runFocusProcessEntropyDeadlockTest();
            return;
        } else if (category == "focus-process") {
            // focus-process类别运行所有重点进程相关的API测试
            runFocusProcessApiTests();
            return;
        }
        
        // 特殊处理：path-mgmt类别需要创建测试路径
        std::string test_protected_path = "/tmp/test_protected_path";
        bool need_cleanup_path = false;
        
        if (category == "path-mgmt") {
            std::cout << "预处理：为path-mgmt测试创建必要的测试路径..." << std::endl;
            if (system(("mkdir -p " + test_protected_path).c_str()) == 0) {
                need_cleanup_path = true;
                std::cout << "✓ 测试路径创建成功: " << test_protected_path << std::endl;
            } else {
                std::cout << "⚠️ 测试路径创建失败: " << test_protected_path << std::endl;
            }
        }
        
        // 处理常规API端点测试
        bool found_category = false;
        for (const auto& endpoint : api_endpoints_) {
            if (endpoint.category == category) {
                found_category = true;
                runSingleApiTest(endpoint);
            }
        }
        
        // 特殊处理：path-mgmt类别需要清理测试路径
        if (category == "path-mgmt" && need_cleanup_path) {
            std::cout << "后处理：清理path-mgmt测试路径..." << std::endl;
            
            // 先尝试删除受保护路径注册（如果存在）
            try {
                auto response = makeHttpRequest("DELETE", "/api/protected-paths?path=" + test_protected_path, "");
                if (response.response_code == 200) {
                    std::cout << "✓ 受保护路径注册已删除" << std::endl;
                }
            } catch (...) {
                // 忽略删除失败的情况
            }
            
            // 删除测试路径
            if (system(("rm -rf " + test_protected_path).c_str()) == 0) {
                std::cout << "✓ 测试路径清理成功: " << test_protected_path << std::endl;
            } else {
                std::cout << "⚠️ 测试路径清理失败: " << test_protected_path << std::endl;
            }
        }
        
        if (!found_category) {
            std::cout << "未找到分类 '" << category << "' 的API端点" << std::endl;
        }
    }
    
    // 运行重点进程相关API测试
    void runFocusProcessApiTests() {
        std::cout << "=== 运行重点进程相关API测试 ===" << std::endl;
        
        // 运行所有重点进程相关的API端点测试
        for (const auto& endpoint : api_endpoints_) {
            if (endpoint.category == "focus-process") {
                runSingleApiTest(endpoint);
            }
        }
    }
    
    // 运行熵值功能专项测试 (统一逻辑版本)
    void runSpecialEntropyTest() {
        std::cout << "=== 启动重点进程熵值功能完整验证测试 ===" << std::endl;
        std::cout << "使用测试模式: " << entropy_test_mode_ << std::endl;

        bool test_passed = true;
        std::string error_detail;

        try {
            // 统一测试逻辑：子程序自注册 -> 运行 -> 通知完成 -> 主程序查询 -> 主程序清理 -> 通知子程序退出
            test_passed = runUnifiedEntropyTest(entropy_test_mode_);

        } catch (const std::exception& e) {
            test_passed = false;
            error_detail = e.what();
            std::cout << "\n✗ 熵值功能测试失败: " << error_detail << std::endl;
        }

        if (test_passed) {
            stats_.passed_tests++;
        } else {
            stats_.failed_tests++;
            stats_.failed_test_names.push_back("entropy-test (完整功能验证)");
            stats_.error_messages.push_back(error_detail);
        }
        stats_.total_tests++;
    }

    // 统一的熵值测试方法 (所有熵值测试使用相同逻辑)
    bool runUnifiedEntropyTest(const std::string& test_mode) {
        std::cout << "\n=== 统一熵值测试逻辑 (模式: " << test_mode << ") ===" << std::endl;

        try {
            // 步骤1：启动具有自注册功能的熵值测试程序
            std::cout << "\n步骤1: 启动自注册熵值测试程序..." << std::endl;
            std::string test_dir = "/tmp/psfsmon_entropy_test_" + test_mode;
            std::string cmd = "./test/entropy_test_program --" + test_mode + " --test-dir " + test_dir + " --self-register --api-url " + base_url_;
            std::cout << "执行命令: " << cmd << std::endl;

            pid_t entropy_child_pid = fork();
            if (entropy_child_pid == 0) {
                // 子进程：直接执行熵值测试程序，避免shell中间层
                std::vector<std::string> args;
                args.push_back("./test/entropy_test_program");
                args.push_back("--" + test_mode);
                args.push_back("--test-dir");
                args.push_back(test_dir);
                args.push_back("--self-register");
                args.push_back("--api-url");
                args.push_back(base_url_);

                // 转换为char*数组
                std::vector<char*> argv_vec;
                for (auto& arg : args) {
                    argv_vec.push_back(const_cast<char*>(arg.c_str()));
                }
                argv_vec.push_back(nullptr);

                execv("./test/entropy_test_program", argv_vec.data());
                exit(1); // 如果exec失败
            } else if (entropy_child_pid < 0) {
                throw std::runtime_error("无法fork子进程启动entropy_test_program");
            }
            std::cout << "✓ 熵值测试程序已启动，PID: " << entropy_child_pid << std::endl;

            // 步骤2：等待子测试程序完成操作并发送完成信号
            // 根据测试模式调整等待时间
            int wait_timeout = 120;  // 默认60秒
            if (test_mode == "complete") {
                wait_timeout = 180;  // complete模式需要更长时间
            } else if (test_mode == "stress") {
                wait_timeout = 240;  // stress模式需要更长时间进行压力测试
            }
            std::cout << "\n步骤2: 等待子测试程序完成操作（超时时间: " << wait_timeout << "秒）..." << std::endl;
            if (!waitForTestCompletion("/tmp/psfsmon_entropy_test_done.flag", wait_timeout)) {
                std::cout << "⚠️ 等待子测试程序完成超时（" << wait_timeout << "秒），但继续进行测试..." << std::endl;
            }

            // 步骤3：直接使用fork出来的PID查询熵值数据
            std::cout << "\n步骤3: 查询熵值数据..." << std::endl;
            int test_pid = entropy_child_pid;  // 直接使用fork出来的PID
            std::cout << "使用fork出来的测试程序PID: " << test_pid << std::endl;
            queryAndDisplayEntropyData(test_pid);

            // 步骤4：通知子测试程序退出（但保持重点进程状态）
            std::cout << "\n步骤4: 通知子测试程序退出..." << std::endl;
            ProcessManager::gracefulStopTestProcess(test_pid, "entropy_test_program");

            // 步骤5：清理重点进程注册（在进程退出后进行清理）
            std::cout << "\n步骤5: 清理重点进程注册..." << std::endl;
            cleanupFocusProcess(test_pid);

            // 备用清理
            int cleanup_result = system("pkill -f entropy_test_program");
            if (cleanup_result != 0) {
                // 进程可能已经结束，忽略错误
            }

            std::cout << "\n✓ 统一熵值测试完成" << std::endl;
            return true;

        } catch (const std::exception& e) {
            std::cout << "\n✗ 统一熵值测试失败: " << e.what() << std::endl;
            int cleanup_result = system("pkill -f entropy_test_program");
            if (cleanup_result != 0) {
                // 进程可能已经结束，忽略错误
            }
            return false;
        }
    }

    // 运行完整行为测试 (综合功能验证)
    void runCompleteBehaviorTest() {
        std::cout << "=== 启动完整PSFSMON-L功能综合验证测试 ===" << std::endl;
        std::cout << "此测试将验证重点进程监控、熵值计算、受保护路径等核心功能" << std::endl;
        
        bool test_passed = true;
        std::string error_detail;
        
        try {
            // 步骤1：运行熵值功能测试
            std::cout << "\n=== 第一部分：重点进程熵值功能测试 ===" << std::endl;
            runSpecialEntropyTest();
            
            // 等待系统稳定
            std::this_thread::sleep_for(std::chrono::seconds(3));
            
            // 步骤2：运行受保护路径测试
            std::cout << "\n=== 第二部分：受保护路径功能测试 ===" << std::endl;
            runProtectedPathTest();
            
            // 等待系统稳定
            std::this_thread::sleep_for(std::chrono::seconds(3));
            
            // 步骤3：综合API死锁检测
            std::cout << "\n=== 第三部分：API死锁检测测试 ===" << std::endl;
            std::cout << "在实际功能运行后测试API稳定性..." << std::endl;
            
            // 测试高风险API
            std::vector<std::string> high_risk_apis = {
                "/api/focus-processes/entropy",
                "/api/focus-processes/entropy-stats", 
                "/api/focus-process/file-info?pid=1",
                "/api/file/events",
                "/api/file/stats"
            };
            
            for (const auto& api : high_risk_apis) {
                std::cout << "测试API: " << api << std::endl;
                auto response = makeHttpRequest("GET", api, "");
                if (response.total_time > 5.0) {
                    throw std::runtime_error("API " + api + " 响应超时: " + std::to_string(response.total_time) + "s");
                }
                std::cout << "✓ API响应正常 (耗时: " << response.total_time << "s)" << std::endl;
            }
            
            // 步骤4：运行大文件熵值测试
            std::cout << "\n=== 第四部分：大文件熵值计算测试 ===" << std::endl;
            std::cout << "使用测试模式: " << entropy_test_mode_ << std::endl;
            if (!runUnifiedEntropyTest(entropy_test_mode_)) {
                std::cout << "⚠️ 大文件熵值测试失败，但继续其他测试" << std::endl;
            }
            
            // 步骤5：最终系统状态检查
            std::cout << "\n=== 第五部分：最终系统状态检查 ===" << std::endl;
            
            // 检查服务健康状态
            auto health_response = makeHttpRequest("GET", "/api/health", "");
            if (health_response.response_code == 200) {
                std::cout << "✓ 服务健康检查通过" << std::endl;
            } else {
                throw std::runtime_error("服务健康检查失败");
            }
            
            // 检查系统统计
            auto stats_response = makeHttpRequest("GET", "/api/stats", "");
            if (stats_response.response_code == 200 && stats_response.is_json_valid) {
                auto total_processes = stats_response.json_data.get("total_processes", 0).asInt();
                auto total_file_events = stats_response.json_data.get("total_file_events", 0).asInt();
                std::cout << "✓ 系统统计查询成功: 进程数=" << total_processes << ", 文件事件数=" << total_file_events << std::endl;
            }
            
            std::cout << "\n✓ 完整功能综合验证测试全部通过" << std::endl;
            std::cout << "所有核心功能（重点进程监控、熵值计算、受保护路径、API稳定性）均正常工作" << std::endl;
            
        } catch (const std::exception& e) {
            test_passed = false;
            error_detail = e.what();
            std::cout << "\n✗ 完整功能测试失败: " << error_detail << std::endl;
            
            // 清理所有测试进程
            std::cout << "执行测试进程清理..." << std::endl;
            if (system("pkill -f entropy_test_program") != 0) {
                // 进程可能已经结束，忽略错误
            }
            if (system("pkill -f protected_path_violator") != 0) {
                // 进程可能已经结束，忽略错误
            }
        }
        
        if (test_passed) {
            stats_.passed_tests++;
        } else {
            stats_.failed_tests++;
            stats_.failed_test_names.push_back("complete-behavior (综合功能验证)");
            stats_.error_messages.push_back(error_detail);
        }
        stats_.total_tests++;
    }
    
    // 运行受保护路径违规测试 (统一逻辑版本)
    void runProtectedPathTest() {
        std::cout << "=== 启动受保护路径功能完整验证测试 ===" << std::endl;
        std::cout << "警告: 此测试可能导致违规进程被终止" << std::endl;

        bool test_passed = true;
        std::string error_detail;
        std::string test_path = "/tmp/psfsmon_protected_test";

        try {
            // 统一测试逻辑：子程序自注册 -> 运行 -> 通知完成 -> 主程序查询 -> 主程序清理 -> 通知子程序退出
            test_passed = runUnifiedProtectedPathTest(test_path);

        } catch (const std::exception& e) {
            test_passed = false;
            error_detail = e.what();
            std::cout << "\n✗ 受保护路径测试失败: " << error_detail << std::endl;
        }

        // 清理测试路径
        if (system(("rm -rf " + test_path).c_str()) != 0) {
            std::cout << "警告: 清理测试路径失败" << std::endl;
        }

        if (test_passed) {
            stats_.passed_tests++;
        } else {
            stats_.failed_tests++;
            stats_.failed_test_names.push_back("protected-path (完整功能验证)");
            stats_.error_messages.push_back(error_detail);
        }
        stats_.total_tests++;
    }

    // 统一的受保护路径测试方法 (所有受保护路径测试使用相同逻辑)
    bool runUnifiedProtectedPathTest(const std::string& test_path) {
        std::cout << "\n=== 统一受保护路径测试逻辑 ===" << std::endl;

        try {
            // 步骤1：创建测试路径
            std::cout << "\n步骤1: 创建测试路径..." << std::endl;
            if (system(("mkdir -p " + test_path).c_str()) != 0) {
                throw std::runtime_error("创建测试路径失败");
            }
            std::cout << "✓ 测试路径创建成功: " + test_path << std::endl;

            // 步骤2：添加受保护路径并检查监控模式
            std::cout << "\n步骤2: 添加受保护路径并检查监控模式..." << std::endl;
            bool is_kill_mode = checkProtectedPathKillMode(test_path);
            std::cout << "检测到监控模式: " << (is_kill_mode ? "KILL (进程终止)" : "LOG (仅记录)") << std::endl;

            // 步骤3：启动违规测试程序 (不需要自注册，因为路径已经添加)
            std::cout << "\n步骤3: 启动违规测试程序..." << std::endl;
            std::string cmd = "./test/protected_path_violator " + test_path + " --api-url " + base_url_;
            std::cout << "执行命令: " << cmd << std::endl;

            pid_t child_pid = fork();
            if (child_pid == 0) {
                // 子进程：直接执行违规测试程序，避免shell中间层
                std::vector<std::string> args;
                args.push_back("./test/protected_path_violator");
                args.push_back(test_path);
                args.push_back("--api-url");
                args.push_back(base_url_);

                // 转换为char*数组
                std::vector<char*> argv_vec;
                for (auto& arg : args) {
                    argv_vec.push_back(const_cast<char*>(arg.c_str()));
                }
                argv_vec.push_back(nullptr);

                execv("./test/protected_path_violator", argv_vec.data());
                exit(1); // 如果exec失败
            } else if (child_pid < 0) {
                throw std::runtime_error("无法fork子进程启动protected_path_violator");
            }
            std::cout << "✓ 违规测试程序已启动，PID: " << child_pid << std::endl;

            // 步骤4：根据监控模式选择不同的等待策略
            std::cout << "\n步骤4: 等待测试程序完成..." << std::endl;
            bool test_completed = false;
            
            if (is_kill_mode) {
                // KILL模式：等待进程被终止
                std::cout << "KILL模式 - 等待进程被监控系统终止..." << std::endl;
                test_completed = waitForProcessTermination(child_pid, 30);
            } else {
                // LOG模式：等待完成标志文件
                std::cout << "LOG模式 - 等待完成标志文件..." << std::endl;
                test_completed = waitForTestCompletion("/tmp/psfsmon_protected_path_test_done.flag", 30);
            }

            if (!test_completed) {
                std::cout << "⚠️ 等待测试完成超时，但继续进行测试..." << std::endl;
            }

            // 步骤5：查询违规数据
            std::cout << "\n步骤5: 查询违规数据..." << std::endl;
            queryAndDisplayProtectedPathData(test_path);

            // 步骤6：清理受保护路径注册
            std::cout << "\n步骤6: 清理受保护路径注册..." << std::endl;
            cleanupProtectedPath(test_path);

            // 步骤7：清理测试进程
            std::cout << "\n步骤7: 清理测试进程..." << std::endl;
            if (is_kill_mode) {
                // 在KILL模式下，进程可能已经被终止
                if (kill(child_pid, 0) == 0) {
                    std::cout << "进程仍在运行，手动终止..." << std::endl;
                    ProcessManager::gracefulStopTestProcess(child_pid, "protected_path_violator");
                } else {
                    std::cout << "✓ 进程已被监控系统终止" << std::endl;
                }
            } else {
                // 在LOG模式下，优雅终止进程
                if (kill(child_pid, 0) == 0) {
                    ProcessManager::gracefulStopTestProcess(child_pid, "protected_path_violator");
                } else {
                    std::cout << "✓ 进程已经退出" << std::endl;
                }
            }

            // 备用清理
            int cleanup_result = system("pkill -f protected_path_violator");
            if (cleanup_result != 0) {
                // 进程可能已经结束，忽略错误
            }

            std::cout << "\n✓ 统一受保护路径测试完成" << std::endl;
            return true;

        } catch (const std::exception& e) {
            std::cout << "\n✗ 统一受保护路径测试失败: " << e.what() << std::endl;
            int cleanup_result = system("pkill -f protected_path_violator");
            if (cleanup_result != 0) {
                // 进程可能已经结束，忽略错误
            }
            cleanupProtectedPath(test_path);
            return false;
        }
    }

    // 统一的测试完成等待方法
    bool waitForTestCompletion(const std::string& flag_file, int max_wait_seconds) {
        std::cout << "📡 监听完成标志文件: " << flag_file << std::endl;

        // 删除可能存在的旧标志文件
        unlink(flag_file.c_str());

        bool operations_completed = false;
        int wait_attempts = 0;

        while (!operations_completed && wait_attempts < max_wait_seconds) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            wait_attempts++;

            // 每5秒输出一次等待状态
            if (wait_attempts % 5 == 0) {
                std::cout << "等待子测试程序完成操作... (" << wait_attempts << "/" << max_wait_seconds << ")" << std::endl;
            }

            // 检查完成标志文件是否存在
            std::ifstream flag_stream(flag_file);
            if (flag_stream.is_open()) {
                std::string flag_content;
                std::getline(flag_stream, flag_content);
                flag_stream.close();

                if (flag_content.find("DONE:") == 0) {
                    operations_completed = true;
                    std::cout << "✅ 收到子测试程序完成信号: " << flag_content << std::endl;
                    std::cout << "🎯 操作已完成，立即进行数据查询..." << std::endl;
                    break;
                }
            }
        }

        // 删除标志文件
        unlink(flag_file.c_str());

        return operations_completed;
    }

    // 检查受保护路径的KILL模式
    bool checkProtectedPathKillMode(const std::string& test_path) {
        std::cout << "检查受保护路径 " << test_path << " 的监控模式..." << std::endl;
        
        // 先尝试查询是否已经存在受保护路径
        auto response = makeHttpRequest("GET", "/api/protected-paths", "");
        if (response.response_code == 200 && response.is_json_valid) {
            auto paths = response.json_data.get("protected_paths", Json::Value());
            if (paths.isArray()) {
                for (const auto& path : paths) {
                    std::string path_str = path.get("path", "").asString();
                    if (path_str == test_path) {
                        bool terminate_violator = path.get("terminate_violator", false).asBool();
                        std::cout << "✓ 受保护路径已存在，terminate_violator: " << terminate_violator << std::endl;
                        return terminate_violator;
                    }
                }
            }
        }

        // 如果不存在，添加受保护路径
        std::cout << "受保护路径不存在，添加新的受保护路径..." << std::endl;
        std::string data = R"({"path": ")" + test_path + R"("})";
        auto add_response = makeHttpRequest("POST", "/api/protected-paths", data);
        if (add_response.response_code == 200) {
            std::cout << "✓ 成功添加受保护路径" << std::endl;
        } else {
            std::cout << "⚠️ 添加受保护路径失败，响应: " << add_response.body << std::endl;
            std::cout << "假设为LOG模式继续测试..." << std::endl;
            return false;
        }

        // 等待配置生效
        std::this_thread::sleep_for(std::chrono::seconds(2));

        // 再次查询受保护路径列表获取配置
        response = makeHttpRequest("GET", "/api/protected-paths", "");
        if (response.response_code == 200 && response.is_json_valid) {
            auto paths = response.json_data.get("protected_paths", Json::Value());
            if (paths.isArray()) {
                for (const auto& path : paths) {
                    std::string path_str = path.get("path", "").asString();
                    if (path_str == test_path) {
                        bool terminate_violator = path.get("terminate_violator", false).asBool();
                        std::cout << "✓ 受保护路径 " << test_path << " 的terminate_violator: " << terminate_violator << std::endl;
                        return terminate_violator;
                    }
                }
            }
        }

        std::cout << "⚠️ 无法确定监控模式，假设为LOG模式" << std::endl;
        return false;
    }

    // 等待进程被终止
    bool waitForProcessTermination(pid_t pid, int max_wait_seconds) {
        std::cout << "等待进程 " << pid << " 被监控系统终止..." << std::endl;

        int wait_attempts = 0;
        while (wait_attempts < max_wait_seconds) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            wait_attempts++;

            // 每5秒输出一次等待状态
            if (wait_attempts % 5 == 0) {
                std::cout << "等待进程终止... (" << wait_attempts << "/" << max_wait_seconds << ")" << std::endl;
            }

            // 方法1：使用waitpid检查子进程状态（非阻塞）
            int status;
            pid_t result = waitpid(pid, &status, WNOHANG);
            if (result == pid) {
                // 子进程已终止
                if (WIFEXITED(status)) {
                    std::cout << "✅ 进程正常退出 (经过 " << wait_attempts << " 秒，退出码: " << WEXITSTATUS(status) << ")" << std::endl;
                    return true;
                } else if (WIFSIGNALED(status)) {
                    std::cout << "✅ 进程被信号终止 (经过 " << wait_attempts << " 秒，信号: " << WTERMSIG(status) << ")" << std::endl;
                    return true;
                }
            } else if (result == 0) {
                // 子进程仍在运行，检查进程状态
                if (!ProcessManager::isProcessRunning(pid)) {
                    std::cout << "✅ 进程已不存在 (经过 " << wait_attempts << " 秒)" << std::endl;
                    return true;
                }
            } else if (result == -1) {
                if (errno == ECHILD) {
                    // 子进程不存在或已被其他进程回收
                    std::cout << "✅ 进程已被回收 (经过 " << wait_attempts << " 秒)" << std::endl;
                    return true;
                } else {
                    std::cout << "⚠️ waitpid出错: " << strerror(errno) << std::endl;
                }
            }
        }

        std::cout << "⚠️ 等待进程终止超时，进程可能仍在运行或未被正确终止" << std::endl;
        return false;
    }



    // 查找测试进程PID
    int findTestProcessPid(const std::string& process_name) {
        std::cout << "查找测试进程: " << process_name << std::endl;

        // 方法1：从API进程列表中查找
        auto processes_response = makeHttpRequest("GET", "/api/processes", "");
        if (processes_response.response_code == 200 && processes_response.is_json_valid) {
            auto& processes = processes_response.json_data["processes"];
            for (const auto& process : processes) {
                std::string name = process.get("name", "").asString();
                if (name.find(process_name) != std::string::npos) {
                    int pid = process.get("pid", 0).asInt();
                    std::cout << "从API进程列表找到: " << name << " (PID: " << pid << ")" << std::endl;
                    return pid;
                }
            }
        }

        // 方法2：使用pgrep命令查找
        std::string find_pid_cmd = "pgrep -f " + process_name;
        FILE* pipe = popen(find_pid_cmd.c_str(), "r");
        if (pipe) {
            char buffer[128];
            if (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
                int pid = std::stoi(std::string(buffer));
                pclose(pipe);
                std::cout << "从系统进程列表找到: " << process_name << " (PID: " << pid << ")" << std::endl;
                return pid;
            }
            pclose(pipe);
        }

        std::cout << "⚠️ 未找到测试进程: " << process_name << std::endl;
        return 0;
    }

    // 查询并显示熵值数据
    void queryAndDisplayEntropyData(int test_pid) {
        std::cout << "查询熵值数据 (PID: " << test_pid << ")..." << std::endl;

        // 查询特定PID的熵值信息
        auto entropy_response = makeHttpRequest("GET", "/api/focus-processes/entropy?pid=" + std::to_string(test_pid), "");
        if (entropy_response.response_code == 200 && entropy_response.is_json_valid) {
            std::cout << "✓ 熵值查询成功" << std::endl;
            std::cout << "熵值数据: " << formatJsonOutput(entropy_response.body) << std::endl;

            // 解析并显示关键数据
            auto total_original = entropy_response.json_data.get("total_original_entropy", 0.0).asDouble();
            auto total_final = entropy_response.json_data.get("total_final_entropy", 0.0).asDouble();
            auto total_change = entropy_response.json_data.get("total_entropy_change", 0.0).asDouble();
            auto file_count = entropy_response.json_data.get("file_count", 0).asInt();

            std::cout << "📊 熵值统计摘要 (格式化显示):" << std::endl;
            std::cout << "  文件数量: " << file_count << std::endl;
            
            // 格式化显示熵值
            auto format_entropy = [](double value, const std::string& label) -> std::string {
                std::ostringstream oss;
                oss << "  " << label << ": ";
                
                if (value == 0) {
                    oss << "0.0";
                } else if (value < 1000) {
                    oss << std::fixed << std::setprecision(2) << value;
                } else if (value < 1000000) {
                    oss << std::fixed << std::setprecision(2) << (value / 1000.0) << "K";
                } else if (value < 1000000000) {
                    oss << std::fixed << std::setprecision(2) << (value / 1000000.0) << "M";
                } else {
                    oss << std::fixed << std::setprecision(2) << (value / 1000000000.0) << "G";
                }
                
                // 注意：内部已经完成标准化（每MB熵值），所以这里直接显示单位
                oss << " (每MB熵值: " << std::fixed << std::setprecision(3) << value << ")";
                
                return oss.str();
            };
            
            std::cout << format_entropy(total_original, "总原始熵值") << std::endl;
            std::cout << format_entropy(total_final, "总最终熵值") << std::endl;
            std::cout << format_entropy(total_change, "总熵值变化") << std::endl;
        } else {
            std::cout << "⚠️ 熵值查询失败: HTTP " << entropy_response.response_code << std::endl;
            std::cout << "响应内容: " << entropy_response.body << std::endl;
        }

        // 查询详细统计信息
        auto stats_response = makeHttpRequest("GET", "/api/focus-processes/entropy-stats?pid=" + std::to_string(test_pid), "");
        if (stats_response.response_code == 200 && stats_response.is_json_valid) {
            std::cout << "✓ 详细统计查询成功" << std::endl;
            std::cout << "详细统计: " << formatJsonOutput(stats_response.body) << std::endl;
        } else {
            std::cout << "⚠️ 详细统计查询失败: HTTP " << stats_response.response_code << std::endl;
        }
    }

    // 查询并显示所有重点进程熵值数据
    void queryAndDisplayAllEntropyData() {
        std::cout << "查询所有重点进程熵值数据..." << std::endl;

        auto entropy_all_response = makeHttpRequest("GET", "/api/focus-processes/entropy", "");
        if (entropy_all_response.response_code == 200 && entropy_all_response.is_json_valid) {
            std::cout << "✓ 所有重点进程熵值查询成功" << std::endl;
            std::cout << "所有熵值数据: " << formatJsonOutput(entropy_all_response.body) << std::endl;

            auto process_count = entropy_all_response.json_data.get("focus_process_count", 0).asInt();
            std::cout << "📊 重点进程数量: " << process_count << std::endl;
        } else {
            std::cout << "⚠️ 所有重点进程熵值查询失败: HTTP " << entropy_all_response.response_code << std::endl;
        }
    }

    // 查询并显示受保护路径数据
    void queryAndDisplayProtectedPathData(const std::string& test_path) {
        std::cout << "查询受保护路径数据 (路径: " << test_path << ")..." << std::endl;

        // 查询文件事件
        auto events_response = makeHttpRequest("GET", "/api/file/events", "");
        if (events_response.response_code == 200 && events_response.is_json_valid) {
            auto event_count = events_response.json_data.get("count", 0).asInt();
            std::cout << "✓ 文件事件查询成功，事件数量: " << event_count << std::endl;
            std::cout << "文件事件数据: " << formatJsonOutput(events_response.body) << std::endl;
        } else {
            std::cout << "⚠️ 文件事件查询失败: HTTP " << events_response.response_code << std::endl;
        }

        // 查询受保护路径列表
        auto protected_paths_response = makeHttpRequest("GET", "/api/protected-paths", "");
        if (protected_paths_response.response_code == 200 && protected_paths_response.is_json_valid) {
            std::cout << "✓ 受保护路径列表查询成功" << std::endl;
            std::cout << "受保护路径: " << formatJsonOutput(protected_paths_response.body) << std::endl;
        } else {
            std::cout << "⚠️ 受保护路径列表查询失败: HTTP " << protected_paths_response.response_code << std::endl;
        }
    }

    // 清理重点进程注册
    void cleanupFocusProcess(int test_pid) {
        std::cout << "清理重点进程注册 (PID: " << test_pid << ")..." << std::endl;

        auto remove_response = makeHttpRequest("DELETE", "/api/focus-processes?type=pid&value=" + std::to_string(test_pid), "");
        if (remove_response.response_code == 200) {
            std::cout << "✓ 成功从重点进程列表中移除测试进程" << std::endl;
        } else {
            std::cout << "⚠️ 移除重点进程失败: " << remove_response.body << std::endl;
        }
    }

    // 清理受保护路径注册
    void cleanupProtectedPath(const std::string& test_path) {
        std::cout << "清理受保护路径注册: " << test_path << std::endl;

        auto remove_response = makeHttpRequest("DELETE", "/api/protected-paths?path=" + test_path, "");
        if (remove_response.response_code == 200) {
            std::cout << "✓ 成功清理受保护路径配置" << std::endl;
        } else {
            std::cout << "⚠️ 清理受保护路径配置失败: " << remove_response.body << std::endl;
        }
    }

    // 创建内部测试文件
    void createInternalTestFiles(const std::string& test_dir) {
        std::cout << "\n--- 创建内部测试文件（低熵值起点，系统自动标准化） ---" << std::endl;
        
        // 创建不同大小的低熵值测试文件
        std::vector<std::pair<std::string, std::string>> test_files = {
            {"small_test.txt", std::string(1000, 'A')}, // 重复字符A，低熵值
            {"medium_test.txt", std::string(5000, 'B')}, // 重复字符B，低熵值
            {"binary_test.bin", std::string(10000, 'C')}, // 重复字符C，低熵值
            {"pattern_test.dat", ""}  // 将被重复模式填充
        };
        
        for (const auto& file_pair : test_files) {
            std::string filepath = test_dir + "/" + file_pair.first;
            std::ofstream file(filepath, std::ios::binary);
            
            if (file_pair.first == "pattern_test.dat") {
                // 创建重复模式文件，非常低熵值
                std::string pattern = "ABCDEFGHIJ";
                for (int i = 0; i < 1000; ++i) {
                    file << pattern;
                }
            } else {
                file << file_pair.second;
            }
            
            file.close();
            std::cout << "创建低熵值文件: " << filepath << " (系统将自动标准化为每MB熵值)" << std::endl;
        }
    }
    
    // 执行内部文件操作（渐进式熵值变化）
    void performInternalFileOperations(const std::string& test_dir) {
        std::cout << "\n--- 执行内部文件操作（渐进式熵值变化） ---" << std::endl;
        
        // 读取文件
        for (const char* filename : {"small_test.txt", "medium_test.txt", "binary_test.bin", "pattern_test.dat"}) {
            std::string filepath = test_dir + "/" + filename;
            std::ifstream file(filepath);
            if (file.is_open()) {
                std::string content((std::istreambuf_iterator<char>(file)),
                                  std::istreambuf_iterator<char>());
                file.close();
                std::cout << "读取文件: " << filepath << " (大小: " << content.size() << " 字节)" << std::endl;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        // 对每个文件进行多次修改，逐渐增加熵值
        std::vector<std::string> test_files = {
            test_dir + "/small_test.txt",
            test_dir + "/medium_test.txt",
            test_dir + "/binary_test.bin"
        };
        
        for (const auto& modify_file : test_files) {
            std::cout << "\n🔄 对文件进行多次修改: " << modify_file << std::endl;
            
            // 进行3次修改，每次增加更多随机性
            for (int round = 1; round <= 3; round++) {
                std::cout << "\n--- 第" << round << "次修改 ---" << std::endl;
                
                std::ofstream out_file(modify_file, std::ios::app);
                if (out_file.is_open()) {
                    // 根据修改次数增加随机性
                    int random_percentage = 20 + (round * 20); // 20%, 40%, 60%
                    std::string mixed_content;
                    mixed_content.reserve(1000);
                    
                    for (int i = 0; i < 1000; ++i) {
                        if ((rand() % 100) < random_percentage) {
                            // 随机字符
                            mixed_content += static_cast<char>(rand() % 256);
                        } else {
                            // 重复字符
                            mixed_content += 'X';
                        }
                    }
                    
                    out_file << mixed_content;
                    out_file.close();
                    std::cout << "修改文件: " << modify_file << " (随机性: " << random_percentage << "%)" << std::endl;
                    
                    // 等待一段时间让系统处理
                    std::this_thread::sleep_for(std::chrono::milliseconds(500));
                    
                    // 查询熵值变化
                    queryAndDisplayEntropyData(getpid());
                }
            }
        }
        
        // 复制文件
        std::string src_file = test_dir + "/medium_test.txt";
        std::string dst_file = test_dir + "/medium_test_copy.txt";
        std::ifstream src(src_file, std::ios::binary);
        std::ofstream dst(dst_file, std::ios::binary);
        dst << src.rdbuf();
        src.close();
        dst.close();
        std::cout << "复制文件: " << src_file << " -> " << dst_file << std::endl;
        
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }
    
    // 运行内部熵值API完整测试 (基于test_comprehensive_entropy_api.cpp的功能)
    void runInternalEntropyApiTest() {
        std::cout << "=== 启动内部熵值API完整测试 ===" << std::endl;
        std::cout << "本测试将执行详细的熵值API功能验证，包括文件操作前后的状态对比" << std::endl;
        
        bool test_passed = true;
        std::string error_detail;
        std::string test_dir = "/tmp/entropy_api_internal_test";
        
        try {
            // 创建测试目录
            if (system(("mkdir -p " + test_dir).c_str()) != 0) {
                throw std::runtime_error("创建测试目录失败");
            }
            std::cout << "✓ 测试目录创建成功: " << test_dir << std::endl;
            
            // 1. 检查API服务器健康状态
            std::cout << "\n=== 第一阶段：API健康检查 ===" << std::endl;
            auto health_response = makeHttpRequest("GET", "/api/health", "");
            if (health_response.response_code != 200) {
                throw std::runtime_error("API服务器不可用");
            }
            std::cout << "✓ API服务器健康检查通过" << std::endl;
            
            // 2. 获取初始统计状态
            std::cout << "\n=== 第二阶段：获取初始统计状态 ===" << std::endl;
            auto initial_file_stats = makeHttpRequest("GET", "/api/file/stats", "");
            auto initial_entropy_stats = makeHttpRequest("GET", "/api/focus-processes/entropy", "");
            auto initial_detailed_stats = makeHttpRequest("GET", "/api/focus-processes/entropy-stats", "");
            
            std::cout << "✓ 初始文件统计: " << formatJsonOutput(initial_file_stats.body) << std::endl;
            std::cout << "✓ 初始熵值统计: " << formatJsonOutput(initial_entropy_stats.body) << std::endl;
            
            // 3. 准备测试文件（必须在注册重点进程之前）
            std::cout << "\n=== 第三阶段：准备测试文件 ===" << std::endl;
            std::cout << "📝 创建具有初始熵值的测试文件，确保测试的准确性" << std::endl;
            createInternalTestFiles(test_dir);
            
            // 4. 设置当前进程为重点进程
            std::cout << "\n=== 第四阶段：设置重点进程 ===" << std::endl;
            pid_t current_pid = getpid();
            std::string add_focus_request = R"({"type": "pid", "value": ")" + std::to_string(current_pid) + R"("})";
            auto add_focus_response = makeHttpRequest("POST", "/api/focus-processes", add_focus_request);
            
            if (add_focus_response.response_code == 200) {
                std::cout << "✓ 成功将当前进程设置为重点进程: PID=" << current_pid << std::endl;
            } else {
                std::cout << "⚠️ 添加重点进程响应: " << add_focus_response.body << std::endl;
            }
            
            // 5. 验证重点进程设置
            auto focus_list_response = makeHttpRequest("GET", "/api/focus-processes", "");
            if (focus_list_response.response_code == 200 && focus_list_response.is_json_valid) {
                std::cout << "✓ 重点进程列表: " << formatJsonOutput(focus_list_response.body) << std::endl;
            }
            
            // 6. 执行文件操作以触发熵值计算
            std::cout << "\n=== 第五阶段：执行文件操作 ===" << std::endl;
            performInternalFileOperations(test_dir);
            
            // 7. 等待事件处理
            std::cout << "\n--- 等待事件处理（5秒）---" << std::endl;
            std::this_thread::sleep_for(std::chrono::seconds(5));
            
            // 8. 获取更新后的统计信息
            std::cout << "\n=== 第六阶段：验证统计信息更新 ===" << std::endl;
            auto updated_file_stats = makeHttpRequest("GET", "/api/file/stats", "");
            auto updated_entropy_stats = makeHttpRequest("GET", "/api/focus-processes/entropy", "");
            auto updated_detailed_stats = makeHttpRequest("GET", "/api/focus-processes/entropy-stats", "");
            
            std::cout << "✓ 更新后的文件统计: " << formatJsonOutput(updated_file_stats.body) << std::endl;
            std::cout << "✓ 更新后的熵值统计: " << formatJsonOutput(updated_entropy_stats.body) << std::endl;
            
            // 9. 测试特定进程查询
            std::cout << "\n=== 第七阶段：特定进程查询测试 ===" << std::endl;
            std::string pid_query = "?pid=" + std::to_string(current_pid);
            auto specific_entropy_response = makeHttpRequest("GET", "/api/focus-processes/entropy" + pid_query, "");
            auto specific_stats_response = makeHttpRequest("GET", "/api/focus-processes/entropy-stats" + pid_query, "");
            auto specific_file_info_response = makeHttpRequest("GET", "/api/focus-process/file-info" + pid_query, "");
            
            if (specific_entropy_response.response_code == 200) {
                std::cout << "✓ 特定进程熵值查询: " << formatJsonOutput(specific_entropy_response.body) << std::endl;
            }
            if (specific_stats_response.response_code == 200) {
                std::cout << "✓ 特定进程详细统计: " << formatJsonOutput(specific_stats_response.body) << std::endl;
            }
            if (specific_file_info_response.response_code == 200) {
                std::cout << "✓ 特定进程文件信息: " << formatJsonOutput(specific_file_info_response.body) << std::endl;
            }
            
            // 10. 测试系统整体状态
            std::cout << "\n=== 第八阶段：系统整体状态测试 ===" << std::endl;
            auto system_stats_response = makeHttpRequest("GET", "/api/stats", "");
            auto monitor_status_response = makeHttpRequest("GET", "/api/monitor/status", "");
            auto realtime_response = makeHttpRequest("GET", "/api/realtime", "");
            
            if (system_stats_response.response_code == 200) {
                std::cout << "✓ 系统统计: " << formatJsonOutput(system_stats_response.body) << std::endl;
            }
            if (monitor_status_response.response_code == 200) {
                std::cout << "✓ 监控状态: " << formatJsonOutput(monitor_status_response.body) << std::endl;
            }
            if (realtime_response.response_code == 200) {
                std::cout << "✓ 实时数据: " << formatJsonOutput(realtime_response.body) << std::endl;
            }
            
            // 11. 清理重点进程设置
            std::cout << "\n=== 第九阶段：清理测试环境 ===" << std::endl;
            auto remove_focus_response = makeHttpRequest("DELETE", "/api/focus-processes?type=pid&value=" + std::to_string(current_pid), "");
            if (remove_focus_response.response_code == 200) {
                std::cout << "✓ 成功移除重点进程设置" << std::endl;
            } else {
                std::cout << "⚠️ 移除重点进程失败: " << remove_focus_response.body << std::endl;
            }
            
            // 清理测试文件
            if (system(("rm -rf " + test_dir).c_str()) == 0) {
                std::cout << "✓ 测试文件清理成功" << std::endl;
            }
            
            std::cout << "\n✓ 内部熵值API完整测试通过" << std::endl;
            std::cout << "\n功能验证总结:" << std::endl;
            std::cout << "1. ✓ 详细事件类型统计（read_events, write_events等）" << std::endl;
            std::cout << "2. ✓ 按事件类型的熵值计算统计（open_entropy_calculations等）" << std::endl;
            std::cout << "3. ✓ 重点进程读写熵值分别统计" << std::endl;
            std::cout << "4. ✓ 文件级别的熵值变化跟踪" << std::endl;
            std::cout << "5. ✓ 全局文件监控统计信息" << std::endl;
            std::cout << "6. ✓ 特定进程的详细查询功能" << std::endl;
            
        } catch (const std::exception& e) {
            test_passed = false;
            error_detail = e.what();
            std::cout << "\n✗ 内部熵值API测试失败: " << error_detail << std::endl;
            
            // 清理
            if (system(("rm -rf " + test_dir).c_str()) != 0) {
                // 清理失败，忽略错误
            }
        }
        
        if (test_passed) {
            stats_.passed_tests++;
        } else {
            stats_.failed_tests++;
            stats_.failed_test_names.push_back("entropy-api-internal (内部熵值API完整测试)");
            stats_.error_messages.push_back(error_detail);
        }
        stats_.total_tests++;
    }
    

    // 运行重点进程熵值API死锁检测测试 (高风险API专项测试)
    void runFocusProcessEntropyDeadlockTest() {
        std::cout << "=== 启动重点进程熵值API死锁检测测试 ===" << std::endl;
        std::cout << "⚠️ 警告：此测试专门针对可能导致死锁的高风险API" << std::endl;
        std::cout << "如果检测到死锁，测试将立即终止并报告问题" << std::endl;
        
        bool test_passed = true;
        std::string error_detail;
        
        try {
            // 测试高风险的重点进程熵值API
            std::vector<std::string> high_risk_apis = {
                "/api/focus-processes/entropy",
                "/api/focus-processes/entropy-stats", 
                "/api/focus-process/file-info?pid=1",
                "/api/focus-processes/entropy?pid=1",
                "/api/focus-processes/entropy-stats?pid=1"
            };
            
            std::cout << "\n第一阶段：快速死锁检测（2秒超时）" << std::endl;
            
            for (const auto& api : high_risk_apis) {
                std::cout << "\n测试高风险API: " << api << std::endl;
                
                // 创建特殊的死锁检测HTTP请求（2秒超时）
                auto response = makeDeadlockDetectionRequest("GET", api, "");
                
                if (response.response_code == 0) {
                    throw std::runtime_error("API " + api + " 发生超时，疑似死锁！");
                }
                
                if (response.total_time > 2.0) {
                    std::cout << "⚠️ 警告：API " << api << " 响应时间过长: " << response.total_time << "s" << std::endl;
                } else {
                    std::cout << "✓ API响应正常 (耗时: " << response.total_time << "s)" << std::endl;
                }
            }
            
            std::cout << "\n第二阶段：并发压力测试" << std::endl;
            std::cout << "同时发起多个熵值API请求，检测并发死锁..." << std::endl;
            
            // 并发测试多个熵值API调用
            auto start_time = std::chrono::steady_clock::now();
            
            for (int i = 0; i < 5; ++i) {
                auto response = makeDeadlockDetectionRequest("GET", "/api/focus-processes/entropy", "");
                if (response.response_code == 0) {
                    throw std::runtime_error("并发测试中检测到死锁！");
                }
            }
            
            auto end_time = std::chrono::steady_clock::now();
            double total_time = std::chrono::duration<double>(end_time - start_time).count();
            
            std::cout << "✓ 并发测试完成，总耗时: " << total_time << "s" << std::endl;
            
            if (total_time > 15.0) {
                std::cout << "⚠️ 警告：并发测试耗时过长，可能存在性能问题" << std::endl;
            }
            
            std::cout << "\n第三阶段：服务健康确认" << std::endl;
            auto health_response = makeDeadlockDetectionRequest("GET", "/api/health", "");
            
            if (health_response.response_code != 200) {
                throw std::runtime_error("服务健康检查失败，服务可能已死机");
            }
            
            std::cout << "✓ 服务健康检查通过" << std::endl;
            std::cout << "\n✅ 重点进程熵值API死锁检测测试通过" << std::endl;
            std::cout << "所有高风险API均未检测到死锁现象" << std::endl;
            
        } catch (const std::exception& e) {
            test_passed = false;
            error_detail = e.what();
            std::cout << "\n❌ 死锁检测测试失败: " << error_detail << std::endl;
            std::cout << "建议检查服务日志: /var/log/psfsmon/" << std::endl;
        }
        
        if (test_passed) {
            stats_.passed_tests++;
        } else {
            stats_.failed_tests++;
            stats_.failed_test_names.push_back("focus-process-entropy (死锁检测)");
            stats_.error_messages.push_back(error_detail);
        }
        stats_.total_tests++;
    }
    
    // 死锁检测专用HTTP请求（短超时）
    HttpResponse makeDeadlockDetectionRequest(const std::string& method, const std::string& endpoint, const std::string& data) {
        HttpResponse response;
        CURL* curl = curl_easy_init();
        
        if (!curl) {
            throw std::runtime_error("CURL初始化失败");
        }
        
        std::string url = base_url_ + endpoint;
        
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response.body);
        curl_easy_setopt(curl, CURLOPT_HEADERFUNCTION, HeaderCallback);
        curl_easy_setopt(curl, CURLOPT_HEADERDATA, &response.headers);
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 2L);        // 死锁检测：2秒超时
        curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 1L);  // 连接超时1秒
        curl_easy_setopt(curl, CURLOPT_VERBOSE, 0L);
        curl_easy_setopt(curl, CURLOPT_NOPROGRESS, 1L);
        
        // 设置HTTP方法
        if (method == "POST") {
            curl_easy_setopt(curl, CURLOPT_POST, 1L);
            if (!data.empty()) {
                curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data.c_str());
            }
        } else if (method == "PUT") {
            curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "PUT");
            if (!data.empty()) {
                curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data.c_str());
            }
        } else if (method == "DELETE") {
            curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "DELETE");
            if (!data.empty()) {
                curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data.c_str());
            }
        }
        
        // 设置HTTP头
        struct curl_slist* headers = nullptr;
        if (!data.empty()) {
            headers = curl_slist_append(headers, "Content-Type: application/json");
            curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        }
        
        // 执行请求
        CURLcode res = curl_easy_perform(curl);
        
        if (res != CURLE_OK) {
            curl_easy_cleanup(curl);
            if (headers) curl_slist_free_all(headers);
            // 超时错误特殊处理
            if (res == CURLE_OPERATION_TIMEDOUT) {
                response.response_code = 0; // 标记为超时
                return response;
            }
            throw std::runtime_error("CURL请求失败: " + std::string(curl_easy_strerror(res)));
        }
        
        // 获取响应信息
        curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &response.response_code);
        curl_easy_getinfo(curl, CURLINFO_TOTAL_TIME, &response.total_time);
        
        curl_easy_cleanup(curl);
        if (headers) curl_slist_free_all(headers);
        
        // 尝试解析JSON
        Json::Reader reader;
        if (reader.parse(response.body, response.json_data)) {
            response.is_json_valid = true;
        }
        
        return response;
    }
    
    // 执行HTTP请求并解析JSON (死锁检测版)
    HttpResponse makeHttpRequest(const std::string& method, const std::string& endpoint, const std::string& data) {
        HttpResponse response;
        CURL* curl = curl_easy_init();
        
        if (!curl) {
            throw std::runtime_error("CURL初始化失败");
        }
        
        std::string url = base_url_ + endpoint;
        
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response.body);
        curl_easy_setopt(curl, CURLOPT_HEADERFUNCTION, HeaderCallback);
        curl_easy_setopt(curl, CURLOPT_HEADERDATA, &response.headers);
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);        // 死锁检测：10秒超时
        curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L);  // 连接超时5秒
        curl_easy_setopt(curl, CURLOPT_VERBOSE, 0L);
        curl_easy_setopt(curl, CURLOPT_NOPROGRESS, 1L);
        
        // 设置HTTP方法
        if (method == "POST") {
            curl_easy_setopt(curl, CURLOPT_POST, 1L);
            if (!data.empty()) {
                curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data.c_str());
            }
        } else if (method == "PUT") {
            curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "PUT");
            if (!data.empty()) {
                curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data.c_str());
            }
        } else if (method == "DELETE") {
            curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "DELETE");
            if (!data.empty()) {
                curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data.c_str());
            }
        }
        
        // 设置HTTP头
        struct curl_slist* headers = nullptr;
        if (!data.empty()) {
            headers = curl_slist_append(headers, "Content-Type: application/json");
            curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        }
        
        // 执行请求
        CURLcode res = curl_easy_perform(curl);
        
        if (res != CURLE_OK) {
            curl_easy_cleanup(curl);
            if (headers) curl_slist_free_all(headers);
            throw std::runtime_error("CURL请求失败: " + std::string(curl_easy_strerror(res)));
        }
        
        // 获取响应信息
        curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &response.response_code);
        curl_easy_getinfo(curl, CURLINFO_TOTAL_TIME, &response.total_time);
        
        curl_easy_cleanup(curl);
        if (headers) curl_slist_free_all(headers);
        
        // 尝试解析JSON
        Json::Reader reader;
        if (reader.parse(response.body, response.json_data)) {
            response.is_json_valid = true;
        }
        
        return response;
    }
    
    // 预检查服务可用性
    bool preflightCheck() {
        std::cout << "--- 预检查服务可用性 ---" << std::endl;
        
        std::string endpoint = "/api/health";
        std::string full_url = base_url_ + endpoint;
        std::cout << "调用URL: GET " << full_url << std::endl;
        
        auto response = makeHttpRequest("GET", endpoint, "");
        
        std::cout << "响应状态: HTTP " << response.response_code << " (" << response.total_time << "s)" << std::endl;
        std::cout << "响应JSON:" << std::endl;
        std::cout << formatJsonOutput(response.body) << std::endl;
        
        if (response.response_code != 200) {
            std::cout << "✗ 服务不可用 (HTTP " << response.response_code << ")" << std::endl;
            return false;
        }
        
        // 解析JSON响应
        Json::Value root;
        Json::Reader reader;
        if (!reader.parse(response.body, root)) {
            std::cout << "✗ 健康检查响应格式错误" << std::endl;
            return false;
        }
        
        if (root.get("status", "").asString() != "ok") {
            std::cout << "✗ 服务状态异常" << std::endl;
            return false;
        }
        
        std::cout << "✓ 服务正常运行" << std::endl;
        std::cout << "  服务名: " << root.get("service", "unknown").asString() << std::endl;
        std::cout << "  时间戳: " << root.get("timestamp", 0).asUInt64() << std::endl;
        std::cout << std::endl;
        
        return true;
    }
    
    // 生成最终测试报告
    void generateDetailedReport() {
        std::cout << "=== 测试报告 ===" << std::endl;
        std::cout << "总测试数: " << stats_.total_tests << std::endl;
        std::cout << "通过: " << stats_.passed_tests << " (" 
                  << (stats_.total_tests > 0 ? (stats_.passed_tests * 100 / stats_.total_tests) : 0) 
                  << "%)" << std::endl;
        std::cout << "失败: " << stats_.failed_tests << std::endl;
        std::cout << "跳过: " << stats_.skipped_tests << std::endl;
        
        if (stats_.failed_tests > 0) {
            std::cout << std::endl << "失败的测试:" << std::endl;
            for (size_t i = 0; i < stats_.failed_test_names.size(); ++i) {
                std::cout << "  - " << stats_.failed_test_names[i] 
                          << " (" << stats_.error_messages[i] << ")" << std::endl;
            }
        }
        
        std::cout << std::endl;
        if (stats_.failed_tests == 0) {
            std::cout << "✓ 所有测试通过！" << std::endl;
        } else {
            std::cout << "✗ 有测试失败，请检查服务状态" << std::endl;
        }
    }
};

// 主函数
int main(int argc, char* argv[]) {
    std::string psfsmon_binary = "./bin/psfsmon";
    std::string api_base_url = "http://127.0.0.1:8080";
    bool start_service = false;  // 默认不启动服务，使用--no-start方式
    std::vector<std::string> test_categories;  // 要执行的测试类别
    std::string entropy_test_mode = "quick";  // 默认熵值测试模式
    
    // 解析命令行参数
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];
        if (arg == "--binary" && i + 1 < argc) {
            psfsmon_binary = argv[++i];
        } else if (arg == "--url" && i + 1 < argc) {
            api_base_url = argv[++i];
        } else if (arg == "--start") {
            start_service = true;
        } else if (arg == "--no-start") {
            start_service = false;
        } else if (arg == "--test" && i + 1 < argc) {
            // 支持逗号分隔的多个测试类别
            std::string test_arg = argv[++i];
            std::stringstream ss(test_arg);
            std::string category;
            while (std::getline(ss, category, ',')) {
                if (!category.empty()) {
                    test_categories.push_back(category);
                }
            }
        } else if (arg == "--entropy-mode" && i + 1 < argc) {
            // 熵值测试模式
            entropy_test_mode = argv[++i];
        } else if (arg == "--all") {
            // 执行所有测试
            test_categories = {"health", "process", "network", "stats", "config", 
                             "process-mgmt", "path-mgmt", "file-monitor", "realtime"};
        } else if (arg == "--help") {
            std::cout << "用法: " << argv[0] << " [选项]" << std::endl;
            std::cout << "选项:" << std::endl;
            std::cout << "  --binary <path>   指定psfsmon二进制文件路径 (默认: ./bin/psfsmon)" << std::endl;
            std::cout << "  --url <url>       指定API基础URL (默认: http://127.0.0.1:8080)" << std::endl;
            std::cout << "  --start           自动启动服务" << std::endl;
            std::cout << "  --no-start        不自动启动服务，假设服务已运行 (默认)" << std::endl;
            std::cout << "  --test <类别>     指定要执行的测试类别 (用逗号分隔多个类别)" << std::endl;
            std::cout << "  --entropy-mode <模式> 指定熵值测试模式 (默认: quick)" << std::endl;
            std::cout << "  --all             执行所有测试类别" << std::endl;
            std::cout << "  --help            显示此帮助信息" << std::endl;
            std::cout << std::endl;
            std::cout << "可用的测试类别:" << std::endl;
            std::cout << "  health            健康检查和状态API测试" << std::endl;
            std::cout << "  process           进程相关API测试" << std::endl;
            std::cout << "  network           网络相关API测试" << std::endl;
            std::cout << "  stats             系统统计API测试" << std::endl;
            std::cout << "  config            配置管理API测试" << std::endl;
            std::cout << "  process-mgmt      进程管理API测试" << std::endl;
            std::cout << "  path-mgmt         路径管理API测试" << std::endl;
            std::cout << "  file-monitor      文件监控API测试" << std::endl;
            std::cout << "  realtime          实时数据API测试" << std::endl;
            std::cout << "  focus-process     重点进程文件监控功能专项测试" << std::endl;
            std::cout << "  focus-process-entropy 重点进程熵值API死锁检测测试(高风险)" << std::endl;
            std::cout << "  entropy-test      重点进程熵值功能专项测试(使用专门测试程序)" << std::endl;
            std::cout << "  entropy-api-internal 内部熵值API完整测试(详细验证)" << std::endl;
            std::cout << "  complete-behavior 完整重点进程行为模拟器(推荐)" << std::endl;
            std::cout << "  protected-path    受保护路径违规测试(测试进程终止)" << std::endl;
            std::cout << std::endl;
            std::cout << "可用的熵值测试模式:" << std::endl;
            std::cout << "  quick             快速测试模式 (小文件，适用于快速验证)" << std::endl;
            std::cout << "  complete          完整测试模式 (大文件，完整功能验证)" << std::endl;
            std::cout << "  stress            压力测试模式 (大量文件，性能测试)" << std::endl;
            std::cout << "  custom            自定义测试模式 (由子测试程序决定)" << std::endl;
            std::cout << std::endl;
            std::cout << "示例:" << std::endl;
            std::cout << "  " << argv[0] << " --test health,network    # 只测试健康检查和网络API" << std::endl;
            std::cout << "  " << argv[0] << " --test focus-process     # 只测试重点进程功能" << std::endl;
            std::cout << "  " << argv[0] << " --test entropy-test      # 重点进程熵值功能测试(推荐)" << std::endl;
            std::cout << "  " << argv[0] << " --test entropy-test --entropy-mode complete # 完整熵值测试" << std::endl;
            std::cout << "  " << argv[0] << " --test entropy-test --entropy-mode stress   # 压力熵值测试" << std::endl;
            std::cout << "  " << argv[0] << " --test entropy-api-internal # 内部熵值API完整测试" << std::endl;
            std::cout << "  " << argv[0] << " --test complete-behavior --entropy-mode complete # 完整行为模拟测试" << std::endl;
            std::cout << "  " << argv[0] << " --test protected-path    # 受保护路径违规测试" << std::endl;
            std::cout << "  " << argv[0] << " --all                    # 执行所有测试" << std::endl;
            return 0;
        }
    }
    
    // 如果没有指定测试类别，显示帮助信息
    if (test_categories.empty()) {
        std::cout << "错误: 未指定要执行的测试类别" << std::endl;
        std::cout << "使用 --help 查看可用选项" << std::endl;
        std::cout << "使用 --test <类别> 指定要执行的测试" << std::endl;
        std::cout << "使用 --all 执行所有测试" << std::endl;
        std::cout << std::endl;
        std::cout << "常用测试命令:" << std::endl;
        std::cout << "  " << argv[0] << " --test focus-process-entropy # 死锁检测测试(最高优先级)" << std::endl;
        std::cout << "  " << argv[0] << " --test entropy-test      # 重点进程熵值功能测试(推荐)" << std::endl;
        std::cout << "  " << argv[0] << " --test entropy-test --entropy-mode complete # 完整熵值测试" << std::endl;
        std::cout << "  " << argv[0] << " --test complete-behavior --entropy-mode complete # 完整行为模拟测试" << std::endl;
        std::cout << "  " << argv[0] << " --test focus-process     # 重点进程功能测试" << std::endl;
        std::cout << "  " << argv[0] << " --test protected-path    # 受保护路径违规测试" << std::endl;
        std::cout << "  " << argv[0] << " --test health,network    # 基础功能测试" << std::endl;
        std::cout << "  " << argv[0] << " --all                    # 完整测试" << std::endl;
        return 1;
    }
    
    pid_t service_pid = -1;
    
    // 如果需要启动服务
    if (start_service) {
        std::cout << "正在启动psfsmon服务..." << std::endl;
        service_pid = ProcessManager::startPsfsmonService(psfsmon_binary);
        
        if (service_pid <= 0) {
            std::cerr << "无法启动psfsmon服务" << std::endl;
            return 1;
        }
        
        std::cout << "psfsmon服务已启动 (PID: " << service_pid << ")" << std::endl;
    } else {
        std::cout << "使用--no-start模式，假设服务已运行于: " << api_base_url << std::endl;
    }
    
    // 运行API测试
    bool test_result = false;
    try {
        ApiTestFramework test_framework(api_base_url, entropy_test_mode);
        test_result = test_framework.runCategoryTests(test_categories);
    } catch (const std::exception& e) {
        std::cerr << "测试框架异常: " << e.what() << std::endl;
    }
    
    // 清理：停止服务
    if (service_pid > 0) {
        std::cout << "正在停止psfsmon服务..." << std::endl;
        ProcessManager::stopProcess(service_pid);
        std::cout << "psfsmon服务已停止" << std::endl;
    }
    
    return test_result ? 0 : 1;
} 