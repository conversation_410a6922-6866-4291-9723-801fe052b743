#include <iostream>
#include <thread>
#include <chrono>
#include <cstdlib>
#include <unistd.h>
#include <sys/wait.h>
#include <signal.h>
#include <vector>
#include <string>

/**
 * 进程清理逻辑测试程序
 * 
 * 测试场景：
 * 1. 创建一个子进程并将其设置为重点进程
 * 2. 创建一个普通进程
 * 3. 验证重点进程不会被自动清理
 * 4. 验证普通进程会被正常清理
 * 5. 手动移除重点进程后，验证死进程清理
 */

class ProcessCleanupTester {
private:
    std::string base_url_;
    bool verbose_;
    
public:
    ProcessCleanupTester(const std::string& base_url = "http://localhost:8080", bool verbose = true) 
        : base_url_(base_url), verbose_(verbose) {}
    
    void log(const std::string& message) {
        if (verbose_) {
            auto now = std::chrono::system_clock::now();
            auto time_val = std::chrono::system_clock::to_time_t(now);
            std::cout << "[" << std::ctime(&time_val) << "] " << message << std::endl;
        }
    }
    
    bool makeHttpRequest(const std::string& method, const std::string& endpoint, const std::string& data = "") {
        std::string curl_cmd = "curl -s -X " + method + " '" + base_url_ + endpoint + "'";
        if (!data.empty()) {
            curl_cmd += " -d '" + data + "'";
        }
        
        if (verbose_) {
            std::cout << "执行命令: " << curl_cmd << std::endl;
        }
        
        int result = system(curl_cmd.c_str());
        return result == 0;
    }
    
    pid_t createTestProcess(const std::string& process_name, int duration_seconds = 10) {
        pid_t pid = fork();
        if (pid == 0) {
            // 子进程
            for (int i = 0; i < duration_seconds; ++i) {
                std::this_thread::sleep_for(std::chrono::seconds(1));
                if (i % 2 == 0) {
                    log("测试进程 " + process_name + " 运行中... (" + std::to_string(i+1) + "/" + std::to_string(duration_seconds) + ")");
                }
            }
            exit(0);
        } else if (pid > 0) {
            log("创建测试进程: " + process_name + " (PID: " + std::to_string(pid) + ")");
            return pid;
        } else {
            log("创建测试进程失败: " + process_name);
            return -1;
        }
    }
    
    bool setFocusProcess(pid_t pid, bool focus = true) {
        std::string endpoint = "/api/focus-processes";
        if (focus) {
            endpoint += "?type=pid&value=" + std::to_string(pid);
            return makeHttpRequest("POST", endpoint);
        } else {
            endpoint += "?type=pid&value=" + std::to_string(pid);
            return makeHttpRequest("DELETE", endpoint);
        }
    }
    
    bool isProcessRunning(pid_t pid) {
        return kill(pid, 0) == 0;
    }
    
    void waitForProcessExit(pid_t pid, int timeout_seconds = 15) {
        for (int i = 0; i < timeout_seconds; ++i) {
            if (!isProcessRunning(pid)) {
                log("进程 " + std::to_string(pid) + " 已退出");
                return;
            }
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        log("等待进程 " + std::to_string(pid) + " 退出超时");
    }
    
    void queryProcessList() {
        log("查询进程列表...");
        makeHttpRequest("GET", "/api/processes");
    }
    
    void queryFocusProcessList() {
        log("查询重点进程列表...");
        makeHttpRequest("GET", "/api/focus-processes");
    }
    
    bool runTest() {
        log("=== 开始进程清理逻辑测试 ===");
        
        // 第1步：创建两个测试进程
        log("\n步骤1: 创建测试进程...");
        pid_t focus_pid = createTestProcess("focus_test_process", 30);
        pid_t normal_pid = createTestProcess("normal_test_process", 15);
        
        if (focus_pid <= 0 || normal_pid <= 0) {
            log("创建测试进程失败");
            return false;
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        // 第2步：设置重点进程
        log("\n步骤2: 设置重点进程...");
        if (!setFocusProcess(focus_pid, true)) {
            log("设置重点进程失败");
            return false;
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        // 第3步：查询进程状态
        log("\n步骤3: 查询进程状态...");
        queryProcessList();
        queryFocusProcessList();
        
        // 第4步：等待普通进程自然退出
        log("\n步骤4: 等待普通进程退出...");
        waitForProcessExit(normal_pid, 20);
        
        // 第5步：等待一段时间，观察进程清理
        log("\n步骤5: 等待进程清理检查...");
        std::this_thread::sleep_for(std::chrono::seconds(5));
        
        // 第6步：手动终止重点进程
        log("\n步骤6: 手动终止重点进程...");
        if (kill(focus_pid, SIGTERM) != 0) {
            log("终止重点进程失败");
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        // 第7步：从重点进程列表中移除（应该触发死进程清理）
        log("\n步骤7: 从重点进程列表中移除...");
        if (!setFocusProcess(focus_pid, false)) {
            log("移除重点进程失败");
            return false;
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        // 第8步：最终状态检查
        log("\n步骤8: 最终状态检查...");
        queryProcessList();
        queryFocusProcessList();
        
        // 清理
        if (isProcessRunning(focus_pid)) {
            kill(focus_pid, SIGKILL);
        }
        if (isProcessRunning(normal_pid)) {
            kill(normal_pid, SIGKILL);
        }
        
        log("\n=== 进程清理逻辑测试完成 ===");
        return true;
    }
};

void printUsage(const char* program_name) {
    std::cout << "用法: " << program_name << " [选项]" << std::endl;
    std::cout << "选项:" << std::endl;
    std::cout << "  --base-url <url>    指定API基础URL (默认: http://localhost:8080)" << std::endl;
    std::cout << "  --quiet            安静模式，减少输出" << std::endl;
    std::cout << "  --help             显示帮助信息" << std::endl;
}

int main(int argc, char* argv[]) {
    std::string base_url = "http://localhost:8080";
    bool verbose = true;
    
    // 解析命令行参数
    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];
        
        if (arg == "--base-url" && i + 1 < argc) {
            base_url = argv[++i];
        } else if (arg == "--quiet") {
            verbose = false;
        } else if (arg == "--help") {
            printUsage(argv[0]);
            return 0;
        } else {
            std::cerr << "未知选项: " << arg << std::endl;
            printUsage(argv[0]);
            return 1;
        }
    }
    
    ProcessCleanupTester tester(base_url, verbose);
    
    if (tester.runTest()) {
        std::cout << "测试完成" << std::endl;
        return 0;
    } else {
        std::cout << "测试失败" << std::endl;
        return 1;
    }
} 