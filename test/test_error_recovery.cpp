/*
 * PSFSMON-L 错误恢复和边界条件测试程序
 * 
 * 本程序专门测试系统在异常情况下的行为，包括：
 * 1. 权限不足的情况
 * 2. 磁盘空间不足的模拟
 * 3. 网络连接异常
 * 4. 大量并发请求
 * 5. 异常数据输入
 */

#include <iostream>
#include <string>
#include <vector>
#include <thread>
#include <chrono>
#include <fstream>
#include <atomic>
#include <sys/stat.h>
#include <sys/statvfs.h>
#include <unistd.h>
#include <curl/curl.h>
#include <cstring>
#include <errno.h>

class ErrorRecoveryTester {
private:
    std::string api_base_url_;
    std::string test_dir_;
    
    // HTTP回调函数
    static size_t WriteCallback(void* contents, size_t size, size_t nmemb, std::string* userp) {
        userp->append((char*)contents, size * nmemb);
        return size * nmemb;
    }
    
    // HTTP请求方法
    bool makeHttpRequest(const std::string& method, const std::string& endpoint, 
                        const std::string& data = "", int timeout = 5) {
        CURL* curl = curl_easy_init();
        if (!curl) return false;
        
        std::string url = api_base_url_ + endpoint;
        std::string response;
        
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, timeout);
        
        if (method == "POST") {
            curl_easy_setopt(curl, CURLOPT_POST, 1L);
            if (!data.empty()) {
                curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data.c_str());
            }
        } else if (method == "DELETE") {
            curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "DELETE");
        }
        
        struct curl_slist* headers = nullptr;
        if (!data.empty()) {
            headers = curl_slist_append(headers, "Content-Type: application/json");
            curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        }
        
        CURLcode res = curl_easy_perform(curl);
        long response_code = 0;
        curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &response_code);
        
        curl_easy_cleanup(curl);
        if (headers) curl_slist_free_all(headers);
        
        return (res == CURLE_OK && response_code >= 200 && response_code < 300);
    }

public:
    ErrorRecoveryTester(const std::string& api_url = "http://127.0.0.1:8080",
                       const std::string& test_dir = "/tmp/error_recovery_test")
        : api_base_url_(api_url), test_dir_(test_dir) {
        
        curl_global_init(CURL_GLOBAL_DEFAULT);
        
        // 创建测试目录
        if (mkdir(test_dir_.c_str(), 0755) != 0 && errno != EEXIST) {
            std::cerr << "警告: 无法创建测试目录 " << test_dir_ << std::endl;
        }
    }
    
    ~ErrorRecoveryTester() {
        curl_global_cleanup();
    }
    
    void log(const std::string& message) {
        auto now = std::chrono::system_clock::now();
        auto time_val = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            now.time_since_epoch()) % 1000;

        char time_str[100];
        std::strftime(time_str, sizeof(time_str), "%Y-%m-%d %H:%M:%S", std::localtime(&time_val));

        std::cout << "[" << time_str << "." << ms.count() << "ms] " << message << std::endl;
    }
    
    // 测试权限不足的情况
    bool test_permission_denied() {
        log("测试权限不足情况...");
        
        // 尝试在只读目录创建文件
        std::string readonly_dir = test_dir_ + "/readonly";
        if (mkdir(readonly_dir.c_str(), 0755) == 0) {
            chmod(readonly_dir.c_str(), 0444); // 设置为只读
            
            std::string test_file = readonly_dir + "/test.txt";
            std::ofstream file(test_file);
            
            bool permission_test_passed = !file.is_open();
            
            // 恢复权限并清理
            chmod(readonly_dir.c_str(), 0755);
            rmdir(readonly_dir.c_str());
            
            if (permission_test_passed) {
                log("✓ 权限不足测试通过");
                return true;
            }
        }
        
        log("✗ 权限不足测试失败");
        return false;
    }
    
    // 测试磁盘空间检查
    bool test_disk_space_check() {
        log("测试磁盘空间检查...");
        
        struct statvfs stat;
        if (statvfs(test_dir_.c_str(), &stat) == 0) {
            unsigned long available_space = stat.f_bavail * stat.f_frsize;
            log("可用磁盘空间: " + std::to_string(available_space / (1024*1024)) + " MB");
            
            if (available_space > 100 * 1024 * 1024) { // 100MB
                log("✓ 磁盘空间充足");
                return true;
            } else {
                log("⚠ 磁盘空间不足，可能影响测试");
                return false;
            }
        }
        
        log("✗ 无法检查磁盘空间");
        return false;
    }
    
    // 测试网络连接异常
    bool test_network_timeout() {
        log("测试网络连接超时...");
        
        // 使用很短的超时时间测试
        bool timeout_handled = !makeHttpRequest("GET", "/api/health", "", 1);
        
        if (timeout_handled) {
            log("✓ 网络超时处理正常");
            return true;
        } else {
            log("✗ 网络超时处理异常");
            return false;
        }
    }
    
    // 测试大量并发请求
    bool test_concurrent_requests() {
        log("测试大量并发请求...");
        
        const int thread_count = 50;
        std::vector<std::thread> threads;
        std::atomic<int> success_count(0);
        
        for (int i = 0; i < thread_count; i++) {
            threads.emplace_back([this, &success_count]() {
                if (makeHttpRequest("GET", "/api/health")) {
                    success_count++;
                }
            });
        }
        
        for (auto& t : threads) {
            t.join();
        }
        
        double success_rate = (double)success_count / thread_count;
        log("并发请求成功率: " + std::to_string(success_rate * 100) + "%");
        
        if (success_rate >= 0.8) { // 80%成功率
            log("✓ 并发请求测试通过");
            return true;
        } else {
            log("✗ 并发请求测试失败");
            return false;
        }
    }
    
    // 测试异常数据输入
    bool test_malformed_data() {
        log("测试异常数据输入...");
        
        std::vector<std::string> malformed_data = {
            "{invalid json}",
            "{\"type\": \"name\", \"value\": null}",
            "{\"type\": \"\", \"value\": \"test\"}",
            "{\"type\": \"name\", \"value\": \"" + std::string(10000, 'A') + "\"}",
            "null",
            "[]",
            "{\"type\": \"name\"}", // 缺少value
            "{\"value\": \"test\"}" // 缺少type
        };
        
        int handled_correctly = 0;
        
        for (const auto& data : malformed_data) {
            // 应该返回错误，而不是崩溃
            if (!makeHttpRequest("POST", "/api/focus-processes", data)) {
                handled_correctly++;
            }
        }
        
        double handle_rate = static_cast<double>(handled_correctly) / static_cast<double>(malformed_data.size());
        log("异常数据处理率: " + std::to_string(handle_rate * 100) + "%");
        
        if (handle_rate >= 0.8) {
            log("✓ 异常数据输入测试通过");
            return true;
        } else {
            log("✗ 异常数据输入测试失败");
            return false;
        }
    }
    
    // 运行所有错误恢复测试
    void run_all_tests() {
        log("=== 开始错误恢复和边界条件测试 ===");
        
        int total_tests = 0;
        int passed_tests = 0;
        
        if (test_permission_denied()) passed_tests++;
        total_tests++;
        
        if (test_disk_space_check()) passed_tests++;
        total_tests++;
        
        if (test_network_timeout()) passed_tests++;
        total_tests++;
        
        if (test_concurrent_requests()) passed_tests++;
        total_tests++;
        
        if (test_malformed_data()) passed_tests++;
        total_tests++;
        
        log("=== 错误恢复测试完成 ===");
        log("总测试数: " + std::to_string(total_tests));
        log("通过测试: " + std::to_string(passed_tests));
        log("成功率: " + std::to_string((double)passed_tests / total_tests * 100) + "%");
        
        if (passed_tests == total_tests) {
            log("🎉 所有错误恢复测试通过！");
        } else {
            log("⚠ 部分错误恢复测试失败，请检查系统健壮性");
        }
    }
};

int main(int argc, char* argv[]) {
    std::string api_url = "http://127.0.0.1:8080";
    std::string test_dir = "/tmp/error_recovery_test";
    
    // 解析命令行参数
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];
        if (arg == "--api-url" && i + 1 < argc) {
            api_url = argv[++i];
        } else if (arg == "--test-dir" && i + 1 < argc) {
            test_dir = argv[++i];
        } else if (arg == "--help") {
            std::cout << "用法: " << argv[0] << " [选项]" << std::endl;
            std::cout << "选项:" << std::endl;
            std::cout << "  --api-url <URL>      API服务器地址 (默认: http://127.0.0.1:8080)" << std::endl;
            std::cout << "  --test-dir <目录>    测试目录 (默认: /tmp/error_recovery_test)" << std::endl;
            std::cout << "  --help               显示此帮助信息" << std::endl;
            return 0;
        }
    }
    
    try {
        ErrorRecoveryTester tester(api_url, test_dir);
        tester.run_all_tests();
    } catch (const std::exception& e) {
        std::cerr << "测试异常: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
