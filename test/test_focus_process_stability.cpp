#include <iostream>
#include <thread>
#include <chrono>
#include <vector>
#include <string>
#include <cstdlib>
#include <unistd.h>
#include <sys/wait.h>
#include <signal.h>
#include <curl/curl.h>
#include <sstream>
#include <iomanip>

/**
 * 重点进程状态稳定性测试程序
 * 
 * 测试场景：
 * 1. 模拟高负载环境（大量文件创建）
 * 2. 验证重点进程状态不会意外丢失
 * 3. 验证scanProcesses不会误认为重点进程是新进程
 * 4. 验证状态同步机制工作正常
 */

class FocusProcessStabilityTester {
private:
    std::string base_url_;
    bool verbose_;
    
public:
    FocusProcessStabilityTester(const std::string& base_url = "http://localhost:8080", bool verbose = true) 
        : base_url_(base_url), verbose_(verbose) {}
    
    void log(const std::string& message) {
        if (verbose_) {
            auto now = std::chrono::system_clock::now();
            auto time_val = std::chrono::system_clock::to_time_t(now);
            std::cout << "[" << std::put_time(std::localtime(&time_val), "%Y-%m-%d %H:%M:%S") 
                      << "] " << message << std::endl;
        }
    }
    
    void logSuccess(const std::string& message) {
        log("✅ " + message);
    }
    
    void logError(const std::string& message) {
        log("❌ " + message);
    }
    
    void logInfo(const std::string& message) {
        log("ℹ️  " + message);
    }
    
    void logWarning(const std::string& message) {
        log("⚠️  " + message);
    }
    
    void printSeparator(const std::string& title = "") {
        std::cout << "\n" << std::string(60, '=') << std::endl;
        if (!title.empty()) {
            std::cout << "  " << title << std::endl;
            std::cout << std::string(60, '=') << std::endl;
        }
    }
    
    void printSubSeparator(const std::string& title = "") {
        std::cout << "\n" << std::string(40, '-') << std::endl;
        if (!title.empty()) {
            std::cout << "  " << title << std::endl;
            std::cout << std::string(40, '-') << std::endl;
        }
    }
    
    // 格式化显示重点进程列表
    void printFocusProcessList(const std::string& json_response) {
        std::cout << "\n📋 重点进程列表:" << std::endl;
        
        // 简单解析JSON中的进程信息
        size_t count_pos = json_response.find("\"count\":");
        if (count_pos != std::string::npos) {
            size_t count_start = json_response.find(":", count_pos) + 1;
            size_t count_end = json_response.find(",", count_start);
            if (count_end == std::string::npos) count_end = json_response.find("}", count_start);
            
            std::string count_str = json_response.substr(count_start, count_end - count_start);
            std::cout << "   总数: " << count_str << " 个重点进程" << std::endl;
        }
        
        // 查找所有PID
        size_t pos = 0;
        int process_num = 1;
        while ((pos = json_response.find("\"pid\":", pos)) != std::string::npos) {
            size_t pid_start = json_response.find(":", pos) + 1;
            size_t pid_end = json_response.find(",", pid_start);
            if (pid_end == std::string::npos) pid_end = json_response.find("}", pid_start);
            
            std::string pid_str = json_response.substr(pid_start, pid_end - pid_start);
            
            // 查找对应的进程名
            size_t name_search_start = pos;
            size_t name_pos = json_response.find("\"name\":", name_search_start);
            std::string name = "unknown";
            if (name_pos != std::string::npos && name_pos < json_response.find("\"pid\":", pos + 1)) {
                size_t name_start = json_response.find("\"", name_pos + 7) + 1;
                size_t name_end = json_response.find("\"", name_start);
                if (name_end != std::string::npos) {
                    name = json_response.substr(name_start, name_end - name_start);
                }
            }
            
            // 查找路径历史数量
            size_t history_pos = json_response.find("\"path_history_count\":", name_search_start);
            std::string history_count = "0";
            if (history_pos != std::string::npos && history_pos < json_response.find("\"pid\":", pos + 1)) {
                size_t history_start = json_response.find(":", history_pos) + 1;
                size_t history_end = json_response.find(",", history_start);
                if (history_end == std::string::npos) history_end = json_response.find("}", history_start);
                history_count = json_response.substr(history_start, history_end - history_start);
                
                // 清理字符串中的多余字符
                size_t clean_end = history_count.find_first_not_of("0123456789");
                if (clean_end != std::string::npos) {
                    history_count = history_count.substr(0, clean_end);
                }
            }
            
            std::cout << "   " << process_num++ << ". PID=" << pid_str 
                      << ", 进程名=" << name 
                      << ", 文件操作=" << history_count << " 次" << std::endl;
            
            pos = pid_end;
        }
        
        std::cout << std::string(40, '-') << std::endl;
    }
    
    // HTTP 请求回调函数
    static size_t WriteCallback(void* contents, size_t size, size_t nmemb, void* userp) {
        ((std::string*)userp)->append((char*)contents, size * nmemb);
        return size * nmemb;
    }
    
    // 发送 HTTP 请求
    bool makeHttpRequest(const std::string& method, const std::string& path, std::string* response = nullptr) {
        CURL* curl;
        CURLcode res;
        std::string readBuffer;
        
        curl = curl_easy_init();
        if (curl) {
            std::string url = base_url_ + path;
            curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
            curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
            curl_easy_setopt(curl, CURLOPT_WRITEDATA, &readBuffer);
            curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
            
            if (method == "POST") {
                curl_easy_setopt(curl, CURLOPT_POST, 1L);
            } else if (method == "DELETE") {
                curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "DELETE");
            }
            
            res = curl_easy_perform(curl);
            curl_easy_cleanup(curl);
            
            if (response) {
                *response = readBuffer;
            }
            
            return res == CURLE_OK;
        }
        return false;
    }
    
    // 发送带请求体的 HTTP 请求
    bool makeHttpRequestWithBody(const std::string& method, const std::string& path, const std::string& body, std::string* response = nullptr) {
        CURL* curl;
        CURLcode res;
        std::string readBuffer;
        
        curl = curl_easy_init();
        if (curl) {
            std::string url = base_url_ + path;
            curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
            curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
            curl_easy_setopt(curl, CURLOPT_WRITEDATA, &readBuffer);
            curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
            
            // 设置请求体
            curl_easy_setopt(curl, CURLOPT_POSTFIELDS, body.c_str());
            curl_easy_setopt(curl, CURLOPT_POSTFIELDSIZE, body.length());
            
            // 设置Content-Type
            struct curl_slist* headers = nullptr;
            headers = curl_slist_append(headers, "Content-Type: application/json");
            curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
            
            if (method == "POST") {
                curl_easy_setopt(curl, CURLOPT_POST, 1L);
            } else if (method == "PUT") {
                curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "PUT");
            }
            
            res = curl_easy_perform(curl);
            curl_slist_free_all(headers);
            curl_easy_cleanup(curl);
            
            if (response) {
                *response = readBuffer;
            }
            
            return res == CURLE_OK;
        }
        return false;
    }
    
    // 创建高负载测试进程
    pid_t createHighLoadProcess(const std::string& name, int duration_seconds) {
        pid_t pid = fork();
        
        if (pid == 0) {
            // 子进程
            setpgid(0, 0);  // 创建新的进程组
            
            // 打开调试日志文件
            std::string log_file = "/tmp/focus_test_debug_" + std::to_string(getpid()) + ".log";
            FILE* debug_log = fopen(log_file.c_str(), "w");
            if (debug_log) {
                fprintf(debug_log, "测试进程开始: PID=%d, 持续时间=%d秒\n", getpid(), duration_seconds);
                fflush(debug_log);
            }
            
            // 模拟高负载：快速创建和删除文件
            std::string test_dir = "/tmp/focus_stability_test";
            int mkdir_result = system(("mkdir -p " + test_dir).c_str());
            if (mkdir_result != 0) {
                fprintf(stderr, "警告: 创建测试目录失败: %s (返回值: %d)\n", test_dir.c_str(), mkdir_result);
                if (debug_log) {
                    fprintf(debug_log, "创建测试目录失败: %s (返回值: %d)\n", test_dir.c_str(), mkdir_result);
                    fflush(debug_log);
                }
            }
            
            if (debug_log) {
                fprintf(debug_log, "开始文件操作循环，总循环次数: %d\n", duration_seconds * 10);
                fflush(debug_log);
            }
            
            for (int i = 0; i < duration_seconds * 10; ++i) {
                std::string filename = test_dir + "/test_" + std::to_string(i) + ".tmp";
                
                // 创建文件
                FILE* file = fopen(filename.c_str(), "w");
                if (file) {
                    fprintf(file, "Test data %d\n", i);
                    fclose(file);
                } else {
                    if (debug_log) {
                        fprintf(debug_log, "创建文件失败: %s\n", filename.c_str());
                        fflush(debug_log);
                    }
                }
                
                // 每100次循环记录一次进度
                if (debug_log && i % 100 == 0) {
                    fprintf(debug_log, "进度: %d/%d (%.1f%%)\n", i, duration_seconds * 10, (float)i / (duration_seconds * 10) * 100);
                    fflush(debug_log);
                }
                
                // 短暂延迟
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                
                // 删除文件
                unlink(filename.c_str());
            }
            
            if (debug_log) {
                fprintf(debug_log, "文件操作循环完成\n");
                fflush(debug_log);
            }
            
            // 清理测试目录
            int rmdir_result = system(("rm -rf " + test_dir).c_str());
            if (rmdir_result != 0) {
                fprintf(stderr, "警告: 删除测试目录失败: %s (返回值: %d)\n", test_dir.c_str(), rmdir_result);
                if (debug_log) {
                    fprintf(debug_log, "删除测试目录失败: %s (返回值: %d)\n", test_dir.c_str(), rmdir_result);
                    fflush(debug_log);
                }
            }
            
            if (debug_log) {
                fprintf(debug_log, "测试进程正常退出: PID=%d\n", getpid());
                fclose(debug_log);
            }
            
            exit(0);
        } else if (pid > 0) {
            logSuccess("创建高负载测试进程: PID=" + std::to_string(pid) + ", Name=" + name);
            return pid;
        } else {
            logError("创建测试进程失败");
            return -1;
        }
    }
    
    // 设置重点进程
    bool setFocusProcess(pid_t pid, bool focus = true) {
        std::string method = focus ? "POST" : "DELETE";
        std::string path = "/api/focus-processes";
        
        std::string response;
        if (focus) {
            // POST请求需要JSON格式的请求体
            std::string json_body = "{\"type\":\"pid\",\"value\":\"" + std::to_string(pid) + "\"}";
            if (makeHttpRequestWithBody(method, path, json_body, &response)) {
                logSuccess("成功设置重点进程: PID=" + std::to_string(pid));
                return true;
            } else {
                logError("设置重点进程失败: PID=" + std::to_string(pid));
                return false;
            }
        } else {
            // DELETE请求使用URL参数
            path += "?type=pid&value=" + std::to_string(pid);
            if (makeHttpRequest(method, path, &response)) {
                logSuccess("成功移除重点进程: PID=" + std::to_string(pid));
                return true;
            } else {
                logError("移除重点进程失败: PID=" + std::to_string(pid));
                return false;
            }
        }
    }
    
    // 查询重点进程列表
    std::vector<pid_t> queryFocusProcessList() {
        std::string response;
        std::vector<pid_t> focus_pids;
        
        if (makeHttpRequest("GET", "/api/focus-processes", &response)) {
            logSuccess("查询重点进程列表成功");
            // 格式化显示重点进程列表
            if (verbose_) {
                printFocusProcessList(response);
            }
        } else {
            logError("查询重点进程列表失败");
        }
        
        return focus_pids;
    }
    
    // 查询进程状态
    bool queryProcessStatus(pid_t pid) {
        std::string response;
        std::string path = "/api/processes";
        
        if (makeHttpRequest("GET", path, &response)) {
            // 简单的搜索，查找进程PID
            std::string pid_str = "\"pid\":" + std::to_string(pid);
            if (response.find(pid_str) != std::string::npos) {
                logSuccess("进程状态查询 - PID=" + std::to_string(pid) + " (进程存在)");
                return true;
            } else {
                logWarning("进程状态查询 - PID=" + std::to_string(pid) + " (进程不在监控列表中)");
                return false;
            }
        } else {
            logError("查询进程状态失败: PID=" + std::to_string(pid));
            return false;
        }
    }
    
    // 检查进程是否存在
    bool processExists(pid_t pid) {
        return kill(pid, 0) == 0;
    }
    
    // 等待进程退出
    bool waitForProcessExit(pid_t pid, int timeout_seconds) {
        for (int i = 0; i < timeout_seconds; ++i) {
            if (!processExists(pid)) {
                logSuccess("进程已退出: PID=" + std::to_string(pid));
                return true;
            }
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        logWarning("等待进程退出超时: PID=" + std::to_string(pid));
        return false;
    }
    
    // 主测试函数
    bool runStabilityTest() {
        printSeparator("重点进程状态稳定性测试");
        
        // 第1步：创建高负载测试进程
        printSubSeparator("步骤 1/7: 创建高负载测试进程");
        logInfo("正在创建测试进程...");
        pid_t test_pid = createHighLoadProcess("focus_stability_test", 60);
        
        if (test_pid <= 0) {
            logError("创建测试进程失败");
            return false;
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        // 第2步：设置为重点进程
        printSubSeparator("步骤 2/7: 设置为重点进程");
        if (!setFocusProcess(test_pid, true)) {
            logError("设置重点进程失败");
            return false;
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        // 第3步：持续监控进程状态（30秒）
        printSubSeparator("步骤 3/7: 持续监控进程状态 (30秒)");
        logInfo("开始监控进程状态，每5秒查询一次...");
        
        for (int i = 0; i < 30; ++i) {
            if (!processExists(test_pid)) {
                logError("测试进程意外退出: PID=" + std::to_string(test_pid));
                return false;
            }
            
            // 每5秒查询一次状态
            if (i % 5 == 0) {
                logInfo("监控进度: " + std::to_string(i) + "/30 秒");
                queryProcessStatus(test_pid);
                queryFocusProcessList();
            }
            
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        
        // 第4步：验证重点进程状态仍然有效
        printSubSeparator("步骤 4/7: 验证重点进程状态");
        if (!queryProcessStatus(test_pid)) {
            logError("查询最终状态失败");
            return false;
        }
        
        // 第5步：手动终止进程
        printSubSeparator("步骤 5/7: 手动终止测试进程");
        if (kill(test_pid, SIGTERM) != 0) {
            logError("终止测试进程失败");
        } else {
            logInfo("已发送终止信号到测试进程");
        }
        
        // 第6步：等待进程退出
        printSubSeparator("步骤 6/7: 等待进程退出");
        waitForProcessExit(test_pid, 10);
        
        // 第7步：从重点进程列表中移除
        printSubSeparator("步骤 7/7: 从重点进程列表中移除");
        setFocusProcess(test_pid, false);
        
        printSeparator("测试完成");
        logSuccess("重点进程状态稳定性测试完成！");
        return true;
    }
    
    // 压力测试：多进程并发
    bool runConcurrentStabilityTest() {
        log("=== 开始并发重点进程状态稳定性测试 ===");
        
        const int num_processes = 5;
        std::vector<pid_t> test_pids;
        
        // 创建多个高负载进程
        log("\n创建多个高负载进程...");
        for (int i = 0; i < num_processes; ++i) {
            std::string name = "concurrent_test_" + std::to_string(i);
            pid_t pid = createHighLoadProcess(name, 45);
            if (pid > 0) {
                test_pids.push_back(pid);
            }
        }
        
        if (test_pids.empty()) {
            log("创建测试进程失败");
            return false;
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        // 将所有进程设置为重点进程
        log("\n设置所有进程为重点进程...");
        for (pid_t pid : test_pids) {
            setFocusProcess(pid, true);
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        // 持续监控所有进程（30秒）
        log("\n持续监控所有进程状态...");
        for (int i = 0; i < 30; ++i) {
            bool all_alive = true;
            for (pid_t pid : test_pids) {
                if (!processExists(pid)) {
                    log("进程意外退出: PID=" + std::to_string(pid));
                    all_alive = false;
                }
            }
            
            if (!all_alive) {
                break;
            }
            
            // 每10秒查询一次状态
            if (i % 10 == 0) {
                queryFocusProcessList();
            }
            
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        
        // 清理所有进程
        log("\n清理所有测试进程...");
        for (pid_t pid : test_pids) {
            if (processExists(pid)) {
                kill(pid, SIGTERM);
            }
        }
        
        // 等待所有进程退出
        for (pid_t pid : test_pids) {
            waitForProcessExit(pid, 10);
        }
        
        // 从重点进程列表中移除
        for (pid_t pid : test_pids) {
            setFocusProcess(pid, false);
        }
        
        log("\n=== 并发重点进程状态稳定性测试完成 ===");
        return true;
    }
};

int main(int argc, char* argv[]) {
    std::string base_url = "http://localhost:8080";
    bool verbose = true;
    
    // 解析命令行参数
    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];
        if (arg == "--url" && i + 1 < argc) {
            base_url = argv[++i];
        } else if (arg == "--quiet") {
            verbose = false;
        } else if (arg == "--help") {
            std::cout << "用法: " << argv[0] << " [选项]\n";
            std::cout << "选项:\n";
            std::cout << "  --url <URL>    API服务器地址 (默认: http://localhost:8080)\n";
            std::cout << "  --quiet        静默模式\n";
            std::cout << "  --help         显示帮助信息\n";
            return 0;
        }
    }
    
    // 初始化curl
    curl_global_init(CURL_GLOBAL_DEFAULT);
    
    try {
        FocusProcessStabilityTester tester(base_url, verbose);
        
        std::cout << "\n🔍 重点进程状态稳定性测试程序" << std::endl;
        std::cout << "🌐 API服务器: " << base_url << std::endl;
        std::cout << "🔧 详细输出: " << (verbose ? "启用" : "禁用") << std::endl;
        std::cout << std::string(60, '=') << std::endl;
        
        // 运行单进程稳定性测试
        bool test1_result = tester.runStabilityTest();
        
        std::this_thread::sleep_for(std::chrono::seconds(5));
        
        // 运行并发稳定性测试
        bool test2_result = tester.runConcurrentStabilityTest();
        
        std::cout << "\n" << std::string(60, '=') << std::endl;
        std::cout << "📊 测试结果汇总" << std::endl;
        std::cout << std::string(60, '=') << std::endl;
        
        std::cout << "1. 单进程稳定性测试: " << (test1_result ? "✅ 通过" : "❌ 失败") << std::endl;
        std::cout << "2. 并发稳定性测试: " << (test2_result ? "✅ 通过" : "❌ 失败") << std::endl;
        
        std::cout << "\n" << std::string(60, '-') << std::endl;
        if (test1_result && test2_result) {
            std::cout << "🎉 所有测试通过！重点进程状态稳定。" << std::endl;
        } else {
            std::cout << "⚠️  部分测试失败！需要进一步检查。" << std::endl;
        }
        std::cout << std::string(60, '=') << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "测试过程中发生异常: " << e.what() << std::endl;
        return 1;
    }
    
    curl_global_cleanup();
    return 0;
} 