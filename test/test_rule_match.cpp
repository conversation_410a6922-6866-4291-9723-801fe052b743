/*
 * 规则匹配功能测试程序
 * 用于验证RuleMatchStats和RuleMatchManager的功能
 */

#include <iostream>
#include <chrono>
#include <thread>
#include "rule_match_info.h"

int main() {
    std::cout << "=== 规则匹配功能测试 ===" << std::endl;
    
    // 测试RuleMatchStats
    std::cout << "\n1. 测试RuleMatchStats结构体:" << std::endl;
    
    RuleMatchStats stats;
    
    // 测试基本规则计数
    stats.incrementRuleCount(CB_MIN_STRICT_DELETE_WATCH_LEVEL);
    stats.incrementRuleCount(CB_MIN_NORMAL_RENAME_WATCH_LEVEL);
    stats.incrementRuleCount(CB_NORMAL_WATCH_LEVEL_DELETE);
    stats.incrementRuleCount(CB_PATCH_WATCH_LEVEL_RENAME);
    
    // 测试文件操作统计
    std::string kernel_string = "0123456789&'()*+,-./";
    stats.calculateFileOperationsFromKernelString(kernel_string);
    
    std::cout << "规则匹配统计摘要:" << std::endl;
    std::cout << stats.getSummary() << std::endl;
    
    // 测试JSON输出
    std::cout << "JSON格式输出:" << std::endl;
    std::cout << stats.toJson() << std::endl;
    
    // 测试RuleMatchManager
    std::cout << "\n2. 测试RuleMatchManager:" << std::endl;
    
    auto& manager = RuleMatchManager::getInstance();
    manager.initialize();
    
    // 模拟多个进程的规则匹配
    pid_t pid1 = 1234;
    pid_t pid2 = 5678;
    
    manager.recordRuleMatch(pid1, CB_MIN_STRICT_DELETE_WATCH_LEVEL, "0123456789");
    manager.recordRuleMatch(pid1, CB_MIN_NORMAL_RENAME_WATCH_LEVEL, "&'()*");
    manager.recordRuleMatch(pid2, CB_NORMAL_WATCH_LEVEL_DELETE, "123");
    
    // 获取进程统计
    auto stats1 = manager.getRuleMatchStats(pid1);
    if (stats1) {
        std::cout << "进程 " << pid1 << " 的统计:" << std::endl;
        std::cout << "  基本删除规则: " << stats1->base_delete_rule_counts << std::endl;
        std::cout << "  基本重命名规则: " << stats1->base_rename_rule_counts << std::endl;
        std::cout << "  删除操作次数: " << stats1->all_delete_times << std::endl;
        std::cout << "  重命名操作次数: " << stats1->all_rename_times << std::endl;
    }
    
    // 获取所有统计
    auto all_stats = manager.getAllRuleMatchStats();
    std::cout << "\n所有进程统计 (" << all_stats.size() << " 个进程):" << std::endl;
    for (const auto& pair : all_stats) {
        std::cout << "  PID " << pair.first << ": " << pair.second.all_catch_count << " 次匹配" << std::endl;
    }
    
    // 测试清理功能
    std::cout << "\n3. 测试清理功能:" << std::endl;
    std::set<pid_t> alive_pids = {pid1}; // 只保留pid1
    manager.cleanupDeadProcesses(alive_pids);
    
    all_stats = manager.getAllRuleMatchStats();
    std::cout << "清理后进程数: " << all_stats.size() << std::endl;
    
    // 测试重置
    manager.resetProcessStats(pid1);
    stats1 = manager.getRuleMatchStats(pid1);
    if (stats1) {
        std::cout << "重置后进程 " << pid1 << " 的总匹配数: " << stats1->all_catch_count << std::endl;
    }
    
    std::cout << "\n=== 测试完成 ===" << std::endl;
    
    return 0;
}
