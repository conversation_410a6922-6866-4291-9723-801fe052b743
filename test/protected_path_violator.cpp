/*
 * 受保护路径违规测试程序
 * 
 * 此程序故意违规操作受保护路径，用于测试PSFSMON-L的受保护路径监控功能
 * 程序会尝试在受保护路径下执行各种文件操作，并等待被监控系统处理
 */

#include <iostream>
#include <fstream>
#include <string>
#include <chrono>
#include <thread>
#include <cstdlib>
#include <unistd.h>
#include <sys/stat.h>
#include <signal.h>
#include <fcntl.h>
#include <cstring>
#include <curl/curl.h>
#include <sstream>
#include <atomic>

volatile bool keep_running = true;
static std::atomic<bool> g_graceful_shutdown(false);
static std::string g_api_url;
static std::string g_protected_path;

// 函数声明
static bool makeHttpRequest(const std::string& api_url, const std::string& method, const std::string& endpoint, const std::string& data = "");
static void signal_handler(int signal);

// HTTP回调函数
static size_t WriteCallback(void* contents, size_t size, size_t nmemb, std::string* userp) {
    userp->append((char*)contents, size * nmemb);
    return size * nmemb;
}

// HTTP请求方法
static bool makeHttpRequest(const std::string& api_url, const std::string& method, const std::string& endpoint, const std::string& data) {
    CURL* curl = curl_easy_init();
    if (!curl) {
        std::cout << "[API] CURL初始化失败" << std::endl;
        return false;
    }
    
    std::string url = api_url + endpoint;
    std::string response;
    
    curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 5L);
    
    // 设置HTTP方法
    if (method == "POST") {
        curl_easy_setopt(curl, CURLOPT_POST, 1L);
        if (!data.empty()) {
            curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data.c_str());
        }
    } else if (method == "DELETE") {
        curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "DELETE");
    }
    
    // 设置HTTP头
    struct curl_slist* headers = nullptr;
    if (!data.empty()) {
        headers = curl_slist_append(headers, "Content-Type: application/json");
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
    }
    
    // 执行请求
    CURLcode res = curl_easy_perform(curl);
    long response_code = 0;
    curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &response_code);
    
    curl_easy_cleanup(curl);
    if (headers) curl_slist_free_all(headers);
    
    if (res != CURLE_OK) {
        std::cout << "[API] HTTP请求失败: " << curl_easy_strerror(res) << std::endl;
        return false;
    }
    
    if (response_code >= 200 && response_code < 300) {
        std::cout << "[API] HTTP请求成功: " << method << " " << endpoint << " -> " << response_code << std::endl;
        return true;
    } else {
        std::cout << "[API] HTTP请求失败: " << method << " " << endpoint << " -> " << response_code << " " << response << std::endl;
        return false;
    }
}

// 优雅退出处理函数
static void handleGracefulShutdown() {
    std::cout << "\n[违规程序] 收到优雅退出信号，准备退出..." << std::endl;

    // 注意：不在这里清理受保护路径注册！
    // 清理工作应该由主测试程序在收集完数据后执行
    if (!g_api_url.empty() && !g_protected_path.empty()) {
        std::cout << "[违规程序] 保留受保护路径配置，由主测试程序负责清理" << std::endl;
    }

    std::cout << "[违规程序] 优雅退出流程完成，程序即将退出" << std::endl;
    std::cout << "[违规程序] 注意：受保护路径清理工作由主测试程序负责" << std::endl;
}

static void signal_handler(int signal) {
    std::cout << "\n[违规程序] 收到信号 " << signal << " (";
    switch(signal) {
        case SIGUSR1:
            std::cout << "SIGUSR1 - 优雅退出请求";
            g_graceful_shutdown.store(true);
            handleGracefulShutdown();
            break;
        case SIGTERM:
            std::cout << "SIGTERM";
            g_graceful_shutdown.store(true);
            break;
        case SIGKILL:
            std::cout << "SIGKILL";
            break;
        case SIGINT:
            std::cout << "SIGINT";
            g_graceful_shutdown.store(true);
            break;
        default:
            std::cout << "未知信号";
            break;
    }
    std::cout << "), 程序即将退出" << std::endl;
    keep_running = false;
}

int main(int argc, char* argv[]) {
    if (argc < 2) {
        std::cerr << "用法: " << argv[0] << " <受保护路径> [选项]" << std::endl;
        std::cerr << "选项:" << std::endl;
        std::cerr << "  --api-url <URL>      API服务器地址 (默认: http://127.0.0.1:8080)" << std::endl;
        std::cerr << "  --self-register      启动时自动添加受保护路径" << std::endl;
        std::cerr << "示例: " << argv[0] << " /tmp/protected" << std::endl;
        std::cerr << "示例: " << argv[0] << " /tmp/protected --self-register" << std::endl;
        return 1;
    }
    
    std::string protected_path = argv[1];
    std::string api_url = "http://127.0.0.1:8080";
    bool self_register = false;
    
    // 解析命令行参数
    for (int i = 2; i < argc; i++) {
        std::string arg = argv[i];
        if (arg == "--api-url" && i + 1 < argc) {
            api_url = argv[++i];
        } else if (arg == "--self-register") {
            self_register = true;
        }
    }
    
    pid_t my_pid = getpid();
    
    // 设置全局变量，用于信号处理
    g_api_url = api_url;
    g_protected_path = protected_path;

    // 初始化CURL
    if (self_register) {
        curl_global_init(CURL_GLOBAL_DEFAULT);
    }

    // 设置信号处理器
    signal(SIGUSR1, signal_handler);  // 优雅退出信号
    signal(SIGTERM, signal_handler);  // 终止信号
    signal(SIGINT, signal_handler);   // 中断信号
    
    std::cout << "=== 受保护路径违规测试程序 ===" << std::endl;
    std::cout << "程序PID: " << my_pid << std::endl;
    std::cout << "目标受保护路径: " << protected_path << std::endl;
    std::cout << "API服务器: " << api_url << std::endl;
    std::cout << "自注册模式: " << (self_register ? "启用" : "禁用") << std::endl;
    
    // 步骤1: 首先创建受保护路径（在自注册之前）
    std::cout << "\n步骤1: 创建受保护路径..." << std::endl;
    struct stat st;
    if (stat(protected_path.c_str(), &st) != 0) {
        std::cout << "创建受保护目录: " << protected_path << std::endl;
        if (mkdir(protected_path.c_str(), 0755) == 0) {
            std::cout << "✓ 成功创建目录" << std::endl;
        } else {
            std::cout << "⚠️ 创建目录失败，可能已存在: " << strerror(errno) << std::endl;
        }
    } else {
        std::cout << "✓ 受保护路径已存在" << std::endl;
    }

    // 步骤2: 如果启用自注册，添加受保护路径到监控系统
    if (self_register) {
        std::cout << "\n步骤2: 自注册受保护路径..." << std::endl;
        std::string data = R"({"path": ")" + protected_path + R"("})";
        if (makeHttpRequest(api_url, "POST", "/api/protected-paths", data)) {
            std::cout << "✓ 成功添加受保护路径: " << protected_path << std::endl;
        } else {
            std::cout << "⚠️ 添加受保护路径失败，可能服务未启动或API不可用" << std::endl;
            std::cout << "继续执行测试..." << std::endl;
        }
        // 等待设置生效
        std::cout << "等待监控设置生效..." << std::endl;
        std::this_thread::sleep_for(std::chrono::seconds(2));
    }

    std::cout << "\n步骤3: 开始执行违规操作..." << std::endl;
    std::cout << "注意: 受保护路径监控有两种模式:" << std::endl;
    std::cout << "  - KILL模式: 违规进程会被立即终止" << std::endl;
    std::cout << "  - LOG模式: 违规行为被记录但进程继续运行" << std::endl;

    // 等待一秒，确保监控系统准备就绪
    std::this_thread::sleep_for(std::chrono::seconds(1));

    int operation_count = 0;
    auto start_time = std::chrono::steady_clock::now();
    bool was_killed = false;  // 标记是否被KILL模式终止

    try {
        // 违规操作1：在受保护路径下创建文件
        if (keep_running && !g_graceful_shutdown.load()) {
            std::string violation_file = protected_path + "/violation_test_" + std::to_string(my_pid) + ".txt";
            std::cout << "[违规操作 " << ++operation_count << "] 创建违规文件: " << violation_file << std::endl;

            // 使用系统调用直接创建文件，确保触发FAN_OPEN_PERM
            int fd = open(violation_file.c_str(), O_WRONLY | O_CREAT | O_TRUNC, 0644);
            if (fd >= 0) {
                const char* content = "这是一个违规文件操作测试\n";
                if (write(fd, content, strlen(content)) == -1) {
                    std::cout << "  写入失败: " << strerror(errno) << std::endl;
                }
                close(fd);
                std::cout << "  ✓ 文件创建成功 (系统调用)" << std::endl;
            } else {
                std::cout << "  ✗ 文件创建失败: " << strerror(errno) << std::endl;
            }

            // 检查是否被KILL模式终止
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
            if (!keep_running) {
                std::cout << "  ⚠️ 进程被终止 - 检测到KILL模式" << std::endl;
                was_killed = true;
                return 0;  // 在KILL模式下，进程会被终止，这里不会执行到
            }
        }

        // 违规操作2：使用open+write老API写文件
        if (keep_running && !g_graceful_shutdown.load()) {
            std::string violation_file = protected_path + "/violation_test_syscall_" + std::to_string(my_pid) + ".txt";
            std::cout << "[违规操作 " << ++operation_count << "] open+write写文件: " << violation_file << std::endl;
            int fd = open(violation_file.c_str(), O_WRONLY | O_CREAT | O_TRUNC, 0644);
            if (fd >= 0) {
                const char* msg = "syscall open/write 测试\n";
                if (write(fd, msg, strlen(msg)) == -1) {
                    std::cout << "  写入失败: " << strerror(errno) << std::endl;
                }
                close(fd);
                std::cout << "  ✓ open+write写入成功" << std::endl;
            } else {
                std::cout << "  ✗ open+write写入失败: " << strerror(errno) << std::endl;
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(500));
            if (!keep_running) {
                std::cout << "  ⚠️ 进程被终止 - 检测到KILL模式" << std::endl;
                was_killed = true;
                return 0;
            }
        }

        // 违规操作3：修改文件
        if (keep_running && !g_graceful_shutdown.load()) {
            std::string violation_file = protected_path + "/violation_test_" + std::to_string(my_pid) + ".txt";
            std::cout << "[违规操作 " << ++operation_count << "] 修改违规文件: " << violation_file << std::endl;

            // 使用系统调用追加写入，确保触发FAN_OPEN_PERM
            int fd = open(violation_file.c_str(), O_WRONLY | O_APPEND);
            if (fd >= 0) {
                const char* content = "修改操作 - 追加数据\n";
                if (write(fd, content, strlen(content)) == -1) {
                    std::cout << "  写入失败: " << strerror(errno) << std::endl;
                }
                close(fd);
                std::cout << "  ✓ 文件修改成功 (系统调用)" << std::endl;
            } else {
                std::cout << "  ✗ 文件修改失败: " << strerror(errno) << std::endl;
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(500));
            if (!keep_running) {
                std::cout << "  ⚠️ 进程被终止 - 检测到KILL模式" << std::endl;
                was_killed = true;
                return 0;
            }
        }

        // 违规操作4：读取受保护路径下的文件
        if (keep_running && !g_graceful_shutdown.load()) {
            std::string violation_file = protected_path + "/violation_test_" + std::to_string(my_pid) + ".txt";
            std::cout << "[违规操作 " << ++operation_count << "] 读取违规文件: " << violation_file << std::endl;

            // 使用系统调用读取文件，确保触发FAN_OPEN_PERM
            int fd = open(violation_file.c_str(), O_RDONLY);
            if (fd >= 0) {
                char buffer[1024];
                ssize_t bytes_read = read(fd, buffer, sizeof(buffer) - 1);
                if (bytes_read > 0) {
                    buffer[bytes_read] = '\0';
                    std::cout << "  ✓ 文件读取成功 (系统调用)，读取 " << bytes_read << " 字节" << std::endl;
                } else {
                    std::cout << "  ✗ 文件读取失败: " << strerror(errno) << std::endl;
                }
                close(fd);
            } else {
                std::cout << "  ✗ 文件打开失败: " << strerror(errno) << std::endl;
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(500));
            if (!keep_running) {
                std::cout << "  ⚠️ 进程被终止 - 检测到KILL模式" << std::endl;
                was_killed = true;
                return 0;
            }
        }
        
        // 违规操作4：创建多个文件
        if (keep_running) {
            for (int i = 1; i <= 3 && keep_running; i++) {
                std::string batch_file = protected_path + "/batch_violation_" + std::to_string(my_pid) + "_" + std::to_string(i) + ".dat";
                std::cout << "[违规操作 " << ++operation_count << "] 批量创建文件 " << i << ": " << batch_file << std::endl;
                
                // 使用系统调用创建文件，确保触发FAN_OPEN_PERM
                int fd = open(batch_file.c_str(), O_WRONLY | O_CREAT | O_TRUNC, 0644);
                if (fd >= 0) {
                    std::string content = "批量违规文件 #" + std::to_string(i) + "\n";
                    for (int j = 0; j < 10; j++) {  // 减少数据量，避免日志过多
                        content += "数据行 " + std::to_string(j) + ": ABCDEFGHIJKLMNOPQRSTUVWXYZ\n";
                    }
                    if (write(fd, content.c_str(), content.length()) == -1) {
                        std::cout << "  写入失败: " << strerror(errno) << std::endl;
                    }
                    close(fd);
                    std::cout << "  批量文件 " << i << " 创建成功 (系统调用)" << std::endl;
                } else {
                    std::cout << "  批量文件 " << i << " 创建失败: " << strerror(errno) << std::endl;
                }
                
                std::this_thread::sleep_for(std::chrono::milliseconds(300));
            }
        }
        
        // 违规操作5：尝试删除文件
        if (keep_running) {
            std::string violation_file = protected_path + "/violation_test_" + std::to_string(my_pid) + ".txt";
            std::cout << "[违规操作 " << ++operation_count << "] 删除违规文件: " << violation_file << std::endl;

            if (std::remove(violation_file.c_str()) == 0) {
                std::cout << "  文件删除成功" << std::endl;
            } else {
                std::cout << "  文件删除失败" << std::endl;
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }

        // 违规操作6：创建目录
        if (keep_running) {
            std::string violation_dir = protected_path + "/violation_dir_" + std::to_string(my_pid);
            std::cout << "[违规操作 " << ++operation_count << "] 创建违规目录: " << violation_dir << std::endl;

            if (mkdir(violation_dir.c_str(), 0755) == 0) {
                std::cout << "  目录创建成功 (系统调用)" << std::endl;

                // 在目录中创建文件
                std::string subfile = violation_dir + "/subfile.txt";
                int fd = open(subfile.c_str(), O_WRONLY | O_CREAT | O_TRUNC, 0644);
                if (fd >= 0) {
                    const char* content = "子目录文件内容\n";
                    ssize_t bytes_written = write(fd, content, strlen(content));
                    if (bytes_written < 0) {
                        std::cout << "  警告: 写入子目录文件失败: " << strerror(errno) << std::endl;
                    }
                    close(fd);
                    std::cout << "  子目录文件创建成功" << std::endl;
                }
            } else {
                std::cout << "  目录创建失败: " << strerror(errno) << std::endl;
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }

        // 违规操作7：创建符号链接
        if (keep_running) {
            std::string target_file = "/tmp/link_target.txt";
            std::string link_file = protected_path + "/violation_link_" + std::to_string(my_pid);
            std::cout << "[违规操作 " << ++operation_count << "] 创建符号链接: " << link_file << std::endl;

            // 先创建目标文件
            int fd = open(target_file.c_str(), O_WRONLY | O_CREAT | O_TRUNC, 0644);
            if (fd >= 0) {
                const char* content = "链接目标文件\n";
                ssize_t bytes_written = write(fd, content, strlen(content));
                if (bytes_written < 0) {
                    std::cout << "  警告: 写入链接目标文件失败: " << strerror(errno) << std::endl;
                }
                close(fd);
            }

            if (symlink(target_file.c_str(), link_file.c_str()) == 0) {
                std::cout << "  符号链接创建成功" << std::endl;
            } else {
                std::cout << "  符号链接创建失败: " << strerror(errno) << std::endl;
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }
        
    } catch (const std::exception& e) {
        std::cout << "违规操作异常: " << e.what() << std::endl;
    }
    
    // 计算运行时间
    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(end_time - start_time);

    std::cout << "\n=== 步骤4: 违规操作完成 ===" << std::endl;
    std::cout << "总操作次数: " << operation_count << std::endl;
    std::cout << "运行时间: " << duration.count() << " 秒" << std::endl;

    // 检测监控模式
    std::string detected_mode = "LOG";  // 默认为LOG模式
    if (was_killed) {
        detected_mode = "KILL";
        std::cout << "⚠️ 检测到KILL模式 - 进程在违规操作中被终止" << std::endl;
        std::cout << "注意: 此代码不应该被执行到，因为在KILL模式下进程会被终止" << std::endl;
    } else {
        std::cout << "✓ 检测到LOG模式 - 进程在违规操作后继续运行" << std::endl;
    }

    // 创建完成标志文件，通知主测试程序
    std::string flag_file = "/tmp/psfsmon_protected_path_test_done.flag";
    std::ofstream flag_stream(flag_file);
    if (flag_stream.is_open()) {
        flag_stream << "DONE:" << getpid() << ":" << time(nullptr) << ":" << operation_count << ":" << detected_mode << std::endl;
        flag_stream.close();
        std::cout << "✅ 创建完成标志文件: " << flag_file << std::endl;
    } else {
        std::cout << "⚠️ 无法创建完成标志文件: " << flag_file << std::endl;
    }

    // 步骤5: 等待主测试程序处理
    std::cout << "\n步骤5: 等待主测试程序处理..." << std::endl;
    std::cout << "监控模式: " << detected_mode << std::endl;
    std::cout << "程序将等待主测试程序进行数据收集和清理" << std::endl;
    
    int wait_seconds = 0;
    while (keep_running && !g_graceful_shutdown.load() && wait_seconds < 60) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
        wait_seconds++;

        if (wait_seconds % 10 == 0) {
            std::cout << "等待中... (" << wait_seconds << "/60 秒)" << std::endl;
        }
    }
    
    if (g_graceful_shutdown.load()) {
        std::cout << "\n程序收到优雅退出信号 - 主测试程序请求退出" << std::endl;
    } else if (keep_running) {
        std::cout << "\n程序正常结束 - 未被监控系统终止" << std::endl;
        std::cout << "这可能意味着:" << std::endl;
        std::cout << "1. 受保护路径监控未启用" << std::endl;
        std::cout << "2. 当前路径未被设置为受保护路径" << std::endl;
        std::cout << "3. 监控系统配置为仅记录而不终止进程" << std::endl;
    } else {
        std::cout << "\n程序被信号终止 - 受保护路径监控正常工作" << std::endl;
    }
    
    // 注意：不再自动清理受保护路径，由主测试程序负责清理
    // 这样确保测试程序能够找到并读取统计信息
    if (self_register) {
        std::cout << "\n[API] 保留受保护路径配置，由主测试程序负责清理" << std::endl;
        curl_global_cleanup();
    }
    
    return 0;
} 