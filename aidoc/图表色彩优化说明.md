# PSFSMON-L 图表色彩优化说明

## 优化概述

本次更新针对aidoc目录下所有Mermaid图表的色彩方案进行了优化，主要解决了原有图表中色彩填充与文字对比度不足的问题，提升了文档的可读性和视觉体验。

## 问题分析

### 原有色彩方案存在的问题：
1. **对比度不足**：使用了过于浅淡的颜色，如 `#ffcdd2`、`#c8e6c9`、`#e1f5fe` 等
2. **视觉疲劳**：浅色背景与黑色文字的对比度低，长时间阅读容易造成眼部疲劳
3. **可读性差**：在不同显示设备和环境光线下，文字可能难以清晰识别

## 优化方案

### 新色彩方案特点：
1. **高对比度**：采用更饱和的颜色，确保与文字有足够的对比度
2. **智能文字颜色**：根据背景色自动选择白色或黑色文字，确保最佳可读性
3. **色彩分类**：不同功能模块使用不同色系，便于区分和理解

### 具体色彩映射：

| 原色彩 | 新色彩 | 文字颜色 | 用途说明 |
|--------|--------|----------|----------|
| `#ffcdd2` (浅粉) | `#ff6b6b` (中等红) | 白色 | 受保护路径相关组件 |
| `#c8e6c9` (浅绿) | `#51cf66` (中等绿) | 黑色 | 重点进程相关组件 |
| `#e1f5fe` (浅蓝) | `#339af0` (中等蓝) | 白色 | 监控层和数据采集 |
| `#fff9c4` (浅黄) | `#ffd43b` (中等黄) | 黑色 | 内核层和API输出 |
| `#f3e5f5` (浅紫) | `#9775fa` (中等紫) | 白色 | 控制层和核心处理 |
| `#e8f5e8` (浅绿2) | `#51cf66` (中等绿) | 黑色 | 监控服务组件 |
| `#fff3e0` (浅橙) | `#ff922b` (中等橙) | 白色 | 数据存储和服务层 |
| `#fce4ec` (浅粉2) | `#f783ac` (中等粉) | 白色 | 日志和输出 |

## 更新的文件列表

### Mermaid图表文件：
1. `双Fanotify架构图.mmd` - 双Fanotify架构图
2. `数据流向图.mmd` - 系统数据流向图
3. `熵值计算流程图.mmd` - 熵值计算流程图
4. `系统整体架构图.mmd` - 系统整体架构图
5. `类层次关系图.mmd` - 类层次关系图（修复语法错误）

### Markdown文档文件：
1. `类关系图形说明.md` - 包含多个嵌入式Mermaid图表（修复语法错误）

## 语法错误修复

### 问题描述
在类图（classDiagram）中发现了不正确的关系语法：
```
MainMonitor ||--o{ FileMonitor : owns
NetworkMonitor }|--|| ProcessMonitor : depends on
```

### 修复方案
将错误的关系语法替换为正确的Mermaid类图语法：

| 错误语法 | 正确语法 | 关系类型 |
|----------|----------|----------|
| `||--o{` | `*--` | 组合关系（拥有） |
| `||--||` | `-->` | 依赖关系 |
| `}|--||` | `-->` | 依赖关系 |
| `||--o|` | `*--` | 组合关系 |

### 修复的关系：
- `MainMonitor *-- FileMonitor : owns` - MainMonitor拥有FileMonitor
- `MainMonitor *-- NetworkMonitor : owns` - MainMonitor拥有NetworkMonitor
- `MainMonitor --> ApiServer : creates` - MainMonitor创建ApiServer
- `ApiServer *-- HttpServer : owns` - ApiServer拥有HttpServer
- `NetworkMonitor --> ProcessMonitor : depends on` - NetworkMonitor依赖ProcessMonitor
- `ProcessMonitor *-- ProcessInfo : manages` - ProcessMonitor管理ProcessInfo
- `ProcessInfo *-- FocusProcessFileInfo : owns` - ProcessInfo拥有FocusProcessFileInfo

## 色彩设计原则

### 1. 可访问性优先
- 确保色彩对比度符合WCAG 2.1 AA标准
- 支持色盲用户的阅读需求
- 在不同设备和环境下保持良好的可读性

### 2. 功能性分类
- **红色系**：警告、保护、安全相关功能
- **绿色系**：正常运行、监控、处理功能
- **蓝色系**：数据流、监控层、基础服务
- **黄色系**：系统核心、API接口
- **紫色系**：控制管理、核心逻辑
- **橙色系**：存储、服务、输出

### 3. 视觉层次
- 使用色彩深浅来表示组件的重要性和层次关系
- 保持整体色彩方案的一致性和协调性

## 使用建议

### 查看图表时：
1. 建议在明亮的环境下阅读，以获得最佳视觉效果
2. 如果仍感觉对比度不够，可以调整显示器的亮度和对比度设置
3. 支持放大查看，新的色彩方案在不同缩放级别下都能保持良好的可读性

### 后续维护：
1. 新增图表时请参考本文档的色彩方案
2. 保持色彩使用的一致性，相同功能的组件使用相同色系
3. 如需调整色彩，请确保新色彩仍能满足对比度要求

## 技术实现

### Mermaid样式语法：
```mermaid
style NodeId fill:#color_code,color:#text_color
```

### 示例：
```mermaid
style A fill:#339af0,color:#ffffff  // 蓝色背景，白色文字
style B fill:#51cf66,color:#000000  // 绿色背景，黑色文字
```

这次优化显著提升了文档的可读性和专业性，为项目文档的长期维护奠定了良好的基础。
