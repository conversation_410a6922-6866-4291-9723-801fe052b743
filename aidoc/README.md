# PSFSMON-L 项目文档索引

## 文档概述

本目录包含了PSFSMON-L项目的完整技术文档，涵盖了类功能说明、类层次关系、类调用关系、接口说明、图形化架构展示和项目架构分析等多个方面。这些文档为理解项目架构、进行系统维护和功能扩展提供了全面的技术参考。

## 文档结构

### 📋 核心文档

#### 1. [类功能说明文档.md](./类功能说明文档.md)
**内容概述**: 详细描述了PSFSMON-L项目中所有类的功能、职责、成员变量和成员函数
**主要章节**:
- MainMonitor - 主监控器类
- FileMonitor - 文件监控器类  
- ProcessMonitor - 进程监控器类
- NetworkMonitor - 网络监控器类
- ApiServer - API服务器类
- HttpServer - HTTP服务器类
- 管理器类（FocusProcessManager、ProtectedPathManager、ProcessExclusionManager）
- 工具类（JsonBuilder、LogManager）
- 核心数据结构（ProcessInfo、FocusProcessFileInfo、FileEvent等）

**适用人群**: 开发人员、系统架构师、代码审查人员

#### 2. [类层次关系说明.md](./类层次关系说明.md)
**内容概述**: 详细分析了项目中所有类的层次关系、继承结构、组合关系和依赖关系
**主要章节**:
- 整体架构层次
- 控制层类层次
- 监控层类层次
- 管理层类层次
- 服务层类层次
- 工具层类层次
- 数据层类层次
- 类关系总结

**适用人群**: 系统架构师、高级开发人员、技术负责人

#### 3. [类调用关系说明.md](./类调用关系说明.md)
**内容概述**: 详细描述了所有类之间的调用关系、数据流向和交互模式
**主要章节**:
- 系统启动调用链
- 文件监控调用关系
- 进程监控调用关系
- 网络监控调用关系
- API服务调用关系
- 管理器类调用关系

**适用人群**: 开发人员、调试工程师、性能优化工程师

#### 4. [类调用接口说明.md](./类调用接口说明.md)
**内容概述**: 详细说明了所有类的公共接口、方法签名、参数说明、返回值和使用示例
**主要章节**:
- MainMonitor接口
- FileMonitor接口
- ProcessMonitor接口
- NetworkMonitor接口
- ApiServer接口
- 管理器类接口
- 数据结构访问接口

**适用人群**: API使用者、集成开发人员、测试工程师

### 📊 图形化文档

#### 5. [类关系图形说明.md](./类关系图形说明.md)
**内容概述**: 通过Mermaid图形化方式展示系统架构和类关系
**主要图表**:
- 系统整体架构图
- 类层次关系图
- 管理器类关系图
- 数据流向图
- 双Fanotify Group架构图
- API请求处理流程图
- 重点进程熵值计算流程图

**适用人群**: 所有技术人员、项目管理人员、新员工培训

### 🎯 独立图形文件

#### 6. Mermaid图形文件(.mmd)
- [系统整体架构图.mmd](./系统整体架构图.mmd) - 系统整体架构的Mermaid图
- [类层次关系图.mmd](./类层次关系图.mmd) - 详细的类层次关系图
- [数据流向图.mmd](./数据流向图.mmd) - 系统数据流向图
- [双Fanotify架构图.mmd](./双Fanotify架构图.mmd) - 核心双Fanotify架构图
- [熵值计算流程图.mmd](./熵值计算流程图.mmd) - 熵值计算流程图

**使用方法**: 这些.mmd文件可以直接在支持Mermaid的工具中打开，如：
- GitHub/GitLab（直接渲染）
- Mermaid Live Editor
- VS Code + Mermaid插件
- 各种Markdown编辑器

### 📈 分析报告

#### 7. [项目架构分析报告.md](./项目架构分析报告.md)
**内容概述**: 从软件架构、设计模式、技术特点和系统优势等维度全面分析项目
**主要章节**:
- 项目技术特色
- 软件架构分析
- 技术实现特点
- 系统优势分析
- 技术创新点
- 应用场景和价值

**适用人群**: 技术决策者、架构师、技术评审人员

### 📋 质量保证

#### 8. [文档修正说明.md](./文档修正说明.md)
**内容概述**: 记录了文档生成过程中发现和修正的错误，确保文档准确性
**主要章节**:
- 已修正的错误列表
- 验证的正确内容
- 修正对开发的影响
- 质量保证措施

**适用人群**: 所有使用文档的人员，特别是需要确保信息准确性的开发人员

## 文档使用指南

### 🚀 快速入门路径

**新手开发人员**:
1. 先阅读 [项目架构分析报告.md](./项目架构分析报告.md) 了解整体架构
2. 查看 [类关系图形说明.md](./类关系图形说明.md) 中的架构图
3. 详细阅读 [类功能说明文档.md](./类功能说明文档.md) 了解各类功能

**系统集成人员**:
1. 重点阅读 [类调用接口说明.md](./类调用接口说明.md)
2. 参考 [类调用关系说明.md](./类调用关系说明.md) 了解调用流程
3. 查看API相关的图形说明

**架构师/技术负责人**:
1. 详细研读 [项目架构分析报告.md](./项目架构分析报告.md)
2. 分析 [类层次关系说明.md](./类层次关系说明.md)
3. 评估系统的可扩展性和维护性

### 🔍 文档查找技巧

**按功能查找**:
- 文件监控相关：搜索"FileMonitor"、"Fanotify"、"熵值"
- 进程监控相关：搜索"ProcessMonitor"、"进程信息"
- 网络监控相关：搜索"NetworkMonitor"、"网络连接"
- API相关：搜索"ApiServer"、"HTTP"、"JSON"

**按类名查找**:
- 每个文档都有详细的目录结构
- 使用Ctrl+F搜索具体的类名
- 参考类关系图快速定位相关类

**按问题类型查找**:
- 架构设计问题：查看架构分析报告和层次关系说明
- 接口使用问题：查看接口说明文档
- 调用流程问题：查看调用关系说明
- 系统理解问题：查看图形化说明

## 文档维护说明

### 📝 更新原则

1. **同步更新**: 代码变更时同步更新相关文档
2. **版本控制**: 重大架构变更时创建文档版本
3. **准确性**: 确保文档与实际代码实现一致
4. **完整性**: 新增类或接口时及时补充文档

### 🔄 维护流程

1. **代码审查**: 代码变更时检查是否需要更新文档
2. **文档审查**: 定期审查文档的准确性和完整性
3. **图形更新**: 架构变更时更新相关的Mermaid图
4. **交叉验证**: 确保各文档间的一致性

### 📋 质量标准

- **准确性**: 文档内容与代码实现完全一致
- **完整性**: 覆盖所有重要的类和接口
- **清晰性**: 表述清晰，易于理解
- **实用性**: 提供实际的使用指导和示例

## 技术支持

如果在使用这些文档过程中遇到问题，可以：

1. **检查代码**: 对照实际代码验证文档内容
2. **查看测试**: 参考测试代码了解使用方式
3. **运行示例**: 通过API测试验证接口功能
4. **提交反馈**: 发现文档错误或不足时及时反馈

## 版本信息

- **文档版本**: v1.0
- **创建日期**: 2025-01-07
- **适用代码版本**: PSFSMON-L 当前版本
- **最后更新**: 2025-01-07

---

**注意**: 本文档集合是PSFSMON-L项目的重要组成部分，请在使用和维护过程中保持其准确性和完整性。
