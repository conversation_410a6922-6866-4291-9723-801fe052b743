# PSFSMON-L 类调用接口说明文档

## 文档概述

本文档详细描述了PSFSMON-L项目中所有类的公共接口、方法签名、参数说明、返回值和使用示例。这些接口构成了系统各模块间通信的标准协议。

## 接口分类说明

PSFSMON-L的类接口可以分为以下几类：
- **生命周期接口**: 初始化、启动、停止、清理
- **数据访问接口**: 查询、获取、更新数据
- **配置管理接口**: 加载、重新加载、验证配置
- **事件处理接口**: 注册回调、处理事件、通知
- **统计信息接口**: 获取运行统计、性能指标

---

## 1. MainMonitor - 主监控器接口

### 1.1 生命周期管理接口

```cpp
class MainMonitor {
public:
    // 构造函数和析构函数
    MainMonitor();
    ~MainMonitor();
    
    /**
     * 初始化系统
     * @param config 监控配置
     * @return 成功返回true，失败返回false
     * @throws std::runtime_error 当系统不兼容或权限不足时
     */
    bool initialize(const MonitorConfig& config);
    
    /**
     * 启动所有监控服务
     * @return 成功返回true，失败返回false
     * @note 必须在initialize()成功后调用
     */
    bool start();
    
    /**
     * 停止所有监控服务
     * @note 线程安全，可以从信号处理器调用
     */
    void stop();
    
    /**
     * 重新加载配置
     * @param config 新的监控配置
     * @return 成功返回true，失败返回false
     * @note 支持热重载，不会中断服务
     */
    bool reloadConfig(const MonitorConfig& config);
};
```

### 1.2 状态查询接口

```cpp
class MainMonitor {
public:
    /**
     * 获取系统运行状态
     * @return 当前监控状态枚举值
     */
    MonitorStatus getStatus() const;
    
    /**
     * 获取系统配置
     * @return 当前配置的常量引用
     */
    const MonitorConfig& getConfig() const;
    
    /**
     * 获取系统统计信息
     * @return 包含运行时统计的结构体
     */
    MonitorStats getStats() const;
};
```

### 1.3 模块访问接口

```cpp
class MainMonitor {
public:
    /**
     * 获取进程监控器实例
     * @return 进程监控器指针，如果未初始化返回nullptr
     */
    ProcessMonitor* getProcessMonitor() const;
    
    /**
     * 获取文件监控器实例
     * @return 文件监控器指针，如果未初始化返回nullptr
     */
    FileMonitor* getFileMonitor() const;
    
    /**
     * 获取网络监控器实例
     * @return 网络监控器指针，如果未初始化返回nullptr
     */
    NetworkMonitor* getNetworkMonitor() const;
};
```

### 1.4 系统管理接口

```cpp
class MainMonitor {
public:
    /**
     * 守护进程化
     * @return 成功返回true，失败返回false
     * @note 调用后进程将脱离终端运行
     */
    bool daemonize();
    
    /**
     * 信号处理器（静态方法）
     * @param signal 信号编号
     * @note 处理SIGTERM、SIGINT、SIGHUP等信号
     */
    static void signalHandler(int signal);
};
```

---

## 2. FileMonitor - 文件监控器接口

### 2.1 核心监控接口

```cpp
class FileMonitor {
public:
    /**
     * 初始化文件监控器
     * @param config 监控配置指针
     * @return 成功返回true，失败返回false
     * @note 会初始化双Fanotify Group
     */
    bool initialize(const MonitorConfig* config);
    
    /**
     * 启动文件监控
     * @return 成功返回true，失败返回false
     * @note 启动双线程监控循环
     */
    bool start();
    
    /**
     * 停止文件监控
     * @note 线程安全，会优雅关闭监控线程
     */
    void stop();
    
    /**
     * 重新加载配置
     * @param config 新的监控配置
     * @return 成功返回true，失败返回false
     */
    bool reloadConfig(const MonitorConfig* config);
};
```

### 2.2 事件处理接口

```cpp
class FileMonitor {
public:
    /**
     * 设置文件事件回调函数
     * @param callback 事件处理回调函数
     * @note 回调函数会在事件处理线程中被调用
     */
    void setEventCallback(std::function<void(const FileEvent&)> callback);
    
    /**
     * 获取总事件数
     * @return 自启动以来处理的总事件数
     */
    uint64_t getTotalEvents() const;
    
    /**
     * 获取详细事件统计
     * @return 包含各类事件统计的结构体
     */
    FileEventStats getEventStats() const;
    
    /**
     * 获取最近的事件列表
     * @param limit 返回事件的最大数量，默认100
     * @return 最近事件的向量
     */
    std::vector<FileEvent> getRecentEvents(size_t limit = 100) const;
};
```

### 2.3 动态管理接口

```cpp
class FileMonitor {
public:
    /**
     * 添加受保护路径标记
     * @param path 要保护的路径
     * @return 成功返回true，失败返回false
     * @note 支持运行时动态添加
     */
    bool addProtectedPathMark(const std::string& path);
    
    /**
     * 移除受保护路径标记
     * @param path 要移除保护的路径
     * @note 支持运行时动态移除
     */
    void removeProtectedPathMark(const std::string& path);
};
```

---

## 3. ProcessMonitor - 进程监控器接口

### 3.1 进程信息访问接口

```cpp
class ProcessMonitor {
public:
    /**
     * 获取指定进程信息
     * @param pid 进程ID
     * @return 进程信息智能指针，不存在返回nullptr
     */
    std::shared_ptr<ProcessInfo> getProcess(pid_t pid) const;
    
    /**
     * 获取所有进程信息
     * @return 所有进程信息的向量
     */
    std::vector<std::shared_ptr<ProcessInfo>> getAllProcesses() const;
    
    /**
     * 获取进程树根节点
     * @return 进程树根节点智能指针
     */
    std::shared_ptr<ProcessTreeNode> getProcessTree() const;
    
    /**
     * 根据进程名查找进程
     * @param name 进程名称
     * @return 匹配的进程信息向量
     */
    std::vector<std::shared_ptr<ProcessInfo>> findProcessesByName(const std::string& name) const;
};
```

### 3.2 重点进程管理接口

```cpp
class ProcessMonitor {
public:
    /**
     * 设置重点进程（按PID）
     * @param pid 进程ID
     * @param focus 是否设为重点进程，默认true
     */
    void setFocusProcess(pid_t pid, bool focus = true);
    
    /**
     * 设置重点进程（按进程名）
     * @param process_name 进程名称
     * @param focus 是否设为重点进程，默认true
     */
    void setFocusProcess(const std::string& process_name, bool focus = true);
    
    /**
     * 检查是否为重点进程
     * @param pid 进程ID
     * @return 是重点进程返回true，否则返回false
     */
    bool isFocusProcess(pid_t pid) const;
    
    /**
     * 获取所有重点进程PID列表
     * @return 重点进程PID向量
     */
    std::vector<pid_t> getFocusProcesses() const;
};
```

### 3.3 监控控制接口

```cpp
class ProcessMonitor {
public:
    /**
     * 初始化进程监控器
     * @return 成功返回true，失败返回false
     */
    bool initialize();
    
    /**
     * 启动进程监控
     * @return 成功返回true，失败返回false
     */
    bool start();
    
    /**
     * 停止进程监控
     */
    void stop();
    
    /**
     * 手动更新所有进程信息
     * @note 强制执行一次完整的进程扫描
     */
    void updateAllProcesses();
};
```

---

## 4. NetworkMonitor - 网络监控器接口

### 4.1 网络信息访问接口

```cpp
class NetworkMonitor {
public:
    /**
     * 获取指定进程的网络连接
     * @param pid 进程ID
     * @return 该进程的网络连接向量
     */
    std::vector<NetworkConnection> getProcessConnections(pid_t pid) const;
    
    /**
     * 获取所有网络连接
     * @return 系统中所有网络连接的向量
     */
    std::vector<SocketInfo> getAllConnections() const;
    
    /**
     * 获取网络统计信息
     * @return 网络统计结构体
     */
    NetworkStats getNetworkStats() const;
    
    /**
     * 获取监听端口列表
     * @return 所有监听端口的向量
     */
    std::vector<SocketInfo> getListeningPorts() const;
    
    /**
     * 获取网络接口信息
     * @return 网络接口信息向量
     */
    std::vector<NetworkInterface> getNetworkInterfaces() const;
};
```

### 4.2 监控控制接口

```cpp
class NetworkMonitor {
public:
    /**
     * 初始化网络监控器
     * @return 成功返回true，失败返回false
     */
    bool initialize();
    
    /**
     * 启动网络监控
     * @return 成功返回true，失败返回false
     */
    bool start();
    
    /**
     * 停止网络监控
     */
    void stop();
    
    /**
     * 更新网络连接信息
     * @note 手动触发一次网络连接扫描
     */
    void updateNetworkConnections();
    
    /**
     * 设置进程监控器引用
     * @param monitor 进程监控器指针
     * @note 用于建立网络连接与进程的关联
     */
    void setProcessMonitor(ProcessMonitor* monitor);
    
    /**
     * 获取事件总数
     * @return 网络事件总数
     */
    uint64_t getTotalEvents() const;
};
```

---

## 5. ApiServer - API服务器接口

### 5.1 服务器管理接口

```cpp
class ApiServer {
public:
    /**
     * 初始化API服务器
     * @param port 监听端口
     * @param monitor 主监控器指针
     * @return 成功返回true，失败返回false
     */
    bool initialize(uint16_t port, MainMonitor* monitor);
    
    /**
     * 启动API服务器
     * @return 成功返回true，失败返回false
     */
    bool start();
    
    /**
     * 停止API服务器
     */
    void stop();
    
    /**
     * 获取服务器运行状态
     * @return 运行中返回true，否则返回false
     */
    bool isRunning() const;
};
```

### 5.2 JsonBuilder工具接口

```cpp
class ApiServer::JsonBuilder {
public:
    /**
     * 开始JSON对象
     * @return JsonBuilder引用，支持链式调用
     */
    JsonBuilder& startObject();
    
    /**
     * 结束JSON对象
     * @return JsonBuilder引用，支持链式调用
     */
    JsonBuilder& endObject();
    
    /**
     * 开始JSON数组
     * @param key 数组键名，为空则创建匿名数组
     * @return JsonBuilder引用，支持链式调用
     */
    JsonBuilder& startArray(const std::string& key = "");
    
    /**
     * 结束JSON数组
     * @return JsonBuilder引用，支持链式调用
     */
    JsonBuilder& endArray();
    
    /**
     * 添加字符串字段
     * @param key 字段名
     * @param value 字符串值
     * @return JsonBuilder引用，支持链式调用
     */
    JsonBuilder& addString(const std::string& key, const std::string& value);
    
    /**
     * 添加数字字段
     * @param key 字段名
     * @param value 数值
     * @return JsonBuilder引用，支持链式调用
     */
    JsonBuilder& addNumber(const std::string& key, int64_t value);
    JsonBuilder& addNumber(const std::string& key, uint64_t value);
    JsonBuilder& addNumber(const std::string& key, double value);
    
    /**
     * 添加布尔字段
     * @param key 字段名
     * @param value 布尔值
     * @return JsonBuilder引用，支持链式调用
     */
    JsonBuilder& addBool(const std::string& key, bool value);
    
    /**
     * 添加null字段
     * @param key 字段名
     * @return JsonBuilder引用，支持链式调用
     */
    JsonBuilder& addNull(const std::string& key);
    
    /**
     * 转换为JSON字符串
     * @return 完整的JSON字符串
     */
    std::string toString() const;
};
```

---

## 6. 管理器类接口

### 6.1 FocusProcessManager - 重点进程管理器接口

```cpp
class FocusProcessManager {
public:
    /**
     * 获取单例实例
     * @return 管理器实例引用
     */
    static FocusProcessManager& getInstance();
    
    /**
     * 添加重点进程（按PID）
     * @param pid 进程ID
     */
    void addFocusProcess(pid_t pid);
    
    /**
     * 添加重点进程（按进程名）
     * @param process_name 进程名称
     */
    void addFocusProcess(const std::string& process_name);
    
    /**
     * 移除重点进程（按PID）
     * @param pid 进程ID
     */
    void removeFocusProcess(pid_t pid);
    
    /**
     * 移除重点进程（按进程名）
     * @param process_name 进程名称
     */
    void removeFocusProcess(const std::string& process_name);
    
    /**
     * 检查是否为重点进程
     * @param pid 进程ID
     * @return 是重点进程返回true，否则返回false
     */
    bool isFocusProcess(pid_t pid) const;
    
    /**
     * 检查是否为重点进程
     * @param process_name 进程名称
     * @return 是重点进程返回true，否则返回false
     */
    bool isFocusProcess(const std::string& process_name) const;
    
    /**
     * 获取重点进程PID列表
     * @return 重点进程PID向量
     */
    std::vector<pid_t> getFocusProcesses() const;
    
    /**
     * 获取重点进程名称列表
     * @return 重点进程名称向量
     */
    std::vector<std::string> getFocusProcessNames() const;
    
    /**
     * 从配置字符串加载
     * @param config_str 配置字符串，格式："pid1,pid2,name1,name2"
     */
    void loadFromConfig(const std::string& config_str);
    
    /**
     * 重新加载配置
     * @param config_str 新的配置字符串
     * @return 成功返回true，失败返回false
     */
    bool reloadConfig(const std::string& config_str);
    
    /**
     * 清空所有重点进程
     */
    void clear();
};
```

### 6.2 ProtectedPathManager - 受保护路径管理器接口

```cpp
class ProtectedPathManager {
public:
    /**
     * 获取单例实例
     * @return 管理器实例引用
     */
    static ProtectedPathManager& getInstance();
    
    /**
     * 添加受保护路径
     * @param path 要保护的路径
     */
    void addProtectedPath(const std::string& path);
    
    /**
     * 移除受保护路径
     * @param path 要移除保护的路径
     */
    void removeProtectedPath(const std::string& path);
    
    /**
     * 检查是否为受保护路径
     * @param path 要检查的路径
     * @return 是受保护路径返回true，否则返回false
     */
    bool isProtectedPath(const std::string& path) const;
    
    /**
     * 获取所有受保护路径
     * @return 受保护路径向量
     */
    std::vector<std::string> getProtectedPaths() const;
    
    /**
     * 处理违规行为
     * @param path 违规访问的路径
     * @param pid 违规进程ID
     * @param process_name 违规进程名称
     */
    void handleViolation(const std::string& path, pid_t pid, const std::string& process_name);
    
    /**
     * 设置是否终止违规进程
     * @param terminate true为KILL模式，false为LOG模式
     */
    void setTerminateViolatingProcesses(bool terminate);
    
    /**
     * 获取是否终止违规进程的设置
     * @return KILL模式返回true，LOG模式返回false
     */
    bool shouldTerminateViolatingProcesses() const;
    
    /**
     * 从配置字符串加载
     * @param config_str 配置字符串，格式："path1,path2,path3"
     */
    void loadFromConfig(const std::string& config_str);
    
    /**
     * 重新加载配置
     * @param config_str 新的配置字符串
     * @param terminate_violating_processes 是否终止违规进程
     * @return 成功返回true，失败返回false
     */
    bool reloadConfig(const std::string& config_str, bool terminate_violating_processes);
    
    /**
     * 清空所有受保护路径
     */
    void clear();
};
```

### 6.3 ProcessExclusionManager - 进程排除管理器接口

```cpp
class ProcessExclusionManager {
public:
    /**
     * 获取单例实例（通过单例管理器）
     * 实际调用：ProcessExclusionManagerSingleton::getInstance()
     * @return 管理器实例引用
     */
    
    /**
     * 添加PID排除规则
     * @param pid 要排除的进程ID
     */
    void addExclusionByPid(pid_t pid);
    
    /**
     * 添加进程名排除规则
     * @param name 要排除的进程名
     */
    void addExclusionByName(const std::string& name);
    
    /**
     * 添加路径排除规则
     * @param path 要排除的可执行文件路径
     */
    void addExclusionByPath(const std::string& path);
    
    /**
     * 添加正则表达式排除规则
     * @param pattern 正则表达式模式
     */
    void addExclusionByPattern(const std::string& pattern);
    
    /**
     * 检查进程是否被排除
     * @param pid 进程ID
     * @return 被排除返回true，否则返回false
     */
    bool isExcluded(pid_t pid) const;
    
    /**
     * 检查进程名是否被排除
     * @param name 进程名
     * @return 被排除返回true，否则返回false
     */
    bool isExcludedByName(const std::string& name) const;
    
    /**
     * 检查路径是否被排除
     * @param executable_path 可执行文件路径
     * @return 被排除返回true，否则返回false
     */
    bool isExcludedByPath(const std::string& executable_path) const;
    
    /**
     * 从配置字符串加载排除规则
     * @param config_str 配置字符串
     */
    void loadFromConfig(const std::string& config_str);
    
    /**
     * 添加默认系统进程排除规则
     */
    void addDefaultExclusions();
    
    /**
     * 清空所有排除规则
     */
    void clearAllExclusions();
};
```

---

## 7. 数据结构访问接口

### 7.1 ProcessInfo - 进程信息接口

```cpp
struct ProcessInfo {
    /**
     * 更新网络连接信息
     * @param connections 新的网络连接列表
     */
    void updateNetworkConnections(const std::vector<NetworkConnection>& connections);
    
    /**
     * 设置为重点进程
     * @param focus 是否设为重点进程
     */
    void setFocusProcess(bool focus);
    
    /**
     * 获取重点进程文件信息
     * @return 重点进程文件信息指针，非重点进程返回nullptr
     */
    FocusProcessFileInfo* getFocusFileInfo() const;
    
    /**
     * 获取基础信息摘要
     * @return 包含基础信息的键值对映射
     */
    std::map<std::string, std::string> getBasicInfoSummary() const;
};
```

### 7.2 FocusProcessFileInfo - 重点进程文件信息接口

```cpp
struct FocusProcessFileInfo {
    /**
     * 记录文件打开时的熵值
     * @param file_path 文件路径
     * @param entropy 计算得到的熵值
     * @param file_size 文件大小
     */
    void recordFileOpen(const std::string& file_path, double entropy, size_t file_size);
    
    /**
     * 记录文件写入关闭时的熵值
     * @param file_path 文件路径
     * @param entropy 计算得到的熵值
     */
    void recordFileCloseWrite(const std::string& file_path, double entropy);
    
    /**
     * 获取指定文件的熵值信息
     * @param file_path 文件路径
     * @return 文件熵值信息指针，不存在返回nullptr
     */
    FileEntropyInfo* getFileEntropyInfo(const std::string& file_path);
    
    /**
     * 获取所有文件熵值信息
     * @return 所有文件熵值信息的向量
     */
    std::vector<FileEntropyInfo> getAllFileEntropyInfo() const;
    
    /**
     * 获取平均读取熵值
     * @return 平均读取熵值
     */
    double getAverageReadEntropy() const;
    
    /**
     * 获取平均写入熵值
     * @return 平均写入熵值
     */
    double getAverageWriteEntropy() const;
    
    /**
     * 获取熵值统计摘要
     * @return 熵值统计摘要结构体
     */
    EntropyStatsSummary getEntropyStatsSummary() const;
    
    /**
     * 记录删除操作
     * @param path 被删除的文件路径
     */
    void recordDelete(const std::string& path);
    
    /**
     * 记录重命名操作
     * @param old_path 原文件路径
     * @param new_path 新文件路径
     */
    void recordRename(const std::string& old_path, const std::string& new_path);
    
    /**
     * 添加文件路径到历史记录
     * @param path 文件路径
     */
    void addFilePathToHistory(const std::string& path);
    
    /**
     * 清理过期数据
     */
    void cleanup();
};
```

这些接口构成了PSFSMON-L系统的完整API，为系统的各个组件提供了标准化的交互方式，确保了系统的模块化和可维护性。
