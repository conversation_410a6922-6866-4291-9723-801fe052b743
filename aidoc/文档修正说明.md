# PSFSMON-L 文档修正说明

## 修正概述

在对生成的文档进行源代码核对后，发现了一些与实际代码不符的错误。本文档记录了所有已发现和修正的错误，确保文档的准确性。

## 已修正的错误

### 1. 数据结构类型错误

**错误位置**: 类功能说明文档.md - FocusProcessManager成员变量
**错误内容**: 
```cpp
std::set<pid_t> focus_pids_;
std::set<std::string> focus_names_;
```

**正确内容**:
```cpp
std::unordered_set<pid_t> focus_pids_;
std::unordered_set<std::string> focus_names_;
```

**修正原因**: 实际代码中使用的是`std::unordered_set`而不是`std::set`，这是为了提高查找性能。

### 2. 数据结构类型错误

**错误位置**: 类功能说明文档.md - ProtectedPathManager成员变量
**错误内容**:
```cpp
std::set<std::string> protected_paths_;
```

**正确内容**:
```cpp
std::unordered_set<std::string> protected_paths_;
```

**修正原因**: 实际代码中使用的是`std::unordered_set`。

### 3. ProcessExclusionManager单例模式错误

**错误位置**: 多个文档中关于ProcessExclusionManager的单例访问
**错误内容**: 
```cpp
ProcessExclusionManager::getInstance()
```

**正确内容**:
```cpp
ProcessExclusionManagerSingleton::getInstance()
```

**修正原因**: ProcessExclusionManager不是直接的单例类，而是通过ProcessExclusionManagerSingleton管理器来实现单例访问。

### 4. ProcessExclusionManager成员变量错误

**错误位置**: 类功能说明文档.md - ProcessExclusionManager成员变量
**错误内容**: 缺少一些成员变量

**正确内容**: 添加了以下成员变量：
```cpp
std::vector<std::string> excluded_pattern_strings_; // 原始正则表达式字符串
static const std::vector<std::string> SYSTEM_PATHS;     // 系统路径列表
static const std::vector<std::string> SYSTEM_PATTERNS;  // 系统模式列表
```

**修正原因**: 实际代码中包含这些额外的成员变量。

### 5. ApiServer成员变量名错误

**错误位置**: 类功能说明文档.md - ApiServer成员变量
**错误内容**:
```cpp
MainMonitor* monitor_;
```

**正确内容**:
```cpp
MainMonitor* main_monitor_;
```

**修正原因**: 实际代码中的成员变量名是`main_monitor_`而不是`monitor_`。

### 6. ProcessExclusionManager数据类型错误

**错误位置**: 类功能说明文档.md - ProcessExclusionManager成员变量
**错误内容**:
```cpp
std::set<pid_t> excluded_pids_;
```

**正确内容**:
```cpp
std::unordered_set<pid_t> excluded_pids_;
```

**修正原因**: 实际代码中PID集合使用的是`std::unordered_set`。

## 验证的正确内容

以下内容经过核对，确认与源代码一致：

### 1. LogManager类
- ✅ 确实存在并且是单例模式
- ✅ 包含LogLevel和LogType枚举
- ✅ 支持文件和控制台双重输出
- ✅ 具有完整的日志方法集合

### 2. Utils类
- ✅ 确实存在并在main_monitor.cpp中实现
- ✅ 包含时间、文件、字符串、进程等工具方法
- ✅ 被多个模块广泛使用

### 3. HttpServer类
- ✅ 确实存在并有完整的实现
- ✅ 支持线程池和路由系统
- ✅ 被ApiServer组合使用

### 4. JsonBuilder类
- ✅ 确实是ApiServer的嵌套类
- ✅ 实现了流式JSON构建接口
- ✅ 支持链式调用

### 5. 管理器类单例模式
- ✅ FocusProcessManager: 直接单例模式
- ✅ ProtectedPathManager: 直接单例模式
- ✅ ProcessExclusionManager: 通过ProcessExclusionManagerSingleton管理
- ✅ ProcessMonitorManager: 单例管理器模式
- ✅ NetworkMonitorManager: 单例管理器模式

### 6. 数据结构
- ✅ FileEntropyInfo: 结构体定义正确
- ✅ EntropyStatsSummary: 结构体定义正确
- ✅ FocusProcessFileInfo: 结构体定义正确
- ✅ ProcessInfo: 结构体定义正确

## 修正影响

### 对开发的影响
1. **API调用**: ProcessExclusionManager的访问方式需要通过单例管理器
2. **数据结构**: 使用正确的容器类型（unordered_set vs set）
3. **成员变量**: 使用正确的成员变量名进行访问

### 对架构理解的影响
1. **单例模式**: 不是所有管理器都是直接单例，有些通过管理器类实现
2. **性能考虑**: unordered_set的使用体现了对查找性能的优化
3. **代码组织**: 单例管理器模式提供了更好的生命周期管理

## 质量保证措施

为了避免类似错误，建议：

1. **代码核对**: 每次文档更新后都要与源代码进行核对
2. **自动化验证**: 考虑编写脚本自动验证文档与代码的一致性
3. **版本同步**: 确保文档版本与代码版本同步更新
4. **交叉验证**: 多人审查文档的准确性

## 修正状态

- ✅ 类功能说明文档.md - 已修正
- ✅ 类层次关系说明.md - 已修正  
- ✅ 类调用关系说明.md - 已修正
- ✅ 类调用接口说明.md - 已修正
- ✅ 类层次关系图.mmd - 已修正

所有发现的错误都已修正，文档现在与源代码保持一致。
