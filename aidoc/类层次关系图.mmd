classDiagram
    class MainMonitor {
        -MonitorConfig config_
        -FileMonitor* file_monitor_
        -NetworkMonitor* network_monitor_
        -MonitorStats stats_
        -thread status_thread_
        -atomic~bool~ status_thread_running_
        +initialize(config) bool
        +start() bool
        +stop() void
        +reloadConfig(config) bool
        +getStatus() MonitorStatus
        +getStats() MonitorStats
        +getProcessMonitor() ProcessMonitor*
        +getFileMonitor() FileMonitor*
        +getNetworkMonitor() NetworkMonitor*
        +daemonize() bool
        +signalHandler(signal) void
    }
    
    class FileMonitor {
        -MonitorConfig* config_
        -atomic~bool~ running_
        -int protected_fanotify_fd_
        -int focus_fanotify_fd_
        -thread protected_monitor_thread_
        -thread focus_monitor_thread_
        -FileEventStats event_stats_
        -vector~FileEvent~ recent_events_
        -function~void(FileEvent)~ event_callback_
        +initialize(config) bool
        +start() bool
        +stop() void
        +reloadConfig(config) bool
        +setEventCallback(callback) void
        +getTotalEvents() uint64_t
        +getEventStats() FileEventStats
        +getRecentEvents(limit) vector~FileEvent~
        +addProtectedPathMark(path) bool
        +removeProtectedPathMark(path) void
    }
    
    class ProcessMonitor {
        -map~pid_t, shared_ptr~ProcessInfo~~ processes_
        -thread monitor_thread_
        -atomic~bool~ running_
        -atomic~bool~ stop_requested_
        -uint32_t update_interval_ms_
        -map~pid_t, CpuStats~ process_cpu_stats_
        -uint32_t num_cpu_cores_
        -uint64_t page_size_
        +initialize() bool
        +start() bool
        +stop() void
        +updateAllProcesses() void
        +getProcess(pid) shared_ptr~ProcessInfo~
        +getAllProcesses() vector~shared_ptr~ProcessInfo~~
        +getProcessTree() shared_ptr~ProcessTreeNode~
        +findProcessesByName(name) vector~shared_ptr~ProcessInfo~~
        +setFocusProcess(pid, focus) void
        +setFocusProcess(name, focus) void
        +isFocusProcess(pid) bool
        +getFocusProcesses() vector~pid_t~
    }
    
    class NetworkMonitor {
        -thread monitor_thread_
        -atomic~bool~ running_
        -ProcessMonitor* process_monitor_
        -vector~SocketInfo~ current_connections_
        -map~pid_t, vector~uint32_t~~ process_to_sockets_
        -map~uint32_t, pid_t~ socket_to_process_
        -vector~NetworkInterface~ network_interfaces_
        -atomic~uint64_t~ total_network_events_
        +initialize() bool
        +start() bool
        +stop() void
        +updateNetworkConnections() void
        +getProcessConnections(pid) vector~NetworkConnection~
        +getAllConnections() vector~SocketInfo~
        +getNetworkStats() NetworkStats
        +getListeningPorts() vector~SocketInfo~
        +getNetworkInterfaces() vector~NetworkInterface~
        +setProcessMonitor(monitor) void
        +getTotalEvents() uint64_t
    }
    
    class ApiServer {
        -unique_ptr~HttpServer~ http_server_
        -MainMonitor* main_monitor_
        -atomic~bool~ running_
        +initialize(port, monitor) bool
        +start() bool
        +stop() void
        +isRunning() bool
    }
    
    class HttpServer {
        -uint16_t port_
        -string bind_address_
        -int server_socket_
        -atomic~bool~ running_
        -map routes_
        -vector~thread~ worker_threads_
        -queue~int~ client_sockets_
        -size_t max_worker_threads_
        +initialize(port, address) bool
        +start() bool
        +stop() void
        +registerRoute(method, path, handler) void
        +setStaticDirectory(directory) void
        +setRequestTimeout(timeout) void
    }
    
    MainMonitor *-- FileMonitor : owns
    MainMonitor *-- NetworkMonitor : owns
    MainMonitor --> ApiServer : creates
    ApiServer *-- HttpServer : owns
    NetworkMonitor --> ProcessMonitor : depends on
    
    class ProcessInfo {
        +pid_t pid
        +pid_t ppid
        +string process_name
        +string executable_path
        +string command_line
        +string cwd
        +uid_t uid
        +gid_t gid
        +double cpu_usage
        +uint64_t memory_usage
        +uint64_t virtual_memory
        +string state
        +uint64_t start_time
        +vector~pid_t~ children
        +vector~NetworkConnection~ network_connections
        +bool is_focus_process
        +unique_ptr~FocusProcessFileInfo~ focus_file_info
        +updateNetworkConnections(connections) void
        +setFocusProcess(focus) void
        +getFocusFileInfo() FocusProcessFileInfo*
        +getBasicInfoSummary() map~string, string~
    }
    
    class FocusProcessFileInfo {
        +double total_read_entropy
        +double total_write_entropy
        +uint32_t read_entropy_count
        +uint32_t write_entropy_count
        +uint32_t delete_count
        +uint32_t rename_count
        +vector~string~ path_history
        +vector~string~ rename_extensions
        +map~uint64_t, FileEntropyInfo~ file_entropy_map
        +mutable mutex file_entropy_mutex
        +mutable mutex mutex
        +recordFileOpen(path, entropy, size) void
        +recordFileCloseWrite(path, entropy) void
        +getFileEntropyInfo(path) FileEntropyInfo*
        +getAllFileEntropyInfo() vector~FileEntropyInfo~
        +getAverageReadEntropy() double
        +getAverageWriteEntropy() double
        +getEntropyStatsSummary() EntropyStatsSummary
        +recordDelete(path) void
        +recordRename(old_path, new_path) void
        +addFilePathToHistory(path) void
        +cleanup() void
    }
    
    ProcessMonitor *-- ProcessInfo : manages
    ProcessInfo *-- FocusProcessFileInfo : owns
