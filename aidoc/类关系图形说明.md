# PSFSMON-L 类关系图形说明文档

## 文档概述

本文档通过Mermaid图形化方式展示PSFSMON-L项目中所有类的关系结构，包括类层次关系图、调用关系图、数据流向图和组件交互图。这些图形化表示有助于直观理解系统架构和各组件间的关系。

---

## 1. 系统整体架构图

```mermaid
graph TB
    subgraph "应用层"
        A[main.cpp 程序入口]
    end
    
    subgraph "控制层"
        B[MainMonitor 主监控器]
        C[MonitorConfig 配置管理]
    end
    
    subgraph "监控层"
        D[FileMonitor 文件监控器]
        E[ProcessMonitor 进程监控器]
        F[NetworkMonitor 网络监控器]
    end
    
    subgraph "管理层"
        G[FocusProcessManager 重点进程管理器]
        H[ProtectedPathManager 受保护路径管理器]
        I[ProcessExclusionManager 进程排除管理器]
    end
    
    subgraph "服务层"
        J[ApiServer API服务器]
        K[HttpServer HTTP服务器]
    end
    
    subgraph "工具层"
        L[JsonBuilder JSON构建器]
        M[LogManager 日志管理器]
        N[Utils 工具类]
    end
    
    subgraph "数据层"
        O[ProcessInfo 进程信息]
        P[FocusProcessFileInfo 重点进程文件信息]
        Q[FileEvent 文件事件]
        R[NetworkConnection 网络连接]
    end
    
    A --> B
    B --> C
    B --> D
    B --> E
    B --> F
    B --> J
    
    D --> G
    D --> H
    D --> I
    E --> G
    F --> E
    
    J --> K
    J --> L
    
    D --> M
    E --> M
    F --> M
    G --> M
    H --> M
    I --> M
    
    E --> O
    O --> P
    D --> Q
    F --> R
    
    style A fill:#339af0,color:#ffffff
    style B fill:#9775fa,color:#ffffff
    style D fill:#51cf66,color:#000000
    style E fill:#51cf66,color:#000000
    style F fill:#51cf66,color:#000000
    style J fill:#ff922b,color:#ffffff
```

---

## 2. 类层次关系图

```mermaid
classDiagram
    class MainMonitor {
        -MonitorConfig config_
        -FileMonitor* file_monitor_
        -NetworkMonitor* network_monitor_
        -MonitorStats stats_
        +initialize(config) bool
        +start() bool
        +stop() void
        +getStatus() MonitorStatus
    }
    
    class FileMonitor {
        -int protected_fanotify_fd_
        -int focus_fanotify_fd_
        -thread protected_monitor_thread_
        -thread focus_monitor_thread_
        -FileEventStats event_stats_
        +initialize(config) bool
        +start() bool
        +stop() void
        +getTotalEvents() uint64_t
    }
    
    class ProcessMonitor {
        -map~pid_t, ProcessInfo~ processes_
        -thread monitor_thread_
        -atomic~bool~ running_
        +getProcess(pid) ProcessInfo*
        +getAllProcesses() vector~ProcessInfo~
        +setFocusProcess(pid, focus) void
    }
    
    class NetworkMonitor {
        -ProcessMonitor* process_monitor_
        -vector~SocketInfo~ current_connections_
        -thread monitor_thread_
        +getProcessConnections(pid) vector~NetworkConnection~
        +getAllConnections() vector~SocketInfo~
        +updateNetworkConnections() void
    }
    
    class ApiServer {
        -HttpServer* http_server_
        -MainMonitor* monitor_
        -atomic~bool~ running_
        +initialize(port, monitor) bool
        +start() bool
        +stop() void
    }
    
    class HttpServer {
        -uint16_t port_
        -vector~thread~ worker_threads_
        -map routes_
        +registerRoute(method, path, handler) void
        +start() bool
        +stop() void
    }
    
    MainMonitor *-- FileMonitor : owns
    MainMonitor *-- NetworkMonitor : owns
    MainMonitor --> ApiServer : creates
    ApiServer *-- HttpServer : owns
    NetworkMonitor --> ProcessMonitor : depends on
    
    class ProcessInfo {
        +pid_t pid
        +string process_name
        +double cpu_usage
        +uint64_t memory_usage
        +bool is_focus_process
        +unique_ptr~FocusProcessFileInfo~ focus_file_info
        +setFocusProcess(focus) void
        +getFocusFileInfo() FocusProcessFileInfo*
    }
    
    class FocusProcessFileInfo {
        +double total_read_entropy
        +double total_write_entropy
        +uint32_t read_entropy_count
        +uint32_t write_entropy_count
        +vector~string~ path_history
        +map~uint64_t, FileEntropyInfo~ file_entropy_map
        +recordFileOpen(path, entropy, size) void
        +recordFileCloseWrite(path, entropy) void
    }
    
    ProcessMonitor *-- ProcessInfo : manages
    ProcessInfo *-- FocusProcessFileInfo : owns
```

---

## 3. 管理器类关系图

```mermaid
classDiagram
    class FocusProcessManager {
        <<singleton>>
        -set~pid_t~ focus_pids_
        -set~string~ focus_names_
        -mutex mutex_
        +getInstance() FocusProcessManager&
        +addFocusProcess(pid) void
        +addFocusProcess(name) void
        +isFocusProcess(pid) bool
        +loadFromConfig(config_str) void
    }
    
    class ProtectedPathManager {
        <<singleton>>
        -set~string~ protected_paths_
        -bool terminate_violating_processes_
        -mutex mutex_
        +getInstance() ProtectedPathManager&
        +addProtectedPath(path) void
        +isProtectedPath(path) bool
        +handleViolation(path, pid, name) void
        +setTerminateViolatingProcesses(terminate) void
    }
    
    class ProcessExclusionManager {
        <<singleton>>
        -set~pid_t~ excluded_pids_
        -set~string~ excluded_names_
        -set~string~ excluded_paths_
        -vector~regex~ excluded_patterns_
        +getInstance() ProcessExclusionManager&
        +addExclusionByPid(pid) void
        +addExclusionByName(name) void
        +isExcluded(pid) bool
        +addDefaultExclusions() void
    }
    
    FileMonitor ..> FocusProcessManager : queries
    FileMonitor ..> ProtectedPathManager : queries
    FileMonitor ..> ProcessExclusionManager : queries
    
    class LogManager {
        <<singleton>>
        +getInstance() LogManager&
        +info(message) void
        +error(message) void
        +debug(message) void
    }
    
    FocusProcessManager ..> LogManager : uses
    ProtectedPathManager ..> LogManager : uses
    ProcessExclusionManager ..> LogManager : uses
```

---

## 4. 数据流向图

```mermaid
flowchart TD
    subgraph "Linux内核"
        A[Fanotify事件]
        B[/proc文件系统]
        C[/proc/net/*]
    end
    
    subgraph "事件采集层"
        D[FileMonitor双线程]
        E[ProcessMonitor扫描线程]
        F[NetworkMonitor扫描线程]
    end
    
    subgraph "数据处理层"
        G[事件过滤和分类]
        H[进程信息解析]
        I[网络连接解析]
    end
    
    subgraph "管理器层"
        J[FocusProcessManager]
        K[ProtectedPathManager]
        L[ProcessExclusionManager]
    end
    
    subgraph "数据存储层"
        M[ProcessInfo集合]
        N[FocusProcessFileInfo]
        O[FileEvent历史]
        P[NetworkConnection集合]
    end
    
    subgraph "API输出层"
        Q[ApiServer]
        R[JsonBuilder]
        S[HTTP响应]
    end
    
    A --> D
    B --> E
    C --> F
    
    D --> G
    E --> H
    F --> I
    
    G --> J
    G --> K
    G --> L
    
    H --> M
    M --> N
    G --> O
    I --> P
    
    M --> Q
    N --> Q
    O --> Q
    P --> Q
    
    Q --> R
    R --> S
    
    style A fill:#ffd43b,color:#000000
    style B fill:#ffd43b,color:#000000
    style C fill:#ffd43b,color:#000000
    style D fill:#339af0,color:#ffffff
    style E fill:#339af0,color:#ffffff
    style F fill:#339af0,color:#ffffff
    style Q fill:#ffd43b,color:#000000
    style S fill:#339af0,color:#ffffff
```

---

## 5. 文件监控双Fanotify Group架构图

```mermaid
graph TB
    subgraph "FileMonitor双Group架构"
        subgraph "受保护路径监控Group"
            A[protected_fanotify_fd_]
            B[protected_monitor_thread_]
            C[processProtectedPathEvents循环]
        end
        
        subgraph "重点进程监控Group"
            D[focus_fanotify_fd_]
            E[focus_monitor_thread_]
            F[processFocusProcessEvents循环]
        end
    end
    
    subgraph "Linux内核Fanotify"
        G[FAN_CLASS_NOTIF Group 1]
        H[FAN_CLASS_NOTIF Group 2]
    end
    
    subgraph "事件处理"
        I[handleProtectedPathEvent]
        J[handleFocusProcessEvent]
    end
    
    subgraph "管理器协作"
        K[ProtectedPathManager]
        L[FocusProcessManager]
        M[ProcessExclusionManager]
    end
    
    subgraph "数据输出"
        N[违规处理/进程终止]
        O[熵值计算和记录]
        P[事件统计更新]
    end
    
    G --> A
    H --> D
    A --> B
    D --> E
    B --> C
    E --> F
    C --> I
    F --> J
    
    I --> K
    I --> M
    J --> L
    J --> M
    
    K --> N
    L --> O
    I --> P
    J --> P
    
    style A fill:#ff6b6b,color:#ffffff
    style D fill:#51cf66,color:#000000
    style B fill:#ff6b6b,color:#ffffff
    style E fill:#51cf66,color:#000000
    style C fill:#ff6b6b,color:#ffffff
    style F fill:#51cf66,color:#000000
```

---

## 6. API请求处理流程图

```mermaid
sequenceDiagram
    participant Client as HTTP客户端
    participant HttpServer as HttpServer
    participant ApiServer as ApiServer
    participant MainMonitor as MainMonitor
    participant ProcessMonitor as ProcessMonitor
    participant JsonBuilder as JsonBuilder
    
    Client->>HttpServer: HTTP请求
    HttpServer->>HttpServer: 解析请求
    HttpServer->>ApiServer: 路由分发
    
    alt 进程信息请求
        ApiServer->>MainMonitor: getProcessMonitor()
        MainMonitor->>ProcessMonitor: 返回ProcessMonitor*
        ApiServer->>ProcessMonitor: getAllProcesses()
        ProcessMonitor->>ApiServer: 返回进程列表
    end
    
    ApiServer->>JsonBuilder: 构建JSON响应
    JsonBuilder->>JsonBuilder: startObject()
    JsonBuilder->>JsonBuilder: addArray("processes")
    
    loop 每个进程
        JsonBuilder->>JsonBuilder: addObject(进程信息)
    end
    
    JsonBuilder->>JsonBuilder: endArray()
    JsonBuilder->>JsonBuilder: endObject()
    JsonBuilder->>ApiServer: toString()
    
    ApiServer->>HttpServer: HTTP响应
    HttpServer->>Client: 返回JSON数据
```

---

## 7. 重点进程熵值计算流程图

```mermaid
flowchart TD
    A[文件操作事件] --> B{是否为重点进程?}
    B -->|否| C[跳过处理]
    B -->|是| D{事件类型?}
    
    D -->|OPEN| E[计算文件原始熵值]
    D -->|CLOSE_WRITE| F[计算文件最终熵值]
    D -->|其他| G[记录路径历史]
    
    E --> H[FileEntropyCalculator::calculateFileEntropy]
    F --> H
    
    H --> I[读取文件内容]
    I --> J[分块计算熵值]
    J --> K[Shannon熵值公式]
    K --> L[返回熵值结果]
    
    L --> M{事件类型?}
    M -->|OPEN| N[recordFileOpen]
    M -->|CLOSE_WRITE| O[recordFileCloseWrite]
    
    N --> P[存储到file_entropy_map]
    O --> P
    G --> Q[添加到path_history]
    
    P --> R[更新统计信息]
    Q --> R
    R --> S[记录调试日志]
    
    style A fill:#339af0,color:#ffffff
    style H fill:#9775fa,color:#ffffff
    style K fill:#51cf66,color:#000000
    style P fill:#ff922b,color:#ffffff
    style S fill:#f783ac,color:#ffffff
```

这些图形化表示清晰地展示了PSFSMON-L系统的架构设计、组件关系和数据流向，为理解和维护系统提供了直观的参考。
