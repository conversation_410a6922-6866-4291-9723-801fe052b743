# PSFSMON-L 类调用关系说明文档

## 文档概述

本文档详细描述了PSFSMON-L项目中所有类之间的调用关系、数据流向和交互模式。通过分析类的调用关系，可以深入理解系统的运行机制和各模块间的协作方式。

## 系统调用关系总览

PSFSMON-L的类调用关系呈现出清晰的层次化结构：

```
main() 程序入口
    ↓
MainMonitor (系统控制中心)
    ├── ProcessMonitorManager::getInstance()
    ├── FileMonitor
    ├── NetworkMonitor  
    └── ApiServer
        └── HttpServer
            └── JsonBuilder

监控数据流向:
FileMonitor → FocusProcessManager → ProcessInfo
FileMonitor → ProtectedPathManager → 违规处理
ProcessMonitor → ProcessInfo → NetworkMonitor
NetworkMonitor → ProcessInfo (更新网络连接)

API数据流向:
ApiServer → MainMonitor → 各监控器 → 数据结构 → JsonBuilder → HTTP响应
```

---

## 1. 系统启动调用链

### 1.1 主程序启动流程

```cpp
// main.cpp 启动调用链
int main(int argc, char* argv[]) {
    // 1. 配置解析和初始化
    MonitorConfig config = parseCommandLine(argc, argv);
    
    // 2. 创建主监控器实例
    MainMonitor monitor;
    
    // 3. 初始化系统
    monitor.initialize(config);
        ├── monitor.checkPermissions()
        ├── monitor.checkSystemCompatibility()
        ├── monitor.initializeProcessMonitor()
        │   └── ProcessMonitorManager::getInstance().initialize()
        ├── monitor.initializeFileMonitor()
        │   └── file_monitor_->initialize(config)
        ├── monitor.initializeNetworkMonitor()
        │   └── network_monitor_->initialize()
        └── monitor.setupSignalHandlers()
    
    // 4. 启动监控服务
    monitor.start();
        ├── monitor.startProcessMonitor()
        ├── monitor.startFileMonitor()
        ├── monitor.startNetworkMonitor()
        └── ApiServer启动 (如果启用)
    
    // 5. 主循环或守护进程模式
    if (config.daemon_mode) {
        monitor.daemonize();
    }
    
    // 6. 等待信号或用户输入
    monitor.waitForShutdown();
    
    // 7. 优雅关闭
    monitor.stop();
}
```

### 1.2 MainMonitor初始化调用详情

```cpp
bool MainMonitor::initialize(const MonitorConfig& config) {
    // 配置存储
    config_ = config;
    
    // 权限检查调用链
    checkPermissions()
        └── hasRootPrivileges()
    
    // 系统兼容性检查调用链  
    checkSystemCompatibility()
        ├── checkKernelVersion()
        └── checkRequiredFeatures()
    
    // 进程监控器初始化调用链
    initializeProcessMonitor()
        └── ProcessMonitorManager::getInstance()
            ├── ProcessMonitorManager::initialize()
            └── ProcessMonitor::initialize()
                ├── initializeSystemInfo()
                └── 创建进程扫描线程
    
    // 文件监控器初始化调用链
    initializeFileMonitor()
        └── FileMonitor::initialize(&config_)
            ├── initializeProtectedPathGroup()
            │   ├── fanotify_init()
            │   └── ProtectedPathManager::getInstance()
            ├── initializeFocusProcessGroup()
            │   ├── fanotify_init()
            │   └── FocusProcessManager::getInstance()
            └── 创建双监控线程
    
    // 网络监控器初始化调用链
    initializeNetworkMonitor()
        └── NetworkMonitor::initialize()
            ├── setProcessMonitor(getProcessMonitor())
            └── 创建网络扫描线程
    
    return true;
}
```

---

## 2. 文件监控调用关系

### 2.1 FileMonitor核心调用链

```cpp
// FileMonitor双线程架构调用关系
FileMonitor::start() {
    // 启动受保护路径监控线程
    protected_monitor_thread_ = std::thread([this]() {
        processProtectedPathEvents();
    });
    
    // 启动重点进程监控线程  
    focus_monitor_thread_ = std::thread([this]() {
        processFocusProcessEvents();
    });
}

// 受保护路径事件处理调用链
void FileMonitor::processProtectedPathEvents() {
    while (running_) {
        // 1. 读取fanotify事件
        read(protected_fanotify_fd_, buffer, buffer_size);
        
        // 2. 处理事件缓冲区
        processEventBuffer(buffer, buffer_len, true);
            └── handleProtectedPathEvent(metadata)
                ├── 获取文件路径: readlink(/proc/self/fd/metadata->fd)
                ├── 获取进程信息: getProcessNameFromPid(metadata->pid)
                ├── 检查排除规则: ProcessExclusionManagerSingleton::getInstance().isExcluded()
                ├── 检查受保护路径: ProtectedPathManager::getInstance().isProtectedPath()
                └── 处理违规: ProtectedPathManager::getInstance().handleViolation()
                    ├── 记录违规日志: LogManager::getInstance().info()
                    └── 终止进程 (如果启用): kill(pid, SIGTERM)
    }
}

// 重点进程事件处理调用链
void FileMonitor::processFocusProcessEvents() {
    while (running_) {
        // 1. 读取fanotify事件
        read(focus_fanotify_fd_, buffer, buffer_size);
        
        // 2. 处理事件缓冲区
        processEventBuffer(buffer, buffer_len, false);
            └── handleFocusProcessEvent(metadata)
                ├── 检查重点进程: FocusProcessManager::getInstance().isFocusProcess()
                ├── 获取进程信息: ProcessMonitorManager::getInstance().getProcess()
                ├── 处理文件操作: processFocusProcessFileOperation(event)
                │   ├── 获取重点进程文件信息: process->getFocusFileInfo()
                │   ├── 计算熵值: calculateAndRecordEntropy(event)
                │   │   ├── FileEntropyCalculator::calculateFileEntropy()
                │   │   ├── FocusProcessFileInfo::recordFileOpen()
                │   │   └── FocusProcessFileInfo::recordFileCloseWrite()
                │   └── 记录路径历史: addFilePathToHistory()
                └── 触发事件回调: event_callback_(event)
    }
}
```

### 2.2 文件熵值计算调用链

```cpp
// 熵值计算完整调用链
void FileMonitor::calculateAndRecordEntropy(FileEvent& event) {
    // 1. 计算文件熵值
    double entropy = FileEntropyCalculator::calculateFileEntropy(event.file_path);
        └── FileEntropyCalculator::calculateFileEntropyInChunks()
            └── FileEntropyCalculator::calculateBufferEntropy()
                └── FileEntropyCalculator::computeEntropy()
    
    // 2. 获取重点进程文件信息
    ProcessInfo* process = ProcessMonitorManager::getInstance()
                          .getProcessMonitor()
                          ->getProcess(event.pid);
    FocusProcessFileInfo* file_info = process->getFocusFileInfo();
    
    // 3. 记录熵值信息
    if (event.type == FileEvent::OPEN) {
        file_info->recordFileOpen(event.file_path, entropy, file_size);
    } else if (event.type == FileEvent::CLOSE && event.mask & FAN_CLOSE_WRITE) {
        file_info->recordFileCloseWrite(event.file_path, entropy);
    }
    
    // 4. 更新统计信息
    event_stats_.entropy_calculations++;
    
    // 5. 记录调试日志
    LogManager::getInstance().debug("熵值计算完成: " + 
                                   event.file_path + " = " + 
                                   std::to_string(entropy));
}
```

---

## 3. 进程监控调用关系

### 3.1 ProcessMonitor核心调用链

```cpp
// ProcessMonitor监控循环调用链
void ProcessMonitor::monitorLoop() {
    while (running_) {
        // 1. 扫描进程
        scanProcesses();
            ├── getPidList() // 获取/proc下的PID列表
            ├── 对每个PID调用: handleNewProcess(pid) 或 更新现有进程
            └── cleanupDeadProcesses() // 清理已退出的进程
        
        // 2. 处理新进程
        handleNewProcess(pid);
            ├── 创建ProcessInfo对象
            ├── readProcessInfo(pid, process)
            │   ├── readProcessStatus(pid, process)
            │   ├── readProcessStat(pid, process)
            │   ├── readProcessCmdline(pid, process)
            │   ├── readProcessCwd(pid, process)
            │   └── readProcessExe(pid, process)
            ├── 检查是否为重点进程: FocusProcessManager::getInstance().isFocusProcess(pid)
            ├── 如果是重点进程: process->setFocusProcess(true)
            └── 存储到processes_映射中
        
        // 3. 更新性能统计
        updatePerformanceStats();
            └── calculateCpuUsage() // 计算CPU使用率
        
        // 4. 休眠等待下次扫描
        std::this_thread::sleep_for(std::chrono::milliseconds(update_interval_ms_));
    }
}

// 进程信息读取调用链详情
bool ProcessMonitor::readProcessInfo(pid_t pid, std::shared_ptr<ProcessInfo> process) {
    // 读取/proc/[pid]/status
    readProcessStatus(pid, process);
        └── 解析Name, Pid, PPid, Uid, Gid等字段
    
    // 读取/proc/[pid]/stat  
    readProcessStat(pid, process);
        └── 解析CPU时间、内存使用、进程状态等
    
    // 读取/proc/[pid]/cmdline
    readProcessCmdline(pid, process);
        └── 获取完整命令行参数
    
    // 读取/proc/[pid]/cwd
    readProcessCwd(pid, process);
        └── readlink获取当前工作目录
    
    // 读取/proc/[pid]/exe
    readProcessExe(pid, process);
        └── readlink获取可执行文件路径
    
    return true;
}
```

### 3.2 重点进程管理调用链

```cpp
// 重点进程设置调用链
void ProcessMonitor::setFocusProcess(pid_t pid, bool focus) {
    // 1. 更新重点进程管理器
    if (focus) {
        FocusProcessManager::getInstance().addFocusProcess(pid);
    } else {
        FocusProcessManager::getInstance().removeFocusProcess(pid);
    }
    
    // 2. 更新进程信息
    auto process = getProcess(pid);
    if (process) {
        process->setFocusProcess(focus);
            └── 如果focus为true，创建FocusProcessFileInfo对象
    }
    
    // 3. 通知文件监控器更新监控范围
    // (通过事件机制或直接调用)
}

// ProcessInfo重点进程设置调用链
void ProcessInfo::setFocusProcess(bool focus) {
    is_focus_process = focus;
    
    if (focus && !focus_file_info) {
        // 创建重点进程文件信息对象
        focus_file_info.reset(new FocusProcessFileInfo());
    } else if (!focus && focus_file_info) {
        // 清理重点进程文件信息对象
        focus_file_info.reset();
    }
}
```

---

## 4. 网络监控调用关系

### 4.1 NetworkMonitor核心调用链

```cpp
// NetworkMonitor监控循环调用链
void NetworkMonitor::monitorLoop() {
    while (running_) {
        // 1. 更新网络连接信息
        updateNetworkConnections();
            ├── scanTcpConnections()
            │   └── parseProcNetFile("/proc/net/tcp", TCP)
            ├── scanUdpConnections()  
            │   └── parseProcNetFile("/proc/net/udp", UDP)
            ├── scanTcp6Connections()
            │   └── parseProcNetFile("/proc/net/tcp6", TCP6)
            ├── scanUdp6Connections()
            │   └── parseProcNetFile("/proc/net/udp6", UDP6)
            └── scanUnixSockets()
                └── parseProcNetFile("/proc/net/unix", UNIX)
        
        // 2. 更新进程套接字映射
        updateProcessSocketMapping();
            └── 扫描/proc/[pid]/fd/目录，建立进程与套接字的映射关系
        
        // 3. 更新进程网络连接信息
        for (auto& connection : current_connections_) {
            if (connection.pid > 0) {
                auto process = process_monitor_->getProcess(connection.pid);
                if (process) {
                    // 更新进程的网络连接列表
                    process->updateNetworkConnections(getProcessConnections(connection.pid));
                }
            }
        }
        
        // 4. 更新统计信息
        updateStatistics();
        
        // 5. 休眠等待下次扫描
        std::this_thread::sleep_for(std::chrono::milliseconds(update_interval_ms_));
    }
}

// 网络文件解析调用链
bool NetworkMonitor::parseProcNetFile(const std::string& filename, ProtocolType protocol) {
    std::ifstream file(filename);
    std::string line;
    
    while (std::getline(file, line)) {
        SocketInfo socket;
        
        if (protocol == TCP || protocol == TCP6) {
            parseTcpLine(line, protocol, socket);
        } else if (protocol == UDP || protocol == UDP6) {
            parseUdpLine(line, protocol, socket);
        } else if (protocol == UNIX) {
            parseUnixLine(line, socket);
        }
        
        if (socket.inode > 0) {
            current_connections_.push_back(socket);
        }
    }
    
    return true;
}
```

---

## 5. API服务调用关系

### 5.1 ApiServer请求处理调用链

```cpp
// API请求完整处理调用链
ApiServer::start() {
    // 1. 启动HTTP服务器
    http_server_->start();
        └── HttpServer::serverLoop() // 主服务循环
            ├── accept() // 接受客户端连接
            └── 将连接加入工作队列
    
    // 2. 工作线程处理请求
    HttpServer::workerLoop() {
        while (running_) {
            // 从队列获取客户端套接字
            int client_socket = getClientSocket();
            
            // 处理客户端请求
            handleClient(client_socket);
                ├── parseHttpRequest() // 解析HTTP请求
                ├── 路由匹配和分发
                └── 调用对应的API处理器
        }
    }
}

// API处理器调用链示例 - 获取进程列表
void ApiServer::handleProcessesRequest(const HttpRequest& request, HttpResponse& response) {
    // 1. 获取进程监控器
    ProcessMonitor* process_monitor = monitor_->getProcessMonitor();
    
    // 2. 获取所有进程信息
    auto processes = process_monitor->getAllProcesses();
    
    // 3. 构建JSON响应
    JsonBuilder json;
    json.startObject()
        .addNumber("count", processes.size())
        .startArray("processes");
    
    for (const auto& process : processes) {
        json.startObject()
            .addNumber("pid", process->pid)
            .addString("name", process->process_name)
            .addString("state", process->state)
            .addNumber("cpu_usage", process->cpu_usage)
            .addNumber("memory_usage", process->memory_usage)
            .endObject();
    }
    
    json.endArray().endObject();
    
    // 4. 设置响应
    response.setStatus(HttpStatus::OK);
    response.setHeader("Content-Type", "application/json");
    response.setBody(json.toString());
}
```

### 5.2 重点进程API调用链

```cpp
// 重点进程文件信息API调用链
void ApiServer::handleFocusProcessFileInfoRequest(const HttpRequest& request, HttpResponse& response) {
    // 1. 解析请求参数
    auto query_params = parseQueryString(request.query_string);
    pid_t pid = std::stoi(query_params["pid"]);
    
    // 2. 获取进程信息
    ProcessMonitor* process_monitor = monitor_->getProcessMonitor();
    auto process = process_monitor->getProcess(pid);
    
    if (!process || !process->is_focus_process) {
        response.setStatus(HttpStatus::NOT_FOUND);
        return;
    }
    
    // 3. 获取重点进程文件信息
    FocusProcessFileInfo* file_info = process->getFocusFileInfo();
    if (!file_info) {
        response.setStatus(HttpStatus::NOT_FOUND);
        return;
    }
    
    // 4. 获取熵值统计摘要
    EntropyStatsSummary summary = file_info->getEntropyStatsSummary();
    
    // 5. 获取所有文件熵值信息
    auto entropy_info_list = file_info->getAllFileEntropyInfo();
    
    // 6. 构建JSON响应
    JsonBuilder json;
    json.startObject()
        .addNumber("pid", pid)
        .addString("process_name", process->process_name)
        .addBool("is_focus_process", process->is_focus_process)
        .startObject("entropy_summary")
            .addNumber("total_files", summary.total_files)
            .addNumber("files_with_original_entropy", summary.files_with_original_entropy)
            .addNumber("files_with_final_entropy", summary.files_with_final_entropy)
            .addNumber("total_entropy_change", summary.total_entropy_change)
            .endObject()
        .startArray("file_entropy_details");
    
    for (const auto& info : entropy_info_list) {
        json.startObject()
            .addString("file_path", info.file_path)
            .addNumber("original_entropy", info.original_entropy)
            .addNumber("final_entropy", info.final_entropy)
            .addNumber("entropy_change", info.entropy_change)
            .addBool("has_original_entropy", info.has_original_entropy)
            .addBool("has_final_entropy", info.has_final_entropy)
            .endObject();
    }
    
    json.endArray().endObject();
    
    // 7. 设置响应
    response.setStatus(HttpStatus::OK);
    response.setHeader("Content-Type", "application/json");
    response.setBody(json.toString());
}
```

---

## 6. 管理器类调用关系

### 6.1 配置重新加载调用链

```cpp
// 系统配置重新加载完整调用链
bool MainMonitor::reloadConfig(const MonitorConfig& new_config) {
    // 1. 保存新配置
    config_ = new_config;
    
    // 2. 重新加载重点进程配置
    bool focus_result = FocusProcessManager::getInstance()
                       .reloadConfig(config_.focus_process_list);
    
    // 3. 重新加载受保护路径配置
    bool protected_result = ProtectedPathManager::getInstance()
                           .reloadConfig(config_.protected_paths, 
                                       config_.terminate_violating_processes);
    
    // 4. 重新加载进程排除配置
    ProcessExclusionManagerSingleton::getInstance()
                           .loadFromConfig(config_.excluded_processes);
    
    // 5. 重新加载文件监控器配置
    bool file_result = file_monitor_->reloadConfig(&config_);
        └── 重新设置fanotify标记和监控范围
    
    // 6. 重新加载网络监控器配置
    // (网络监控器通常不需要重新加载配置)
    
    // 7. 记录配置重新加载结果
    LogManager::getInstance().info("配置重新加载完成");
    
    return focus_result && protected_result && file_result;
}
```

### 6.2 管理器间协作调用链

```cpp
// 文件监控事件处理中的管理器协作调用链
void FileMonitor::handleProtectedPathEvent(struct fanotify_event_metadata* metadata) {
    // 1. 获取基础信息
    std::string file_path = getFilePathFromFd(metadata->fd);
    std::string process_name = getProcessNameFromPid(metadata->pid);
    
    // 2. 检查进程排除规则
    if (ProcessExclusionManagerSingleton::getInstance().isExcluded(metadata->pid)) {
        return; // 跳过排除的进程
    }
    
    // 3. 检查是否为受保护路径
    if (ProtectedPathManager::getInstance().isProtectedPath(file_path)) {
        // 4. 处理违规行为
        ProtectedPathManager::getInstance()
                            .handleViolation(file_path, metadata->pid, process_name);
        
        // 5. 记录事件统计
        event_stats_.protected_path_events++;
        
        // 6. 触发事件回调
        if (event_callback_) {
            FileEvent event;
            event.pid = metadata->pid;
            event.file_path = file_path;
            event.is_protected_path = true;
            event_callback_(event);
        }
    }
}

void FileMonitor::handleFocusProcessEvent(struct fanotify_event_metadata* metadata) {
    // 1. 检查是否为重点进程
    if (!FocusProcessManager::getInstance().isFocusProcess(metadata->pid)) {
        return; // 不是重点进程，跳过
    }
    
    // 2. 获取进程信息
    auto process = ProcessMonitorManager::getInstance()
                  .getProcessMonitor()
                  ->getProcess(metadata->pid);
    
    if (!process) {
        return; // 进程不存在
    }
    
    // 3. 处理重点进程文件操作
    processFocusProcessFileOperation(event);
        ├── 计算文件熵值
        ├── 记录到FocusProcessFileInfo
        └── 更新统计信息
    
    // 4. 记录事件统计
    event_stats_.focus_process_events++;
}
```

通过以上详细的调用关系分析，可以清楚地看到PSFSMON-L系统中各个类之间的交互模式和数据流向，这为理解系统运行机制和进行系统维护提供了重要参考。
