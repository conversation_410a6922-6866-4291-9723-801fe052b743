graph TB
    subgraph "FileMonitor双Group架构"
        subgraph "受保护路径监控Group"
            A[protected_fanotify_fd_]
            B[protected_monitor_thread_]
            C[processProtectedPathEvents循环]
        end
        
        subgraph "重点进程监控Group"
            D[focus_fanotify_fd_]
            E[focus_monitor_thread_]
            F[processFocusProcessEvents循环]
        end
    end
    
    subgraph "Linux内核Fanotify"
        G[FAN_CLASS_NOTIF Group 1]
        H[FAN_CLASS_NOTIF Group 2]
    end
    
    subgraph "事件处理"
        I[handleProtectedPathEvent]
        J[handleFocusProcessEvent]
    end
    
    subgraph "管理器协作"
        K[ProtectedPathManager]
        L[FocusProcessManager]
        M[ProcessExclusionManager]
    end
    
    subgraph "数据输出"
        N[违规处理/进程终止]
        O[熵值计算和记录]
        P[事件统计更新]
    end
    
    G --> A
    H --> D
    A --> B
    D --> E
    B --> C
    E --> F
    C --> I
    F --> J
    
    I --> K
    I --> M
    J --> L
    J --> M
    
    K --> N
    L --> O
    I --> P
    J --> P
    
    style A fill:#ff6b6b,color:#ffffff
    style D fill:#51cf66,color:#000000
    style B fill:#ff6b6b,color:#ffffff
    style E fill:#51cf66,color:#000000
    style C fill:#ff6b6b,color:#ffffff
    style F fill:#51cf66,color:#000000
