flowchart TD
    subgraph "Linux内核"
        A[Fanotify事件]
        B[proc文件系统]
        C[proc/net/*]
    end
    
    subgraph "事件采集层"
        D[FileMonitor双线程]
        E[ProcessMonitor扫描线程]
        F[NetworkMonitor扫描线程]
    end
    
    subgraph "数据处理层"
        G[事件过滤和分类]
        H[进程信息解析]
        I[网络连接解析]
    end
    
    subgraph "管理器层"
        J[FocusProcessManager]
        K[ProtectedPathManager]
        L[ProcessExclusionManager]
    end
    
    subgraph "数据存储层"
        M[ProcessInfo集合]
        N[FocusProcessFileInfo]
        O[FileEvent历史]
        P[NetworkConnection集合]
    end
    
    subgraph "API输出层"
        Q[ApiServer]
        R[JsonBuilder]
        S[HTTP响应]
    end
    
    A --> D
    B --> E
    C --> F
    
    D --> G
    E --> H
    F --> I
    
    G --> J
    G --> K
    G --> L
    
    H --> M
    M --> N
    G --> O
    I --> P
    
    M --> Q
    N --> Q
    O --> Q
    P --> Q
    
    Q --> R
    R --> S
    
    style A fill:#ffd43b,color:#000000
    style B fill:#ffd43b,color:#000000
    style C fill:#ffd43b,color:#000000
    style D fill:#339af0,color:#ffffff
    style E fill:#339af0,color:#ffffff
    style F fill:#339af0,color:#ffffff
    style Q fill:#ffd43b,color:#000000
    style S fill:#339af0,color:#ffffff
