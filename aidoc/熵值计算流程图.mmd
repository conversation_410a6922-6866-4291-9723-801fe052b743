flowchart TD
    A[文件操作事件] --> B{是否为重点进程?}
    B -->|否| C[跳过处理]
    B -->|是| D{事件类型?}
    
    D -->|OPEN| E[计算文件原始熵值]
    D -->|CLOSE_WRITE| F[计算文件最终熵值]
    D -->|其他| G[记录路径历史]
    
    E --> H[FileEntropyCalculator::calculateFileEntropy]
    F --> H
    
    H --> I[读取文件内容]
    I --> J[分块计算熵值]
    J --> K[Shannon熵值公式]
    K --> L[返回熵值结果]
    
    L --> M{事件类型?}
    M -->|OPEN| N[recordFileOpen]
    M -->|CLOSE_WRITE| O[recordFileCloseWrite]
    
    N --> P[存储到file_entropy_map]
    O --> P
    G --> Q[添加到path_history]
    
    P --> R[更新统计信息]
    Q --> R
    R --> S[记录调试日志]
    
    style A fill:#339af0,color:#ffffff
    style H fill:#9775fa,color:#ffffff
    style K fill:#51cf66,color:#000000
    style P fill:#ff922b,color:#ffffff
    style S fill:#f783ac,color:#ffffff
