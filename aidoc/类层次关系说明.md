# PSFSMON-L 类层次关系说明文档

## 文档概述

本文档详细描述了PSFSMON-L项目中所有类的层次关系、继承结构、组合关系和依赖关系。PSFSMON-L采用模块化设计，通过清晰的类层次结构实现了高内聚、低耦合的系统架构。

## 整体架构层次

PSFSMON-L的类层次结构可以分为以下几个层次：

```
应用层 (Application Layer)
├── 主程序入口 (main.cpp)
└── 命令行处理

控制层 (Control Layer)  
├── MainMonitor (主监控器)
└── 配置管理

监控层 (Monitoring Layer)
├── FileMonitor (文件监控器)
├── ProcessMonitor (进程监控器)
└── NetworkMonitor (网络监控器)

管理层 (Management Layer)
├── FocusProcessManager (重点进程管理器)
├── ProtectedPathManager (受保护路径管理器)
└── ProcessExclusionManager (进程排除管理器)

服务层 (Service Layer)
├── ApiServer (API服务器)
└── HttpServer (HTTP服务器)

工具层 (Utility Layer)
├── JsonBuilder (JSON构建器)
├── LogManager (日志管理器)
└── Utils (工具类)

数据层 (Data Layer)
├── ProcessInfo (进程信息)
├── FocusProcessFileInfo (重点进程文件信息)
├── FileEvent (文件事件)
├── NetworkConnection (网络连接)
└── 其他数据结构
```

---

## 1. 控制层类层次

### 1.1 MainMonitor - 系统核心控制器

**层次位置**: 控制层顶级类
**设计模式**: 单例模式 + 外观模式

```cpp
class MainMonitor {
    // 组合关系 - 拥有并管理所有监控器
    std::unique_ptr<FileMonitor> file_monitor_;
    std::unique_ptr<NetworkMonitor> network_monitor_;
    
    // 依赖关系 - 通过ProcessMonitorManager访问
    // ProcessMonitor* (通过单例管理器访问)
    
    // 聚合关系 - 配置和统计信息
    MonitorConfig config_;
    MonitorStats stats_;
};
```

**关系说明**:
- **拥有关系**: MainMonitor拥有FileMonitor和NetworkMonitor的生命周期
- **管理关系**: 负责所有子模块的初始化、启动、停止和配置更新
- **协调关系**: 作为系统的中央协调器，处理模块间的交互

### 1.2 配置管理层次

```cpp
struct MonitorConfig {
    // 配置数据结构，被所有模块使用
    // 无继承关系，纯数据结构
};

// 配置使用关系
MainMonitor -> MonitorConfig (拥有)
FileMonitor -> MonitorConfig (引用)
ProcessMonitor -> MonitorConfig (间接使用)
NetworkMonitor -> MonitorConfig (间接使用)
```

---

## 2. 监控层类层次

### 2.1 监控器基础架构

虽然没有显式的基类，但所有监控器都遵循相同的接口模式：

```cpp
// 监控器通用接口模式 (概念上的基类)
interface Monitor {
    bool initialize();
    bool start();
    void stop();
    // 统计信息获取
    uint64_t getTotalEvents() const;
};
```

### 2.2 FileMonitor - 文件监控器

**层次位置**: 监控层核心类
**设计特点**: 双Fanotify Group架构

```cpp
class FileMonitor {
    // 依赖关系 - 与管理器协作
    // FocusProcessManager& (单例依赖)
    // ProtectedPathManager& (单例依赖)
    // ProcessExclusionManagerSingleton& (单例依赖)
    
    // 组合关系 - 拥有事件处理线程
    std::thread protected_monitor_thread_;
    std::thread focus_monitor_thread_;
    
    // 聚合关系 - 事件统计和存储
    FileEventStats event_stats_;
    std::vector<FileEvent> recent_events_;
};
```

**关系特点**:
- **双线程架构**: 拥有两个独立的事件处理线程
- **管理器协作**: 与三个管理器类紧密协作
- **事件生产**: 是系统中FileEvent的主要生产者

### 2.3 ProcessMonitor - 进程监控器

**层次位置**: 监控层基础类
**管理模式**: 通过ProcessMonitorManager单例管理

```cpp
class ProcessMonitor {
    // 组合关系 - 拥有进程信息映射
    std::unordered_map<pid_t, std::shared_ptr<ProcessInfo>> processes_;
    
    // 组合关系 - 拥有监控线程
    std::thread monitor_thread_;
    
    // 聚合关系 - CPU统计信息
    std::unordered_map<pid_t, CpuStats> process_cpu_stats_;
};

class ProcessMonitorManager {
    // 单例管理器
    std::unique_ptr<ProcessMonitor> process_monitor_;
    
    static ProcessMonitorManager& getInstance();
};
```

**关系特点**:
- **单例管理**: 通过管理器类实现单例访问
- **数据中心**: 是ProcessInfo数据的中央存储和管理中心
- **生命周期管理**: 负责进程的创建、更新和清理

### 2.4 NetworkMonitor - 网络监控器

**层次位置**: 监控层专业类
**依赖关系**: 依赖ProcessMonitor获取进程信息

```cpp
class NetworkMonitor {
    // 依赖关系 - 需要进程监控器提供进程信息
    ProcessMonitor* process_monitor_;
    
    // 组合关系 - 拥有监控线程
    std::thread monitor_thread_;
    
    // 聚合关系 - 网络连接信息
    std::vector<SocketInfo> current_connections_;
    std::unordered_map<pid_t, std::vector<uint32_t>> process_to_sockets_;
};

class NetworkMonitorManager {
    // 单例管理器
    std::unique_ptr<NetworkMonitor> network_monitor_;
    
    static NetworkMonitorManager& getInstance();
};
```

**关系特点**:
- **依赖注入**: 通过setProcessMonitor()方法注入ProcessMonitor依赖
- **数据关联**: 负责建立网络连接与进程的关联关系
- **单例管理**: 同样通过管理器类实现单例访问

---

## 3. 管理层类层次

### 3.1 管理器类通用模式

所有管理器类都采用相同的设计模式：

```cpp
// 管理器通用模式 (概念上的基类)
interface Manager {
    static Manager& getInstance();  // 单例访问
    void loadFromConfig(const std::string& config_str);
    bool reloadConfig(const std::string& config_str);
    void clear();
};
```

### 3.2 FocusProcessManager - 重点进程管理器

**层次位置**: 管理层专业类
**设计模式**: 单例模式

```cpp
class FocusProcessManager {
    // 数据存储 - 双重索引结构
    std::set<pid_t> focus_pids_;
    std::set<std::string> focus_names_;
    
    // 线程安全
    mutable std::mutex mutex_;
    
    // 私有构造函数 - 单例模式
    FocusProcessManager() = default;
};
```

**关系特点**:
- **独立管理**: 不依赖其他类，独立管理重点进程列表
- **被动服务**: 被FileMonitor查询，不主动操作其他类
- **配置驱动**: 支持从配置字符串动态加载设置

### 3.3 ProtectedPathManager - 受保护路径管理器

**层次位置**: 管理层安全类
**设计模式**: 单例模式

```cpp
class ProtectedPathManager {
    // 数据存储
    std::set<std::string> protected_paths_;
    bool terminate_violating_processes_;
    
    // 线程安全
    mutable std::mutex mutex_;
    
    // 私有构造函数 - 单例模式
    ProtectedPathManager() = default;
};
```

**关系特点**:
- **安全执行**: 具有终止违规进程的能力
- **路径匹配**: 提供灵活的路径匹配算法
- **模式选择**: 支持KILL和LOG两种处理模式

### 3.4 ProcessExclusionManager - 进程排除管理器

**层次位置**: 管理层过滤类
**设计模式**: 单例模式

```cpp
class ProcessExclusionManager {
    // 多维度排除数据
    std::unordered_set<pid_t> excluded_pids_;
    std::set<std::string> excluded_names_;
    std::set<std::string> excluded_paths_;
    std::vector<std::regex> excluded_patterns_;
    std::vector<std::string> excluded_pattern_strings_;

    // 预定义系统进程列表
    static const std::vector<std::string> SYSTEM_PROCESSES;
    static const std::vector<std::string> SYSTEM_PATHS;
    static const std::vector<std::string> SYSTEM_PATTERNS;

    // 线程安全
    mutable std::mutex exclusions_mutex_;
};

class ProcessExclusionManagerSingleton {
    // 单例管理器
    std::unique_ptr<ProcessExclusionManager> manager_;

    static ProcessExclusionManagerSingleton& getInstance();
};
```

**关系特点**:
- **多维过滤**: 支持PID、名称、路径、正则表达式多种过滤方式
- **系统保护**: 内置系统进程保护列表
- **广泛使用**: 被所有监控器查询使用

---

## 4. 服务层类层次

### 4.1 ApiServer - API服务器

**层次位置**: 服务层应用类
**设计模式**: 外观模式

```cpp
class ApiServer {
    // 组合关系 - 拥有HTTP服务器
    std::unique_ptr<HttpServer> http_server_;
    
    // 依赖关系 - 需要主监控器提供数据
    MainMonitor* monitor_;
    
    // 内部工具类
    class JsonBuilder;  // 嵌套类
};
```

**关系特点**:
- **外观接口**: 为整个系统提供统一的API外观
- **数据聚合**: 从各个监控器聚合数据提供给客户端
- **HTTP封装**: 封装HTTP服务器的复杂性

### 4.2 HttpServer - HTTP服务器

**层次位置**: 服务层基础类
**设计模式**: 线程池模式

```cpp
class HttpServer {
    // 组合关系 - 拥有工作线程池
    std::vector<std::thread> worker_threads_;
    
    // 聚合关系 - 路由表
    std::map<std::pair<HttpMethod, std::string>, ApiHandler> routes_;
    
    // 队列管理
    std::queue<int> client_sockets_;
};
```

**关系特点**:
- **基础服务**: 提供基础的HTTP协议支持
- **线程池**: 使用线程池处理并发请求
- **路由系统**: 实现灵活的路由注册和分发机制

---

## 5. 工具层类层次

### 5.1 JsonBuilder - JSON构建器

**层次位置**: 工具层实用类
**设计模式**: 建造者模式

```cpp
class ApiServer::JsonBuilder {
    // 内部状态
    std::string json_;
    std::vector<bool> first_element_stack_;
    
    // 流式接口
    JsonBuilder& startObject();
    JsonBuilder& endObject();
    // ... 其他方法
};
```

**关系特点**:
- **嵌套类**: 作为ApiServer的嵌套类存在
- **建造者模式**: 提供流式的JSON构建接口
- **类型安全**: 强类型的数据添加方法

### 5.2 LogManager - 日志管理器

**层次位置**: 工具层基础类
**设计模式**: 单例模式

```cpp
class LogManager {
    // 单例实现
    static LogManager& getInstance();
    
    // 日志方法
    void info(const std::string& message);
    void error(const std::string& message);
    void debug(const std::string& message);
};
```

**关系特点**:
- **全局服务**: 为整个系统提供日志服务
- **单例模式**: 确保日志的一致性和线程安全
- **广泛使用**: 被所有模块使用

---

## 6. 数据层类层次

### 6.1 核心数据结构关系

```cpp
// 进程信息层次
ProcessInfo {
    // 组合关系 - 拥有重点进程文件信息
    std::unique_ptr<FocusProcessFileInfo> focus_file_info;
    
    // 聚合关系 - 网络连接列表
    std::vector<NetworkConnection> network_connections;
    
    // 聚合关系 - 子进程列表
    std::vector<pid_t> children;
};

// 重点进程文件信息层次
FocusProcessFileInfo {
    // 组合关系 - 拥有文件熵值映射
    std::unordered_map<uint64_t, FileEntropyInfo> file_entropy_map;
    
    // 聚合关系 - 路径历史
    std::vector<std::string> path_history;
};

// 文件熵值信息
FileEntropyInfo {
    // 纯数据结构，无组合关系
    // 包含熵值计算结果
};
```

### 6.2 事件数据结构关系

```cpp
// 文件事件
FileEvent {
    // 枚举类型 - 事件类型
    enum EventType { READ, WRITE, OPEN, CLOSE, ... };
    
    // 基础数据，无组合关系
};

// 网络连接
NetworkConnection {
    // 枚举类型 - 协议类型和连接状态
    enum ProtocolType { TCP, UDP, UNIX };
    enum ConnectionState { ESTABLISHED, LISTEN, ... };
    
    // 基础数据，无组合关系
};
```

---

## 7. 类关系总结

### 7.1 继承关系
PSFSMON-L项目中**没有使用传统的继承关系**，而是采用了以下设计模式：
- **组合优于继承**: 通过组合关系实现功能复用
- **接口一致性**: 通过约定的接口模式保持一致性
- **单例模式**: 管理器类普遍采用单例模式

### 7.2 组合关系
- **MainMonitor** 组合 **FileMonitor**, **NetworkMonitor**
- **ApiServer** 组合 **HttpServer**
- **ProcessInfo** 组合 **FocusProcessFileInfo**
- **FocusProcessFileInfo** 组合 **FileEntropyInfo**映射

### 7.3 依赖关系
- **FileMonitor** 依赖 **FocusProcessManager**, **ProtectedPathManager**, **ProcessExclusionManagerSingleton**
- **NetworkMonitor** 依赖 **ProcessMonitor**
- **ApiServer** 依赖 **MainMonitor**
- 所有类都依赖 **LogManager**

### 7.4 聚合关系
- **ProcessMonitor** 聚合 **ProcessInfo**集合
- **NetworkMonitor** 聚合 **NetworkConnection**集合
- **FileMonitor** 聚合 **FileEvent**集合

这种清晰的类层次关系设计确保了系统的模块化、可维护性和可扩展性。
