# PSFSMON-L 类功能说明文档

## 文档概述

本文档详细描述了PSFSMON-L项目中所有类的功能、职责、成员变量、成员函数以及它们之间的关系。PSFSMON-L是一个基于Linux内核Fanotify机制的进程和文件系统监控系统，采用双Fanotify Group架构实现高效的安全监控。

## 核心架构概述

PSFSMON-L采用模块化设计，主要包含以下核心组件：

- **主监控器(MainMonitor)**: 系统的核心控制器，协调各个监控模块
- **文件监控器(FileMonitor)**: 基于双Fanotify Group的文件系统监控
- **进程监控器(ProcessMonitor)**: 进程生命周期和资源监控
- **网络监控器(NetworkMonitor)**: 网络连接和流量监控
- **API服务器(ApiServer)**: RESTful API接口服务
- **管理器类**: 重点进程、受保护路径、进程排除等专项管理

---

## 1. 主监控器类 (MainMonitor)

### 类定义位置
- **头文件**: `include/main_monitor.h`
- **实现文件**: `src/main_monitor.cpp`

### 功能职责
MainMonitor是整个系统的核心控制器，负责：
- 系统初始化和配置管理
- 各监控模块的生命周期管理
- 守护进程模式支持
- 信号处理和优雅关闭
- 系统兼容性检查
- 性能统计和监控

### 主要成员变量
```cpp
MonitorConfig config_;                    // 系统配置
std::atomic<MonitorStatus> status_;       // 运行状态
std::unique_ptr<FileMonitor> file_monitor_;     // 文件监控器
std::unique_ptr<NetworkMonitor> network_monitor_; // 网络监控器
MonitorStats stats_;                      // 统计信息
std::thread status_thread_;               // 状态监控线程
std::atomic<bool> status_thread_running_; // 线程运行标志
```

### 核心成员函数
```cpp
// 系统生命周期管理
bool initialize(const MonitorConfig& config);  // 初始化系统
bool start();                                  // 启动监控
void stop();                                   // 停止监控
bool reloadConfig(const MonitorConfig& config); // 重新加载配置

// 状态和统计
MonitorStatus getStatus() const;               // 获取运行状态
MonitorStats getStats() const;                 // 获取统计信息

// 模块访问器
ProcessMonitor* getProcessMonitor() const;     // 获取进程监控器
FileMonitor* getFileMonitor() const;           // 获取文件监控器
NetworkMonitor* getNetworkMonitor() const;     // 获取网络监控器

// 守护进程和信号处理
bool daemonize();                              // 守护进程化
static void signalHandler(int signal);        // 信号处理器
```

### 设计特点
- **单例模式**: 通过静态实例指针确保全局唯一性
- **RAII管理**: 使用智能指针管理子模块生命周期
- **线程安全**: 使用原子变量和互斥锁保护共享状态
- **异常安全**: 完善的错误处理和资源清理机制

---

## 2. 文件监控器类 (FileMonitor)

### 类定义位置
- **头文件**: `include/file_monitor.h`
- **实现文件**: `src/file_monitor.cpp`

### 功能职责
FileMonitor是系统的核心创新模块，实现双Fanotify Group架构：
- **受保护路径监控Group**: 精确监控特定路径的访问权限
- **重点进程监控Group**: 全局监控重点进程的文件操作
- 文件熵值计算和变化检测
- 违规行为检测和处理
- 事件统计和历史记录

### 双Fanotify Group架构
```cpp
// 双文件描述符设计
int protected_fanotify_fd_;  // 受保护路径监控Group
int focus_fanotify_fd_;      // 重点进程监控Group

// 双线程处理
std::thread protected_monitor_thread_;  // 受保护路径事件处理线程
std::thread focus_monitor_thread_;      // 重点进程事件处理线程
```

### 主要成员变量
```cpp
const MonitorConfig* config_;                    // 配置引用
std::atomic<bool> running_;                      // 运行状态
bool kernel_supports_filesystem_mark_;           // 内核特性支持
FileEventStats event_stats_;                     // 事件统计
std::vector<FileEvent> recent_events_;           // 最近事件记录
std::function<void(const FileEvent&)> event_callback_; // 事件回调
std::vector<std::string> mount_points_;          // 挂载点缓存
```

### 核心成员函数
```cpp
// 系统生命周期
bool initialize(const MonitorConfig* config);   // 初始化
bool start();                                   // 启动监控
void stop();                                    // 停止监控
bool reloadConfig(const MonitorConfig* config); // 重新加载配置

// 双Group初始化
bool initializeProtectedPathGroup();            // 初始化受保护路径Group
bool initializeFocusProcessGroup();             // 初始化重点进程Group

// 双线程事件处理
void processProtectedPathEvents();              // 受保护路径事件处理循环
void processFocusProcessEvents();               // 重点进程事件处理循环

// 事件处理和统计
void setEventCallback(std::function<void(const FileEvent&)> callback);
uint64_t getTotalEvents() const;
FileEventStats getEventStats() const;
std::vector<FileEvent> getRecentEvents(size_t limit = 100) const;

// 动态路径管理
bool addProtectedPathMark(const std::string& path);
void removeProtectedPathMark(const std::string& path);
```

### 设计特点
- **双Group架构**: 分离受保护路径监控和重点进程监控，避免事件冲突
- **内核兼容性**: 自动检测内核版本，选择最优监控策略
- **高性能处理**: 双线程并行处理，提高事件处理效率
- **线程安全**: 完善的锁机制保护共享数据结构

---

## 3. 进程监控器类 (ProcessMonitor)

### 类定义位置
- **头文件**: `include/process_monitor.h`
- **实现文件**: `src/process_monitor.cpp`

### 功能职责
ProcessMonitor负责系统中所有进程的监控和管理：
- 进程生命周期跟踪（创建、运行、退出）
- 进程资源监控（CPU、内存使用率）
- 进程树构建和维护
- 重点进程标记和管理
- 进程信息的实时更新

### 主要成员变量
```cpp
std::unordered_map<pid_t, std::shared_ptr<ProcessInfo>> processes_; // 进程信息映射
std::thread monitor_thread_;                     // 监控线程
std::atomic<bool> running_;                      // 运行状态
std::atomic<bool> stop_requested_;               // 停止请求标志
uint32_t update_interval_ms_;                    // 更新间隔
std::unordered_map<pid_t, CpuStats> process_cpu_stats_; // CPU统计
uint32_t num_cpu_cores_;                         // CPU核心数
uint64_t page_size_;                             // 页面大小
uint64_t clock_ticks_per_sec_;                   // 时钟频率
```

### 核心成员函数
```cpp
// 系统生命周期
bool initialize();                               // 初始化
bool start();                                   // 启动监控
void stop();                                    // 停止监控

// 进程信息访问
std::shared_ptr<ProcessInfo> getProcess(pid_t pid) const;
std::vector<std::shared_ptr<ProcessInfo>> getAllProcesses() const;
std::shared_ptr<ProcessTreeNode> getProcessTree() const;
std::vector<std::shared_ptr<ProcessInfo>> findProcessesByName(const std::string& name) const;

// 重点进程管理
void setFocusProcess(pid_t pid, bool focus = true);
void setFocusProcess(const std::string& process_name, bool focus = true);
bool isFocusProcess(pid_t pid) const;
std::vector<pid_t> getFocusProcesses() const;

// 手动更新
void updateAllProcesses();                       // 手动更新所有进程信息
```

### 设计特点
- **高效扫描**: 基于/proc文件系统的高效进程信息收集
- **增量更新**: 智能的增量更新机制，减少系统开销
- **资源监控**: 精确的CPU和内存使用率计算
- **进程树维护**: 实时维护完整的进程父子关系树

---

## 4. 网络监控器类 (NetworkMonitor)

### 类定义位置
- **头文件**: `include/network_monitor.h`
- **实现文件**: `src/network_monitor.cpp`

### 功能职责
NetworkMonitor负责系统网络活动的监控：
- TCP/UDP连接状态监控
- 网络接口统计信息收集
- 进程与网络连接的关联
- 监听端口检测
- 网络流量统计

### 主要成员变量
```cpp
std::thread monitor_thread_;                     // 监控线程
std::atomic<bool> running_;                      // 运行状态
ProcessMonitor* process_monitor_;                // 进程监控器引用
std::vector<SocketInfo> current_connections_;    // 当前连接列表
std::unordered_map<pid_t, std::vector<uint32_t>> process_to_sockets_; // 进程到套接字映射
std::unordered_map<uint32_t, pid_t> socket_to_process_; // 套接字到进程映射
std::vector<NetworkInterface> network_interfaces_; // 网络接口信息
std::atomic<uint64_t> total_network_events_;     // 事件计数
```

### 核心成员函数
```cpp
// 系统生命周期
bool initialize();                               // 初始化
bool start();                                   // 启动监控
void stop();                                    // 停止监控

// 网络信息访问
void updateNetworkConnections();                // 更新网络连接信息
std::vector<NetworkConnection> getProcessConnections(pid_t pid) const;
std::vector<SocketInfo> getAllConnections() const;
NetworkStats getNetworkStats() const;
std::vector<SocketInfo> getListeningPorts() const;
std::vector<NetworkInterface> getNetworkInterfaces() const;

// 进程监控器关联
void setProcessMonitor(ProcessMonitor* monitor);
```

### 设计特点
- **多协议支持**: 支持TCP、UDP、Unix套接字监控
- **进程关联**: 精确关联网络连接与对应进程
- **实时统计**: 提供详细的网络流量和连接统计
- **接口监控**: 监控所有网络接口的状态和统计信息

---

## 5. API服务器类 (ApiServer)

### 类定义位置
- **头文件**: `include/api_server.h`
- **实现文件**: `src/api_server.cpp`

### 功能职责
ApiServer提供完整的RESTful API接口：
- HTTP服务器管理
- 25个API端点的路由处理
- JSON数据序列化
- 错误处理和状态码管理
- 线程安全的数据访问

### API端点分类
```cpp
// 基础API
/api/health                    // 健康检查
/api/stats                     // 系统统计
/api/monitor/status            // 监控状态

// 进程API
/api/processes                 // 进程列表
/api/process-tree              // 进程树
/api/process/{pid}             // 特定进程信息

// 网络API
/api/network                   // 网络连接
/api/network/stats             // 网络统计

// 重点进程API
/api/focus-processes           // 重点进程管理
/api/focus-process/file-info   // 重点进程文件信息

// 管理API
/api/exclusions                // 进程排除规则
/api/protected-paths           // 受保护路径管理
```

### 主要成员变量
```cpp
std::unique_ptr<HttpServer> http_server_;        // HTTP服务器
MainMonitor* main_monitor_;                      // 主监控器引用
std::atomic<bool> running_;                      // 运行状态
```

### 核心成员函数
```cpp
// 系统生命周期
bool initialize(uint16_t port, MainMonitor* monitor);
bool start();                                   // 启动服务器
void stop();                                    // 停止服务器
bool isRunning() const;                         // 获取运行状态

// API处理器注册（内部使用）
void registerApiHandlers();                     // 注册所有API处理器
```

### 设计特点
- **RESTful设计**: 遵循REST架构原则的API设计
- **JSON响应**: 统一的JSON格式响应
- **错误处理**: 完善的HTTP状态码和错误信息
- **线程安全**: 多线程环境下的安全数据访问

---

## 6. HTTP服务器类 (HttpServer)

### 类定义位置
- **头文件**: `include/api_server.h`
- **实现文件**: `src/http_server.cpp`

### 功能职责
HttpServer是ApiServer的底层HTTP服务实现：
- TCP套接字管理
- HTTP请求解析
- 路由匹配和分发
- 线程池管理
- 静态文件服务

### 主要成员变量
```cpp
uint16_t port_;                                  // 监听端口
std::string bind_address_;                       // 绑定地址
int server_socket_;                              // 服务器套接字
std::atomic<bool> running_;                      // 运行状态
std::map<std::pair<HttpMethod, std::string>, ApiHandler> routes_; // 路由表
std::vector<std::thread> worker_threads_;        // 工作线程池
std::queue<int> client_sockets_;                 // 客户端套接字队列
size_t max_worker_threads_;                      // 最大工作线程数
```

### 核心成员函数
```cpp
// 服务器管理
bool initialize(uint16_t port, const std::string& bind_address = "0.0.0.0");
bool start();                                   // 启动服务器
void stop();                                    // 停止服务器

// 路由管理
void registerRoute(HttpMethod method, const std::string& path, ApiHandler handler);
void setStaticDirectory(const std::string& directory);
void setRequestTimeout(uint32_t timeout_seconds);
```

### 设计特点
- **线程池架构**: 使用线程池处理并发请求
- **路由系统**: 灵活的路由注册和匹配机制
- **HTTP协议**: 完整的HTTP/1.1协议支持
- **性能优化**: 高效的请求处理和资源管理

---

## 7. 重点进程管理器 (FocusProcessManager)

### 类定义位置
- **头文件**: `include/file_monitor.h`
- **实现文件**: `src/focus_process_manager.cpp`

### 功能职责
FocusProcessManager管理系统中的重点进程：
- 重点进程的添加、删除和查询
- 支持按PID和进程名管理
- 配置文件加载和动态更新
- 线程安全的进程状态查询

### 主要成员变量
```cpp
std::unordered_set<pid_t> focus_pids_;           // 重点进程PID集合
std::unordered_set<std::string> focus_names_;    // 重点进程名集合
mutable std::mutex mutex_;                       // 线程安全锁
```

### 核心成员函数
```cpp
// 单例访问
static FocusProcessManager& getInstance();

// 重点进程管理
void addFocusProcess(pid_t pid);
void addFocusProcess(const std::string& process_name);
void removeFocusProcess(pid_t pid);
void removeFocusProcess(const std::string& process_name);

// 查询功能
bool isFocusProcess(pid_t pid) const;
bool isFocusProcess(const std::string& process_name) const;
std::vector<pid_t> getFocusProcesses() const;
std::vector<std::string> getFocusProcessNames() const;

// 配置管理
void loadFromConfig(const std::string& config_str);
bool reloadConfig(const std::string& config_str);
void clear();
```

### 设计特点
- **单例模式**: 全局唯一的管理器实例
- **双重索引**: 同时支持PID和进程名查询
- **动态管理**: 支持运行时动态添加和删除
- **配置驱动**: 支持从配置字符串加载设置

---

## 8. 受保护路径管理器 (ProtectedPathManager)

### 类定义位置
- **头文件**: `include/file_monitor.h`
- **实现文件**: `src/protected_path_manager.cpp`

### 功能职责
ProtectedPathManager管理系统中的受保护路径：
- 受保护路径的添加、删除和查询
- 违规行为检测和处理
- KILL模式和LOG模式支持
- 路径匹配和权限检查

### 主要成员变量
```cpp
std::unordered_set<std::string> protected_paths_; // 受保护路径集合
bool terminate_violating_processes_;             // 是否终止违规进程
mutable std::mutex mutex_;                       // 线程安全锁
```

### 核心成员函数
```cpp
// 单例访问
static ProtectedPathManager& getInstance();

// 路径管理
void addProtectedPath(const std::string& path);
void removeProtectedPath(const std::string& path);
bool isProtectedPath(const std::string& path) const;
std::vector<std::string> getProtectedPaths() const;

// 违规处理
void handleViolation(const std::string& path, pid_t pid, const std::string& process_name);
void setTerminateViolatingProcesses(bool terminate);
bool shouldTerminateViolatingProcesses() const;

// 配置管理
void loadFromConfig(const std::string& config_str);
bool reloadConfig(const std::string& config_str, bool terminate_violating_processes);
void clear();
```

### 设计特点
- **路径匹配**: 支持精确路径和前缀匹配
- **违规处理**: 灵活的KILL/LOG模式选择
- **实时保护**: 与FileMonitor紧密集成的实时保护
- **配置驱动**: 支持动态配置更新

---

## 9. 进程排除管理器 (ProcessExclusionManager)

### 类定义位置
- **头文件**: `include/process_exclusion.h`
- **实现文件**: `src/process_exclusion.cpp`

### 功能职责
ProcessExclusionManager管理监控排除规则：
- 系统进程自动排除
- 自定义排除规则管理
- 多种匹配模式支持（PID、进程名、路径、正则表达式）
- 预定义系统进程排除列表

### 主要成员变量
```cpp
std::unordered_set<pid_t> excluded_pids_;        // 排除的PID集合
std::set<std::string> excluded_names_;           // 排除的进程名集合
std::set<std::string> excluded_paths_;           // 排除的路径集合
std::vector<std::regex> excluded_patterns_;      // 排除的正则表达式
std::vector<std::string> excluded_pattern_strings_; // 原始正则表达式字符串
static const std::vector<std::string> SYSTEM_PROCESSES; // 系统进程列表
static const std::vector<std::string> SYSTEM_PATHS;     // 系统路径列表
static const std::vector<std::string> SYSTEM_PATTERNS;  // 系统模式列表
mutable std::mutex exclusions_mutex_;            // 线程安全锁
```

### 核心成员函数
```cpp
// 单例访问（通过单例管理器）
// 实际使用：ProcessExclusionManagerSingleton::getInstance()

// 排除规则管理
void addExclusionByPid(pid_t pid);
void addExclusionByName(const std::string& name);
void addExclusionByPath(const std::string& path);
void addExclusionByPattern(const std::string& pattern);

// 查询功能
bool isExcluded(pid_t pid) const;
bool isExcludedByName(const std::string& name) const;
bool isExcludedByPath(const std::string& executable_path) const;

// 配置管理
void loadFromConfig(const std::string& config_str);
void addDefaultExclusions();
void clearAllExclusions();
```

### 设计特点
- **多维度排除**: 支持PID、名称、路径、正则表达式多种排除方式
- **系统进程保护**: 预定义的系统关键进程排除列表
- **正则表达式**: 支持复杂的模式匹配排除规则
- **自动排除**: 自动排除自身进程避免自我监控
- **单例管理**: 通过ProcessExclusionManagerSingleton管理单例实例

---

## 10. JSON构建器 (JsonBuilder)

### 类定义位置
- **头文件**: `include/api_server.h`
- **实现文件**: `src/api_json_builder.cpp`

### 功能职责
JsonBuilder是ApiServer的内部工具类，用于构建JSON响应：
- 流式JSON构建
- 类型安全的数据添加
- 自动格式化和转义
- 嵌套对象和数组支持

### 主要成员变量
```cpp
std::string json_;                               // JSON字符串缓冲区
std::vector<bool> first_element_stack_;          // 元素状态栈
```

### 核心成员函数
```cpp
// 对象和数组管理
JsonBuilder& startObject();
JsonBuilder& endObject();
JsonBuilder& startArray(const std::string& key = "");
JsonBuilder& endArray();

// 数据添加
JsonBuilder& addString(const std::string& key, const std::string& value);
JsonBuilder& addNumber(const std::string& key, int64_t value);
JsonBuilder& addNumber(const std::string& key, uint64_t value);
JsonBuilder& addNumber(const std::string& key, double value);
JsonBuilder& addBool(const std::string& key, bool value);
JsonBuilder& addNull(const std::string& key);

// 值添加（用于数组）
JsonBuilder& addStringValue(const std::string& value);
JsonBuilder& addNumberValue(int64_t value);
JsonBuilder& addBoolValue(bool value);
JsonBuilder& addNullValue();

// 输出
std::string toString() const;
```

### 设计特点
- **流式接口**: 支持链式调用的流畅接口
- **类型安全**: 强类型的数据添加方法
- **自动格式化**: 自动处理逗号、引号和转义
- **嵌套支持**: 完整的嵌套对象和数组支持

---

## 总结

PSFSMON-L的类设计体现了以下核心原则：

1. **模块化设计**: 每个类都有明确的职责边界，便于维护和扩展
2. **线程安全**: 所有共享数据都有适当的同步机制保护
3. **资源管理**: 使用RAII和智能指针确保资源的正确管理
4. **性能优化**: 采用高效的数据结构和算法，最小化系统开销
5. **可配置性**: 支持灵活的配置和运行时动态调整
6. **错误处理**: 完善的异常处理和错误恢复机制

这些类共同构成了一个功能完整、性能优异的Linux系统监控解决方案。

---

## 11. 核心数据结构

### 11.1 ProcessInfo - 进程信息结构

ProcessInfo是系统中最重要的数据结构之一，包含了进程的完整信息：

```cpp
struct ProcessInfo {
    // 基础标识信息
    pid_t pid;                      // 进程ID
    pid_t ppid;                     // 父进程ID
    std::string process_name;       // 进程名称
    std::string executable_path;    // 可执行文件路径
    std::string command_line;       // 命令行参数
    std::string cwd;               // 当前工作目录
    uid_t uid;                     // 用户ID
    gid_t gid;                     // 组ID

    // 性能信息
    double cpu_usage;              // CPU使用率
    uint64_t memory_usage;         // 内存使用量 (KB)
    uint64_t virtual_memory;       // 虚拟内存使用量 (KB)
    std::string state;             // 进程状态
    uint64_t start_time;           // 启动时间

    // 关系信息
    std::vector<pid_t> children;   // 子进程ID列表

    // 网络连接
    std::vector<NetworkConnection> network_connections;
    mutable std::mutex network_mutex;

    // 重点进程信息
    bool is_focus_process;         // 是否为重点进程
    std::unique_ptr<FocusProcessFileInfo> focus_file_info; // 重点进程文件信息
};
```

**主要方法**:
- `updateNetworkConnections()`: 更新网络连接信息
- `setFocusProcess()`: 设置为重点进程
- `getFocusFileInfo()`: 获取重点进程文件信息
- `getBasicInfoSummary()`: 获取基础信息摘要

### 11.2 FocusProcessFileInfo - 重点进程文件信息

专门用于存储重点进程的文件操作信息和熵值计算结果：

```cpp
struct FocusProcessFileInfo {
    // 熵值统计
    double total_read_entropy;      // 总读取熵值
    double total_write_entropy;     // 总写入熵值
    uint32_t read_entropy_count;    // 读取熵值计算次数
    uint32_t write_entropy_count;   // 写入熵值计算次数

    // 文件操作统计
    uint32_t delete_count;          // 删除操作次数
    uint32_t rename_count;          // 重命名操作次数

    // 路径历史记录
    std::vector<std::string> path_history;        // 文件路径历史
    std::vector<std::string> rename_extensions;   // 重命名扩展名变化

    // 文件熵值映射
    std::unordered_map<uint64_t, FileEntropyInfo> file_entropy_map;
    mutable std::mutex file_entropy_mutex;
    mutable std::mutex mutex;
};
```

**主要方法**:
- `recordFileOpen()`: 记录文件打开时的熵值
- `recordFileCloseWrite()`: 记录文件写入关闭时的熵值
- `getAverageReadEntropy()`: 获取平均读取熵值
- `getAverageWriteEntropy()`: 获取平均写入熵值
- `getAllFileEntropyInfo()`: 获取所有文件熵值信息

### 11.3 FileEvent - 文件事件结构

表示文件系统中发生的各种事件：

```cpp
struct FileEvent {
    enum EventType {
        READ = 0, WRITE, OPEN, CLOSE, ACCESS,
        MODIFY, DELETE, RENAME, PERMISSION_DENIED, UNKNOWN
    };

    pid_t pid;                     // 触发事件的进程ID
    EventType type;                // 事件类型
    std::string file_path;         // 文件路径
    uint64_t timestamp;            // 时间戳
    uint64_t mask;                 // fanotify事件掩码
    bool is_focus_process;         // 是否为重点进程事件
    bool is_protected_path;        // 是否为受保护路径事件
};
```

### 11.4 NetworkConnection - 网络连接信息

描述网络连接的详细信息：

```cpp
struct NetworkConnection {
    ProtocolType protocol;         // 协议类型 (TCP/UDP/Unix)
    std::string local_address;     // 本地地址
    uint16_t local_port;          // 本地端口
    std::string remote_address;    // 远程地址
    uint16_t remote_port;         // 远程端口
    ConnectionState state;         // 连接状态
    pid_t pid;                    // 关联进程ID
    uint32_t inode;               // 套接字inode
    uint64_t rx_bytes;            // 接收字节数
    uint64_t tx_bytes;            // 发送字节数
};
```

### 11.5 MonitorConfig - 监控配置结构

系统的完整配置信息：

```cpp
struct MonitorConfig {
    // 基础开关
    bool enable_file_monitoring;       // 启用文件监控
    bool enable_network_monitoring;    // 启用网络监控
    bool enable_process_monitoring;    // 启用进程监控
    bool daemon_mode;                  // 守护进程模式
    bool debug_mode;                   // 调试模式
    bool verbose_mode;                 // 详细输出模式

    // 时间间隔配置
    uint32_t update_interval_ms;       // 更新间隔(毫秒)
    uint32_t process_scan_interval_ms; // 进程扫描间隔
    uint32_t full_scan_interval;       // 完整扫描间隔(秒)

    // API配置
    uint32_t api_port;                 // API端口
    std::string bind_address;          // 绑定地址
    bool api_auth_required;            // API认证要求

    // 文件路径配置
    std::string log_file;              // 日志文件路径
    std::string config_file;           // 配置文件路径
    std::string pid_file;              // PID文件路径

    // 重点进程配置
    bool enable_focus_process_monitoring; // 启用重点进程监控
    bool calculate_file_entropy;      // 计算文件熵值
    std::string focus_process_list;    // 重点进程列表

    // 受保护路径配置
    bool enable_protected_path_monitoring; // 启用受保护路径监控
    std::string protected_paths;       // 受保护路径列表
    bool terminate_violating_processes; // 终止违规进程
};
```

### 11.6 FileEntropyInfo - 文件熵值信息

存储单个文件的熵值计算结果：

```cpp
struct FileEntropyInfo {
    std::string file_path;         // 文件路径
    uint64_t file_hash;           // 文件路径哈希值
    double original_entropy;       // 原始熵值
    double final_entropy;         // 最终熵值
    double entropy_change;        // 熵值变化量
    bool has_original_entropy;    // 是否有原始熵值
    bool has_final_entropy;       // 是否有最终熵值
    time_t open_time;             // 打开时间
    time_t close_time;            // 关闭时间
    size_t file_size;             // 文件大小
};
```

### 11.7 MonitorStats - 监控统计信息

系统运行统计信息：

```cpp
struct MonitorStats {
    uint64_t uptime_seconds;           // 运行时间(秒)
    uint64_t total_processes;          // 总进程数
    uint64_t total_file_events;        // 总文件事件数
    uint64_t total_network_events;     // 总网络事件数
    uint64_t focus_processes_count;    // 重点进程数量
    uint64_t protected_paths_count;    // 受保护路径数量
    double cpu_usage_percent;          // CPU使用率
    uint64_t memory_usage_kb;          // 内存使用量
    uint64_t api_requests_count;       // API请求总数
    uint64_t api_errors_count;         // API错误总数
};
```

这些数据结构构成了PSFSMON-L系统的数据基础，它们之间通过精心设计的关系和接口，实现了高效的数据存储、查询和更新机制。
