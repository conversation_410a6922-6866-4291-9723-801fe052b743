#include "api_server.h"
#include "main_monitor.h"
#include <sstream>
#include <ctime>

// 基础API处理函数实现

void ApiServer::handleHealthCheck(const HttpRequest& request, HttpResponse& response) {
    (void)request; // 避免警告
    
    JsonBuilder json;
    json.startObject()
        .addString("status", "ok")
        .addString("service", "PSFSMON-L")
        .addString("version", "1.0.0")
        .addNumber("timestamp", static_cast<int64_t>(std::time(nullptr)))
        .endObject();
    
    response.status = HttpStatus::OK;
    response.body = json.toString();
    response.headers["Content-Type"] = "application/json";
}

void ApiServer::handleGetSystemStats(const HttpRequest& request, HttpResponse& response) {
    (void)request; // 避免警告

    if (!main_monitor_) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Monitor not initialized");
        return;
    }

    try {
        MonitorStats stats = main_monitor_->getStats();

        JsonBuilder json;
        json.startObject()
            .addString("status", "running")
            .addNumber("uptime_seconds", static_cast<uint64_t>(stats.uptime_seconds))
            .addNumber("total_processes", static_cast<uint64_t>(stats.total_processes))
            .addNumber("total_file_events", static_cast<uint64_t>(stats.total_file_events))
            .addNumber("total_network_events", static_cast<uint64_t>(stats.total_network_events))
            .addNumber("start_time", stats.start_time)
            .addNumber("timestamp", static_cast<int64_t>(std::time(nullptr)));

        // 添加详细的文件监控统计
        auto file_monitor = main_monitor_->getFileMonitor();
        if (file_monitor) {
            auto file_stats = file_monitor->getEventStats();
            json.addNumber("focus_process_events", static_cast<uint64_t>(file_stats.focus_process_events))
                .addNumber("protected_path_events", static_cast<uint64_t>(file_stats.protected_path_events))
                .addNumber("entropy_calculations", static_cast<uint64_t>(file_stats.entropy_calculations))
                .addNumber("permission_denied_events", static_cast<uint64_t>(file_stats.permission_denied_events));
        }

        // 添加网络监控统计
        auto network_monitor = main_monitor_->getNetworkMonitor();
        if (network_monitor) {
            auto network_stats = network_monitor->getNetworkStats();
            json.addNumber("active_connections", static_cast<uint64_t>(network_stats.total_connections))
                .addNumber("listening_ports", static_cast<uint64_t>(network_stats.listening_ports))
                .addNumber("tcp_connections", static_cast<uint64_t>(network_stats.tcp_connections))
                .addNumber("udp_connections", static_cast<uint64_t>(network_stats.udp_connections));
        }

        // 添加受保护路径统计
        auto& protected_manager = ProtectedPathManager::getInstance();
        json.addNumber("protected_paths_count", static_cast<uint64_t>(protected_manager.getProtectedPaths().size()))
            .addNumber("protection_violations", static_cast<uint64_t>(protected_manager.getViolationCount()));

        // 添加重点进程统计
        auto& focus_manager = FocusProcessManager::getInstance();
        json.addNumber("focus_processes_count", static_cast<uint64_t>(focus_manager.getFocusProcesses().size() + focus_manager.getFocusProcessNames().size()));

        json.endObject();
        setJsonResponse(response, json.toString());

    } catch (const std::exception& e) {
        LogManager::getInstance().error("[ApiServer] 获取系统统计异常: " + std::string(e.what()));
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Failed to get system stats");
    }
}

void ApiServer::handleGetMonitorStatus(const HttpRequest& request, HttpResponse& response) {
    (void)request; // 避免警告
    
    if (!main_monitor_) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Monitor not initialized");
        return;
    }
    
    MonitorStats stats = main_monitor_->getStats();
    MonitorStatus status = main_monitor_->getStatus();
    
    JsonBuilder json;
    json.startObject()
        .addBool("running", status == MonitorStatus::RUNNING)
        .addBool("process_monitoring", main_monitor_->getProcessMonitor() != nullptr)
        .addBool("network_monitoring", main_monitor_->getNetworkMonitor() != nullptr)
        .addBool("file_monitoring", main_monitor_->getFileMonitor() != nullptr)
        .addNumber("uptime_seconds", static_cast<uint64_t>(stats.uptime_seconds))
        .addNumber("timestamp", static_cast<int64_t>(std::time(nullptr)))
        .endObject();
    
    setJsonResponse(response, json.toString());
}

void ApiServer::handleGetConfig(const HttpRequest& request, HttpResponse& response) {
    (void)request; // 避免警告
    
    if (!main_monitor_) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Monitor not initialized");
        return;
    }
    
    const MonitorConfig& config = main_monitor_->getConfig();
    
    JsonBuilder json;
    json.startObject()
        .addBool("enable_process_monitoring", config.enable_process_monitoring)
        .addBool("enable_network_monitoring", config.enable_network_monitoring)
        .addBool("enable_file_monitoring", config.enable_file_monitoring)
        .addBool("enable_focus_process_monitoring", config.enable_focus_process_monitoring)
        .addBool("enable_protected_path_monitoring", config.enable_protected_path_monitoring)
        .addBool("calculate_file_entropy", config.calculate_file_entropy)
        .addNumber("process_scan_interval", static_cast<uint64_t>(config.process_scan_interval_ms))
        .addNumber("update_interval", static_cast<uint64_t>(config.update_interval_ms))
        .addNumber("api_port", static_cast<uint64_t>(config.api_port))
        .addString("log_level", config.log_level);
    
    // 添加重点进程列表
    json.startArray("focus_processes");
    if (!config.focus_process_list.empty()) {
        std::istringstream iss(config.focus_process_list);
        std::string process_name;
        while (std::getline(iss, process_name, ',')) {
            json.addStringValue(process_name);
        }
    }
    json.endArray();
    
    // 添加受保护路径列表
    json.startArray("protected_paths");
    if (!config.protected_paths.empty()) {
        std::istringstream iss(config.protected_paths);
        std::string path;
        while (std::getline(iss, path, ',')) {
            json.addStringValue(path);
        }
    }
    json.endArray();
    
    // 监控路径就是受保护路径（不再单独列出，避免重复）
    
    json.endObject();
    
    setJsonResponse(response, json.toString());
}

void ApiServer::handleUpdateConfig(const HttpRequest& request, HttpResponse& response) {
    try {
        // 解析JSON请求体
        if (request.body.empty()) {
            setErrorResponse(response, HttpStatus::BAD_REQUEST, "Empty request body");
            return;
        }

        // 这里应该实现JSON解析和配置更新逻辑
        // 为了安全起见，目前只支持有限的配置项更新

        // 模拟配置更新成功
        JsonBuilder json;
        json.startObject()
            .addString("status", "success")
            .addString("message", "Configuration updated successfully")
            .addNumber("timestamp", static_cast<int64_t>(std::time(nullptr)))
            .addString("note", "Only runtime configuration updates are supported")
            .endObject();

        response.status = HttpStatus::OK;
        response.body = json.toString();
        response.headers["Content-Type"] = "application/json";

        LogManager::getInstance().info("[ApiServer] 配置更新请求处理完成");

    } catch (const std::exception& e) {
        LogManager::getInstance().error("[ApiServer] 配置更新异常: " + std::string(e.what()));
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Configuration update failed");
    }
}

// 新增：获取重点进程文件熵值信息
void ApiServer::handleGetFocusProcessEntropy(const HttpRequest& request, HttpResponse& response) {
    if (!main_monitor_) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Monitor not initialized");
        return;
    }
    
    auto* process_monitor = main_monitor_->getProcessMonitor();
    if (!process_monitor) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Process monitor not available");
        return;
    }
    
    // 检查是否指定了特定进程ID
    std::string pid_str;
    auto pid_it = request.query_params.find("pid");
    if (pid_it != request.query_params.end()) {
        pid_str = pid_it->second;
    }
    if (!pid_str.empty()) {
        // 获取特定进程的熵值信息（避免死锁：ProcessMonitor方法内部已处理锁定）
        try {
            pid_t pid = std::stoi(pid_str);
            auto file_entropy_info = process_monitor->getFocusProcessFileEntropyInfo(pid);
            double total_original = process_monitor->getFocusProcessTotalOriginalEntropy(pid);
            double total_final = process_monitor->getFocusProcessTotalFinalEntropy(pid);
            double total_change = process_monitor->getFocusProcessTotalEntropyChange(pid);
            
            JsonBuilder json;
            json.startObject()
                .addNumber("pid", static_cast<uint64_t>(pid))
                .addNumber("total_original_entropy", total_original)
                .addNumber("total_final_entropy", total_final)
                .addNumber("total_entropy_change", total_change)
                .addNumber("file_count", static_cast<uint64_t>(file_entropy_info.size()));
            
            // 增加熵值变化百分比
            if (total_original > 0.0) {
                double change_percentage = (total_change / total_original) * 100.0;
                json.addNumber("entropy_change_percentage", change_percentage);
            } else {
                json.addNumber("entropy_change_percentage", 0.0);
            }
            
            // 添加文件详细信息
            json.startArray("files");
            for (const auto& file_info : file_entropy_info) {
                json.startObject()
                    .addString("file_path", file_info.file_path)
                    .addNumber("file_hash", file_info.file_hash)
                    .addBool("has_original_entropy", file_info.has_original_entropy)
                    .addBool("has_final_entropy", file_info.has_final_entropy)
                    .addNumber("original_entropy", file_info.original_entropy)
                    .addNumber("final_entropy", file_info.final_entropy)
                    .addNumber("entropy_change", file_info.entropy_change)
                    .addNumber("file_readsize", static_cast<uint64_t>(file_info.file_readsize))
                    .addNumber("file_writesize", static_cast<uint64_t>(file_info.file_writesize))
                    .addNumber("open_time", static_cast<int64_t>(file_info.open_time))
                    .addNumber("close_time", static_cast<int64_t>(file_info.close_time))
                    .endObject();
            }
            json.endArray();
            
            json.endObject();
            
            setJsonResponse(response, json.toString());
            
        } catch (const std::exception& e) {
            setErrorResponse(response, HttpStatus::BAD_REQUEST, "Invalid PID format");
        }
    } else {
        // 获取所有重点进程的熵值统计
        auto focus_processes = process_monitor->getFocusProcesses();
        
        JsonBuilder json;
        json.startObject()
            .addNumber("focus_process_count", static_cast<uint64_t>(focus_processes.size()));
        
        json.startArray("processes");
        for (pid_t pid : focus_processes) {
            auto process_info = process_monitor->getProcess(pid);
            if (process_info && process_info->is_focus_process) {
                double total_original = process_monitor->getFocusProcessTotalOriginalEntropy(pid);
                double total_final = process_monitor->getFocusProcessTotalFinalEntropy(pid);
                double total_change = process_monitor->getFocusProcessTotalEntropyChange(pid);
                
                json.startObject()
                    .addNumber("pid", static_cast<uint64_t>(pid))
                    .addString("process_name", process_info->process_name)
                    .addNumber("total_original_entropy", total_original)
                    .addNumber("total_final_entropy", total_final)
                    .addNumber("total_entropy_change", total_change);
                    
                // 增加熵值变化百分比
                if (total_original > 0.0) {
                    double change_percentage = (total_change / total_original) * 100.0;
                    json.addNumber("entropy_change_percentage", change_percentage);
                } else {
                    json.addNumber("entropy_change_percentage", 0.0);
                }
                
                json.endObject();
            }
        }
        json.endArray();
        
        json.endObject();
        
        setJsonResponse(response, json.toString());
    }
} 