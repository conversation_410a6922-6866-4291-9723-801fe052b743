/*
 * PSFSMON-L 重点进程管理器实现
 * 
 * 【需求12支持】
 * 本模块实现重点进程管理功能，包括：
 * - 按PID和进程名管理重点进程
 * - 支持配置加载和动态管理
 * - 线程安全的进程状态查询
 * - 与FileMonitor的双Fanotify Group架构协作
 */

#include "file_monitor.h"
#include "main_monitor.h"
#include <sstream>
#include <fstream>
#include <algorithm>

// 移除未使用的日志函数

// FocusProcessManager 实现
FocusProcessManager& FocusProcessManager::getInstance() {
    static FocusProcessManager instance;
    return instance;
}

void FocusProcessManager::addFocusProcess(pid_t pid) {
    std::lock_guard<std::mutex> lock(FocusProcessManager_focus_data_mutex_);
    focus_pids_.insert(pid);
}

void FocusProcessManager::addFocusProcess(const std::string& process_name) {
    std::lock_guard<std::mutex> lock(FocusProcessManager_focus_data_mutex_);
    focus_names_.insert(process_name);
}

void FocusProcessManager::removeFocusProcess(pid_t pid) {
    std::lock_guard<std::mutex> lock(FocusProcessManager_focus_data_mutex_);
    focus_pids_.erase(pid);
}

void FocusProcessManager::removeFocusProcess(const std::string& process_name) {
    std::lock_guard<std::mutex> lock(FocusProcessManager_focus_data_mutex_);
    focus_names_.erase(process_name);
}

bool FocusProcessManager::isFocusProcess(pid_t pid) const {
    std::lock_guard<std::mutex> lock(FocusProcessManager_focus_data_mutex_);
    
    // 检查PID
    if (focus_pids_.find(pid) != focus_pids_.end()) {
        return true;
    }
    
    // 检查进程名
    if (!focus_names_.empty()) {
        std::string process_name = getProcessNameFromPid(pid);
        if (!process_name.empty()) {
            return focus_names_.find(process_name) != focus_names_.end();
        }
    }
    
    return false;
}

bool FocusProcessManager::isFocusProcess(const std::string& process_name) const {
    std::lock_guard<std::mutex> lock(FocusProcessManager_focus_data_mutex_);
    return focus_names_.find(process_name) != focus_names_.end();
}

std::vector<pid_t> FocusProcessManager::getFocusProcesses() const {
    std::lock_guard<std::mutex> lock(FocusProcessManager_focus_data_mutex_);
    return std::vector<pid_t>(focus_pids_.begin(), focus_pids_.end());
}

std::vector<std::string> FocusProcessManager::getFocusProcessNames() const {
    std::lock_guard<std::mutex> lock(FocusProcessManager_focus_data_mutex_);
    return std::vector<std::string>(focus_names_.begin(), focus_names_.end());
}

void FocusProcessManager::loadFromConfig(const std::string& config_str) {
    std::lock_guard<std::mutex> lock(FocusProcessManager_focus_data_mutex_);
    
    if (config_str.empty()) {
        return;
    }
    
    // 解析配置字符串，格式: "pid1,pid2,name1,name2"
    std::istringstream iss(config_str);
    std::string token;
    
    while (std::getline(iss, token, ',')) {
        token = Utils::trim(token);
        if (token.empty()) {
            continue;
        }
        
        // 尝试解析为PID
        char* endptr;
        long pid_val = strtol(token.c_str(), &endptr, 10);
        
        if (*endptr == '\0' && pid_val > 0) {
            // 是PID
            focus_pids_.insert(static_cast<pid_t>(pid_val));
        } else {
            // 是进程名
            focus_names_.insert(token);
        }
    }
}

void FocusProcessManager::clear() {
    std::lock_guard<std::mutex> lock(FocusProcessManager_focus_data_mutex_);
    focus_pids_.clear();
    focus_names_.clear();
}

bool FocusProcessManager::reloadConfig(const std::string& config_str) {
    LogManager::getInstance().info("[FocusProcessManager] 开始重新加载配置...");

    try {
        std::lock_guard<std::mutex> lock(FocusProcessManager_focus_data_mutex_);
        
        // 清空现有配置
        focus_pids_.clear();
        focus_names_.clear();
        
        // 重新加载配置
        if (!config_str.empty()) {
            // 解析配置字符串，格式: "pid1,pid2,name1,name2"
            std::istringstream iss(config_str);
            std::string token;
            
            while (std::getline(iss, token, ',')) {
                token = Utils::trim(token);
                if (token.empty()) {
                    continue;
                }
                
                // 尝试解析为PID
                char* endptr;
                long pid_val = strtol(token.c_str(), &endptr, 10);
                
                if (*endptr == '\0' && pid_val > 0) {
                    // 是PID
                    focus_pids_.insert(static_cast<pid_t>(pid_val));
                } else {
                    // 是进程名
                    focus_names_.insert(token);
                }
            }
        }
        
        LogManager::getInstance().info("[FocusProcessManager] 配置重新加载成功 - 重点进程PID数量: " + 
                                      std::to_string(focus_pids_.size()) + 
                                      ", 重点进程名数量: " + std::to_string(focus_names_.size()));
        
        return true;
        
    } catch (const std::exception& e) {
        LogManager::getInstance().error("[FocusProcessManager] 配置重新加载时发生异常: " + std::string(e.what()));
        return false;
    }
}

std::string FocusProcessManager::getProcessNameFromPid(pid_t pid) const {
    return Utils::getProcessName(pid);
}