#include "process_monitor.h"
#include "main_monitor.h"
#include "file_monitor.h"  // 包含FocusProcessManager定义
#include "rule_match_info.h"  // 包含规则匹配管理器
#include <fstream>
#include <sstream>
#include <algorithm>
#include <cstring>
#include <unistd.h>
#include <sys/stat.h>

// ProcessMonitor实现
ProcessMonitor::ProcessMonitor() : running_(false), focus_monitor_running_(false), stop_requested_(false), 
                                   update_interval_ms_(2000), last_full_scan_(0) {
    initializeSystemInfo();
}

ProcessMonitor::~ProcessMonitor() {
    stop();
}

bool ProcessMonitor::initialize() {
    // 检查/proc文件系统
    if (!fileExists("/proc")) {
        logError("无法访问/proc文件系统");
        return false;
    }
    
    logInfo("进程监控器初始化成功");
    return true;
}

bool ProcessMonitor::start() {
    if (running_) {
        return true;
    }
    
    running_ = true;
    focus_monitor_running_ = true;
    stop_requested_ = false;
    
    monitor_thread_ = std::thread(&ProcessMonitor::monitorLoop, this);
    focus_monitor_thread_ = std::thread(&ProcessMonitor::focusMonitorLoop, this);
    
    logInfo("进程监控器已启动（包含重点进程监控线程）");
    return true;
}

void ProcessMonitor::stop() {
    if (!running_) {
        return;
    }
    
    stop_requested_ = true;
    running_ = false;
    focus_monitor_running_ = false;
    
    if (monitor_thread_.joinable()) {
        monitor_thread_.join();
    }
    
    if (focus_monitor_thread_.joinable()) {
        focus_monitor_thread_.join();
    }
    
    logInfo("进程监控器已停止（包含重点进程监控线程）");
}

void ProcessMonitor::updateAllProcesses() {
    scanProcesses();
}

std::shared_ptr<ProcessInfo> ProcessMonitor::getProcess(pid_t pid) const {
    std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);
    auto it = processes_.find(pid);
    return (it != processes_.end()) ? it->second : nullptr;
}

std::vector<std::shared_ptr<ProcessInfo>> ProcessMonitor::getAllProcesses() const {
    std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);
    std::vector<std::shared_ptr<ProcessInfo>> result;
    result.reserve(processes_.size());

    for (const auto& pair : processes_) {
        result.push_back(pair.second);
    }

    return result;
}

std::shared_ptr<ProcessTreeNode> ProcessMonitor::getProcessTree() const {
    return buildProcessTree();
}

std::vector<std::shared_ptr<ProcessInfo>> ProcessMonitor::findProcessesByName(const std::string& name) const {
    std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);
    std::vector<std::shared_ptr<ProcessInfo>> result;
    
    for (const auto& pair : processes_) {
        if (pair.second->process_name.find(name) != std::string::npos) {
            result.push_back(pair.second);
        }
    }
    
    return result;
}

// 重点进程管理方法
void ProcessMonitor::setFocusProcess(pid_t pid, bool focus) {
    std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);
    
    logInfo("【调试】setFocusProcess调用: PID=" + std::to_string(pid) + ", Focus=" + (focus ? "true" : "false"));
    
    auto process_it = processes_.find(pid);
    if (process_it != processes_.end()) {
        process_it->second->setFocusProcess(focus);
        logInfo("【调试】设置重点进程（已存在）: PID=" + std::to_string(pid) + ", Focus=" + (focus ? "true" : "false"));
        
        // 【增强逻辑】：如果是手动移除重点进程，检查该进程是否已经死亡
        if (!focus) {
            // 手动移除重点进程，使用严格的进程存在检查
            if (!Utils::processExistsStrict(pid)) {
                logInfo("【手动清理】检测到被移除的重点进程已死亡，立即清理: PID=" + std::to_string(pid));
                
                // 清理进程CPU统计数据
                process_cpu_stats_.erase(pid);
                // 从进程列表中移除
                processes_.erase(process_it);
                logInfo("【手动清理】成功清理已死亡的重点进程: PID=" + std::to_string(pid));
                
                // 同步清理FocusProcessManager中的记录
                // 注意：这里需要在锁外调用，但由于已经移除了进程，我们在这里直接清理
                // 避免在handleProcessExit中再次清理
                return; // 提前返回，避免后续处理
            } else {
                logInfo("【手动移除】重点进程仍在运行，保留监控数据: PID=" + std::to_string(pid));
            }
        }
    } else if (focus) {
        // 如果要添加为重点进程但进程不在列表中，立即尝试扫描该进程
        logInfo("【调试】重点进程不在列表中，尝试立即扫描: PID=" + std::to_string(pid));
        
        // 使用严格的进程存在检查
        if (Utils::processExistsStrict(pid)) {
            logInfo("【调试】进程存在，开始读取进程信息: PID=" + std::to_string(pid));
            
            auto new_process = std::make_shared<ProcessInfo>();
            new_process->pid = pid;
            
            // 读取进程基本信息
            if (readProcessInfo(pid, new_process)) {
                new_process->setFocusProcess(true);
                processes_[pid] = new_process;
                logInfo("【调试】成功扫描并添加重点进程: PID=" + std::to_string(pid) + ", Name=" + new_process->process_name);
                
                // 验证添加是否成功
                auto verify_it = processes_.find(pid);
                if (verify_it != processes_.end() && verify_it->second->is_focus_process) {
                    logInfo("【调试】验证成功: 进程已在processes_中且is_focus_process=true");
                } else {
                    logInfo("【调试】验证失败: 进程添加或设置有问题");
                }
            } else {
                logInfo("【调试】无法读取进程信息: PID=" + std::to_string(pid));
            }
        } else {
            logInfo("【调试】进程不存在或已死亡: PID=" + std::to_string(pid));
        }
    } else {
        logInfo("【调试】移除重点进程，但进程不在列表中: PID=" + std::to_string(pid));
    }
}

void ProcessMonitor::setFocusProcess(const std::string& process_name, bool focus) {
    std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);
    
    bool found = false;
    std::vector<pid_t> removed_focus_pids; // 记录被移除的重点进程PID，用于后续死进程检查
    
    for (const auto& pair : processes_) {
        bool should_set_focus = false;
        
        // 精确匹配
        if (pair.second->process_name == process_name) {
            should_set_focus = true;
        }
        // 兼容性匹配：处理进程名截断情况
        else if (process_name.length() > 15 && pair.second->process_name.length() <= 15) {
            // 如果请求的进程名长度>15，而系统中的进程名<=15，检查是否为截断匹配
            if (process_name.substr(0, pair.second->process_name.length()) == pair.second->process_name) {
                should_set_focus = true;
            }
        }
        // 反向兼容性匹配：处理旧配置使用截断名称的情况
        else if (process_name.length() <= 15 && pair.second->process_name.length() > 15) {
            // 如果请求的进程名长度<=15，而系统中的进程名>15，检查是否为截断匹配
            if (pair.second->process_name.substr(0, process_name.length()) == process_name) {
                should_set_focus = true;
            }
        }
        
        if (should_set_focus) {
            // 记录原来的重点进程状态
            bool was_focus_process = pair.second->is_focus_process;
            
            pair.second->setFocusProcess(focus);
            found = true;
            
            std::string match_type = (pair.second->process_name == process_name) ? "精确匹配" : "截断匹配";
            logInfo("设置重点进程(" + match_type + "): Name=" + process_name + ", PID=" + std::to_string(pair.first) + 
                   ", Focus=" + (focus ? "true" : "false"));
            
            // 【新增逻辑】：如果是手动移除重点进程，记录PID用于后续死进程检查
            if (was_focus_process && !focus) {
                removed_focus_pids.push_back(pair.first);
            }
        }
    }
    
    // 【新增逻辑】：对被移除的重点进程进行死进程检查
    if (!removed_focus_pids.empty()) {
        logInfo("检查被移除的重点进程是否已死亡...");
        
        auto it = processes_.begin();
        while (it != processes_.end()) {
            pid_t pid = it->first;
            
            // 检查是否是被移除的重点进程
            if (std::find(removed_focus_pids.begin(), removed_focus_pids.end(), pid) != removed_focus_pids.end()) {
                // 使用严格的进程存在检查
                if (!Utils::processExistsStrict(pid)) {
                    logInfo("【手动清理】检测到被移除的重点进程已死亡，立即清理: PID=" + std::to_string(pid));
                    
                    // 清理进程CPU统计数据
                    process_cpu_stats_.erase(pid);
                    // 从进程列表中移除
                    it = processes_.erase(it);
                    logInfo("【手动清理】成功清理已死亡的重点进程: PID=" + std::to_string(pid));
                    continue;
                } else {
                    logInfo("【手动移除】重点进程仍在运行，保留监控数据: PID=" + std::to_string(pid));
                }
            }
            ++it;
        }
    }
    
    if (!found) {
        logInfo("未找到匹配的进程: Name=" + process_name + ", 当前进程总数=" + std::to_string(processes_.size()));
        
        // 输出调试信息：显示当前所有进程名，便于排查
        if (processes_.size() < 50) { // 只在进程数量不太多时输出，避免日志过长
            std::string all_names;
            for (const auto& pair : processes_) {
                if (!all_names.empty()) all_names += ", ";
                all_names += pair.second->process_name;
            }
            logDebug("当前所有进程名: " + all_names);
        }
    }
}

bool ProcessMonitor::isFocusProcess(pid_t pid) const {
    std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);
    
    auto process_it = processes_.find(pid);
    if (process_it != processes_.end()) {
        return process_it->second->is_focus_process;
    }
    return false;
}

std::vector<pid_t> ProcessMonitor::getFocusProcesses() const {
    std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);
    
    std::vector<pid_t> focus_pids;
    for (const auto& pair : processes_) {
        if (pair.second->is_focus_process) {
            focus_pids.push_back(pair.first);
        }
    }
    return focus_pids;
}

size_t ProcessMonitor::getTotalProcesses() const {
    std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);
    return processes_.size();
}

// 删除getTotalThreads方法，因为不再管理线程信息

void ProcessMonitor::monitorLoop() {
    uint32_t cleanup_counter = 0;
    const uint32_t cleanup_interval = 15; // 每15次循环进行一次死进程清理（从5次增加到15次，减少清理频率）
    
    while (!stop_requested_) {
        try {
            if (needsFullScan()) {
                performFullScan();
            } else {
                performIncrementalUpdate();
            }
            
            // 定期清理死进程和孤立数据
            cleanup_counter++;
            if (cleanup_counter >= cleanup_interval) {
                cleanupDeadProcesses();
                cleanup_counter = 0;
            }
            
        } catch (const std::exception& e) {
            logError("监控循环异常: " + std::string(e.what()));
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(update_interval_ms_));
    }
}

void ProcessMonitor::focusMonitorLoop() {
    const uint32_t focus_monitor_interval_ms = 2000; // 重点进程监控间隔2秒
    
    while (focus_monitor_running_.load()) {
        try {
            monitorFocusProcesses();
        } catch (const std::exception& e) {
            logError("重点进程监控循环异常: " + std::string(e.what()));
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(focus_monitor_interval_ms));
    }
}

void ProcessMonitor::monitorFocusProcesses() {
    std::vector<pid_t> focus_pids;
    
    // 获取当前所有重点进程
    {
        std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);
        for (const auto& pair : processes_) {
            if (pair.second->is_focus_process) {
                focus_pids.push_back(pair.first);
            }
        }
    }
    
    if (focus_pids.empty()) {
        return;
    }
    
    // 监控每个重点进程
    for (pid_t pid : focus_pids) {
        // 检查进程是否仍然存在
        if (!Utils::processExistsStrict(pid)) {
            logDebug("重点进程已不存在，准备清理: PID=" + std::to_string(pid));
            handleProcessExit(pid);
            continue;
        }
        
        // 更新重点进程信息
        std::shared_ptr<ProcessInfo> process;
        {
            std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);
            auto it = processes_.find(pid);
            if (it != processes_.end()) {
                process = it->second;
            }
        }
        
        if (process) {
            // 更新进程信息
            readProcessInfo(pid, process);
            
            // 记录监控日志
            logDebug("监控重点进程: PID=" + std::to_string(pid) + 
                    ", Name=" + process->process_name + 
                    ", CPU=" + std::to_string(process->cpu_usage) + "%" +
                    ", Memory=" + std::to_string(process->memory_usage) + "KB");
        }
    }
}

void ProcessMonitor::scanProcesses() {
    std::vector<pid_t> current_pids = getPidList();
    std::set<pid_t> current_pid_set(current_pids.begin(), current_pids.end());

    // 【重点进程保护】：获取重点进程列表，用于多层保护
    std::vector<pid_t> focus_process_pids;
    {
        std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);
        for (const auto& process_pair : processes_) {
            if (process_pair.second->is_focus_process) {
                focus_process_pids.push_back(process_pair.first);
            }
        }
    }

    // 【重点进程状态验证】：确保重点进程不会被误认为新进程
    for (pid_t focus_pid : focus_process_pids) {
        if (current_pid_set.find(focus_pid) != current_pid_set.end()) {
            // 重点进程在当前进程列表中，确保它不会被当作新进程处理
            if (previous_pids_.find(focus_pid) == previous_pids_.end()) {
                logDebug("【重点进程保护】检测到重点进程在previous_pids_中缺失，添加保护: PID=" + std::to_string(focus_pid));
                // 强制添加到previous_pids_中，避免被误认为新进程
                previous_pids_.insert(focus_pid);
            }
        }
    }

    // 处理新进程
    for (pid_t pid : current_pids) {
        if (previous_pids_.find(pid) == previous_pids_.end()) {
            // 【额外保护】：检查是否为重点进程，如果是则特殊处理
            bool is_focus_process = false;
            {
                std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);
                auto it = processes_.find(pid);
                if (it != processes_.end() && it->second->is_focus_process) {
                    is_focus_process = true;
                }
            }
            
            if (is_focus_process) {
                logDebug("【重点进程保护】重点进程被误认为新进程，跳过handleNewProcess: PID=" + std::to_string(pid));
                // 只更新进程信息，不调用handleNewProcess
                std::shared_ptr<ProcessInfo> process;
                {
                    std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);
                    auto it = processes_.find(pid);
                    if (it != processes_.end()) {
                        process = it->second;
                    }
                }
                if (process) {
                    readProcessInfo(pid, process);
                }
            } else {
                handleNewProcess(pid);
            }
        } else {
            // 更新现有进程 - 避免在锁内进行耗时操作
            std::shared_ptr<ProcessInfo> process;
            {
                std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);
                auto it = processes_.find(pid);
                if (it != processes_.end()) {
                    process = it->second;
                }
            }
            if (process) {
                readProcessInfo(pid, process);
            }
        }
    }

    // 【修改后的逻辑】：进行死进程检查，但保护重点进程
    std::vector<pid_t> potentially_dead_pids;
    for (pid_t pid : previous_pids_) {
        if (current_pid_set.find(pid) == current_pid_set.end()) {
            // 如果是重点进程，跳过这个进程的清理，但不跳过所有检查
            if (std::find(focus_process_pids.begin(), focus_process_pids.end(), pid) != focus_process_pids.end()) {
                logDebug("scanProcesses: 重点进程保护，跳过清理: PID=" + std::to_string(pid) + 
                        " (提示：只有手动从重点进程列表移除后才会被清理)");
                continue;
            }
            potentially_dead_pids.push_back(pid);
        }
    }

    // 对可能死亡的进程进行严格的二次验证
    std::vector<pid_t> confirmed_dead_pids;
    for (pid_t pid : potentially_dead_pids) {
        // 使用更严格的进程存在检查
        if (!Utils::processExistsStrict(pid)) {
            confirmed_dead_pids.push_back(pid);
            logDebug("scanProcesses严格验证确认死进程: PID=" + std::to_string(pid));
        } else {
            logDebug("scanProcesses严格验证发现进程仍活跃，跳过清理: PID=" + std::to_string(pid));
        }
    }

    // 清理确认死亡的进程
    for (pid_t pid : confirmed_dead_pids) {
        logDebug("scanProcesses清理死进程: PID=" + std::to_string(pid));
        handleProcessExit(pid);
    }

    // 【重点进程状态验证】：扫描完成后验证所有重点进程状态
    for (pid_t focus_pid : focus_process_pids) {
        if (current_pid_set.find(focus_pid) != current_pid_set.end()) {
            // 重点进程仍然活跃，验证其状态
            std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);
            auto it = processes_.find(focus_pid);
            if (it != processes_.end()) {
                if (!it->second->is_focus_process) {
                    logError("【状态异常】检测到重点进程状态丢失，尝试恢复: PID=" + std::to_string(focus_pid));
                    it->second->setFocusProcess(true);
                    logInfo("【状态恢复】成功恢复重点进程状态: PID=" + std::to_string(focus_pid));
                }
            }
        }
    }

    // 统计保护效果
    size_t protected_count = focus_process_pids.size();
    size_t false_positive_count = potentially_dead_pids.size() - confirmed_dead_pids.size();
    
    if (protected_count > 0) {
        logDebug("scanProcesses: 保护了 " + std::to_string(protected_count) + " 个重点进程");
    }
    if (false_positive_count > 0) {
        logDebug("scanProcesses: getPidList() 漏掉了 " + std::to_string(false_positive_count) + " 个活跃进程，已通过严格验证避免误删");
    }

    previous_pids_ = current_pid_set;
}

bool ProcessMonitor::readProcessInfo(pid_t pid, std::shared_ptr<ProcessInfo> process) {
    if (!readProcessStatus(pid, process)) {
        return false;
    }
    
    if (!readProcessStat(pid, process)) {
        return false;
    }
    
    readProcessCmdline(pid, process);
    readProcessCwd(pid, process);
    readProcessExe(pid, process);
    
    return true;
}

bool ProcessMonitor::readProcessStatus(pid_t pid, std::shared_ptr<ProcessInfo> process) {
    std::string path = getProcPath(pid, "status");
    std::vector<std::string> lines = readFileLines(path);
    
    // 使用统一的进程名获取函数
    process->process_name = Utils::getProcessName(pid);
    
    for (const std::string& line : lines) {
        if (line.find("Name:") == 0) {
            // 如果统一函数获取失败，使用Name字段作为备选
            if (process->process_name.empty() || process->process_name == "unknown") {
                process->process_name = Utils::trim(line.substr(5));
            }
        } else if (line.find("PPid:") == 0) {
            process->ppid = std::stoi(Utils::trim(line.substr(5)));
        } else if (line.find("Uid:") == 0) {
            auto parts = splitString(Utils::trim(line.substr(4)), '\t');
            if (!parts.empty()) {
                process->uid = std::stoi(parts[0]);
            }
        } else if (line.find("Gid:") == 0) {
            auto parts = splitString(Utils::trim(line.substr(4)), '\t');
            if (!parts.empty()) {
                process->gid = std::stoi(parts[0]);
            }
        } else if (line.find("VmRSS:") == 0) {
            auto parts = splitString(Utils::trim(line.substr(6)), ' ');
            if (!parts.empty()) {
                process->memory_usage = std::stoull(parts[0]);
            }
        } else if (line.find("VmSize:") == 0) {
            auto parts = splitString(Utils::trim(line.substr(7)), ' ');
            if (!parts.empty()) {
                process->virtual_memory = std::stoull(parts[0]);
            }
        } else if (line.find("State:") == 0) {
            std::string state_str = Utils::trim(line.substr(6));
            if (!state_str.empty()) {
                process->state = processStateToString(charToProcessState(state_str[0]));
            }
        }
    }
    
    return true;
}

bool ProcessMonitor::readProcessStat(pid_t pid, std::shared_ptr<ProcessInfo> process) {
    std::string path = getProcPath(pid, "stat");
    std::string content = readFileContent(path);
    
    if (content.empty()) {
        return false;
    }
    
    auto parts = splitString(content, ' ');
    if (parts.size() < 20) {
        return false;
    }
    
    // 计算CPU使用率
    uint64_t utime = std::stoull(parts[13]);
    uint64_t stime = std::stoull(parts[14]);
    uint64_t current_time = Utils::getCurrentTimestamp();
    
    CpuStats current_stats;
    current_stats.utime = utime;
    current_stats.stime = stime;
    current_stats.total_time = utime + stime;
    current_stats.timestamp = current_time;
    
    process->cpu_usage = calculateCpuUsage(pid, current_stats);
    
    return true;
}

bool ProcessMonitor::readProcessCmdline(pid_t pid, std::shared_ptr<ProcessInfo> process) {
    std::string path = getProcPath(pid, "cmdline");
    std::string content = readFileContent(path);
    
    // 替换null字符为空格
    std::replace(content.begin(), content.end(), '\0', ' ');
    process->command_line = Utils::trim(content);
    
    return true;
}

bool ProcessMonitor::readProcessCwd(pid_t pid, std::shared_ptr<ProcessInfo> process) {
    std::string path = getProcPath(pid, "cwd");
    char buffer[4096];
    ssize_t len = readlink(path.c_str(), buffer, sizeof(buffer) - 1);
    
    if (len > 0) {
        buffer[len] = '\0';
        process->cwd = std::string(buffer);
        return true;
    }
    
    return false;
}

bool ProcessMonitor::readProcessExe(pid_t pid, std::shared_ptr<ProcessInfo> process) {
    std::string path = getProcPath(pid, "exe");
    char buffer[4096];
    ssize_t len = readlink(path.c_str(), buffer, sizeof(buffer) - 1);
    
    if (len > 0) {
        buffer[len] = '\0';
        process->executable_path = std::string(buffer);
        return true;
    }
    
    return false;
}

double ProcessMonitor::calculateCpuUsage(pid_t id, const CpuStats& current_stats) {
    std::lock_guard<std::mutex> lock(ProcessMonitor_cpu_stats_mutex_);
    
    auto it = process_cpu_stats_.find(id);
    
    if (it == process_cpu_stats_.end()) {
        process_cpu_stats_[id] = current_stats;
        return 0.0;
    }
    
    const CpuStats& prev_stats = it->second;
    uint64_t time_diff = current_stats.timestamp - prev_stats.timestamp;
    uint64_t cpu_diff = current_stats.total_time - prev_stats.total_time;
    
    double usage = 0.0;
    if (time_diff > 0) {
        usage = (double(cpu_diff) / double(time_diff * clock_ticks_per_sec_)) * 100.0;
    }
    
    process_cpu_stats_[id] = current_stats;
    return usage;
}

std::shared_ptr<ProcessTreeNode> ProcessMonitor::buildProcessTree() const {
    std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);
    
    // 找到init进程(PID 1)作为根节点
    auto init_process = processes_.find(1);
    if (init_process == processes_.end()) {
        return nullptr;
    }
    
    auto root = std::make_shared<ProcessTreeNode>(init_process->second);
    
    // 构建父子关系映射
    std::unordered_map<pid_t, std::vector<pid_t>> children_map;
    for (const auto& pair : processes_) {
        pid_t ppid = pair.second->ppid;
        if (ppid > 0) {
            children_map[ppid].push_back(pair.first);
        }
    }
    
    addChildrenToNode(root, children_map);
    return root;
}

void ProcessMonitor::addChildrenToNode(std::shared_ptr<ProcessTreeNode> node, 
                                       const std::unordered_map<pid_t, std::vector<pid_t>>& children_map) const {
    auto it = children_map.find(node->process->pid);
    if (it == children_map.end()) {
        return;
    }
    
    for (pid_t child_pid : it->second) {
        auto child_process = processes_.find(child_pid);
        if (child_process != processes_.end()) {
            auto child_node = std::make_shared<ProcessTreeNode>(child_process->second);
            node->children.push_back(child_node);
            addChildrenToNode(child_node, children_map);
        }
    }
}

ProcessState ProcessMonitor::charToProcessState(char state_char) {
    switch (state_char) {
        case 'R': return ProcessState::RUNNING;
        case 'S': return ProcessState::SLEEPING;
        case 'D': return ProcessState::DISK_SLEEP;
        case 'Z': return ProcessState::ZOMBIE;
        case 'T': return ProcessState::STOPPED;
        case 'W': return ProcessState::PAGING;
        case 'X': return ProcessState::DEAD;
        default:  return ProcessState::UNKNOWN;
    }
}

std::string ProcessMonitor::processStateToString(ProcessState state) {
    switch (state) {
        case ProcessState::RUNNING:    return "RUNNING";
        case ProcessState::SLEEPING:   return "SLEEPING";
        case ProcessState::DISK_SLEEP: return "DISK_SLEEP";
        case ProcessState::ZOMBIE:     return "ZOMBIE";
        case ProcessState::STOPPED:    return "STOPPED";
        case ProcessState::PAGING:     return "PAGING";
        case ProcessState::DEAD:       return "DEAD";
        default:                       return "UNKNOWN";
    }
}

bool ProcessMonitor::fileExists(const std::string& path) {
    struct stat buffer;
    return (stat(path.c_str(), &buffer) == 0);
}

std::string ProcessMonitor::readFileContent(const std::string& path) {
    std::ifstream file(path);
    if (!file.is_open()) {
        return "";
    }
    
    std::stringstream buffer;
    buffer << file.rdbuf();
    return buffer.str();
}

std::vector<std::string> ProcessMonitor::readFileLines(const std::string& path) {
    std::vector<std::string> lines;
    std::ifstream file(path);
    std::string line;
    
    while (std::getline(file, line)) {
        lines.push_back(line);
    }
    
    return lines;
}

std::vector<std::string> ProcessMonitor::splitString(const std::string& str, char delimiter) {
    return Utils::split(str, delimiter);
}

std::vector<pid_t> ProcessMonitor::getPidList() {
    std::vector<pid_t> pids;
    DIR* proc_dir = opendir("/proc");
    
    if (!proc_dir) {
        return pids;
    }
    
    struct dirent* entry;
    while ((entry = readdir(proc_dir)) != nullptr) {
        if (entry->d_type == DT_DIR) {
            char* endptr;
            pid_t pid = strtol(entry->d_name, &endptr, 10);
            if (*endptr == '\0' && pid > 0) {
                pids.push_back(pid);
            }
        }
    }
    
    closedir(proc_dir);
    return pids;
}

std::string ProcessMonitor::getProcPath(pid_t pid, const std::string& file) {
    return "/proc/" + std::to_string(pid) + "/" + file;
}

void ProcessMonitor::handleNewProcess(pid_t pid) {
    // 【关键修复】：检查进程是否已存在，避免无条件替换
    std::shared_ptr<ProcessInfo> existing_process;
    bool process_exists = false;
    bool was_focus_process = false;
    
    {
        std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);
        auto it = processes_.find(pid);
        if (it != processes_.end()) {
            existing_process = it->second;
            process_exists = true;
            was_focus_process = existing_process->is_focus_process;
        }
    }
    
    // 如果进程已存在，只更新信息，不替换整个对象
    if (process_exists) {
        if (was_focus_process) {
            logDebug("【重点进程保护】发现已存在的重点进程，仅更新信息: PID=" + std::to_string(pid));
        }
        
        // 只更新进程信息，保持现有状态
        readProcessInfo(pid, existing_process);
        
        // 【重点进程状态同步验证】：确保与FocusProcessManager一致
        if (was_focus_process) {
            auto& focus_manager = FocusProcessManager::getInstance();
            if (!focus_manager.isFocusProcess(pid)) {
                // 如果FocusProcessManager中没有记录，重新添加
                focus_manager.addFocusProcess(pid);
                logDebug("【状态同步修复】重新添加重点进程到FocusProcessManager: PID=" + std::to_string(pid));
            }
        }
        return;
    }
    
    // 如果进程不存在，创建新的ProcessInfo对象
    auto process = std::make_shared<ProcessInfo>();
    process->pid = pid;
    process->start_time = Utils::getCurrentTimestamp();
    
    if (readProcessInfo(pid, process)) {
        // 【重点进程状态恢复】：检查是否应该是重点进程
        auto& focus_manager = FocusProcessManager::getInstance();
        if (focus_manager.isFocusProcess(pid)) {
            process->setFocusProcess(true);
            logDebug("【状态恢复】恢复重点进程状态: PID=" + std::to_string(pid));
        } else {
            // 按进程名检查
            std::string process_name = process->process_name;
            if (!process_name.empty() && focus_manager.isFocusProcess(process_name)) {
                process->setFocusProcess(true);
                // 同步添加到FocusProcessManager的PID列表
                focus_manager.addFocusProcess(pid);
                logDebug("【状态恢复】根据进程名恢复重点进程状态: PID=" + std::to_string(pid) + ", Name=" + process_name);
            }
        }
        
        std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);
        processes_[pid] = process;
        
        if (process->is_focus_process) {
            logDebug("【新建重点进程】成功创建重点进程: PID=" + std::to_string(pid) + ", Name=" + process->process_name);
        }
    }
}

void ProcessMonitor::handleProcessExit(pid_t pid) {
    logDebug("处理进程退出: PID=" + std::to_string(pid));

    // 使用单一锁来避免死锁
    std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);

    auto it = processes_.find(pid);
    if (it != processes_.end()) {
        // 如果是重点进程，同步清理FocusProcessManager中的记录
        if (it->second->is_focus_process) {
            auto& focus_manager = FocusProcessManager::getInstance();
            focus_manager.removeFocusProcess(pid);
            logDebug("同步清理重点进程记录: PID=" + std::to_string(pid));
        }

        // 清理进程CPU统计数据
        process_cpu_stats_.erase(pid);
        // 从进程列表中移除
        processes_.erase(it);
        logDebug("成功清理退出进程数据: PID=" + std::to_string(pid));
    }
}

void ProcessMonitor::cleanupDeadProcesses() {
    try {
        // 获取当前系统中所有活跃的进程ID
        std::vector<pid_t> current_pids = getPidList();
        std::set<pid_t> current_pid_set(current_pids.begin(), current_pids.end());

        // 获取我们监控的所有进程ID - 使用局部作用域确保锁及时释放
        std::vector<pid_t> monitored_pids;
        {
            std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);
            monitored_pids.reserve(processes_.size());
            for (const auto& process_pair : processes_) {
                monitored_pids.push_back(process_pair.first);
            }
        }

        // 【新逻辑】：获取所有重点进程ID，用于保护
        std::vector<pid_t> focus_process_pids;
        {
            std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);
            for (const auto& process_pair : processes_) {
                if (process_pair.second->is_focus_process) {
                    focus_process_pids.push_back(process_pair.first);
                }
            }
        }

        // 【修改后的逻辑】：检查每个监控的进程是否仍然存在，保护重点进程
        std::vector<pid_t> potentially_dead_pids;
        for (pid_t pid : monitored_pids) {
            if (current_pid_set.find(pid) == current_pid_set.end()) {
                // 如果是重点进程，跳过这个进程的清理，但不跳过所有检查
                if (std::find(focus_process_pids.begin(), focus_process_pids.end(), pid) != focus_process_pids.end()) {
                    logDebug("cleanupDeadProcesses: 重点进程保护，跳过自动清理: PID=" + std::to_string(pid) + 
                            " (提示：只有手动从重点进程列表移除后才会被清理)");
                    continue;
                }
                potentially_dead_pids.push_back(pid);
            }
        }

        // 对剩余的可能死亡进程进行严格的二次验证
        std::vector<pid_t> confirmed_dead_pids;
        for (pid_t pid : potentially_dead_pids) {
            // 使用更严格的进程存在检查
            if (!Utils::processExistsStrict(pid)) {
                confirmed_dead_pids.push_back(pid);
                logDebug("cleanupDeadProcesses严格验证确认死进程: PID=" + std::to_string(pid));
            } else {
                logDebug("cleanupDeadProcesses严格验证发现进程仍活跃，跳过清理: PID=" + std::to_string(pid));
            }
        }

        // 清理确认死亡的普通进程（非重点进程）
        for (pid_t pid : confirmed_dead_pids) {
            logDebug("cleanupDeadProcesses清理死进程: PID=" + std::to_string(pid));
            handleProcessExit(pid);
        }
        
        // 清理规则匹配统计信息
        RuleMatchManager::getInstance().cleanupDeadProcesses(current_pid_set);

        // 【重点进程状态验证】：清理完成后验证所有重点进程状态
        for (pid_t focus_pid : focus_process_pids) {
            if (current_pid_set.find(focus_pid) != current_pid_set.end()) {
                // 重点进程仍然活跃，验证其状态
                std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);
                auto it = processes_.find(focus_pid);
                if (it != processes_.end()) {
                    if (!it->second->is_focus_process) {
                        logError("【状态异常】cleanupDeadProcesses中检测到重点进程状态丢失，尝试恢复: PID=" + std::to_string(focus_pid));
                        it->second->setFocusProcess(true);
                        logInfo("【状态恢复】cleanupDeadProcesses中成功恢复重点进程状态: PID=" + std::to_string(focus_pid));
                    }
                }
            }
        }

        // 统计保护效果
        size_t protected_count = focus_process_pids.size();
        size_t false_positive_count = potentially_dead_pids.size() - confirmed_dead_pids.size();
        
        if (protected_count > 0) {
            logDebug("cleanupDeadProcesses: 保护了 " + std::to_string(protected_count) + " 个重点进程");
        }
        if (false_positive_count > 0) {
            logDebug("cleanupDeadProcesses: getPidList() 漏掉了 " + std::to_string(false_positive_count) + " 个活跃进程，已通过严格验证避免误删");
        }
        
    } catch (const std::exception& e) {
        logError("清理死进程异常: " + std::string(e.what()));
    }
}

void ProcessMonitor::initializeSystemInfo() {
    num_cpu_cores_ = Utils::getCpuCores();
    page_size_ = Utils::getPageSize();
    clock_ticks_per_sec_ = sysconf(_SC_CLK_TCK);
}

bool ProcessMonitor::needsFullScan() const {
    time_t now = time(nullptr);
    
    // 基本的60秒扫描周期
    if ((now - last_full_scan_) <= 60) {
        return false;
    }
    
    // 【重点进程保护】：检查是否有活跃的重点进程，如果有则延迟完整扫描
    bool has_active_focus_processes = false;
    {
        std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);
        for (const auto& process_pair : processes_) {
            if (process_pair.second->is_focus_process) {
                has_active_focus_processes = true;
                break;
            }
        }
    }
    
    if (has_active_focus_processes) {
        // 有重点进程时，延迟完整扫描到120秒
        if ((now - last_full_scan_) < 120) {
            // 发现活跃重点进程，延迟完整扫描
            return false;
        }
    }
    
    return true;
}

void ProcessMonitor::performIncrementalUpdate() {
    // 增量更新现有进程
    std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);
    for (const auto& pair : processes_) {
        readProcessInfo(pair.first, pair.second);
        
        // 定期清理重点进程的过期文件监控记录
        if (pair.second->is_focus_process && pair.second->focus_file_info) {
            pair.second->focus_file_info->cleanup();
        }
    }
}

void ProcessMonitor::performFullScan() {
    scanProcesses();
    last_full_scan_ = time(nullptr);
}

void ProcessMonitor::logError(const std::string& message) {
    LogManager::getInstance().error("[ProcessMonitor] " + message);
}

void ProcessMonitor::logInfo(const std::string& message) {
    LogManager::getInstance().info("[ProcessMonitor] " + message);
}

void ProcessMonitor::logDebug(const std::string& message) {
    LogManager::getInstance().debug("[ProcessMonitor] " + message);
}

// 注意：删除了详细的文件操作序列和熵值记录功能

void ProcessMonitor::recordFocusProcessDelete(pid_t pid, const std::string& file_path) {
    std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);
    
    auto process_it = processes_.find(pid);
    if (process_it == processes_.end()) {
        return;
    }
    
    auto process = process_it->second;
    if (!process->is_focus_process || !process->focus_file_info) {
        return;
    }
    
    process->focus_file_info->recordDelete(file_path);
    
    logInfo("重点进程删除操作: PID=" + std::to_string(pid) + ", Path=" + file_path);
}

void ProcessMonitor::recordFocusProcessRename(pid_t pid, const std::string& old_path, const std::string& new_path) {
    std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);
    
    auto process_it = processes_.find(pid);
    if (process_it == processes_.end()) {
        return;
    }
    
    auto process = process_it->second;
    if (!process->is_focus_process || !process->focus_file_info) {
        return;
    }
    
    process->focus_file_info->recordRename(old_path, new_path);
    
    logInfo("重点进程重命名操作: PID=" + std::to_string(pid) + ", From=" + old_path + ", To=" + new_path);
}

void ProcessMonitor::addReadEntropyToFocusProcess(pid_t pid, double entropy) {
    std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);

    auto process_it = processes_.find(pid);
    if (process_it == processes_.end()) {
        return;
    }

    auto process = process_it->second;
    if (!process->is_focus_process || !process->focus_file_info) {
        return;
    }

    process->focus_file_info->addReadEntropy(entropy);

    logDebug("重点进程读熵值: PID=" + std::to_string(pid) + ", Entropy=" + std::to_string(entropy));
}

void ProcessMonitor::addWriteEntropyToFocusProcess(pid_t pid, double entropy) {
    std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);

    auto process_it = processes_.find(pid);
    if (process_it == processes_.end()) {
        return;
    }

    auto process = process_it->second;
    if (!process->is_focus_process || !process->focus_file_info) {
        return;
    }

    process->focus_file_info->addWriteEntropy(entropy);

    logDebug("重点进程写熵值: PID=" + std::to_string(pid) + ", Entropy=" + std::to_string(entropy));
}

void ProcessMonitor::addFilePathToHistory(pid_t pid, const std::string& file_path) {
    std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);

    auto process_it = processes_.find(pid);
    if (process_it == processes_.end()) {
        return;
    }

    auto process = process_it->second;
    if (!process->is_focus_process || !process->focus_file_info) {
        return;
    }

    process->focus_file_info->addFilePathToHistory(file_path);

    logDebug("重点进程文件路径历史: PID=" + std::to_string(pid) + ", Path=" + file_path);
}

// 新的文件熵值记录方法
void ProcessMonitor::recordFileReadOnceEntropy(pid_t pid, const std::string& file_path, double entropy, size_t file_size) {
    std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);

    logDebug("【调试】recordFileReadOnceEntropy调用: PID=" + std::to_string(pid) + ", Path=" + file_path + ", Entropy=" + std::to_string(entropy));

    auto process_it = processes_.find(pid);
    if (process_it == processes_.end()) {
        logDebug("【调试】进程不在processes_映射表中: PID=" + std::to_string(pid) + ", 当前进程总数=" + std::to_string(processes_.size()));
        
        // 输出所有进程PID以便调试
        std::string all_pids;
        for (const auto& pair : processes_) {
            if (!all_pids.empty()) all_pids += ", ";
            all_pids += std::to_string(pair.first);
        }
        logDebug("【调试】当前所有进程PID: " + all_pids);
        return;
    }

    auto process = process_it->second;
    if (!process->is_focus_process || !process->focus_file_info) {
        logDebug("【调试】进程不是重点进程或focus_file_info为空: PID=" + std::to_string(pid) + 
                 ", is_focus_process=" + (process->is_focus_process ? "true" : "false") + 
                 ", focus_file_info=" + (process->focus_file_info ? "valid" : "null"));
        return;
    }

    process->focus_file_info->recordFileReadOnce(file_path, entropy, file_size);

    logDebug("【调试】成功记录文件读取熵值: PID=" + std::to_string(pid) + 
             ", Path=" + file_path + ", Entropy=" + std::to_string(entropy) + 
             ", Size=" + std::to_string(file_size));
}

void ProcessMonitor::recordFileCloseWriteEntropy(pid_t pid, const std::string& file_path, double entropy, size_t file_size) {
    std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);

    logDebug("【调试】recordFileCloseWriteEntropy调用: PID=" + std::to_string(pid) + ", Path=" + file_path + ", Entropy=" + std::to_string(entropy));

    auto process_it = processes_.find(pid);
    if (process_it == processes_.end()) {
        logDebug("【调试】进程不在processes_映射表中: PID=" + std::to_string(pid) + ", 当前进程总数=" + std::to_string(processes_.size()));
        return;
    }

    auto process = process_it->second;
    if (!process->is_focus_process || !process->focus_file_info) {
        logDebug("【调试】进程不是重点进程或focus_file_info为空: PID=" + std::to_string(pid) + 
                 ", is_focus_process=" + (process->is_focus_process ? "true" : "false") + 
                 ", focus_file_info=" + (process->focus_file_info ? "valid" : "null"));
        return;
    }

    process->focus_file_info->recordFileCloseWrite(file_path, entropy, file_size);

    logDebug("【调试】成功记录文件关闭熵值: PID=" + std::to_string(pid) +
             ", Path=" + file_path + ", Entropy=" + std::to_string(entropy) +
             ", Size=" + std::to_string(file_size));
}

// 【性能优化】：检查是否需要计算熵值（避免重复计算）
bool ProcessMonitor::needsEntropyCalculation(pid_t pid, const std::string& file_path) const {
    std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);

    auto process_it = processes_.find(pid);
    if (process_it == processes_.end()) {
        return false;  // 进程不存在，不需要计算
    }

    auto process = process_it->second;
    if (!process->is_focus_process || !process->focus_file_info) {
        return false;  // 不是重点进程，不需要计算
    }

    // 检查该文件是否已经记录过原始熵值
    return process->focus_file_info->needsOriginalEntropyCalculation(file_path);
}

// 获取文件熵值信息
std::vector<FileEntropyInfo> ProcessMonitor::getFocusProcessFileEntropyInfo(pid_t pid) const {
    std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);

    auto process_it = processes_.find(pid);
    if (process_it == processes_.end()) {
        return std::vector<FileEntropyInfo>();
    }

    auto process = process_it->second;
    if (!process->is_focus_process || !process->focus_file_info) {
        return std::vector<FileEntropyInfo>();
    }

    return process->focus_file_info->getAllFileEntropyInfo();
}

double ProcessMonitor::getFocusProcessTotalOriginalEntropy(pid_t pid) const {
    std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);

    auto process_it = processes_.find(pid);
    if (process_it == processes_.end()) {
        return 0.0;
    }

    auto process = process_it->second;
    if (!process->is_focus_process || !process->focus_file_info) {
        return 0.0;
    }

    return process->focus_file_info->getTotalOriginalEntropy();
}

double ProcessMonitor::getFocusProcessTotalFinalEntropy(pid_t pid) const {
    std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);

    auto process_it = processes_.find(pid);
    if (process_it == processes_.end()) {
        return 0.0;
    }

    auto process = process_it->second;
    if (!process->is_focus_process || !process->focus_file_info) {
        return 0.0;
    }

    return process->focus_file_info->getTotalFinalEntropy();
}

double ProcessMonitor::getFocusProcessTotalEntropyChange(pid_t pid) const {
    std::lock_guard<std::mutex> lock(ProcessMonitor_processes_mutex_);

    auto process_it = processes_.find(pid);
    if (process_it == processes_.end()) {
        return 0.0;
    }

    auto process = process_it->second;
    if (!process->is_focus_process || !process->focus_file_info) {
        return 0.0;
    }

    return process->focus_file_info->getTotalEntropyChange();
}

// ProcessMonitorManager 实现
ProcessMonitorManager& ProcessMonitorManager::getInstance() {
    static ProcessMonitorManager instance;
    return instance;
}

bool ProcessMonitorManager::initialize() {
    if (!process_monitor_) {
        process_monitor_.reset(new ProcessMonitor());
    }
    return process_monitor_->initialize();
}

bool ProcessMonitorManager::start() {
    if (!process_monitor_) {
        return false;
    }
    return process_monitor_->start();
}

void ProcessMonitorManager::stop() {
    if (process_monitor_) {
        process_monitor_->stop();
    }
}

bool ProcessMonitorManager::reinitialize() {
    LogManager::getInstance().systemInfo("重新初始化进程监控器...");
    
    try {
        // 停止当前的进程监控器
        if (process_monitor_) {
            process_monitor_->stop();
        }
        
        // 创建新的进程监控器实例
        process_monitor_.reset(new ProcessMonitor());
        
        // 重新初始化
        if (!process_monitor_->initialize()) {
            LogManager::getInstance().systemError("重新初始化进程监控器失败");
            return false;
        }
        
        // 重新启动
        if (!process_monitor_->start()) {
            LogManager::getInstance().systemError("重新启动进程监控器失败");
            return false;
        }
        
        LogManager::getInstance().systemInfo("进程监控器重新初始化成功");
        return true;
        
    } catch (const std::exception& e) {
        LogManager::getInstance().systemError("重新初始化进程监控器时发生异常: " + std::string(e.what()));
        return false;
    }
}
