/*
 * HTTP服务器实现
 * 
 * 提供基础的HTTP服务功能，支持RESTful API路由和请求处理
 */

#include "api_server.h"
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <fcntl.h>
#include <cstring>
#include <sstream>
#include <thread>
#include <errno.h>
#include <chrono>

// 添加SO_REUSEPORT的兼容性定义
#ifndef SO_REUSEPORT
#define SO_REUSEPORT 15
#endif

// HttpServer实现
HttpServer::HttpServer() : port_(0), server_socket_(-1), running_(false), 
                           stop_requested_(false), max_worker_threads_(4),
                           request_timeout_(30), max_request_size_(1024*1024),
                           max_header_size_(8192) {
}

HttpServer::~HttpServer() {
    stop();
}

bool HttpServer::initialize(uint16_t port, const std::string& bind_address) {
    port_ = port;
    bind_address_ = bind_address;
    
    logInfo("正在初始化HTTP服务器 - 地址: " + bind_address + ", 端口: " + std::to_string(port));
    
    server_socket_ = socket(AF_INET, SOCK_STREAM, 0);
    if (server_socket_ < 0) {
        logError("无法创建socket: " + std::string(strerror(errno)));
        return false;
    }
    
    // 设置socket选项 - 允许地址重用
    int opt = 1;
    if (setsockopt(server_socket_, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt)) < 0) {
        logError("设置SO_REUSEADDR失败: " + std::string(strerror(errno)));
    }
    
    // 设置SO_REUSEPORT选项（如果支持）- 允许端口重用
    if (setsockopt(server_socket_, SOL_SOCKET, SO_REUSEPORT, &opt, sizeof(opt)) < 0) {
        logDebug("设置SO_REUSEPORT失败（可能不支持）: " + std::string(strerror(errno)));
    }
    
    struct sockaddr_in addr;
    memset(&addr, 0, sizeof(addr));
    addr.sin_family = AF_INET;
    addr.sin_port = htons(port);
    
    // 使用更安全的IP地址解析方法
    if (bind_address == "0.0.0.0") {
        addr.sin_addr.s_addr = INADDR_ANY;
    } else if (bind_address == "127.0.0.1" || bind_address == "localhost") {
        addr.sin_addr.s_addr = htonl(INADDR_LOOPBACK);
    } else {
        // 使用inet_pton替代inet_addr
        if (inet_pton(AF_INET, bind_address.c_str(), &addr.sin_addr) <= 0) {
            logError("无效的IP地址: " + bind_address);
            close(server_socket_);
            server_socket_ = -1;
            return false;
        }
    }
    
    logDebug("尝试绑定到 " + bind_address + ":" + std::to_string(port));
    
    if (bind(server_socket_, (struct sockaddr*)&addr, sizeof(addr)) < 0) {
        int error_code = errno;
        std::string error_msg = "bind失败: " + std::string(strerror(error_code)) + 
                               " (地址: " + bind_address + ", 端口: " + std::to_string(port) + 
                               ", 错误码: " + std::to_string(error_code) + ")";
        
        if (error_code == EADDRINUSE) {
            error_msg += " - 地址已被使用";
        } else if (error_code == EACCES) {
            error_msg += " - 权限不足";
        } else if (error_code == EADDRNOTAVAIL) {
            error_msg += " - 地址不可用";
        }
        
        logError(error_msg);
        close(server_socket_);
        server_socket_ = -1;
        return false;
    }
    
    logDebug("bind成功，开始listen");
    
    if (listen(server_socket_, 10) < 0) {
        logError("listen失败: " + std::string(strerror(errno)));
        close(server_socket_);
        server_socket_ = -1;
        return false;
    }
    
    logInfo("HTTP服务器初始化成功 - " + bind_address + ":" + std::to_string(port));
    return true;
}

bool HttpServer::start() {
    if (running_ || server_socket_ < 0) {
        return false;
    }
    
    running_ = true;
    stop_requested_ = false;
    
    // 启动工作线程池
    for (size_t i = 0; i < max_worker_threads_; ++i) {
        worker_threads_.emplace_back([this]() {
            workerLoop();
        });
    }
    
    // 启动服务器主循环
    std::thread([this]() {
        serverLoop();
    }).detach();
    
    logInfo("HTTP服务器已启动，端口: " + std::to_string(port_));
    return true;
}

void HttpServer::stop() {
    if (!running_) {
        return;
    }
    
    logInfo("正在停止HTTP服务器...");
    stop_requested_ = true;
    running_ = false;
    
    // 关闭服务器socket，这会使accept()立即返回错误
    if (server_socket_ >= 0) {
        shutdown(server_socket_, SHUT_RDWR);  // 先关闭读写
        close(server_socket_);
        server_socket_ = -1;
        logDebug("服务器socket已关闭");
    }
    
    // 通知所有工作线程停止
    {
        std::lock_guard<std::mutex> lock(HttpServer_socket_queue_mutex_);
        // 清空队列中的客户端socket
        while (!client_sockets_.empty()) {
            int client_socket = client_sockets_.front();
            client_sockets_.pop();
            if (client_socket >= 0) {
                shutdown(client_socket, SHUT_RDWR);
                close(client_socket);
            }
        }
        socket_condition_.notify_all();
    }
    
    // 等待所有工作线程结束（设置超时）
    const auto start_time = std::chrono::steady_clock::now();
    const auto timeout = std::chrono::seconds(5);
    
    for (auto& thread : worker_threads_) {
        if (thread.joinable()) {
            auto elapsed = std::chrono::steady_clock::now() - start_time;
            if (elapsed < timeout) {
                thread.join();
            } else {
                logError("工作线程停止超时，强制退出");
                // 注意：这里不能强制终止线程，只能记录错误
            }
        }
    }
    worker_threads_.clear();
    
    logInfo("HTTP服务器已停止");
}

void HttpServer::registerRoute(HttpMethod method, const std::string& path, ApiHandler handler) {
    std::lock_guard<std::mutex> lock(HttpServer_routes_mutex_);
    routes_[std::make_pair(method, path)] = handler;
}

void HttpServer::setStaticDirectory(const std::string& directory) {
    static_directory_ = directory;
}

void HttpServer::setRequestTimeout(uint32_t timeout_seconds) {
    request_timeout_ = timeout_seconds;
}

void HttpServer::serverLoop() {
    logInfo("服务器主循环已启动");
    while (!stop_requested_) {
        struct sockaddr_in client_addr;
        socklen_t client_len = sizeof(client_addr);
        
        logDebug("等待客户端连接...");
        int client_socket = accept(server_socket_, (struct sockaddr*)&client_addr, &client_len);
        
        if (client_socket >= 0) {
            logDebug("接受到客户端连接，socket: " + std::to_string(client_socket));
            {
                std::lock_guard<std::mutex> lock(HttpServer_socket_queue_mutex_);
                client_sockets_.push(client_socket);
            }
            socket_condition_.notify_one();
        } else {
            // 添加错误处理
            if (errno == EINTR) {
                logDebug("accept被信号中断，继续等待");
                continue;
            } else if (errno == EAGAIN || errno == EWOULDBLOCK) {
                logDebug("accept暂时无连接，继续等待");
                continue;
            } else if (!stop_requested_) {
                logError("accept失败: " + std::string(strerror(errno)));
                break;
            }
        }
    }
    logInfo("服务器主循环已退出");
}

void HttpServer::workerLoop() {
    while (!stop_requested_) {
        int client_socket = -1;
        
        {
            std::unique_lock<std::mutex> lock(HttpServer_socket_queue_mutex_);
            socket_condition_.wait(lock, [this]() {
                return !client_sockets_.empty() || stop_requested_;
            });
            
            if (stop_requested_) {
                break;
            }
            
            if (!client_sockets_.empty()) {
                client_socket = client_sockets_.front();
                client_sockets_.pop();
            }
        }
        
        if (client_socket >= 0) {
            handleClient(client_socket);
        }
    }
}

void HttpServer::handleClient(int client_socket) {
    logDebug("开始处理客户端连接，socket: " + std::to_string(client_socket));
    
    // 设置socket选项，确保连接能够正确关闭
    int opt = 1;
    setsockopt(client_socket, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt));
    
    // 设置linger选项，确保连接正确关闭
    struct linger linger_opt;
    linger_opt.l_onoff = 1;   // 启用linger
    linger_opt.l_linger = 1;  // 等待1秒
    setsockopt(client_socket, SOL_SOCKET, SO_LINGER, &linger_opt, sizeof(linger_opt));
    
    // 设置接收超时
    struct timeval timeout;
    timeout.tv_sec = request_timeout_;
    timeout.tv_usec = 0;
    setsockopt(client_socket, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout));
    
    const size_t buffer_size = 8192;
    char buffer[buffer_size];
    
    ssize_t bytes_received = recv(client_socket, buffer, buffer_size - 1, 0);
    if (bytes_received <= 0) {
        if (bytes_received == 0) {
            logDebug("客户端正常关闭连接");
        } else {
            logDebug("接收数据失败: " + std::string(strerror(errno)));
        }
        shutdown(client_socket, SHUT_RDWR);
        close(client_socket);
        return;
    }
    
    buffer[bytes_received] = '\0';
    std::string request_data(buffer);
    
    logDebug("收到HTTP请求，长度: " + std::to_string(bytes_received));
    
    // 解析HTTP请求
    HttpRequest request;
    HttpResponse response;
    
    if (!parseHttpRequest(request_data, request)) {
        logError("HTTP请求解析失败");
        response.status = HttpStatus::BAD_REQUEST;
        response.body = "Invalid HTTP request";
    } else {
        logDebug("解析HTTP请求成功，方法: " + methodToString(request.method) + ", 路径: " + request.path);
        
        // 查找处理器
        ApiHandler handler = findHandler(request.method, request.path);
        if (handler) {
            logDebug("找到API处理器，开始处理请求");
            try {
                // 设置响应头，确保连接正确关闭
                response.headers["Connection"] = "close";
                response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate";
                
                handler(request, response);
                logDebug("API处理器执行完成");
            } catch (const std::exception& e) {
                logError("API处理器执行异常: " + std::string(e.what()));
                response.status = HttpStatus::INTERNAL_SERVER_ERROR;
                response.body = "Internal server error: " + std::string(e.what());
                response.headers["Connection"] = "close";
            }
        } else {
            logDebug("未找到路径的处理器: " + request.path);
            response.status = HttpStatus::NOT_FOUND;
            response.body = "Not Found";
            response.headers["Connection"] = "close";
        }
    }
    
    // 发送响应
    std::string response_str = buildHttpResponse(response);
    logDebug("发送响应，长度: " + std::to_string(response_str.length()));
    
    ssize_t sent = send(client_socket, response_str.c_str(), response_str.length(), MSG_NOSIGNAL);
    if (sent < 0) {
        logError("发送响应失败: " + std::string(strerror(errno)));
    } else {
        logDebug("响应发送成功，发送字节数: " + std::to_string(sent));
    }
    
    // 确保数据发送完毕
    fsync(client_socket);
    
    // 正确关闭连接
    shutdown(client_socket, SHUT_RDWR);
    close(client_socket);
    logDebug("客户端连接处理完成，socket已正确关闭");
}

bool HttpServer::parseHttpRequest(const std::string& request_data, HttpRequest& request) {
    std::istringstream iss(request_data);
    std::string line;
    
    // 解析请求行
    if (!std::getline(iss, line)) {
        return false;
    }
    
    std::istringstream request_line(line);
    std::string method_str, path_with_query, version;
    
    if (!(request_line >> method_str >> path_with_query >> version)) {
        return false;
    }
    
    // 解析方法
    request.method = stringToMethod(method_str);
    if (request.method == HttpMethod::UNKNOWN) {
        return false;
    }
    
    // 解析路径和查询参数
    size_t query_pos = path_with_query.find('?');
    if (query_pos != std::string::npos) {
        request.path = path_with_query.substr(0, query_pos);
        request.query_string = path_with_query.substr(query_pos + 1);
        request.query_params = parseQueryString(request.query_string);
    } else {
        request.path = path_with_query;
    }
    
    // 解析请求头
    while (std::getline(iss, line) && !line.empty() && line != "\r") {
        size_t colon_pos = line.find(':');
        if (colon_pos != std::string::npos) {
            std::string header_name = line.substr(0, colon_pos);
            std::string header_value = line.substr(colon_pos + 1);
            
            // 去除空格
            header_value.erase(0, header_value.find_first_not_of(" \t"));
            header_value.erase(header_value.find_last_not_of(" \t\r") + 1);
            
            request.headers[header_name] = header_value;
        }
    }
    
    // 读取请求体
    std::string body_line;
    while (std::getline(iss, body_line)) {
        request.body += body_line + "\n";
    }
    
    // 去除最后的换行符
    if (!request.body.empty() && request.body.back() == '\n') {
        request.body.pop_back();
    }
    
    return true;
}

std::string HttpServer::buildHttpResponse(const HttpResponse& response) {
    std::ostringstream oss;
    
    oss << "HTTP/1.1 " << static_cast<int>(response.status) << " " 
        << statusToString(response.status) << "\r\n";
    
    oss << "Content-Type: " << response.content_type << "\r\n";
    oss << "Content-Length: " << response.body.length() << "\r\n";
    
    for (const auto& header : response.headers) {
        oss << header.first << ": " << header.second << "\r\n";
    }
    
    oss << "\r\n" << response.body;
    
    return oss.str();
}

ApiHandler HttpServer::findHandler(HttpMethod method, const std::string& path) {
    std::lock_guard<std::mutex> lock(HttpServer_routes_mutex_);
    
    auto it = routes_.find(std::make_pair(method, path));
    if (it != routes_.end()) {
        return it->second;
    }
    
    return nullptr;
}

bool HttpServer::matchRoute(const std::string& pattern, const std::string& path) {
    // 简单的路径匹配，可以扩展为支持通配符
    return pattern == path;
}

void HttpServer::handleStaticFile(const HttpRequest& request, HttpResponse& response) {
    // 静态文件处理的简单实现
    (void)request;  // 避免未使用参数警告
    response.status = HttpStatus::NOT_FOUND;
    response.body = "Static file serving not implemented";
}

std::string HttpServer::getMimeType(const std::string& file_extension) {
    if (file_extension == ".html" || file_extension == ".htm") {
        return "text/html";
    } else if (file_extension == ".css") {
        return "text/css";
    } else if (file_extension == ".js") {
        return "application/javascript";
    } else if (file_extension == ".json") {
        return "application/json";
    } else if (file_extension == ".png") {
        return "image/png";
    } else if (file_extension == ".jpg" || file_extension == ".jpeg") {
        return "image/jpeg";
    } else {
        return "application/octet-stream";
    }
}

HttpMethod HttpServer::stringToMethod(const std::string& method_str) {
    if (method_str == "GET") return HttpMethod::GET;
    if (method_str == "POST") return HttpMethod::POST;
    if (method_str == "PUT") return HttpMethod::PUT;
    if (method_str == "DELETE") return HttpMethod::DELETE;
    return HttpMethod::UNKNOWN;
}

std::string HttpServer::methodToString(HttpMethod method) {
    switch (method) {
        case HttpMethod::GET: return "GET";
        case HttpMethod::POST: return "POST";
        case HttpMethod::PUT: return "PUT";
        case HttpMethod::DELETE: return "DELETE";
        default: return "UNKNOWN";
    }
}

std::string HttpServer::statusToString(HttpStatus status) {
    switch (status) {
        case HttpStatus::OK: return "OK";
        case HttpStatus::CREATED: return "Created";
        case HttpStatus::BAD_REQUEST: return "Bad Request";
        case HttpStatus::NOT_FOUND: return "Not Found";
        case HttpStatus::INTERNAL_SERVER_ERROR: return "Internal Server Error";
        default: return "Unknown";
    }
}

std::map<std::string, std::string> HttpServer::parseQueryString(const std::string& query) {
    std::map<std::string, std::string> params;
    
    if (query.empty()) {
        return params;
    }
    
    std::stringstream ss(query);
    std::string item;
    
    while (std::getline(ss, item, '&')) {
        size_t eq_pos = item.find('=');
        if (eq_pos != std::string::npos) {
            std::string key = item.substr(0, eq_pos);
            std::string value = item.substr(eq_pos + 1);
            params[key] = value;
        }
    }
    
    return params;
}

std::string HttpServer::urlDecode(const std::string& str) {
    // URL解码的简单实现
    std::string result;
    for (size_t i = 0; i < str.length(); ++i) {
        if (str[i] == '%' && i + 2 < str.length()) {
            // 解析十六进制字符
            char hex[3] = {str[i+1], str[i+2], '\0'};
            char* end;
            long value = strtol(hex, &end, 16);
            if (*end == '\0') {
                result += static_cast<char>(value);
                i += 2;
            } else {
                result += str[i];
            }
        } else if (str[i] == '+') {
            result += ' ';
        } else {
            result += str[i];
        }
    }
    return result;
}

std::string HttpServer::urlEncode(const std::string& str) {
    // URL编码的简单实现
    std::string result;
    for (char c : str) {
        if (std::isalnum(c) || c == '-' || c == '_' || c == '.' || c == '~') {
            result += c;
        } else {
            std::ostringstream oss;
            oss << '%' << std::hex << std::uppercase << static_cast<unsigned char>(c);
            result += oss.str();
        }
    }
    return result;
}

void HttpServer::logError(const std::string& message) {
    LogManager::getInstance().error("[HttpServer] " + message);
}

void HttpServer::logInfo(const std::string& message) {
    LogManager::getInstance().info("[HttpServer] " + message);
}

void HttpServer::logDebug(const std::string& message) {
    LogManager::getInstance().debug("[HttpServer] " + message);
} 