#include "psfsmon.h"
#include "main_monitor.h"
#include <algorithm>
#include <cmath>
#include <functional>
#include <cstring>

// 定义FocusProcessFileInfo的静态成员变量
constexpr size_t FocusProcessFileInfo::MAX_FILE_SIZE_FOR_ENTROPY;

// FocusProcessFileInfo 方法实现
void FocusProcessFileInfo::addReadEntropy(double entropy) {
    std::lock_guard<std::mutex> lock(FocusProcessFileInfo_data_mutex_);
    total_read_entropy += entropy;
    read_entropy_count++;
}

void FocusProcessFileInfo::addWriteEntropy(double entropy) {
    std::lock_guard<std::mutex> lock(FocusProcessFileInfo_data_mutex_);
    total_write_entropy += entropy;
    write_entropy_count++;
}

void FocusProcessFileInfo::recordDelete(const std::string& path) {
    std::lock_guard<std::mutex> lock(FocusProcessFileInfo_data_mutex_);
    delete_count++;
    recordPathOperation(path);
}

void FocusProcessFileInfo::recordRename(const std::string& old_path, const std::string& new_path) {
    std::lock_guard<std::mutex> lock(FocusProcessFileInfo_data_mutex_);
    rename_count++;
    
    // 记录路径操作
    recordPathOperation(old_path);
    recordPathOperation(new_path);
    
    // 提取和记录扩展名变化
    auto getExtension = [](const std::string& path) -> std::string {
        size_t dot_pos = path.find_last_of('.');
        if (dot_pos != std::string::npos && dot_pos != path.length() - 1) {
            return path.substr(dot_pos + 1);
        }
        return "";
    };
    
    std::string old_ext = getExtension(old_path);
    std::string new_ext = getExtension(new_path);
    
    if (old_ext != new_ext) {
        std::string ext_change = old_ext + "->" + new_ext;

        // 检查扩展名变化是否已经存在，避免重复记录
        auto it = std::find(rename_extensions.begin(), rename_extensions.end(), ext_change);
        if (it == rename_extensions.end()) {
            // 扩展名变化不存在，添加到记录中
            if (rename_extensions.size() < Constants::MAX_EXTENSION_LIST) {
                rename_extensions.push_back(ext_change);
                LogManager::getInstance().debug("[FocusProcessFileInfo] 记录新扩展名变化: " + ext_change);
            } else {
                LogManager::getInstance().debug("[FocusProcessFileInfo] 扩展名变化列表已满，跳过: " + ext_change);
            }
        } else {
            LogManager::getInstance().debug("[FocusProcessFileInfo] 扩展名变化已存在，跳过重复记录: " + ext_change);
        }
    }
}

void FocusProcessFileInfo::recordPathOperation(const std::string& path) {
    // 注意：这里不需要再次加锁，因为调用此方法的函数已经加锁

    // 检查路径是否已经存在，避免记录重复的路径
    auto it = std::find(path_history.begin(), path_history.end(), path);
    if (it != path_history.end()) {
        // 路径已存在，不重复记录
        LogManager::getInstance().debug("[FocusProcessFileInfo] 路径已存在，跳过重复记录: " + path);
        return;
    }

    // 路径不存在，添加到历史记录
    if (path_history.size() >= Constants::MAX_PATH_HISTORY) {
        // 移除最旧的记录
        path_history.erase(path_history.begin());
    }
    path_history.push_back(path);

    LogManager::getInstance().debug("[FocusProcessFileInfo] 记录新路径: " + path +
                                   " 当前历史记录数量: " + std::to_string(path_history.size()));
}

void FocusProcessFileInfo::addFilePathToHistory(const std::string& path) {
    std::lock_guard<std::mutex> lock(FocusProcessFileInfo_data_mutex_);
    recordPathOperation(path);
}

double FocusProcessFileInfo::getAverageReadEntropy() const {
    std::lock_guard<std::mutex> lock(FocusProcessFileInfo_data_mutex_);
    if (read_entropy_count == 0) {
        return 0.0;
    }
    return total_read_entropy / read_entropy_count;
}

double FocusProcessFileInfo::getAverageWriteEntropy() const {
    std::lock_guard<std::mutex> lock(FocusProcessFileInfo_data_mutex_);
    if (write_entropy_count == 0) {
        return 0.0;
    }
    return total_write_entropy / write_entropy_count;
}

void FocusProcessFileInfo::cleanup() {
    std::lock_guard<std::mutex> lock(FocusProcessFileInfo_data_mutex_);
    
    // 清理过长的路径历史记录
    if (path_history.size() > Constants::MAX_PATH_HISTORY) {
        size_t to_remove = path_history.size() - Constants::MAX_PATH_HISTORY;
        path_history.erase(path_history.begin(), path_history.begin() + to_remove);
    }
    
    // 清理过长的扩展名列表
    if (rename_extensions.size() > Constants::MAX_EXTENSION_LIST) {
        size_t to_remove = rename_extensions.size() - Constants::MAX_EXTENSION_LIST;
        rename_extensions.erase(rename_extensions.begin(), rename_extensions.begin() + to_remove);
    }
}

// 计算文件路径哈希值
uint64_t FocusProcessFileInfo::calculateFileHash(const std::string& file_path) {
    std::hash<std::string> hasher;
    return hasher(file_path);
}

// 文件熵值管理方法
void FocusProcessFileInfo::recordFileReadOnce(const std::string& file_path, double entropy, size_t file_size) {
    uint64_t file_hash = calculateFileHash(file_path);

    // 确保熵值计算使用的文件大小不超过100MB限制
    size_t entropy_calc_size = std::min(file_size, FocusProcessFileInfo::MAX_FILE_SIZE_FOR_ENTROPY);
    
    // 完全标准化：计算每MB的熵值
    constexpr size_t MB_SIZE = 1024 * 1024;
    double standardized_entropy = entropy * entropy_calc_size / MB_SIZE;

    // 【性能优化】：使用更细粒度的锁，减少锁持有时间
    std::lock_guard<std::mutex> lock(FocusProcessFileInfo_file_entropy_mutex_);

    try {
        auto it = file_entropy_map.find(file_hash);
        if (it == file_entropy_map.end()) {
            // 创建新的文件熵值信息 - 使用emplace避免不必要的拷贝
            FileEntropyInfo file_info(file_path, file_hash);
            file_info.original_entropy = standardized_entropy;  // 使用标准化熵值
            file_info.has_original_entropy = true;
            file_info.open_time = time(nullptr);
            file_info.file_readsize = file_size;  // 仍记录实际文件大小用于统计

            // 使用emplace提高性能
            auto result = file_entropy_map.emplace(file_hash, std::move(file_info));
            
            if (result.second) {
                LogManager::getInstance().debug("[FocusProcessFileInfo] 记录文件读取熵值: " + file_path +
                                               " 原始熵值=" + std::to_string(entropy) +
                                               " 实际文件大小=" + std::to_string(file_size) +
                                               " 熵值计算大小=" + std::to_string(entropy_calc_size) +
                                               " 标准化熵值(每MB)=" + std::to_string(standardized_entropy) +
                                               " 文件哈希=" + std::to_string(file_hash));
            } else {
                LogManager::getInstance().warning("[FocusProcessFileInfo] 文件哈希冲突，跳过记录: " + file_path);
            }
        } else {
            // 更新现有文件信息
            FileEntropyInfo& file_info = it->second;
            if (!file_info.has_original_entropy) {
                file_info.original_entropy = standardized_entropy;  // 使用标准化熵值
                file_info.has_original_entropy = true;
                file_info.open_time = time(nullptr);
                file_info.file_readsize = file_size;  // 仍记录实际文件大小用于统计

                LogManager::getInstance().debug("[FocusProcessFileInfo] 更新文件原始熵值: " + file_path +
                                               " 原始熵值=" + std::to_string(entropy) +
                                               " 熵值计算大小=" + std::to_string(entropy_calc_size) +
                                               " 标准化熵值(每MB)=" + std::to_string(standardized_entropy) +
                                               " 文件哈希=" + std::to_string(file_hash));
            } else {
                LogManager::getInstance().debug("[FocusProcessFileInfo] 文件原始熵值已存在，跳过更新: " + file_path);
            }
        }
    } catch (const std::exception& e) {
        LogManager::getInstance().error("[FocusProcessFileInfo] 记录文件读取熵值异常: " + std::string(e.what()) +
                                       " 文件=" + file_path + " 哈希=" + std::to_string(file_hash));
    }
}

void FocusProcessFileInfo::recordFileCloseWrite(const std::string& file_path, double entropy, size_t file_size) {
    uint64_t file_hash = calculateFileHash(file_path);

    // 确保熵值计算使用的文件大小不超过100MB限制
    size_t entropy_calc_size = std::min(file_size, FocusProcessFileInfo::MAX_FILE_SIZE_FOR_ENTROPY);
    
    // 完全标准化：计算每MB的熵值
    constexpr size_t MB_SIZE = 1024 * 1024;
    double standardized_entropy = entropy * entropy_calc_size / MB_SIZE;

    // 【性能优化】：使用更细粒度的锁，减少锁持有时间
    std::lock_guard<std::mutex> lock(FocusProcessFileInfo_file_entropy_mutex_);

    try {
        auto it = file_entropy_map.find(file_hash);
        if (it != file_entropy_map.end()) {
            // 更新现有文件信息
            FileEntropyInfo& file_info = it->second;
            file_info.final_entropy = standardized_entropy;  // 使用标准化熵值
            file_info.has_final_entropy = true;
            file_info.close_time = time(nullptr);
            file_info.file_writesize = file_size;  // 仍记录实际文件大小用于统计

            // 计算熵值变化量
            file_info.entropy_change = file_info.final_entropy - file_info.original_entropy;

            LogManager::getInstance().debug("[FocusProcessFileInfo] 记录文件关闭熵值: " + file_path +
                                           " 最终熵值=" + std::to_string(entropy) +
                                           " 实际文件大小=" + std::to_string(file_size) +
                                           " 熵值计算大小=" + std::to_string(entropy_calc_size) +
                                           " 标准化熵值(每MB)=" + std::to_string(standardized_entropy) +
                                           " 变化量=" + std::to_string(file_info.entropy_change) +
                                           " 文件哈希=" + std::to_string(file_hash));
        } else {
            // 如果没有找到对应的打开记录，创建一个新的记录
            FileEntropyInfo file_info(file_path, file_hash);
            file_info.final_entropy = standardized_entropy;  // 使用标准化熵值
            file_info.has_final_entropy = true;
            file_info.close_time = time(nullptr);
            file_info.file_writesize = file_size;  // 仍记录实际文件大小用于统计

            // 使用emplace提高性能
            auto result = file_entropy_map.emplace(file_hash, std::move(file_info));
            
            if (result.second) {
                LogManager::getInstance().debug("[FocusProcessFileInfo] 记录文件关闭熵值（无打开记录）: " + file_path +
                                               " 最终熵值=" + std::to_string(entropy) +
                                               " 实际文件大小=" + std::to_string(file_size) +
                                               " 熵值计算大小=" + std::to_string(entropy_calc_size) +
                                               " 标准化熵值(每MB)=" + std::to_string(standardized_entropy) +
                                               " 文件哈希=" + std::to_string(file_hash));
            } else {
                LogManager::getInstance().warning("[FocusProcessFileInfo] 文件哈希冲突，跳过记录: " + file_path);
            }
        }
    } catch (const std::exception& e) {
        LogManager::getInstance().error("[FocusProcessFileInfo] 记录文件关闭熵值异常: " + std::string(e.what()) +
                                       " 文件=" + file_path + " 哈希=" + std::to_string(file_hash));
    }
}

FileEntropyInfo* FocusProcessFileInfo::getFileEntropyInfo(const std::string& file_path) {
    uint64_t file_hash = calculateFileHash(file_path);
    
    std::lock_guard<std::mutex> lock(FocusProcessFileInfo_file_entropy_mutex_);
    
    auto it = file_entropy_map.find(file_hash);
    if (it != file_entropy_map.end()) {
        return &(it->second);
    }
    return nullptr;
}

std::vector<FileEntropyInfo> FocusProcessFileInfo::getAllFileEntropyInfo() const {
    // 使用单一锁避免死锁风险
    std::lock_guard<std::mutex> lock(FocusProcessFileInfo_file_entropy_mutex_);

    std::vector<FileEntropyInfo> result;
    result.reserve(file_entropy_map.size());

    for (const auto& pair : file_entropy_map) {
        result.push_back(pair.second);
    }

    return result;
}

// 【性能优化】：检查是否需要计算原始熵值（避免重复计算）
bool FocusProcessFileInfo::needsOriginalEntropyCalculation(const std::string& file_path) const {
    uint64_t file_hash = calculateFileHash(file_path);

    // 使用RAII确保异常安全
    std::lock_guard<std::mutex> lock(FocusProcessFileInfo_file_entropy_mutex_);

    auto it = file_entropy_map.find(file_hash);
    if (it == file_entropy_map.end()) {
        // 文件不存在于映射表中，需要计算
        return true;
    }

    // 文件存在，检查是否已经有原始熵值
    return !it->second.has_original_entropy;
}

// 获取总体熵值统计
double FocusProcessFileInfo::getTotalOriginalEntropy() const {
    std::lock_guard<std::mutex> lock(FocusProcessFileInfo_file_entropy_mutex_);
    
    double total = 0.0;
    for (const auto& pair : file_entropy_map) {
        if (pair.second.has_original_entropy) {
            total += pair.second.original_entropy;
        }
    }
    return total;
}

double FocusProcessFileInfo::getTotalFinalEntropy() const {
    std::lock_guard<std::mutex> lock(FocusProcessFileInfo_file_entropy_mutex_);
    
    double total = 0.0;
    for (const auto& pair : file_entropy_map) {
        if (pair.second.has_final_entropy) {
            total += pair.second.final_entropy;
        }
    }
    return total;
}

double FocusProcessFileInfo::getTotalEntropyChange() const {
    std::lock_guard<std::mutex> lock(FocusProcessFileInfo_file_entropy_mutex_);

    double total = 0.0;
    for (const auto& pair : file_entropy_map) {
        //if (pair.second.has_original_entropy && pair.second.has_final_entropy) {
        total += pair.second.entropy_change;
        //}
    }
    return total;
}

// 新增：获取熵值统计摘要
EntropyStatsSummary FocusProcessFileInfo::getEntropyStatsSummary() const {
    std::lock_guard<std::mutex> lock(FocusProcessFileInfo_file_entropy_mutex_);

    EntropyStatsSummary summary;
    summary.total_files = file_entropy_map.size();
    summary.files_with_original_entropy = 0;
    summary.files_with_final_entropy = 0;
    summary.files_with_both_entropy = 0;
    summary.total_original_entropy = 0.0;
    summary.total_final_entropy = 0.0;
    summary.total_entropy_change = 0.0;
    summary.max_entropy_change = 0.0;
    summary.min_entropy_change = 0.0;
    summary.entropy_change_percentage = 0.0;

    bool first_change = true;
    for (const auto& pair : file_entropy_map) {
        const auto& info = pair.second;

        if (info.has_original_entropy) {
            summary.files_with_original_entropy++;
            summary.total_original_entropy += info.original_entropy;
        }

        if (info.has_final_entropy) {
            summary.files_with_final_entropy++;
            summary.total_final_entropy += info.final_entropy;
        }

        //if (info.has_original_entropy && info.has_final_entropy) 
		{
            if (info.has_original_entropy && info.has_final_entropy) 
				summary.files_with_both_entropy++;
			
            summary.total_entropy_change += info.entropy_change;

            if (first_change) {
                summary.max_entropy_change = info.entropy_change;
                summary.min_entropy_change = info.entropy_change;
                first_change = false;
            } else {
                summary.max_entropy_change = std::max(summary.max_entropy_change, info.entropy_change);
                summary.min_entropy_change = std::min(summary.min_entropy_change, info.entropy_change);
            }
        }
    }

    // 计算熵值变化百分比
    if (summary.total_original_entropy > 0.0) {
        summary.entropy_change_percentage = (summary.total_entropy_change / summary.total_original_entropy) * 100.0;
    } else {
        summary.entropy_change_percentage = 0.0;
    }

    return summary;
}

// ProcessInfo 方法实现
void ProcessInfo::updateNetworkConnections(const std::vector<NetworkConnection>& connections) {
    std::lock_guard<std::mutex> lock(ProcessInfo_network_connections_mutex_);
    network_connections = connections;
}

void ProcessInfo::setFocusProcess(bool focus) {
    is_focus_process = focus;
    
    if (focus && !focus_file_info) {
        // 创建重点进程文件信息对象 (C++11兼容方式)
        focus_file_info.reset(new FocusProcessFileInfo());
    } else if (!focus && focus_file_info) {
        // 清理重点进程文件信息对象
        focus_file_info.reset();
    }
}

FocusProcessFileInfo* ProcessInfo::getFocusFileInfo() const {
    if (is_focus_process && focus_file_info) {
        return focus_file_info.get();
    }
    return nullptr;
}

std::map<std::string, std::string> ProcessInfo::getBasicInfoSummary() const {
    std::map<std::string, std::string> summary;
    
    summary["pid"] = std::to_string(pid);
    summary["ppid"] = std::to_string(ppid);
    summary["process_name"] = process_name;
    summary["executable_path"] = executable_path;
    summary["command_line"] = command_line;
    summary["cwd"] = cwd;
    summary["uid"] = std::to_string(uid);
    summary["gid"] = std::to_string(gid);
    summary["cpu_usage"] = std::to_string(cpu_usage);
    summary["memory_usage"] = std::to_string(memory_usage);
    summary["virtual_memory"] = std::to_string(virtual_memory);
    summary["state"] = state;
    summary["start_time"] = std::to_string(start_time);
    summary["children_count"] = std::to_string(children.size());
    summary["is_focus_process"] = is_focus_process ? "true" : "false";
    
    // 网络连接统计
    {
        std::lock_guard<std::mutex> lock(ProcessInfo_network_connections_mutex_);
        summary["network_connections_count"] = std::to_string(network_connections.size());
    }
    
    // 重点进程文件信息摘要
    if (is_focus_process && focus_file_info) {
        std::lock_guard<std::mutex> lock(focus_file_info->FocusProcessFileInfo_data_mutex_);
        summary["focus_read_entropy_count"] = std::to_string(focus_file_info->read_entropy_count);
        summary["focus_write_entropy_count"] = std::to_string(focus_file_info->write_entropy_count);
        summary["focus_delete_count"] = std::to_string(focus_file_info->delete_count);
        summary["focus_rename_count"] = std::to_string(focus_file_info->rename_count);
        summary["focus_path_history_count"] = std::to_string(focus_file_info->path_history.size());
        summary["focus_average_read_entropy"] = std::to_string(focus_file_info->getAverageReadEntropy());
        summary["focus_average_write_entropy"] = std::to_string(focus_file_info->getAverageWriteEntropy());
        
        // 规则匹配统计信息
        if (focus_file_info->rule_match_stats) {
            const auto& stats = *focus_file_info->rule_match_stats;
            summary["rule_base_delete_count"] = std::to_string(stats.base_delete_rule_counts);
            summary["rule_base_rename_count"] = std::to_string(stats.base_rename_rule_counts);
            summary["rule_change_delete_count"] = std::to_string(stats.change_delete_rule_counts);
            summary["rule_change_rename_count"] = std::to_string(stats.change_rename_rule_counts);
            summary["rule_patch_delete_count"] = std::to_string(stats.patch_delete_rule_counts);
            summary["rule_patch_rename_count"] = std::to_string(stats.patch_rename_rule_counts);
            summary["rule_total_matches"] = std::to_string(stats.all_catch_count);
            summary["rule_last_match_time"] = std::to_string(stats.last_rule_catch_ticket);
            summary["file_delete_operations"] = std::to_string(stats.all_delete_times);
            summary["file_rename_operations"] = std::to_string(stats.all_rename_times);
        }
    }
    
    return summary;
}
