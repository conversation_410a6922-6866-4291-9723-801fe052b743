/*
 * PSFSMON-L 文件监控器实现 - 双Fanotify Group架构
 * 
 * 【需求13核心实现】
 * 本模块严格按照需求13实现双Fanotify Group + 双线程监控策略：
 * 
 * 1. 受保护路径监控Group：FAN_CLASS_NOTIF + FAN_MODIFY | FAN_CLOSE_WRITE | FAN_EVENT_ON_CHILD
 *    - 使用fanotify_init建立独立的fanotify group
 *    - 对具体受保护路径进行精确标记，不使用FAN_MARK_MOUNT
 *    - 实现权限检查和路径保护，支持KILL/LOG模式
 * 
 * 2. 重点进程监控Group：FAN_CLASS_NOTIF + 全局监控  
 *    - 使用fanotify_init建立独立的fanotify group
 *    - 使用FAN_MARK_MOUNT或FAN_MARK_FILESYSTEM进行全局监控
 *    - 过滤重点进程事件，实现文件操作信息收集和熵值计算
 * 
 * 【基础特性原则】
 * - 仅使用4.19+内核支持的基础Fanotify特性
 * - 唯一例外：4.20+可使用FAN_MARK_FILESYSTEM优化
 * - 不使用FAN_REPORT_TID、FAN_REPORT_PIDFD、FAN_REPORT_FID等复杂特性
 * - 完全移除Inotify机制依赖
 * 
 * 【双线程架构】
 * - protected_monitor_thread_：专门处理受保护路径权限检查
 * - focus_monitor_thread_：专门处理重点进程文件操作收集
 * - 独立事件循环，避免事件冲突和阻塞
 */

#include "file_monitor.h"
#include "main_monitor.h"
#include "process_exclusion.h"
#include "psfsmon.h"  // 包含100MB常量定义
#include <sys/stat.h>
#include <sys/mount.h>
#include <sys/utsname.h>
#include <mntent.h>
#include <fcntl.h>
#include <unistd.h>
#include <signal.h>
#include <chrono>
#include <thread>
#include <cstring>  // for strerror
#include <sstream>
#include <algorithm>
#include <fstream>
#include <cmath>
#include <vector>

// FileEntropyCalculator 实现 - 需求12核心算法
double FileEntropyCalculator::calculateFileEntropy(const std::string& file_path) {
    std::ifstream file(file_path, std::ios::binary);
    if (!file.is_open()) {
        return 0.0;
    }
    
    // 获取文件大小
    file.seekg(0, std::ios::end);
    std::streamsize file_size = file.tellg();
    file.seekg(0, std::ios::beg);
    
    if (file_size <= 0) {
        return 0.0;
    }
    
    // 限制计算的文件大小（使用统一的100MB常量）
    size_t calc_size = static_cast<size_t>(file_size);
    if (calc_size > FocusProcessFileInfo::MAX_FILE_SIZE_FOR_ENTROPY) {
        calc_size = FocusProcessFileInfo::MAX_FILE_SIZE_FOR_ENTROPY;
        LogManager::getInstance().debug("[FileEntropyCalculator] 文件过大，只计算前100MB: " + file_path + 
                                       " 总大小=" + std::to_string(file_size) + 
                                       " 计算大小=" + std::to_string(calc_size));
    }
    
    // 分块读取文件内容计算熵值
    return calculateFileEntropyInChunks(file, calc_size);
}

double FileEntropyCalculator::calculateFileEntropyInChunks(std::ifstream& file, size_t total_size) {
    if (total_size == 0) {
        return 0.0;
    }
    
    // 分块大小：1MB
    const size_t CHUNK_SIZE = 1024 * 1024; // 1MB
    std::vector<char> buffer(CHUNK_SIZE);
    
    // 计算每个字节的频率
    std::vector<size_t> frequencies(256, 0);
    size_t total_bytes_read = 0;
    
    while (total_bytes_read < total_size && !file.eof()) {
        // 计算当前块的大小
        size_t remaining = total_size - total_bytes_read;
        size_t current_chunk_size = std::min(CHUNK_SIZE, remaining);
        
        // 读取当前块
        file.read(buffer.data(), current_chunk_size);
        size_t bytes_read = static_cast<size_t>(file.gcount());
        
        if (bytes_read == 0) {
            break;
        }
        
        // 计算当前块的字节频率
        for (size_t i = 0; i < bytes_read; ++i) {
            frequencies[static_cast<unsigned char>(buffer[i])]++;
        }
        
        total_bytes_read += bytes_read;
        
        LogManager::getInstance().debug("[FileEntropyCalculator] 已读取: " + std::to_string(total_bytes_read) + 
                                       "/" + std::to_string(total_size) + " 字节");
    }
    
    file.close();
    
    if (total_bytes_read == 0) {
        return 0.0;
    }
    
    return computeEntropy(frequencies, total_bytes_read);
}

double FileEntropyCalculator::calculateBufferEntropy(const char* buffer, size_t size) {
    if (size == 0) {
        return 0.0;
    }
    
    // 计算每个字节的频率
    std::vector<size_t> frequencies(256, 0);
    for (size_t i = 0; i < size; ++i) {
        frequencies[static_cast<unsigned char>(buffer[i])]++;
    }
    
    return computeEntropy(frequencies, size);
}

double FileEntropyCalculator::computeEntropy(const std::vector<size_t>& frequencies, size_t total_bytes) {
    double entropy = 0.0;
    
    for (size_t freq : frequencies) {
        if (freq > 0) {
            double probability = static_cast<double>(freq) / total_bytes;
            entropy -= probability * log2(probability);
        }
    }
    
    return entropy;
}

// FileMonitor 实现
FileMonitor::FileMonitor() : 
    config_(nullptr),
    running_(false),
    kernel_supports_filesystem_mark_(false),
    protected_fanotify_fd_(-1),
    focus_fanotify_fd_(-1) {
}

FileMonitor::~FileMonitor() {
    stop();
    cleanup();
}

bool FileMonitor::initialize(const MonitorConfig* config) {
    config_ = config;
    
    LogManager::getInstance().info("[FileMonitor] 初始化文件监控器 - 双Fanotify Group架构");
    
    // 检测内核能力
    detectKernelCapabilities();
    
    // 初始化双Fanotify Group - 需求13核心实现
    if (!initializeProtectedPathGroup()) {
        LogManager::getInstance().error("[FileMonitor] 受保护路径监控Group初始化失败");
        return false;
    }
    
    if (!initializeFocusProcessGroup()) {
        LogManager::getInstance().error("[FileMonitor] 重点进程监控Group初始化失败");
        cleanup();
        return false;
    }
    
    // 设置监控
    if (!setupProtectedPathMonitoring()) {
        LogManager::getInstance().error("[FileMonitor] 受保护路径监控设置失败");
        cleanup();
        return false;
    }
    
    if (!setupFocusProcessMonitoring()) {
        LogManager::getInstance().error("[FileMonitor] 重点进程监控设置失败");
        cleanup();
        return false;
    }
    
    // 加载重点进程配置
    if (config_ && config_->enable_focus_process_monitoring) {
        auto& focus_manager = FocusProcessManager::getInstance();
        focus_manager.loadFromConfig(config_->focus_process_list);
    }
    
    LogManager::getInstance().info("[FileMonitor] 文件监控器初始化成功 (双Fanotify Group模式)");
    return true;
}

bool FileMonitor::start() {
    if (running_) {
        return true;
    }
    
    running_ = true;
    
    // 启动双线程监控 - 需求13关键架构
    protected_monitor_thread_ = std::thread(&FileMonitor::processProtectedPathEvents, this);
    focus_monitor_thread_ = std::thread(&FileMonitor::processFocusProcessEvents, this);
    
    LogManager::getInstance().info("[FileMonitor] 双线程文件监控已启动");
    return true;
}

bool FileMonitor::reloadConfig(const MonitorConfig* config) {
    LogManager::getInstance().info("[FileMonitor] 开始重新加载配置...");
    
    if (!config) {
        LogManager::getInstance().error("[FileMonitor] 配置指针为空，无法重新加载");
        return false;
    }
    
    try {
        // 更新配置指针
        config_ = config;
        
        // 重新加载受保护路径配置
        if (config_->enable_protected_path_monitoring) {
            auto& protected_manager = ProtectedPathManager::getInstance();
            if (!protected_manager.reloadConfig(config_->protected_paths, config_->terminate_violating_processes)) {
                LogManager::getInstance().error("[FileMonitor] 重新加载受保护路径配置失败");
                return false;
            }
            
            LogManager::getInstance().info("[FileMonitor] 受保护路径配置已重新加载 - 终止违规进程: " + 
                                          std::string(config_->terminate_violating_processes ? "是" : "否"));
            
            // 重新设置受保护路径监控 - 这是关键修复！
            if (!setupProtectedPathMonitoring()) {
                LogManager::getInstance().error("[FileMonitor] 重新设置受保护路径监控失败");
                return false;
            }
        }
        
        // 重新加载重点进程配置
        if (config_->enable_focus_process_monitoring) {
            auto& focus_manager = FocusProcessManager::getInstance();
            if (!focus_manager.reloadConfig(config_->focus_process_list)) {
                LogManager::getInstance().error("[FileMonitor] 重新加载重点进程配置失败");
                return false;
            }
            
            LogManager::getInstance().info("[FileMonitor] 重点进程配置已重新加载");
        }
        
        LogManager::getInstance().info("[FileMonitor] 配置重新加载成功");
        return true;
        
    } catch (const std::exception& e) {
        LogManager::getInstance().error("[FileMonitor] 配置重新加载时发生异常: " + std::string(e.what()));
        return false;
    }
}

void FileMonitor::stop() {
    if (!running_) {
        return;
    }
    
    running_ = false;
    
    // 等待线程结束
    if (protected_monitor_thread_.joinable()) {
        protected_monitor_thread_.join();
    }
    
    if (focus_monitor_thread_.joinable()) {
        focus_monitor_thread_.join();
    }
    
    LogManager::getInstance().info("[FileMonitor] 文件监控已停止");
}

void FileMonitor::setEventCallback(std::function<void(const FileEvent&)> callback) {
    event_callback_ = callback;
}

uint64_t FileMonitor::getTotalEvents() const {
    std::lock_guard<std::mutex> lock(FileMonitor_stats_mutex_);
    return event_stats_.total_events.load();
}

FileEventStats FileMonitor::getEventStats() const {
    std::lock_guard<std::mutex> lock(FileMonitor_stats_mutex_);
    return event_stats_;
}

std::vector<FileEvent> FileMonitor::getRecentEvents(size_t limit) const {
    std::lock_guard<std::mutex> lock(FileMonitor_events_mutex_);
    
    if (recent_events_.size() <= limit) {
        return recent_events_;
    }
    
    return std::vector<FileEvent>(
        recent_events_.end() - limit,
        recent_events_.end()
    );
}

// 双Fanotify Group初始化 - 需求13核心实现
bool FileMonitor::initializeProtectedPathGroup() {
    // 受保护路径监控：使用FAN_CLASS_NOTIF进行通知收集
    // 添加FAN_NONBLOCK标志确保非阻塞操作，便于程序中断
    protected_fanotify_fd_ = fanotify_init(
        FAN_CLASS_NOTIF | FAN_CLOEXEC | FAN_NONBLOCK,
        O_RDONLY | O_LARGEFILE | O_NONBLOCK | O_CLOEXEC
    );
    
    if (protected_fanotify_fd_ == -1) {
        LogManager::getInstance().error("[FileMonitor] 受保护路径fanotify_init失败: " + std::string(strerror(errno)));
        
        // 检查是否是权限问题
        if (errno == EPERM) {
            LogManager::getInstance().error("[FileMonitor] fanotify需要CAP_SYS_ADMIN权限，请以root用户运行或设置capabilities");
        } else if (errno == EINVAL) {
            LogManager::getInstance().error("[FileMonitor] fanotify参数无效，可能是内核版本不支持");
        }
        
        return false;
    }
    
    LogManager::getInstance().info("[FileMonitor] 受保护路径监控Group初始化成功 (FAN_CLASS_NOTIF | FAN_NONBLOCK)");
    LogManager::getInstance().info("[FileMonitor] 受保护路径fanotify文件描述符: " + std::to_string(protected_fanotify_fd_));
    return true;
}

bool FileMonitor::initializeFocusProcessGroup() {
    // 重点进程监控：使用FAN_CLASS_NOTIF进行通知收集
    // 添加FAN_NONBLOCK标志确保非阻塞操作，提高响应性
    focus_fanotify_fd_ = fanotify_init(
        FAN_CLASS_NOTIF | FAN_CLOEXEC | FAN_NONBLOCK,
        O_RDONLY | O_LARGEFILE | O_NONBLOCK
    );
    
    if (focus_fanotify_fd_ == -1) {
        LogManager::getInstance().error("[FileMonitor] 重点进程fanotify_init失败: " + std::string(strerror(errno)));
        return false;
    }
    
    LogManager::getInstance().info("[FileMonitor] 重点进程监控Group初始化成功 (FAN_CLASS_NOTIF | FAN_NONBLOCK)");
    return true;
}

bool FileMonitor::setupProtectedPathMonitoring() {
    // 受保护路径监控设置：加载配置并初始化受保护路径管理器
    if (config_ && config_->enable_protected_path_monitoring) {
        auto& protected_manager = ProtectedPathManager::getInstance();
        protected_manager.loadFromConfig(config_->protected_paths);
        protected_manager.setTerminateViolatingProcesses(config_->terminate_violating_processes);
        
        // 清除现有的fanotify标记（如果存在）
        if (protected_fanotify_fd_ != -1) {
            // 清除所有现有的标记
            fanotify_mark(protected_fanotify_fd_, FAN_MARK_FLUSH, 0, AT_FDCWD, "/");
            LogManager::getInstance().info("[FileMonitor] 已清除现有受保护路径fanotify标记");
        }
        
        // 对配置中的受保护路径进行fanotify标记
        auto protected_paths = protected_manager.getProtectedPaths();
        int success_count = 0;
        
        for (const auto& path : protected_paths) {
            if (!addProtectedPathMark(path)) {
                LogManager::getInstance().warning("[FileMonitor] 添加受保护路径标记失败: " + path);
            } else {
                success_count++;
            }
        }
        
        LogManager::getInstance().info("[FileMonitor] 受保护路径监控设置完成 - 已加载 " + 
                                      std::to_string(protected_paths.size()) + " 个受保护路径，成功标记 " + 
                                      std::to_string(success_count) + " 个，终止违规进程: " + 
                                      (config_->terminate_violating_processes ? "是" : "否"));
        LogManager::getInstance().debug("[FileMonitor] 配置详情 - enable_protected_path_monitoring: " + 
                                       std::string(config_->enable_protected_path_monitoring ? "true" : "false") + 
                                       ", terminate_violating_processes: " + 
                                       std::string(config_->terminate_violating_processes ? "true" : "false"));
    } else {
        LogManager::getInstance().info("[FileMonitor] 受保护路径监控已禁用");
    }
    
    return true;
}

bool FileMonitor::setupFocusProcessMonitoring() {
    // 重点进程监控：全局文件系统监控
    uint64_t mask = FAN_OPEN | FAN_CLOSE_WRITE | FAN_MODIFY | FAN_ACCESS;
    
    if (kernel_supports_filesystem_mark_) {
        // 4.20+内核：使用FAN_MARK_FILESYSTEM优化
        LogManager::getInstance().info("[FileMonitor] 使用FAN_MARK_FILESYSTEM进行全局监控");
        
        if (fanotify_mark(focus_fanotify_fd_, FAN_MARK_ADD | FAN_MARK_FILESYSTEM,
                         mask, AT_FDCWD, "/") == -1) {
            LogManager::getInstance().warning("[FileMonitor] FAN_MARK_FILESYSTEM失败，回退到挂载点监控: " +
                std::string(strerror(errno)) + ", errno=" + std::to_string(errno) +
                ", mask=0x" + std::to_string(mask) + ", fd=" + std::to_string(focus_fanotify_fd_) +
                ", AT_FDCWD=" + std::to_string(AT_FDCWD));
            return enumerateMountPoints();
        }
    } else {
        LogManager::getInstance().info("[FileMonitor] 使用基础挂载点监控策略");
        return enumerateMountPoints();
    }
    
    LogManager::getInstance().info("[FileMonitor] 重点进程监控设置完成");
    return true;
}

bool FileMonitor::enumerateMountPoints() {
    // 首先枚举挂载点
    std::lock_guard<std::mutex> lock(FileMonitor_mount_points_mutex_);
    mount_points_.clear();
    
    std::ifstream mounts("/proc/mounts");
    if (!mounts.is_open()) {
        LogManager::getInstance().error("[FileMonitor] 无法读取 /proc/mounts");
        return false;
    }
    
    std::string line;
    while (std::getline(mounts, line)) {
        std::istringstream iss(line);
        std::string device, mount_point, fs_type;
        if (iss >> device >> mount_point >> fs_type) {
            if (shouldMonitorMountPoint(mount_point)) {
                mount_points_.push_back(mount_point);
            }
        }
    }
    
    LogManager::getInstance().info("[FileMonitor] 发现 " + std::to_string(mount_points_.size()) + " 个挂载点");
    
    // 然后标记这些挂载点进行监控
    uint64_t mask = FAN_OPEN | FAN_CLOSE_WRITE | FAN_MODIFY | FAN_ACCESS;
    int success_count = 0;
    
    for (const auto& mount_point : mount_points_) {
        if (fanotify_mark(focus_fanotify_fd_, FAN_MARK_ADD | FAN_MARK_MOUNT,
                         mask, AT_FDCWD, mount_point.c_str()) == 0) {
            success_count++;
            LogManager::getInstance().debug("[FileMonitor] 成功标记挂载点: " + mount_point);
        } else {
            LogManager::getInstance().warning("[FileMonitor] 标记挂载点失败: " + mount_point + 
                                            " - " + std::string(strerror(errno)) +
                                            ", errno=" + std::to_string(errno) +
                                            ", mask=0x" + std::to_string(mask) +
                                            ", fd=" + std::to_string(focus_fanotify_fd_) +
                                            ", AT_FDCWD=" + std::to_string(AT_FDCWD));
        }
    }
    
    if (success_count == 0) {
        LogManager::getInstance().error("[FileMonitor] 没有成功标记任何挂载点");
        return false;
    }
    
    LogManager::getInstance().info("[FileMonitor] 成功标记 " + std::to_string(success_count) + " 个挂载点");
    return true;
}

// 双线程事件处理 - 需求13关键实现 (优化非阻塞版本)
void FileMonitor::processProtectedPathEvents() {
    LogManager::getInstance().info("[FileMonitor] 受保护路径监控线程已启动 (非阻塞模式)");
    
    const size_t buffer_size = 16384; // 增大缓冲区以处理更多事件
    char buffer[buffer_size];
    
    while (running_) {
        fd_set read_fds;
        FD_ZERO(&read_fds);
        FD_SET(protected_fanotify_fd_, &read_fds);
        
        // 缩短超时时间，提高响应性
        struct timeval timeout = {0, 100000}; // 100ms超时
        int ready = select(protected_fanotify_fd_ + 1, &read_fds, nullptr, nullptr, &timeout);
        
        if (!running_) break;
        
        if (ready > 0 && FD_ISSET(protected_fanotify_fd_, &read_fds)) {
            // 连续读取多个事件批次，直到没有更多事件
            while (running_) {
                ssize_t len = read(protected_fanotify_fd_, buffer, buffer_size);
                
                if (len > 0) {
                    processEventBuffer(buffer, len, true); // true表示受保护路径事件
                } else if (len == -1) {
                    if (errno == EAGAIN || errno == EWOULDBLOCK) {
                        // 非阻塞模式下没有更多事件，正常情况
                        break;
                    } else if (errno == EINTR) {
                        // 被信号中断，继续处理
                        if (!running_) break;
                        continue;
                    } else {
                        // 其他错误
                        LogManager::getInstance().error("[FileMonitor] 受保护路径事件读取错误: " + 
                                                       std::string(strerror(errno)));
                        break;
                    }
                } else {
                    // len == 0, 通常不会发生，但防御性编程
                    break;
                }
            }
        } else if (ready == -1) {
            if (errno == EINTR) {
                // select被信号中断，检查running状态
                continue;
            } else {
                LogManager::getInstance().error("[FileMonitor] 受保护路径select错误: " + 
                                               std::string(strerror(errno)));
                // 短暂休眠后重试，避免CPU占用过高
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
        }
        // ready == 0 表示超时，正常情况，继续循环
    }
    
    LogManager::getInstance().info("[FileMonitor] 受保护路径监控线程已停止");
}

void FileMonitor::processFocusProcessEvents() {
    LogManager::getInstance().info("[FileMonitor] 重点进程监控线程已启动 (非阻塞模式)");
    
    const size_t buffer_size = 16384; // 增大缓冲区以处理更多事件
    char buffer[buffer_size];
    
    while (running_) {
        fd_set read_fds;
        FD_ZERO(&read_fds);
        FD_SET(focus_fanotify_fd_, &read_fds);
        
        // 缩短超时时间，提高响应性
        struct timeval timeout = {0, 100000}; // 100ms超时
        int ready = select(focus_fanotify_fd_ + 1, &read_fds, nullptr, nullptr, &timeout);
        
        if (!running_) break;
        
        if (ready > 0 && FD_ISSET(focus_fanotify_fd_, &read_fds)) {
            // 连续读取多个事件批次，直到没有更多事件
            while (running_) {
                ssize_t len = read(focus_fanotify_fd_, buffer, buffer_size);
                
                if (len > 0) {
                    processEventBuffer(buffer, len, false); // false表示重点进程事件
                } else if (len == -1) {
                    if (errno == EAGAIN || errno == EWOULDBLOCK) {
                        // 非阻塞模式下没有更多事件，正常情况
                        break;
                    } else if (errno == EINTR) {
                        // 被信号中断，继续处理
                        if (!running_) break;
                        continue;
                    } else {
                        LogManager::getInstance().error("[FileMonitor] 重点进程事件读取错误: " + 
                                                       std::string(strerror(errno)));
                        break;
                    }
                } else {
                    // len == 0, 通常不会发生，但防御性编程
                    break;
                }
            }
        } else if (ready == -1) {
            if (errno == EINTR) {
                // select被信号中断，检查running状态
                continue;
            } else {
                LogManager::getInstance().error("[FileMonitor] 重点进程select错误: " + 
                                               std::string(strerror(errno)));
                // 短暂休眠后重试，避免CPU占用过高
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
        }
        // ready == 0 表示超时，正常情况，继续循环
    }
    
    LogManager::getInstance().info("[FileMonitor] 重点进程监控线程已停止");
}

// 统一的事件缓冲区处理方法 - 优化版本
void FileMonitor::processEventBuffer(const char* buffer, size_t buffer_len, bool is_protected_path) {
    if (buffer_len <= 0) {
        return;
    }
    
    const struct fanotify_event_metadata* metadata = 
        reinterpret_cast<const struct fanotify_event_metadata*>(buffer);
    
    size_t processed_events = 0;
    
    while (FAN_EVENT_OK(metadata, buffer_len) && running_) {
        // 验证元数据版本
        if (metadata->vers != FANOTIFY_METADATA_VERSION) {
            LogManager::getInstance().error("[FileMonitor] 不支持的fanotify元数据版本: " + 
                                           std::to_string(metadata->vers));
            break;
        }
        
        // 验证事件长度
        if (metadata->event_len < FAN_EVENT_METADATA_LEN || 
            metadata->event_len > buffer_len) {
            LogManager::getInstance().error("[FileMonitor] 无效的事件长度: " + 
                                           std::to_string(metadata->event_len));
            break;
        }
        
        try {
            // 根据事件类型选择处理函数
            if (is_protected_path) {
                handleProtectedPathEvent(const_cast<struct fanotify_event_metadata*>(metadata));
            } else {
                handleFocusProcessEvent(const_cast<struct fanotify_event_metadata*>(metadata));
            }
            processed_events++;
        } catch (const std::exception& e) {
            LogManager::getInstance().error("[FileMonitor] 事件处理异常: " + std::string(e.what()));
            // 关闭文件描述符防止泄漏
            if (metadata->fd >= 0) {
                close(metadata->fd);
            }
        } catch (...) {
            LogManager::getInstance().error("[FileMonitor] 未知事件处理异常");
            // 关闭文件描述符防止泄漏
            if (metadata->fd >= 0) {
                close(metadata->fd);
            }
        }
        
        // 移动到下一个事件
        metadata = FAN_EVENT_NEXT(metadata, buffer_len);
        
        // 防止处理过多事件造成线程长时间占用，定期检查running状态
        if (processed_events > 0 && processed_events % 100 == 0) {
            if (!running_) {
                break;
            }
            // 短暂让出CPU，避免高负载情况下其他线程饿死
            std::this_thread::yield();
        }
    }
    
    if (processed_events > 0) {
        //LogManager::getInstance().debug("[FileMonitor] 批量处理了 " + std::to_string(processed_events) + 
        //                               " 个" + (is_protected_path ? "受保护路径" : "重点进程") + "事件");
    }
}

bool FileMonitor::handleProtectedPathEvent(struct fanotify_event_metadata* metadata) {
    if (metadata->mask & FAN_Q_OVERFLOW) {
        LogManager::getInstance().warning("[FileMonitor] 受保护路径事件队列溢出");
        return true;
    }
    
    // 记录所有受保护路径事件用于调试
    std::string file_path = getPathFromFd(metadata->fd);
    std::string process_name = getProcessName(metadata->pid);
    LogManager::getInstance().debug("[FileMonitor] 受保护路径事件: PID=" + std::to_string(metadata->pid) + 
                                   " 进程=" + process_name + " 路径=" + file_path + 
                                   " 掩码=0x" + std::to_string(metadata->mask));
    
    // 检查是否为受保护路径
    auto& protected_manager = ProtectedPathManager::getInstance();
    bool is_protected = protected_manager.isProtectedPath(file_path);
    
    if (is_protected) {
        // 检查是否为排除进程
        auto& exclusion_manager = ProcessExclusionManagerSingleton::getInstance();
        if (!exclusion_manager.isExcluded(metadata->pid)) {
            // 检测到受保护路径的操作
            LogManager::getInstance().info("[FileMonitor] 检测到受保护路径操作: " + 
                                          file_path + " (进程: " + process_name + ", PID: " + 
                                          std::to_string(metadata->pid) + ")");
            
            // 处理违规
            protected_manager.handleViolation(file_path, metadata->pid, process_name);
            
            // 记录事件
            FileEvent event;
            event.pid = metadata->pid;
            event.type = fanotifyMaskToEventType(metadata->mask);
            event.file_path = file_path;
            event.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::system_clock::now().time_since_epoch()).count();
            event.is_protected_path = true;
            
            addRecentEvent(event);
            
            // 统计更新
            event_stats_.protected_path_events++;
            event_stats_.total_events++;
            
            if (event_callback_) {
                event_callback_(event);
            }
        } else {
            LogManager::getInstance().debug("[FileMonitor] 排除进程操作 (记录): " + 
                                           file_path + " (进程: " + process_name + ")");
        }
    } else {
        LogManager::getInstance().debug("[FileMonitor] 非受保护路径操作 (记录): " + 
                                       file_path + " (进程: " + process_name + ")");
    }
    
    // 关闭文件描述符
    close(metadata->fd);
    return true;
}

void FileMonitor::handleFocusProcessEvent(struct fanotify_event_metadata* metadata) {
    if (metadata->mask & FAN_Q_OVERFLOW) {
        LogManager::getInstance().warning("[FileMonitor] 重点进程事件队列溢出");
        return;
    }
    
    // 快速检查是否为重点进程 - 需求13优化策略
    auto& focus_manager = FocusProcessManager::getInstance();
    bool is_focus_by_pid = focus_manager.isFocusProcess(metadata->pid);

    // 如果按PID检查失败，再按进程名检查
    bool is_focus_process = is_focus_by_pid;
    if (!is_focus_by_pid) {
        std::string process_name = getProcessName(metadata->pid);
        is_focus_process = focus_manager.isFocusProcess(process_name);

        if (is_focus_process) {
            LogManager::getInstance().debug("[FileMonitor] 重点进程匹配(按名称): PID=" + std::to_string(metadata->pid) +
                                           " 进程名=" + process_name);
        }
    }

    if (!is_focus_process) {
        // 非重点进程，快速跳过
        //LogManager::getInstance().debug("[FileMonitor] 跳过非重点进程: PID=" + std::to_string(metadata->pid));
        close(metadata->fd);
        return;
    }
    
    LogManager::getInstance().debug("[FileMonitor] 处理重点进程事件: PID=" + std::to_string(metadata->pid));
    
    // 构建文件事件
    FileEvent event;
    event.pid = metadata->pid;
    event.type = fanotifyMaskToEventType(metadata->mask);
    event.mask = metadata->mask;  // 保存fanotify事件掩码
    event.file_path = getPathFromFd(metadata->fd);
    event.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    event.is_focus_process = true;
    
    // 处理重点进程文件操作 - 需求12核心功能
    processFocusProcessFileOperation(event);
    
    // 详细事件类型统计更新
    event_stats_.focus_process_events++;
    event_stats_.total_events++;
    event_stats_.fanotify_events++;
    
    // 根据事件类型更新详细统计
    switch (event.type) {
        case FileEvent::READ:
            event_stats_.read_events++;
            break;
        case FileEvent::WRITE:
            event_stats_.write_events++;
            break;
		/*
        case FileEvent::OPEN:
            event_stats_.open_events++;
            break;
        case FileEvent::CLOSE:
            event_stats_.close_events++;
            break;
        case FileEvent::ACCESS:
            event_stats_.access_events++;
            break;
        case FileEvent::MODIFY:
            event_stats_.modify_events++;
            break;
        */
        case FileEvent::DELETE:
            event_stats_.delete_events++;
            break;
        case FileEvent::RENAME:
            event_stats_.move_events++;
            break;
		/*
        case FileEvent::PERMISSION_DENIED:
            event_stats_.permission_events++;
            break;
        */
        default:
            break;
    }
    
    // 重点进程文件统计
    if (!event.file_path.empty()) {
        event_stats_.focus_process_files++;
    }
    
    // 临时调试日志
    LogManager::getInstance().debug("[FileMonitor] 统计更新: focus_process_events=" + 
                                   std::to_string(event_stats_.focus_process_events.load()) +
                                   " total_events=" + std::to_string(event_stats_.total_events.load()));
    
    addRecentEvent(event);
    
    if (event_callback_) {
        event_callback_(event);
    }
    
    // 关闭文件描述符
    close(metadata->fd);
}

// sendPermissionResponse函数已移除 - 在FAN_CLASS_NOTIF模式下不再需要权限响应

// 重点进程文件操作处理 - 需求12核心功能
void FileMonitor::processFocusProcessFileOperation(const FileEvent& event) {

    if (!config_ || !config_->calculate_file_entropy) {
        return;
    }
    
    // 计算熵值并记录到重点进程
    calculateAndRecordEntropy(const_cast<FileEvent&>(event));
    
    // 记录文件操作历史
    recordFileOperationHistory(event.pid, event);
}

void FileMonitor::calculateAndRecordEntropy(FileEvent& event) {
    if (event.file_path.empty()) {
        return;
    }
    
    // 检查文件是否存在且可读
    struct stat file_stat;
    if (stat(event.file_path.c_str(), &file_stat) != 0) {
        LogManager::getInstance().debug("[FileMonitor] 文件不存在，跳过熵值计算: " + event.file_path);
        return;
    }
    
    // 只处理普通文件
    if (!S_ISREG(file_stat.st_mode)) {
        return;
    }
    
    // 获取ProcessMonitor
    auto& process_manager = ProcessMonitorManager::getInstance();
    auto* process_monitor = process_manager.getProcessMonitor();
    
    if (!process_monitor) {
        LogManager::getInstance().warning("[FileMonitor] ProcessMonitor指针为空，无法记录熵值");
        return;
    }
    
    // 记录开始时间，用于性能监控
    auto start_time = std::chrono::steady_clock::now();
    
    // 根据事件类型决定是否计算熵值
    switch (event.type) {
        case FileEvent::READ:
            if (event.mask & FAN_ACCESS) {
                // FAN_ACCESS事件：文件被读取时计算原始熵值（如果文件有内容且未记录过）
                if (file_stat.st_size > 0) {
                    // 【性能优化】：先检查是否需要计算熵值，避免重复计算
                    if (process_monitor->needsEntropyCalculation(event.pid, event.file_path)) {
                        try {
                            double entropy = FileEntropyCalculator::calculateFileEntropy(event.file_path);
                            
                            // 计算耗时
                            auto end_time = std::chrono::steady_clock::now();
                            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
                            
                            // 修复：允许记录熵值为0的情况，因为这是有效的熵值
                            if (entropy >= 0.0) {
                                // 使用新的文件熵值管理方法
                                process_monitor->recordFileReadOnceEntropy(event.pid, event.file_path, entropy, file_stat.st_size);

                                // 【关键修复】：更新熵值计算统计计数器
                                event_stats_.entropy_calculations++;
                                event_stats_.read_entropy_calculations++;  // 新增：按事件类型统计

                                LogManager::getInstance().debug("[FileMonitor] 计算文件读取熵值: " + event.file_path +
                                                              " PID=" + std::to_string(event.pid) +
                                                              " 原始熵值=" + std::to_string(entropy) +
                                                              " 文件大小=" + std::to_string(file_stat.st_size) +
                                                              " 计算耗时=" + std::to_string(duration.count()) + "ms" +
                                                              " 总熵值计算次数=" + std::to_string(event_stats_.entropy_calculations.load()) +
                                                              " 读熵值计算次数=" + std::to_string(event_stats_.read_entropy_calculations.load()));
                            } else {
                                LogManager::getInstance().warning("[FileMonitor] 熵值计算结果无效: " + event.file_path +
                                                                 " 熵值=" + std::to_string(entropy));
                            }
                        } catch (const std::exception& e) {
                            LogManager::getInstance().error("[FileMonitor] 文件熵值计算异常: " + event.file_path +
                                                           " 错误=" + std::string(e.what()));
                        }
                    } else {
                        LogManager::getInstance().debug("[FileMonitor] 跳过重复熵值计算: " + event.file_path +
                                                      " PID=" + std::to_string(event.pid) + " (已记录过原始熵值)");
                    }
                }
            }
            break;
            
        case FileEvent::WRITE:
            // 需要区分FAN_CLOSE_WRITE和FAN_CLOSE_NOWRITE
            // 这里我们只处理FAN_CLOSE_WRITE（写操作关闭）
            // FAN_CLOSE_NOWRITE不会影响熵值，所以不计算
            if (event.mask & FAN_CLOSE_WRITE) {
                try {
                    double entropy = FileEntropyCalculator::calculateFileEntropy(event.file_path);
                    
                    // 计算耗时
                    auto end_time = std::chrono::steady_clock::now();
                    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
                    
                    // 修复：允许记录熵值为0的情况，因为这是有效的熵值
                    if (entropy >= 0.0) {
                        // 使用新的文件熵值管理方法，传递文件大小
                        process_monitor->recordFileCloseWriteEntropy(event.pid, event.file_path, entropy, file_stat.st_size);

                        // 【关键修复】：更新熵值计算统计计数器
                        event_stats_.entropy_calculations++;
                        event_stats_.write_entropy_calculations++;  // 新增：按写操作统计

                        LogManager::getInstance().debug("[FileMonitor] 计算文件关闭熵值: " + event.file_path +
                                                      " PID=" + std::to_string(event.pid) +
                                                      " 最终熵值=" + std::to_string(entropy) +
                                                      " 文件大小=" + std::to_string(file_stat.st_size) +
                                                      " 计算耗时=" + std::to_string(duration.count()) + "ms" +
                                                      " 总熵值计算次数=" + std::to_string(event_stats_.entropy_calculations.load()) +
                                                      " 写熵值计算次数=" + std::to_string(event_stats_.write_entropy_calculations.load()));
                    } else {
                        LogManager::getInstance().warning("[FileMonitor] 熵值计算结果无效: " + event.file_path +
                                                         " 熵值=" + std::to_string(entropy));
                    }
                } catch (const std::exception& e) {
                    LogManager::getInstance().error("[FileMonitor] 文件熵值计算异常: " + event.file_path +
                                                   " 错误=" + std::string(e.what()));
                }
            }
            break;
            
        default:
            // 其他事件类型不需要计算熵值
            break;
    }
}

void FileMonitor::recordFileOperationHistory(pid_t pid, const FileEvent& event) {
    auto& focus_manager = FocusProcessManager::getInstance();
    if (!focus_manager.isFocusProcess(pid)) {
        return;
    }
    
    // 通过ProcessMonitor记录文件操作历史和其他操作
    auto& process_manager = ProcessMonitorManager::getInstance();
    auto* process_monitor = process_manager.getProcessMonitor();
    
    if (process_monitor) {
        LogManager::getInstance().debug("[FileMonitor] 记录文件操作历史: PID=" + std::to_string(pid) + 
                                       " 路径=" + event.file_path);
        
        // 记录文件路径到历史记录
        process_monitor->addFilePathToHistory(pid, event.file_path);
        
        // 根据事件类型记录特定操作
        switch (event.type) {
            case FileEvent::DELETE:
                process_monitor->recordFocusProcessDelete(pid, event.file_path);
                LogManager::getInstance().debug("[FileMonitor] 记录删除操作: PID=" + std::to_string(pid));
                break;
            case FileEvent::RENAME:
                // 对于重命名，我们只有一个路径，旧路径暂时为空
                process_monitor->recordFocusProcessRename(pid, event.file_path, "");
                LogManager::getInstance().debug("[FileMonitor] 记录重命名操作: PID=" + std::to_string(pid));
                break;
            default:
                // 其他类型的操作只记录路径历史
                break;
        }
    } else {
        LogManager::getInstance().warning("[FileMonitor] ProcessMonitor指针为空，无法记录文件操作历史");
    }
    
    LogManager::getInstance().debug("[FileMonitor] 记录重点进程文件操作: PID=" + 
                                   std::to_string(pid) + " 路径=" + event.file_path + 
                                   " 类型=" + std::to_string(static_cast<int>(event.type)));
}

// 工具方法实现
FileEvent::EventType FileMonitor::fanotifyMaskToEventType(uint64_t mask) {
    // 【关键修复】：调整事件类型检查的优先级
    // FAN_CLOSE_WRITE 和 FAN_CLOSE_NOWRITE 应该优先于其他事件类型检查
    // 因为关闭事件可能同时包含其他标志位（如FAN_ACCESS）

    if (mask & FAN_CLOSE_WRITE) return FileEvent::WRITE;
    if (mask & FAN_ACCESS) return FileEvent::READ;
    if (mask & FAN_MODIFY) return FileEvent::WRITE;
    ///if (mask & FAN_OPEN) return FileEvent::READ;

    return FileEvent::UNKNOWN;
}

std::string FileMonitor::getPathFromFd(int fd) {
    char path_buffer[PATH_MAX];
    char proc_path[64];
    
    snprintf(proc_path, sizeof(proc_path), "/proc/self/fd/%d", fd);
    ssize_t len = readlink(proc_path, path_buffer, sizeof(path_buffer) - 1);
    
    if (len != -1) {
        path_buffer[len] = '\0';
        return std::string(path_buffer);
    }
    
    return "unknown";
}

std::string FileMonitor::getProcessName(pid_t pid) {
    return Utils::getProcessName(pid);
}

// isWriteOperation函数已移除 - 在FAN_CLASS_NOTIF模式下不再需要检查写入操作

void FileMonitor::addRecentEvent(const FileEvent& event) {
    std::lock_guard<std::mutex> lock(FileMonitor_events_mutex_);
    
    recent_events_.push_back(event);
    
    // 保持最大事件数量限制
    if (recent_events_.size() > MAX_RECENT_EVENTS) {
        recent_events_.erase(recent_events_.begin());
    }
}

// 内核版本检测
void FileMonitor::detectKernelCapabilities() {
    struct utsname uts;
    if (uname(&uts) == 0) {
        LogManager::getInstance().info("[FileMonitor] 检测到内核版本: " + std::string(uts.release));
        
        // 简单检查是否支持FAN_MARK_FILESYSTEM (4.20+)
        int major, minor;
        if (sscanf(uts.release, "%d.%d", &major, &minor) == 2) {
            kernel_supports_filesystem_mark_ = (major > 4 || (major == 4 && minor >= 20));
            
            LogManager::getInstance().info("[FileMonitor] FAN_MARK_FILESYSTEM支持: " + 
                                         std::string(kernel_supports_filesystem_mark_ ? "是" : "否"));
        }
    }
}

bool FileMonitor::checkKernelVersion(int major, int minor) {
    struct utsname uts;
    if (uname(&uts) == 0) {
        int k_major, k_minor;
        if (sscanf(uts.release, "%d.%d", &k_major, &k_minor) == 2) {
            return (k_major > major || (k_major == major && k_minor >= minor));
        }
    }
    return false;
}

// 挂载点管理
bool FileMonitor::shouldMonitorMountPoint(const std::string& mount_point) {
    // 跳过虚拟文件系统，但保留/tmp目录用于测试
    const std::vector<std::string> skip_fs = {
        "/proc", "/sys", "/dev", "/run",
        "/dev/shm", "/dev/pts", "/sys/fs/cgroup"
    };
    
    for (const auto& skip : skip_fs) {
        if (mount_point.find(skip) == 0) {
            return false;
        }
    }
    
    return true;
}

// 受保护路径动态管理
bool FileMonitor::addProtectedPathMark(const std::string& path) {
    if (protected_fanotify_fd_ == -1) {
        LogManager::getInstance().error("[FileMonitor] 受保护路径fanotify文件描述符无效，无法添加标记: " + path);
        return false;
    }
    
    // 检查路径是否存在
    struct stat path_stat;
    if (stat(path.c_str(), &path_stat) != 0) {
        LogManager::getInstance().warning("[FileMonitor] 受保护路径不存在，跳过fanotify标记: " + path + 
                                         " (" + std::string(strerror(errno)) + ")");
        // 路径不存在，但仍然返回true，因为路径已加入保护列表
        // 当路径被创建时，会通过其他监控机制检测到
        return true;
    }
    
    // 记录路径信息
    std::string path_type = S_ISDIR(path_stat.st_mode) ? "目录" : "文件";
    LogManager::getInstance().debug("[FileMonitor] 准备添加受保护路径标记: " + path + 
                                   " (类型: " + path_type + ", 权限: " + 
                                   std::to_string(path_stat.st_mode & 0777) + ")");
    
    uint64_t mask = FAN_MODIFY | FAN_CLOSE_WRITE | FAN_EVENT_ON_CHILD;
    
    if (fanotify_mark(protected_fanotify_fd_, FAN_MARK_ADD | FAN_MARK_ONLYDIR,
                     mask, AT_FDCWD, path.c_str()) == -1) {
        LogManager::getInstance().error("[FileMonitor] 添加受保护路径标记失败: " + path + 
                                       " - " + std::string(strerror(errno)));
        
        // 检查具体错误原因
        if (errno == EPERM) {
            LogManager::getInstance().error("[FileMonitor] 权限不足，无法监控路径: " + path);
        } else if (errno == ENOENT) {
            LogManager::getInstance().error("[FileMonitor] 路径不存在: " + path);
        } else if (errno == EINVAL) {
            LogManager::getInstance().error("[FileMonitor] 无效的fanotify参数");
        }
        
        return false;
    }
    
    LogManager::getInstance().info("[FileMonitor] 成功添加受保护路径标记: " + path + " (FAN_MODIFY | FAN_CLOSE_WRITE)");
    return true;
}

void FileMonitor::removeProtectedPathMark(const std::string& path) {
    if (protected_fanotify_fd_ == -1) {
        return;
    }
    
    uint64_t mask = FAN_MODIFY | FAN_CLOSE_WRITE | FAN_EVENT_ON_CHILD;
    
    if (fanotify_mark(protected_fanotify_fd_, FAN_MARK_REMOVE | FAN_MARK_ONLYDIR,
                     mask, AT_FDCWD, path.c_str()) == -1) {
        // 如果路径不存在或已经没有标记，这是正常的，只记录debug级别日志
        LogManager::getInstance().debug("[FileMonitor] 移除受保护路径标记: " + path + 
                                       " (" + std::string(strerror(errno)) + ")");
    } else {
        LogManager::getInstance().info("[FileMonitor] 成功移除受保护路径标记: " + path);
    }
}

// 清理方法
void FileMonitor::cleanup() {
    try {
        closeAllFds();
        LogManager::getInstance().debug("[FileMonitor] 清理完成");
    } catch (const std::exception& e) {
        LogManager::getInstance().error("[FileMonitor] 清理异常: " + std::string(e.what()));
    }
}

void FileMonitor::closeAllFds() {
    // 安全关闭受保护路径fanotify文件描述符
    if (protected_fanotify_fd_ != -1) {
        if (close(protected_fanotify_fd_) == -1) {
            LogManager::getInstance().warning("[FileMonitor] 关闭受保护路径fanotify文件描述符失败: " +
                                            std::string(strerror(errno)));
        } else {
            LogManager::getInstance().debug("[FileMonitor] 成功关闭受保护路径fanotify文件描述符: " +
                                          std::to_string(protected_fanotify_fd_));
        }
        protected_fanotify_fd_ = -1;
    }

    // 安全关闭重点进程fanotify文件描述符
    if (focus_fanotify_fd_ != -1) {
        if (close(focus_fanotify_fd_) == -1) {
            LogManager::getInstance().warning("[FileMonitor] 关闭重点进程fanotify文件描述符失败: " +
                                            std::string(strerror(errno)));
        } else {
            LogManager::getInstance().debug("[FileMonitor] 成功关闭重点进程fanotify文件描述符: " +
                                          std::to_string(focus_fanotify_fd_));
        }
        focus_fanotify_fd_ = -1;
    }
}