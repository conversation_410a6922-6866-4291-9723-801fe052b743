#include "process_exclusion.h"
#include "main_monitor.h"
#include <fstream>
#include <sstream>
#include <algorithm>

// 预定义的系统进程
const std::vector<std::string> ProcessExclusionManager::SYSTEM_PROCESSES = {
    "kernel", "kthreadd", "ksoftirqd", "migration", "rcu_gp", "rcu_par_gp",
    "kworker", "mm_percpu_wq", "ksoftirqd", "rcu_preempt", "rcu_sched", "rcu_bh",
    "systemd", "systemd-", "dbus", "NetworkManager", "sshd", "rsyslog",
    "init", "kthread", "watchdog", "systemd-logind", "systemd-resolved",
    "systemd-networkd", "systemd-timesyncd", "cron", "irqbalance"
};

const std::vector<std::string> ProcessExclusionManager::SYSTEM_PATHS = {
    "/sbin/init", "/usr/sbin/", "/sbin/", "/usr/lib/systemd/",
    "/lib/systemd/", "/usr/bin/dbus", "/usr/sbin/NetworkManager",
    "/usr/sbin/sshd", "/usr/sbin/rsyslog"
};

const std::vector<std::string> ProcessExclusionManager::SYSTEM_PATTERNS = {
    "^kworker/.*", "^ksoftirqd/.*", "^migration/.*", "^rcu_.*",
    "^systemd-.*", "^\\[.*\\]$", "^watchdog/.*"
};

// 单例实现
std::unique_ptr<ProcessExclusionManager> ProcessExclusionManagerSingleton::instance_;
std::mutex ProcessExclusionManagerSingleton_instance_mutex_;

ProcessExclusionManager& ProcessExclusionManagerSingleton::getInstance() {
    std::lock_guard<std::mutex> lock(ProcessExclusionManagerSingleton_instance_mutex_);
    if (!instance_) {
        instance_.reset(new ProcessExclusionManager());
        instance_->initialize();
    }
    return *instance_;
}

// ProcessExclusionManager 实现
ProcessExclusionManager::ProcessExclusionManager() {
}

ProcessExclusionManager::~ProcessExclusionManager() {
}

bool ProcessExclusionManager::initialize() {
    logInfo("初始化进程排除管理器");
    
    // 添加默认的系统进程排除规则
    addDefaultExclusions();
    
    logInfo("进程排除管理器初始化完成，总计 " + 
            std::to_string(getTotalExclusions()) + " 个排除规则");
    return true;
}

bool ProcessExclusionManager::isExcluded(pid_t pid) const {
    std::lock_guard<std::mutex> lock(ProcessExclusionManager_exclusions_mutex_);
    return excluded_pids_.find(pid) != excluded_pids_.end();
}

bool ProcessExclusionManager::isExcluded(const std::string& process_name) const {
    if (process_name.empty()) {
        return false;
    }

    std::lock_guard<std::mutex> lock(ProcessExclusionManager_exclusions_mutex_);
    
    // 检查精确匹配
    if (excluded_names_.find(process_name) != excluded_names_.end()) {
        return true;
    }
    
    // 检查模式匹配
    return matchesPattern(process_name);
}

bool ProcessExclusionManager::isExcludedByPath(const std::string& executable_path) const {
    if (executable_path.empty()) {
        return false;
    }

    std::lock_guard<std::mutex> lock(ProcessExclusionManager_exclusions_mutex_);
    
    // 检查精确路径匹配
    if (excluded_paths_.find(executable_path) != excluded_paths_.end()) {
        return true;
    }
    
    // 检查路径前缀匹配
    for (const auto& excluded_path : excluded_paths_) {
        if (executable_path.find(excluded_path) == 0) {
            return true;
        }
    }
    
    // 检查模式匹配
    return matchesPattern(executable_path);
}

void ProcessExclusionManager::addExclusionByPid(pid_t pid) {
    std::lock_guard<std::mutex> lock(ProcessExclusionManager_exclusions_mutex_);
    excluded_pids_.insert(pid);
    logDebug("添加PID排除规则: " + std::to_string(pid));
}

void ProcessExclusionManager::addExclusionByName(const std::string& name) {
    if (name.empty()) {
        return;
    }

    std::lock_guard<std::mutex> lock(ProcessExclusionManager_exclusions_mutex_);
    excluded_names_.insert(name);
    logDebug("添加进程名排除规则: " + name);
}

void ProcessExclusionManager::addExclusionByPattern(const std::string& pattern) {
    if (pattern.empty()) {
        return;
    }

    std::lock_guard<std::mutex> lock(ProcessExclusionManager_exclusions_mutex_);
    
    try {
        excluded_patterns_.emplace_back(pattern);
        excluded_pattern_strings_.push_back(pattern);
        logDebug("添加模式排除规则: " + pattern);
    } catch (const std::regex_error& e) {
        logError("无效的正则表达式模式: " + pattern + " - " + e.what());
    }
}

void ProcessExclusionManager::addExclusionByPath(const std::string& path) {
    if (path.empty()) {
        return;
    }

    std::lock_guard<std::mutex> lock(ProcessExclusionManager_exclusions_mutex_);
    excluded_paths_.insert(path);
    logDebug("添加路径排除规则: " + path);
}

void ProcessExclusionManager::removeExclusionByPid(pid_t pid) {
    std::lock_guard<std::mutex> lock(ProcessExclusionManager_exclusions_mutex_);
    excluded_pids_.erase(pid);
    logDebug("移除PID排除规则: " + std::to_string(pid));
}

void ProcessExclusionManager::removeExclusionByName(const std::string& name) {
    std::lock_guard<std::mutex> lock(ProcessExclusionManager_exclusions_mutex_);
    excluded_names_.erase(name);
    logDebug("移除进程名排除规则: " + name);
}

void ProcessExclusionManager::removeExclusionByPath(const std::string& path) {
    std::lock_guard<std::mutex> lock(ProcessExclusionManager_exclusions_mutex_);
    excluded_paths_.erase(path);
    logDebug("移除路径排除规则: " + path);
}

void ProcessExclusionManager::addExclusionsFromConfig(const std::vector<std::string>& config_lines) {
    for (const auto& line : config_lines) {
        std::string trimmed = Utils::trim(line);
        if (trimmed.empty() || trimmed[0] == '#') {
            continue; // 跳过空行和注释
        }
        
        // 解析配置行格式: type:value
        size_t colon_pos = trimmed.find(':');
        if (colon_pos == std::string::npos) {
            // 默认作为进程名处理
            addExclusionByName(trimmed);
            continue;
        }
        
        std::string type = Utils::trim(trimmed.substr(0, colon_pos));
        std::string value = Utils::trim(trimmed.substr(colon_pos + 1));
        
        if (type == "pid") {
            try {
                pid_t pid = std::stoi(value);
                addExclusionByPid(pid);
            } catch (const std::exception& e) {
                logError("无效的PID值: " + value);
            }
        } else if (type == "name") {
            addExclusionByName(value);
        } else if (type == "path") {
            addExclusionByPath(value);
        } else if (type == "pattern") {
            addExclusionByPattern(value);
        } else {
            logError("未知的排除类型: " + type);
        }
    }
}

void ProcessExclusionManager::loadExclusionsFromFile(const std::string& file_path) {
    std::ifstream file(file_path);
    if (!file.is_open()) {
        logError("无法打开排除规则文件: " + file_path);
        return;
    }
    
    std::vector<std::string> lines;
    std::string line;
    
    while (std::getline(file, line)) {
        lines.push_back(line);
    }
    
    addExclusionsFromConfig(lines);
    logInfo("从文件加载排除规则: " + file_path + "，共 " + std::to_string(lines.size()) + " 行");
}

void ProcessExclusionManager::saveExclusionsToFile(const std::string& file_path) const {
    std::ofstream file(file_path);
    if (!file.is_open()) {
        logError("无法创建排除规则文件: " + file_path);
        return;
    }
    
    file << "# PSFSMON 进程排除规则配置文件\n";
    file << "# 格式: type:value\n";
    file << "# 类型: pid, name, path, pattern\n\n";
    
    std::lock_guard<std::mutex> lock(ProcessExclusionManager_exclusions_mutex_);
    
    // 保存PID排除规则
    for (pid_t pid : excluded_pids_) {
        file << "pid:" << pid << "\n";
    }
    
    // 保存进程名排除规则
    for (const auto& name : excluded_names_) {
        file << "name:" << name << "\n";
    }
    
    // 保存路径排除规则
    for (const auto& path : excluded_paths_) {
        file << "path:" << path << "\n";
    }
    
    // 保存模式排除规则
    for (const auto& pattern : excluded_pattern_strings_) {
        file << "pattern:" << pattern << "\n";
    }
    
    logInfo("保存排除规则到文件: " + file_path);
}

std::set<pid_t> ProcessExclusionManager::getExcludedPids() const {
    std::lock_guard<std::mutex> lock(ProcessExclusionManager_exclusions_mutex_);
    return std::set<pid_t>(excluded_pids_.begin(), excluded_pids_.end());
}

std::set<std::string> ProcessExclusionManager::getExcludedNames() const {
    std::lock_guard<std::mutex> lock(ProcessExclusionManager_exclusions_mutex_);
    return excluded_names_;
}

std::set<std::string> ProcessExclusionManager::getExcludedPaths() const {
    std::lock_guard<std::mutex> lock(ProcessExclusionManager_exclusions_mutex_);
    return excluded_paths_;
}

std::vector<std::string> ProcessExclusionManager::getExcludedPatterns() const {
    std::lock_guard<std::mutex> lock(ProcessExclusionManager_exclusions_mutex_);
    return excluded_pattern_strings_;
}

size_t ProcessExclusionManager::getTotalExclusions() const {
    std::lock_guard<std::mutex> lock(ProcessExclusionManager_exclusions_mutex_);
    return excluded_pids_.size() + excluded_names_.size() +
           excluded_paths_.size() + excluded_patterns_.size();
}

size_t ProcessExclusionManager::getExclusionsByType(const std::string& type) const {
    std::lock_guard<std::mutex> lock(ProcessExclusionManager_exclusions_mutex_);
    
    if (type == "pid") {
        return excluded_pids_.size();
    } else if (type == "name") {
        return excluded_names_.size();
    } else if (type == "path") {
        return excluded_paths_.size();
    } else if (type == "pattern") {
        return excluded_patterns_.size();
    }
    
    return 0;
}

void ProcessExclusionManager::clearAllExclusions() {
    std::lock_guard<std::mutex> lock(ProcessExclusionManager_exclusions_mutex_);
    excluded_pids_.clear();
    excluded_names_.clear();
    excluded_paths_.clear();
    excluded_patterns_.clear();
    excluded_pattern_strings_.clear();
    logInfo("清除所有排除规则");
}

void ProcessExclusionManager::clearExclusionsByType(const std::string& type) {
    std::lock_guard<std::mutex> lock(ProcessExclusionManager_exclusions_mutex_);
    
    if (type == "pid") {
        excluded_pids_.clear();
    } else if (type == "name") {
        excluded_names_.clear();
    } else if (type == "path") {
        excluded_paths_.clear();
    } else if (type == "pattern") {
        excluded_patterns_.clear();
        excluded_pattern_strings_.clear();
    }
    
    logInfo("清除类型为 " + type + " 的排除规则");
}

void ProcessExclusionManager::addDefaultExclusions() {
    logInfo("添加默认系统进程排除规则");
    
    // 添加系统进程名
    for (const auto& process : SYSTEM_PROCESSES) {
        addExclusionByName(process);
    }
    
    // 添加系统路径
    for (const auto& path : SYSTEM_PATHS) {
        addExclusionByPath(path);
    }
    
    // 添加系统模式
    for (const auto& pattern : SYSTEM_PATTERNS) {
        addExclusionByPattern(pattern);
    }
    
    // 添加自己进程（避免自我监控）
    addExclusionByName("psfsmon");
    addExclusionByPid(getpid());
}

bool ProcessExclusionManager::matchesPattern(const std::string& text) const {
    for (const auto& pattern : excluded_patterns_) {
        try {
            if (std::regex_match(text, pattern)) {
                return true;
            }
        } catch (const std::regex_error& e) {
            logError("正则表达式匹配错误: " + std::string(e.what()));
        }
    }
    return false;
}

void ProcessExclusionManager::compilePatterns() {
    excluded_patterns_.clear();
    for (const auto& pattern_str : excluded_pattern_strings_) {
        try {
            excluded_patterns_.emplace_back(pattern_str);
        } catch (const std::regex_error& e) {
            logError("编译正则表达式失败: " + pattern_str + " - " + e.what());
        }
    }
}

void ProcessExclusionManager::logInfo(const std::string& message) const {
    LogManager::getInstance().info("[ProcessExclusionManager] " + message);
}

void ProcessExclusionManager::logError(const std::string& message) const {
    LogManager::getInstance().error("[ProcessExclusionManager] " + message);
}

void ProcessExclusionManager::logDebug(const std::string& message) const {
    LogManager::getInstance().debug("[ProcessExclusionManager] " + message);
} 