#include "api_server.h"
#include "main_monitor.h"
#include "process_exclusion.h"
#include "file_monitor.h"  // 包含FocusProcessManager定义
#include <sstream>
#include <algorithm>
#include <fstream>
#include <ctime>

// ApiServer简化实现
ApiServer::ApiServer() : main_monitor_(nullptr), running_(false) {
}

ApiServer::~ApiServer() {
    stop();
}

bool ApiServer::initialize(uint16_t port, MainMonitor* monitor) {
    main_monitor_ = monitor;
    
    http_server_.reset(new HttpServer());
        if (!http_server_->initialize(port, "127.0.0.1")) {
        LogManager::getInstance().error("[ApiServer] Unable to initialize HTTP server");
        return false;
    }

    registerApiEndpoints();

    LogManager::getInstance().info("[ApiServer] API server initialized successfully, port: " + std::to_string(port));
    return true;
}

bool ApiServer::start() {
    if (running_ || !http_server_) {
        return false;
    }
    
        if (!http_server_->start()) {
        LogManager::getInstance().error("[ApiServer] Unable to start HTTP server");
        return false;
    }

    running_ = true;
    LogManager::getInstance().info("[ApiServer] API server started");
    return true;
}

void ApiServer::stop() {
    if (!running_) {
        return;
    }
    
    running_ = false;
    
    if (http_server_) {
        http_server_->stop();
    }
    
    LogManager::getInstance().info("[ApiServer] API server stopped");
}

void ApiServer::registerApiEndpoints() {
    if (!http_server_) {
        return;
    }
    
    // 基础API路由
    http_server_->registerRoute(HttpMethod::GET, "/api/health", 
        [this](const HttpRequest& req, HttpResponse& resp) {
            handleHealthCheck(req, resp);
        });
    
    http_server_->registerRoute(HttpMethod::GET, "/api/stats", 
        [this](const HttpRequest& req, HttpResponse& resp) {
            handleGetSystemStats(req, resp);
        });
    
    // 进程相关API
    http_server_->registerRoute(HttpMethod::GET, "/api/processes", 
        [this](const HttpRequest& req, HttpResponse& resp) {
            handleGetAllProcesses(req, resp);
        });
    
    http_server_->registerRoute(HttpMethod::GET, "/api/processes-list", 
        [this](const HttpRequest& req, HttpResponse& resp) {
            handleGetProcessesList(req, resp);
        });
    
    http_server_->registerRoute(HttpMethod::GET, "/api/process-tree", 
        [this](const HttpRequest& req, HttpResponse& resp) {
            handleGetProcessTree(req, resp);
        });
    
    // 网络相关API
    http_server_->registerRoute(HttpMethod::GET, "/api/network", 
        [this](const HttpRequest& req, HttpResponse& resp) {
            handleGetNetworkConnections(req, resp);
        });
    
    http_server_->registerRoute(HttpMethod::GET, "/api/network/stats", 
        [this](const HttpRequest& req, HttpResponse& resp) {
            handleGetNetworkStats(req, resp);
        });
    
    http_server_->registerRoute(HttpMethod::GET, "/api/network/listening", 
        [this](const HttpRequest& req, HttpResponse& resp) {
            handleGetListeningPorts(req, resp);
        });
    
    // 特定进程API
    http_server_->registerRoute(HttpMethod::GET, "/api/process", 
        [this](const HttpRequest& req, HttpResponse& resp) {
            handleGetProcess(req, resp);
        });
    
    // 注意：根据需求10，已删除文件操作序列API
    
    // 重点进程文件详细信息API
    http_server_->registerRoute(HttpMethod::GET, "/api/focus-process/file-info", 
        [this](const HttpRequest& req, HttpResponse& resp) {
            handleGetFocusProcessFileInfo(req, resp);
        });
    
    // 排除进程管理API
    http_server_->registerRoute(HttpMethod::GET, "/api/exclusions", 
        [this](const HttpRequest& req, HttpResponse& resp) {
            handleGetExclusions(req, resp);
        });
    
    http_server_->registerRoute(HttpMethod::POST, "/api/exclusions", 
        [this](const HttpRequest& req, HttpResponse& resp) {
            handleAddExclusion(req, resp);
        });
    
    http_server_->registerRoute(HttpMethod::DELETE, "/api/exclusions", 
        [this](const HttpRequest& req, HttpResponse& resp) {
            handleRemoveExclusion(req, resp);
        });
    
    // 受保护路径管理API
    http_server_->registerRoute(HttpMethod::GET, "/api/protected-paths", 
        [this](const HttpRequest& req, HttpResponse& resp) {
            handleGetProtectedPaths(req, resp);
        });
    
    http_server_->registerRoute(HttpMethod::POST, "/api/protected-paths", 
        [this](const HttpRequest& req, HttpResponse& resp) {
            handleAddProtectedPath(req, resp);
        });
    
    http_server_->registerRoute(HttpMethod::DELETE, "/api/protected-paths", 
        [this](const HttpRequest& req, HttpResponse& resp) {
            handleRemoveProtectedPath(req, resp);
        });
    
    // 重点进程管理API
    http_server_->registerRoute(HttpMethod::GET, "/api/focus-processes", 
        [this](const HttpRequest& req, HttpResponse& resp) {
            handleGetFocusProcesses(req, resp);
        });
    
    http_server_->registerRoute(HttpMethod::POST, "/api/focus-processes", 
        [this](const HttpRequest& req, HttpResponse& resp) {
            handleAddFocusProcess(req, resp);
        });
    
    http_server_->registerRoute(HttpMethod::DELETE, "/api/focus-processes", 
        [this](const HttpRequest& req, HttpResponse& resp) {
            handleRemoveFocusProcess(req, resp);
        });
    
    // 重点进程熵值信息API
    http_server_->registerRoute(HttpMethod::GET, "/api/focus-processes/entropy", 
        [this](const HttpRequest& req, HttpResponse& resp) {
            handleGetFocusProcessEntropy(req, resp);
        });
    
    // 新增：重点进程详细熵值统计API
    http_server_->registerRoute(HttpMethod::GET, "/api/focus-processes/entropy-stats", 
        [this](const HttpRequest& req, HttpResponse& resp) {
            handleGetFocusProcessEntropyStats(req, resp);
        });
    
    // 系统监控状态API
    http_server_->registerRoute(HttpMethod::GET, "/api/monitor/status", 
        [this](const HttpRequest& req, HttpResponse& resp) {
            handleGetMonitorStatus(req, resp);
        });
    
    // 配置API
    http_server_->registerRoute(HttpMethod::GET, "/api/config", 
        [this](const HttpRequest& req, HttpResponse& resp) {
            handleGetConfig(req, resp);
        });
    
    http_server_->registerRoute(HttpMethod::PUT, "/api/config", 
        [this](const HttpRequest& req, HttpResponse& resp) {
            handleUpdateConfig(req, resp);
        });
    
    // 文件监控API
    http_server_->registerRoute(HttpMethod::GET, "/api/file/events", 
        [this](const HttpRequest& req, HttpResponse& resp) {
            handleGetFileEvents(req, resp);
        });
    
    http_server_->registerRoute(HttpMethod::GET, "/api/file/stats", 
        [this](const HttpRequest& req, HttpResponse& resp) {
            handleGetFileStats(req, resp);
        });
    
    // 实时数据API
    http_server_->registerRoute(HttpMethod::GET, "/api/realtime", 
        [this](const HttpRequest& req, HttpResponse& resp) {
            handleGetRealtimeData(req, resp);
        });
}



void ApiServer::handleGetAllProcesses(const HttpRequest& request, HttpResponse& response) {
    (void)request; // 避免警告
    
    if (!main_monitor_) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Monitor not initialized");
        return;
    }
    
        auto process_monitor = main_monitor_->getProcessMonitor();
    if (!process_monitor) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Process monitor not initialized");
        return;
    }

    auto processes = process_monitor->getAllProcesses();
    
    JsonBuilder json;
    json.startObject()
        .addNumber("count", static_cast<uint64_t>(processes.size()))
        .startArray("processes");
    
    for (const auto& process : processes) {
        json.startObject()
            .addNumber("pid", static_cast<int64_t>(process->pid))
            .addNumber("ppid", static_cast<int64_t>(process->ppid))
            .addString("name", process->process_name)
            .addString("state", process->state)
            .addNumber("cpu_usage", process->cpu_usage)
            .addNumber("memory_usage", process->memory_usage)
            .addString("executable_path", process->executable_path)
            .addNumber("start_time", process->start_time)
            .endObject();
    }
    
    json.endArray().endObject();
    
    setJsonResponse(response, json.toString());
}

void ApiServer::handleGetProcessTree(const HttpRequest& request, HttpResponse& response) {
    (void)request; // 避免警告
    
    if (!main_monitor_) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Monitor not initialized");
        return;
    }
    
        auto process_monitor = main_monitor_->getProcessMonitor();
    if (!process_monitor) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Process monitor not initialized");
        return;
    }

    auto tree = process_monitor->getProcessTree();
    if (!tree) {
        setErrorResponse(response, HttpStatus::NOT_FOUND, "Process tree unavailable");
        return;
    }
    
    std::string tree_json = serializeProcessTree(*tree);
    setJsonResponse(response, tree_json);
}

std::string ApiServer::serializeProcessTree(const ProcessTreeNode& node) {
    JsonBuilder json;
    json.startObject()
        .addNumber("pid", static_cast<int64_t>(node.process->pid))
        .addString("name", node.process->process_name)
        .addString("state", node.process->state)
        .addNumber("cpu_usage", node.process->cpu_usage)
        .addNumber("memory_usage", node.process->memory_usage);
    
    if (!node.children.empty()) {
        json.startArray("children");
        for (const auto& child : node.children) {
            // 直接内联子对象，而不是作为字符串
            json.startObject()
                .addNumber("pid", static_cast<int64_t>(child->process->pid))
                .addString("name", child->process->process_name)
                .addString("state", child->process->state)
                .addNumber("cpu_usage", child->process->cpu_usage)
                .addNumber("memory_usage", child->process->memory_usage);
            
            // 递归处理子对象的子进程（这里暂时简化，只处理一层）
            if (!child->children.empty()) {
                json.addNumber("child_count", static_cast<uint64_t>(child->children.size()));
            }
            
            json.endObject();
        }
        json.endArray();
    }
    
    json.endObject();
    return json.toString();
}

void ApiServer::handleGetNetworkConnections(const HttpRequest& request, HttpResponse& response) {
    (void)request; // 避免警告
    
    if (!main_monitor_) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Monitor not initialized");
        return;
    }

    auto network_monitor = main_monitor_->getNetworkMonitor();
    if (!network_monitor) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Network monitor not initialized");
        return;
    }
    
    auto connections = network_monitor->getAllConnections();
    
    JsonBuilder json;
    json.startObject()
        .addNumber("count", static_cast<uint64_t>(connections.size()))
        .startArray("connections");
    
    for (const auto& conn : connections) {
        json.startObject()
            .addString("protocol", "TCP") // 简化处理，实际应该根据conn.protocol转换
            .addString("local_address", conn.local_addr + ":" + std::to_string(conn.local_port))
            .addString("remote_address", conn.remote_addr + ":" + std::to_string(conn.remote_port))
            .addString("state", "ESTABLISHED") // 简化处理，实际应该根据conn.state转换
            .addNumber("pid", static_cast<int64_t>(conn.pid))
            .addNumber("inode", static_cast<uint64_t>(conn.inode))
            .endObject();
    }
    
    json.endArray().endObject();
    
    setJsonResponse(response, json.toString());
}

void ApiServer::handleGetProcess(const HttpRequest& request, HttpResponse& response) {
    // 从查询参数中获取PID，因为我们暂时不支持路径参数
    std::string pid_str;
    auto it = request.query_params.find("pid");
    if (it != request.query_params.end()) {
        pid_str = it->second;
    } else {
        setErrorResponse(response, HttpStatus::BAD_REQUEST, "Missing pid parameter");
        return;
    }
    
    pid_t pid;
    try {
        pid = std::stoi(pid_str);
    } catch (const std::exception& e) {
        setErrorResponse(response, HttpStatus::BAD_REQUEST, "Invalid pid parameter");
        return;
    }
    
        if (!main_monitor_) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Monitor not initialized");
        return;
    }

    auto process_monitor = main_monitor_->getProcessMonitor();
    if (!process_monitor) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Process monitor not initialized");
        return;
    }

    auto process = process_monitor->getProcess(pid);
    if (!process) {
        setErrorResponse(response, HttpStatus::NOT_FOUND, "Process not found or inaccessible");
        return;
    }
    
    JsonBuilder json;
    json.startObject()
        .addNumber("pid", static_cast<int64_t>(process->pid))
        .addNumber("ppid", static_cast<int64_t>(process->ppid))
        .addString("name", process->process_name)
        .addString("state", process->state)
        .addNumber("cpu_usage", process->cpu_usage)
        .addNumber("memory_usage", process->memory_usage)
        .addNumber("virtual_memory", process->virtual_memory)
        .addString("executable_path", process->executable_path)
        .addString("command_line", process->command_line)
        .addString("cwd", process->cwd)
        .addNumber("uid", static_cast<uint64_t>(process->uid))
        .addNumber("gid", static_cast<uint64_t>(process->gid))
        .addNumber("start_time", process->start_time)
        .addNumber("num_children", static_cast<uint64_t>(process->children.size()))
        .endObject();
    
    setJsonResponse(response, json.toString());
}

void ApiServer::handleGetProcessesList(const HttpRequest& request, HttpResponse& response) {
    (void)request; // 避免警告
    
    if (!main_monitor_) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Monitor not initialized");
        return;
    }

    auto process_monitor = main_monitor_->getProcessMonitor();
    if (!process_monitor) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Process monitor not initialized");
        return;
    }
    
    auto processes = process_monitor->getAllProcesses();
    
    // 创建PID到进程名的映射，用于查找父进程名称
    std::unordered_map<pid_t, std::string> pid_to_name;
    for (const auto& process : processes) {
        pid_to_name[process->pid] = process->process_name;
    }
    
    JsonBuilder json;
    json.startObject()
        .addNumber("count", static_cast<uint64_t>(processes.size()))
        .startArray("processes");
    
    for (const auto& process : processes) {
        // 查找父进程名称
        std::string parent_name = "";
        auto parent_it = pid_to_name.find(process->ppid);
        if (parent_it != pid_to_name.end()) {
            parent_name = parent_it->second;
        } else if (process->ppid > 0) {
            // 如果父进程不在当前进程列表中，尝试从/proc读取
            std::string parent_comm_path = "/proc/" + std::to_string(process->ppid) + "/comm";
            std::ifstream parent_file(parent_comm_path);
            if (parent_file.is_open()) {
                std::getline(parent_file, parent_name);
                parent_name = Utils::trim(parent_name);
            }
        }
        
        json.startObject()
            .addNumber("pid", static_cast<int64_t>(process->pid))
            .addNumber("ppid", static_cast<int64_t>(process->ppid))
            .addString("parent_name", parent_name)
            .addString("name", process->process_name)
            .addString("state", process->state)
            .addNumber("cpu_usage", process->cpu_usage)
            .addNumber("memory_usage", process->memory_usage)
            .addNumber("virtual_memory", process->virtual_memory)
            .addString("executable_path", process->executable_path)
            .addString("command_line", process->command_line)
            .addString("cwd", process->cwd)
            .addNumber("uid", static_cast<uint64_t>(process->uid))
            .addNumber("gid", static_cast<uint64_t>(process->gid))
            .addNumber("start_time", process->start_time)
            .addNumber("num_children", static_cast<uint64_t>(process->children.size()))
            .endObject();
    }
    
    json.endArray().endObject();
    
    setJsonResponse(response, json.toString());
}

void ApiServer::handleGetNetworkStats(const HttpRequest& request, HttpResponse& response) {
    (void)request; // 避免警告
    
    if (!main_monitor_) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Monitor not initialized");
        return;
    }

    auto network_monitor = main_monitor_->getNetworkMonitor();
    if (!network_monitor) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Network monitor not initialized");
        return;
    }
    
    auto stats = network_monitor->getNetworkStats();
    
    JsonBuilder json;
    json.startObject()
        .addNumber("total_connections", static_cast<uint64_t>(stats.total_connections))
        .addNumber("tcp_connections", static_cast<uint64_t>(stats.tcp_connections))
        .addNumber("udp_connections", static_cast<uint64_t>(stats.udp_connections))
        .addNumber("listening_ports", static_cast<uint64_t>(stats.listening_ports))
        .addNumber("bytes_sent", stats.total_bytes_sent)
        .addNumber("bytes_received", stats.total_bytes_received)
        .addNumber("packets_sent", stats.packets_sent)
        .addNumber("packets_received", stats.packets_received)
        .endObject();
    
    setJsonResponse(response, json.toString());
}

void ApiServer::handleGetListeningPorts(const HttpRequest& request, HttpResponse& response) {
    (void)request; // 避免警告
    
    if (!main_monitor_) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Monitor not initialized");
        return;
    }

    auto network_monitor = main_monitor_->getNetworkMonitor();
    if (!network_monitor) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Network monitor not initialized");
        return;
    }
    
    auto listening_ports = network_monitor->getListeningPorts();
    
    JsonBuilder json;
    json.startObject()
        .addNumber("count", static_cast<uint64_t>(listening_ports.size()))
        .startArray("listening_ports");
    
    for (const auto& port_info : listening_ports) {
        json.startObject()
            .addNumber("port", static_cast<uint64_t>(port_info.local_port))
            .addString("protocol", "TCP")  // 简化处理，实际应该根据protocol转换
            .addString("address", port_info.local_addr)
            .addNumber("pid", static_cast<int64_t>(port_info.pid))
            .addString("process_name", "")  // 需要从进程监控器获取
            .endObject();
    }
    
    json.endArray().endObject();
    
    setJsonResponse(response, json.toString());
}



void ApiServer::handleGetFileEvents(const HttpRequest& request, HttpResponse& response) {
    auto params = parseQueryParameters(request);
    
    if (!main_monitor_) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Monitor not initialized");
        return;
    }
    
    auto file_monitor = main_monitor_->getFileMonitor();
    if (!file_monitor) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "File monitor not initialized");
        return;
    }
    
    // 可选参数：limit（限制返回的事件数量）
    size_t limit = 100; // 默认限制
    auto limit_it = params.find("limit");
    if (limit_it != params.end()) {
        try {
            limit = std::stoul(limit_it->second);
            if (limit > 1000) limit = 1000; // 最大限制
        } catch (const std::exception& e) {
            // 使用默认值
        }
    }
    
    // 简化实现：返回基本的文件监控信息
    JsonBuilder json;
    json.startObject()
        .addNumber("count", static_cast<uint64_t>(0))  // 暂时返回0，需要实现事件存储
        .addNumber("limit", static_cast<uint64_t>(limit))
        .startArray("events");
    
    // 不再提供详细事件序列，只提供统计信息
    // 根据新需求，我们只关心受保护路径违规和重点进程统计
    
    json.endArray().endObject();
    
    setJsonResponse(response, json.toString());
}

void ApiServer::handleGetFileStats(const HttpRequest& request, HttpResponse& response) {
    (void)request; // 避免警告
    
    if (!main_monitor_) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Monitor not initialized");
        return;
    }
    
    auto file_monitor = main_monitor_->getFileMonitor();
    if (!file_monitor) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "File monitor not initialized");
        return;
    }
    
    // 获取详细的文件监控统计
    auto event_stats = file_monitor->getEventStats();
    JsonBuilder json;
    json.startObject()
        // 基础统计
        .addNumber("total_events", static_cast<uint64_t>(event_stats.total_events))
        .addNumber("fanotify_events", static_cast<uint64_t>(event_stats.fanotify_events))
        .addNumber("focus_process_events", static_cast<uint64_t>(event_stats.focus_process_events))
        .addNumber("protected_path_events", static_cast<uint64_t>(event_stats.protected_path_events))
        .addNumber("permission_denied_events", static_cast<uint64_t>(event_stats.permission_denied_events))
        
        // 详细事件类型统计
        .addNumber("read_events", static_cast<uint64_t>(event_stats.read_events))
        .addNumber("write_events", static_cast<uint64_t>(event_stats.write_events))
        .addNumber("open_events", static_cast<uint64_t>(event_stats.open_events))
        .addNumber("close_events", static_cast<uint64_t>(event_stats.close_events))
        //.addNumber("access_events", static_cast<uint64_t>(event_stats.access_events))
        //.addNumber("modify_events", static_cast<uint64_t>(event_stats.modify_events))
        .addNumber("create_events", static_cast<uint64_t>(event_stats.create_events))
        .addNumber("delete_events", static_cast<uint64_t>(event_stats.delete_events))
        .addNumber("move_events", static_cast<uint64_t>(event_stats.move_events))
        .addNumber("permission_events", static_cast<uint64_t>(event_stats.permission_events))
        
        // 熵值计算统计
        .addNumber("entropy_calculations", static_cast<uint64_t>(event_stats.entropy_calculations))
        .addNumber("read_entropy_calculations", static_cast<uint64_t>(event_stats.read_entropy_calculations))
        .addNumber("write_entropy_calculations", static_cast<uint64_t>(event_stats.write_entropy_calculations))
        //.addNumber("open_entropy_calculations", static_cast<uint64_t>(event_stats.open_entropy_calculations))
        //.addNumber("close_entropy_calculations", static_cast<uint64_t>(event_stats.close_entropy_calculations))
        
        // 重点进程文件统计
        .addNumber("focus_process_files", static_cast<uint64_t>(event_stats.focus_process_files))
        .addNumber("focus_process_unique_files", static_cast<uint64_t>(event_stats.focus_process_unique_files))
        
        // 兼容性字段（保留旧API字段）
        .addNumber("monitored_paths", static_cast<uint64_t>(0))  // TODO: 需要实现路径统计
        .addNumber("timestamp", Utils::getCurrentTimestamp())
        .endObject();
    
    setJsonResponse(response, json.toString());
}

void ApiServer::handleGetRealtimeData(const HttpRequest& request, HttpResponse& response) {
    (void)request; // 避免警告
    
    if (!main_monitor_) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Monitor not initialized");
        return;
    }
    
    // 获取最近的实时数据
    auto process_monitor = main_monitor_->getProcessMonitor();
    auto file_monitor = main_monitor_->getFileMonitor();
    auto network_monitor = main_monitor_->getNetworkMonitor();
    
    JsonBuilder json;
    json.startObject()
        .addNumber("timestamp", Utils::getCurrentTimestamp());
    
    // 进程统计
    if (process_monitor) {
        json.addNumber("active_processes", static_cast<uint64_t>(process_monitor->getTotalProcesses()))
            .addNumber("active_processes", static_cast<uint64_t>(process_monitor->getTotalProcesses()))
            .addNumber("new_processes", static_cast<uint64_t>(0))  // TODO: 需要实现新进程统计
            .addNumber("terminated_processes", static_cast<uint64_t>(0));  // TODO: 需要实现终止进程统计
    }
    
    // 文件活动
    if (file_monitor) {
        json.addNumber("recent_file_events", static_cast<uint64_t>(file_monitor->getTotalEvents()));
    }
    
    // 网络活动
    if (network_monitor) {
        auto network_stats = network_monitor->getNetworkStats();
        json.addNumber("active_connections", static_cast<uint64_t>(network_stats.total_connections))
            .addNumber("recent_network_events", static_cast<uint64_t>(0));  // TODO: 需要实现最近网络事件统计
    }
    
    json.endObject();
    
    setJsonResponse(response, json.toString());
}

// 注意：根据需求10，已删除handleGetProcessFiles函数（文件操作序列）

void ApiServer::handleGetExclusions(const HttpRequest& request, HttpResponse& response) {
    (void)request; // 避免警告
    
    auto& exclusion_manager = ProcessExclusionManagerSingleton::getInstance();
    
    auto excluded_pids = exclusion_manager.getExcludedPids();
    auto excluded_names = exclusion_manager.getExcludedNames();
    auto excluded_paths = exclusion_manager.getExcludedPaths();
    auto excluded_patterns = exclusion_manager.getExcludedPatterns();
    
    JsonBuilder json;
    json.startObject()
        .addNumber("total_exclusions", static_cast<uint64_t>(
            excluded_pids.size() + excluded_names.size() + 
            excluded_paths.size() + excluded_patterns.size()))
        .startArray("excluded_pids");
    
    for (pid_t pid : excluded_pids) {
        json.addNumberValue(static_cast<int64_t>(pid));
    }
    
    json.endArray()
        .startArray("excluded_names");
    
    for (const auto& name : excluded_names) {
        json.addStringValue(name);
    }
    
    json.endArray()
        .startArray("excluded_paths");
    
    for (const auto& path : excluded_paths) {
        json.addStringValue(path);
    }
    
    json.endArray()
        .startArray("excluded_patterns");
    
    for (const auto& pattern : excluded_patterns) {
        json.addStringValue(pattern);
    }
    
    json.endArray().endObject();
    
    setJsonResponse(response, json.toString());
}

void ApiServer::handleAddExclusion(const HttpRequest& request, HttpResponse& response) {
    if (request.body.empty()) {
        setErrorResponse(response, HttpStatus::BAD_REQUEST, "Request body cannot be empty");
        return;
    }
    
    // 简单的JSON解析（需要type和value字段）
    std::string type, value;
    
    // 查找type字段
    size_t type_pos = request.body.find("\"type\"");
    if (type_pos != std::string::npos) {
        size_t colon_pos = request.body.find(":", type_pos);
        size_t start_quote = request.body.find("\"", colon_pos);
        size_t end_quote = request.body.find("\"", start_quote + 1);
        if (start_quote != std::string::npos && end_quote != std::string::npos) {
            type = request.body.substr(start_quote + 1, end_quote - start_quote - 1);
        }
    }
    
    // 查找value字段
    size_t value_pos = request.body.find("\"value\"");
    if (value_pos != std::string::npos) {
        size_t colon_pos = request.body.find(":", value_pos);
        size_t start_quote = request.body.find("\"", colon_pos);
        size_t end_quote = request.body.find("\"", start_quote + 1);
        if (start_quote != std::string::npos && end_quote != std::string::npos) {
            value = request.body.substr(start_quote + 1, end_quote - start_quote - 1);
        }
    }
    
    if (type.empty() || value.empty()) {
        setErrorResponse(response, HttpStatus::BAD_REQUEST, "Missing type or value field");
        return;
    }
    
    auto& exclusion_manager = ProcessExclusionManagerSingleton::getInstance();
    
    try {
        if (type == "pid") {
            pid_t pid = std::stoi(value);
            exclusion_manager.addExclusionByPid(pid);
        } else if (type == "name") {
            exclusion_manager.addExclusionByName(value);
        } else if (type == "path") {
            exclusion_manager.addExclusionByPath(value);
        } else if (type == "pattern") {
            exclusion_manager.addExclusionByPattern(value);
        } else {
            setErrorResponse(response, HttpStatus::BAD_REQUEST, "Invalid exclusion type");
            return;
        }
    } catch (const std::exception& e) {
        setErrorResponse(response, HttpStatus::BAD_REQUEST, "Failed to add exclusion rule: " + std::string(e.what()));
        return;
    }
    
    JsonBuilder json;
    json.startObject()
        .addString("status", "success")
        .addString("message", "Exclusion rule added successfully")
        .addString("type", type)
        .addString("value", value)
        .endObject();
    
    setJsonResponse(response, json.toString());
}

void ApiServer::handleRemoveExclusion(const HttpRequest& request, HttpResponse& response) {
    // 从查询参数获取type和value
    auto type_it = request.query_params.find("type");
    auto value_it = request.query_params.find("value");
    
    if (type_it == request.query_params.end() || value_it == request.query_params.end()) {
        setErrorResponse(response, HttpStatus::BAD_REQUEST, "Missing type or value parameter");
        return;
    }
    
    std::string type = type_it->second;
    std::string value = value_it->second;
    
    auto& exclusion_manager = ProcessExclusionManagerSingleton::getInstance();
    
    try {
        if (type == "pid") {
            pid_t pid = std::stoi(value);
            exclusion_manager.removeExclusionByPid(pid);
        } else if (type == "name") {
            exclusion_manager.removeExclusionByName(value);
        } else if (type == "path") {
            exclusion_manager.removeExclusionByPath(value);
        } else {
            setErrorResponse(response, HttpStatus::BAD_REQUEST, "Invalid exclusion type");
            return;
        }
    } catch (const std::exception& e) {
        setErrorResponse(response, HttpStatus::BAD_REQUEST, "Failed to remove exclusion rule: " + std::string(e.what()));
        return;
    }
    
    JsonBuilder json;
    json.startObject()
        .addString("status", "success")
        .addString("message", "Exclusion rule removed successfully")
        .addString("type", type)
        .addString("value", value)
        .endObject();
    
    setJsonResponse(response, json.toString());
}

void ApiServer::handleGetProtectedPaths(const HttpRequest& request, HttpResponse& response) {
    (void)request; // 避免警告

    auto& protected_path_manager = ProtectedPathManager::getInstance();
    auto protected_paths = protected_path_manager.getProtectedPaths();

    JsonBuilder json;
    json.startObject()
        .addNumber("count", static_cast<uint64_t>(protected_paths.size()))
        .startArray("protected_paths");

    for (const auto& path : protected_paths) {
        json.startObject()
            .addString("path", path)
            .addString("level", "write_protect") // 统一为写保护级别
            .addBool("recursive", true)
            .addBool("terminate_violator", protected_path_manager.shouldTerminateViolatingProcesses())
            .endObject();
    }

    json.endArray().endObject();

    setJsonResponse(response, json.toString());
}

void ApiServer::handleAddProtectedPath(const HttpRequest& request, HttpResponse& response) {
    if (request.body.empty()) {
        setErrorResponse(response, HttpStatus::BAD_REQUEST, "Request body cannot be empty");
        return;
    }
    
    // 简单的JSON解析
    std::string path;
    
    // 解析path字段
    size_t path_pos = request.body.find("\"path\"");
    if (path_pos != std::string::npos) {
        size_t colon_pos = request.body.find(":", path_pos);
        size_t start_quote = request.body.find("\"", colon_pos);
        size_t end_quote = request.body.find("\"", start_quote + 1);
        if (start_quote != std::string::npos && end_quote != std::string::npos) {
            path = request.body.substr(start_quote + 1, end_quote - start_quote - 1);
        }
    }
    
    if (path.empty()) {
        setErrorResponse(response, HttpStatus::BAD_REQUEST, "Missing path field");
        return;
    }
    
    auto& protected_path_manager = ProtectedPathManager::getInstance();
    protected_path_manager.addProtectedPath(path);
    
    // 调用FileMonitor添加fanotify标记
    if (main_monitor_) {
        auto* file_monitor = main_monitor_->getFileMonitor();
        if (file_monitor) {
            if (!file_monitor->addProtectedPathMark(path)) {
                LogManager::getInstance().warning("[ApiServer] 添加受保护路径fanotify标记失败: " + path);
            }
        }
    }
    
    JsonBuilder json;
    json.startObject()
        .addString("status", "success")
        .addString("message", "Protected path added successfully")
        .addString("path", path)
        .endObject();
    
    setJsonResponse(response, json.toString());
}

void ApiServer::handleRemoveProtectedPath(const HttpRequest& request, HttpResponse& response) {
    auto path_it = request.query_params.find("path");
    
    if (path_it == request.query_params.end()) {
        setErrorResponse(response, HttpStatus::BAD_REQUEST, "Missing path parameter");
        return;
    }
    
    std::string path = path_it->second;
    
    auto& protected_path_manager = ProtectedPathManager::getInstance();
    protected_path_manager.removeProtectedPath(path);
    
    // 调用FileMonitor移除fanotify标记
    if (main_monitor_) {
        auto* file_monitor = main_monitor_->getFileMonitor();
        if (file_monitor) {
            file_monitor->removeProtectedPathMark(path);
        }
    }
    
    JsonBuilder json;
    json.startObject()
        .addString("status", "success")
        .addString("message", "Protected path removed successfully")
        .addString("path", path)
        .endObject();
    
    setJsonResponse(response, json.toString());
}


// 重点进程管理API实现
void ApiServer::handleGetFocusProcesses(const HttpRequest& request, HttpResponse& response) {
    (void)request; // 避免警告
    
    auto process_monitor = main_monitor_->getProcessMonitor();
    if (!process_monitor) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Process monitor not initialized");
        return;
    }
    
    // 获取FocusProcessManager中的重点进程列表（这是权威数据源）
    auto& focus_manager = FocusProcessManager::getInstance();
    auto focus_pids = focus_manager.getFocusProcesses();
    auto focus_names = focus_manager.getFocusProcessNames();
    
    JsonBuilder json;
    json.startObject()
        .addNumber("count", static_cast<uint64_t>(focus_pids.size() + focus_names.size()))
        .startArray("focus_processes");
    
    // 添加按PID管理的重点进程
    for (pid_t pid : focus_pids) {
        auto process = process_monitor->getProcess(pid);
        json.startObject()
            .addNumber("pid", static_cast<int64_t>(pid))
            .addString("type", "pid");
        
        if (process) {
            json.addString("name", process->process_name)
                .addString("executable_path", process->executable_path)
                .addBool("is_focus_process", process->is_focus_process)
                .addBool("process_exists", true);
            
            if (process->focus_file_info) {
                auto* file_info = process->focus_file_info.get();
                std::lock_guard<std::mutex> lock(file_info->FocusProcessFileInfo_data_mutex_);
                
                json.addNumber("read_entropy_count", static_cast<uint64_t>(file_info->read_entropy_count))
                    .addNumber("write_entropy_count", static_cast<uint64_t>(file_info->write_entropy_count))
                    .addNumber("delete_count", static_cast<uint64_t>(file_info->delete_count))
                    .addNumber("rename_count", static_cast<uint64_t>(file_info->rename_count))
                    .addNumber("path_history_count", static_cast<uint64_t>(file_info->path_history.size()));
            }
        } else {
            json.addString("name", "unknown")
                .addString("executable_path", "")
                .addBool("is_focus_process", false)
                .addBool("process_exists", false);
        }
        
        json.endObject();
    }
    
    // 添加按进程名管理的重点进程
    for (const std::string& name : focus_names) {
        json.startObject()
            .addString("type", "name")
            .addString("name", name)
            .addNumber("pid", static_cast<int64_t>(-1))  // 进程名类型没有固定PID
            .addString("executable_path", "")
            .addBool("is_focus_process", true)
            .addBool("process_exists", true)  // 进程名类型总是认为存在
            .endObject();
    }
    
    json.endArray().endObject();
    
    setJsonResponse(response, json.toString());
}

void ApiServer::handleAddFocusProcess(const HttpRequest& request, HttpResponse& response) {
    if (request.body.empty()) {
        setErrorResponse(response, HttpStatus::BAD_REQUEST, "Request body cannot be empty");
        return;
    }
    
    // 简单的JSON解析（实际项目中应使用专业的JSON库）
    std::string type, value;
    size_t type_pos = request.body.find("\"type\":");
    size_t value_pos = request.body.find("\"value\":");
    
    if (type_pos != std::string::npos && value_pos != std::string::npos) {
        // 提取type值
        size_t type_start = request.body.find("\"", type_pos + 7);
        size_t type_end = request.body.find("\"", type_start + 1);
        if (type_start != std::string::npos && type_end != std::string::npos) {
            type = request.body.substr(type_start + 1, type_end - type_start - 1);
        }
        
        // 提取value值
        size_t value_start = request.body.find("\"", value_pos + 8);
        size_t value_end = request.body.find("\"", value_start + 1);
        if (value_start != std::string::npos && value_end != std::string::npos) {
            value = request.body.substr(value_start + 1, value_end - value_start - 1);
        }
    }
    
    if (type.empty() || value.empty()) {
        setErrorResponse(response, HttpStatus::BAD_REQUEST, "Invalid request format");
        return;
    }
    
        auto process_monitor = main_monitor_->getProcessMonitor();
    if (!process_monitor) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Process monitor not initialized");
        return;
    }

    // 需要同时更新ProcessMonitor和FocusProcessManager以保持一致性
    auto& focus_manager = FocusProcessManager::getInstance();
    
    if (type == "pid") {
        try {
            pid_t pid = std::stoi(value);
            process_monitor->setFocusProcess(pid, true);
            focus_manager.addFocusProcess(pid);  // 同步更新FocusProcessManager
        } catch (const std::exception& e) {
            setErrorResponse(response, HttpStatus::BAD_REQUEST, "Invalid PID");
            return;
        }
    } else if (type == "name") {
        process_monitor->setFocusProcess(value, true);
        focus_manager.addFocusProcess(value);  // 同步更新FocusProcessManager
    } else {
        setErrorResponse(response, HttpStatus::BAD_REQUEST, "Invalid type, must be 'pid' or 'name'");
        return;
    }
    
    JsonBuilder json;
    json.startObject()
        .addString("message", "Focus process added successfully")
        .addString("type", type)
        .addString("value", value)
        .endObject();
    
    setJsonResponse(response, json.toString());
}

void ApiServer::handleRemoveFocusProcess(const HttpRequest& request, HttpResponse& response) {
    auto params = parseQueryParameters(request);
    std::string type = params["type"];
    std::string value = params["value"];
    
    if (type.empty() || value.empty()) {
        setErrorResponse(response, HttpStatus::BAD_REQUEST, "Missing required parameters: type and value");
        return;
    }
    
    auto process_monitor = main_monitor_->getProcessMonitor();
    if (!process_monitor) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Process monitor not initialized");
        return;
    }
    
    // 需要同时更新ProcessMonitor和FocusProcessManager以保持一致性
    auto& focus_manager = FocusProcessManager::getInstance();
    
    if (type == "pid") {
        try {
            pid_t pid = std::stoi(value);
            process_monitor->setFocusProcess(pid, false);
            focus_manager.removeFocusProcess(pid);  // 同步更新FocusProcessManager
        } catch (const std::exception& e) {
            setErrorResponse(response, HttpStatus::BAD_REQUEST, "Invalid PID");
            return;
        }
    } else if (type == "name") {
        process_monitor->setFocusProcess(value, false);
        focus_manager.removeFocusProcess(value);  // 同步更新FocusProcessManager
    } else {
        setErrorResponse(response, HttpStatus::BAD_REQUEST, "Invalid type, must be 'pid' or 'name'");
        return;
    }
    
    JsonBuilder json;
    json.startObject()
        .addString("message", "Focus process removed successfully")
        .addString("type", type)
        .addString("value", value)
        .endObject();
    
    setJsonResponse(response, json.toString());
}

void ApiServer::handleGetFocusProcessFileInfo(const HttpRequest& request, HttpResponse& response) {
    auto params = parseQueryParameters(request);
    std::string pid_str = params["pid"];
    
    if (pid_str.empty()) {
        setErrorResponse(response, HttpStatus::BAD_REQUEST, "Missing required parameter: pid");
        return;
    }
    
    pid_t pid;
    try {
        pid = std::stoi(pid_str);
    } catch (const std::exception& e) {
        setErrorResponse(response, HttpStatus::BAD_REQUEST, "Invalid PID");
        return;
    }
    
    // 首先检查是否为重点进程（优先于进程是否存在的检查）
    auto& focus_manager = FocusProcessManager::getInstance();
    if (!focus_manager.isFocusProcess(pid)) {
        setErrorResponse(response, HttpStatus::BAD_REQUEST, "Specified process is not a focus process");
        return;
    }
    
        auto process_monitor = main_monitor_->getProcessMonitor();
    if (!process_monitor) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Process monitor not initialized");
        return;
    }

    auto process = process_monitor->getProcess(pid);
    if (!process) {
        // 进程不存在但是重点进程管理器中有记录，可能是进程已经结束
        // 返回基本信息而不是错误
        JsonBuilder json;
        json.startObject()
            .addNumber("pid", static_cast<int64_t>(pid))
            .addString("process_name", "process_ended")
            .addString("status", "Process has ended, unable to get detailed file information")
            .addNumber("read_entropy_count", static_cast<uint64_t>(0))
            .addNumber("write_entropy_count", static_cast<uint64_t>(0))
            .addNumber("delete_count", static_cast<uint64_t>(0))
            .addNumber("rename_count", static_cast<uint64_t>(0))
            .addNumber("total_path_history_count", static_cast<uint64_t>(0))
            .addNumber("average_read_entropy", 0.0)
            .addNumber("average_write_entropy", 0.0)
            .addNumber("timestamp", Utils::getCurrentTimestamp())
            .endObject();
        
        setJsonResponse(response, json.toString());
        return;
    }
    
    if (!process->is_focus_process || !process->focus_file_info) {
        setErrorResponse(response, HttpStatus::BAD_REQUEST, "Specified process is not a focus process or has no file information");
        return;
    }
    
    auto* file_info = process->focus_file_info.get();
    
    // 使用try_lock避免阻塞，如果无法获取锁则快速返回
    std::unique_lock<std::mutex> lock(file_info->FocusProcessFileInfo_data_mutex_, std::try_to_lock);
    if (!lock.owns_lock()) {
        setErrorResponse(response, HttpStatus::SERVICE_UNAVAILABLE, "Focus process file information is being updated, please try again later");
        return;
    }
    
    // 快速复制关键数据，减少锁持有时间
    double total_read_entropy = file_info->total_read_entropy;
    double total_write_entropy = file_info->total_write_entropy;
    size_t read_entropy_count = file_info->read_entropy_count;
    size_t write_entropy_count = file_info->write_entropy_count;
    size_t delete_count = file_info->delete_count;
    size_t rename_count = file_info->rename_count;
    size_t total_path_count = file_info->path_history.size();
    
    // 复制少量扩展名和路径历史
    std::vector<std::string> extensions_copy;
    std::vector<std::string> recent_paths;
    
    // 只复制最近的扩展名（最多10个）
    if (file_info->rename_extensions.size() <= 10) {
        extensions_copy = file_info->rename_extensions;
    } else {
        auto it = file_info->rename_extensions.end();
        std::advance(it, -10);
        extensions_copy.assign(it, file_info->rename_extensions.end());
    }
    
    // 只复制最近的路径历史（最多20个）
    size_t max_paths = 20;
    if (file_info->path_history.size() <= max_paths) {
        recent_paths = file_info->path_history;
    } else {
        size_t start_idx = file_info->path_history.size() - max_paths;
        recent_paths.assign(file_info->path_history.begin() + start_idx, file_info->path_history.end());
    }
    
    // 释放锁，在锁外进行JSON构建
    lock.unlock();
    
    JsonBuilder json;
    json.startObject()
        .addNumber("pid", static_cast<int64_t>(pid))
        .addString("process_name", process->process_name)
        .addString("executable_path", process->executable_path)
        .addNumber("total_read_entropy", total_read_entropy)
        .addNumber("total_write_entropy", total_write_entropy)
        .addNumber("read_entropy_count", static_cast<uint64_t>(read_entropy_count))
        .addNumber("write_entropy_count", static_cast<uint64_t>(write_entropy_count))
        .addNumber("average_read_entropy", read_entropy_count > 0 ? total_read_entropy / read_entropy_count : 0.0)
        .addNumber("average_write_entropy", write_entropy_count > 0 ? total_write_entropy / write_entropy_count : 0.0)
        .addNumber("delete_count", static_cast<uint64_t>(delete_count))
        .addNumber("rename_count", static_cast<uint64_t>(rename_count))
        .startArray("rename_extensions");
    
    for (const auto& ext : extensions_copy) {
        json.addStringValue(ext);
    }
    
    json.endArray()
        .startArray("recent_path_history");
    
    for (const auto& path : recent_paths) {
        json.addStringValue(path);
    }
    
    json.endArray()
        .addNumber("total_path_history_count", static_cast<uint64_t>(total_path_count))
        .addString("note", "Showing the most recent 20 paths and 10 extensions")
        .endObject();
    
    setJsonResponse(response, json.toString());
}

// API辅助函数实现 - 从api_helpers.cpp合并
void ApiServer::setJsonResponse(HttpResponse& response, const std::string& json) {
    response.status = HttpStatus::OK;
    response.content_type = "application/json";
    response.body = json;
    setCorsHeaders(response);
}

void ApiServer::setErrorResponse(HttpResponse& response, HttpStatus status, const std::string& error_message) {
    JsonBuilder json;
    json.startObject()
        .addString("error", error_message)
        .addNumber("timestamp", static_cast<int64_t>(std::time(nullptr)))
        .endObject();
    
    response.status = status;
    response.content_type = "application/json";
    response.body = json.toString();
    setCorsHeaders(response);
}

void ApiServer::setCorsHeaders(HttpResponse& response) {
    response.headers["Access-Control-Allow-Origin"] = "*";
    response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS";
    response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization";
}

std::map<std::string, std::string> ApiServer::parseQueryParameters(const HttpRequest& request) {
    return request.query_params;
}

// 新增：重点进程详细熵值统计API实现
void ApiServer::handleGetFocusProcessEntropyStats(const HttpRequest& request, HttpResponse& response) {
    if (!main_monitor_) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Monitor not initialized");
        return;
    }
    
    auto* process_monitor = main_monitor_->getProcessMonitor();
    if (!process_monitor) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "Process monitor not available");
        return;
    }
    
    auto file_monitor = main_monitor_->getFileMonitor();
    if (!file_monitor) {
        setErrorResponse(response, HttpStatus::INTERNAL_SERVER_ERROR, "File monitor not available");
        return;
    }
    
    // 检查是否指定了特定进程ID
    std::string pid_str;
    auto pid_it = request.query_params.find("pid");
    if (pid_it != request.query_params.end()) {
        pid_str = pid_it->second;
    }
    
    // 获取文件监控统计信息
    auto file_stats = file_monitor->getEventStats();
    
    JsonBuilder json;
    json.startObject()
        .addNumber("timestamp", Utils::getCurrentTimestamp());
    
    if (!pid_str.empty()) {
        // 获取特定进程的详细统计
        try {
            pid_t pid = std::stoi(pid_str);
            auto process_info = process_monitor->getProcess(pid);
            
            if (!process_info || !process_info->is_focus_process) {
                setErrorResponse(response, HttpStatus::NOT_FOUND, "Process not found or not a focus process");
                return;
            }
            
            if (!process_info->focus_file_info) {
                setErrorResponse(response, HttpStatus::NOT_FOUND, "No file entropy information available for this process");
                return;
            }
            
            // 修复死锁：避免嵌套锁定，分别获取数据
            // 1. 先获取ProcessMonitor级别的数据（内部会处理锁定）
            auto file_entropy_info = process_monitor->getFocusProcessFileEntropyInfo(pid);
            double total_original = process_monitor->getFocusProcessTotalOriginalEntropy(pid);
            double total_final = process_monitor->getFocusProcessTotalFinalEntropy(pid);
            double total_change = process_monitor->getFocusProcessTotalEntropyChange(pid);

            // 2. 然后获取文件级别的统计信息（避免在已有锁的情况下再次锁定）
            auto* file_info = process_info->focus_file_info.get();

            // 分别获取需要的数据，避免长时间持有锁
            double total_read_entropy, total_write_entropy, average_read_entropy, average_write_entropy;
            size_t read_entropy_count, write_entropy_count, delete_count, rename_count, path_history_size;
            std::vector<std::string> recent_paths, rename_extensions;

            {
                std::lock_guard<std::mutex> lock(file_info->FocusProcessFileInfo_data_mutex_);
                total_read_entropy = file_info->total_read_entropy;
                total_write_entropy = file_info->total_write_entropy;
                read_entropy_count = file_info->read_entropy_count;
                write_entropy_count = file_info->write_entropy_count;

                // 修复死锁：直接计算平均值，避免调用会重复加锁的方法
                average_read_entropy = (read_entropy_count == 0) ? 0.0 : (total_read_entropy / read_entropy_count);
                average_write_entropy = (write_entropy_count == 0) ? 0.0 : (total_write_entropy / write_entropy_count);

                delete_count = file_info->delete_count;
                rename_count = file_info->rename_count;
                path_history_size = file_info->path_history.size();

                // 复制最近的路径（限制20个）
                size_t path_limit = std::min(static_cast<size_t>(20), file_info->path_history.size());
                if (path_limit > 0) {
                    recent_paths.assign(
                        file_info->path_history.end() - path_limit,
                        file_info->path_history.end()
                    );
                }

                // 复制重命名扩展名
                rename_extensions = file_info->rename_extensions;
            }
            
            json.addNumber("pid", static_cast<int64_t>(pid))
                .addString("process_name", process_info->process_name)
                .addString("executable_path", process_info->executable_path);
            
            // 总体熵值统计
            json.addObjectKey("entropy_summary")
                .addNumber("total_original_entropy", total_original)
                .addNumber("total_final_entropy", total_final)
                .addNumber("total_entropy_change", total_change)
                .addNumber("file_count", static_cast<uint64_t>(file_entropy_info.size()));
            
            // 增加熵值变化百分比
            if (total_original > 0.0) {
                double change_percentage = (total_change / total_original) * 100.0;
                json.addNumber("entropy_change_percentage", change_percentage);
            } else {
                json.addNumber("entropy_change_percentage", 0.0);
            }
            
            json.endObject();
            
            // 兼容性统计（老格式）
            json.addObjectKey("legacy_stats")
                .addNumber("total_read_entropy", total_read_entropy)
                .addNumber("total_write_entropy", total_write_entropy)
                .addNumber("read_entropy_count", static_cast<uint64_t>(read_entropy_count))
                .addNumber("write_entropy_count", static_cast<uint64_t>(write_entropy_count))
                .addNumber("average_read_entropy", average_read_entropy)
                .addNumber("average_write_entropy", average_write_entropy)
                .addNumber("delete_count", static_cast<uint64_t>(delete_count))
                .addNumber("rename_count", static_cast<uint64_t>(rename_count))
                .endObject();
            
            // 操作历史统计
            json.addObjectKey("operation_history")
                .addNumber("total_path_history_count", static_cast<uint64_t>(path_history_size));
            
            json.startArray("recent_paths");
            for (const auto& path : recent_paths) {
                json.addStringValue(path);
            }
            json.endArray();
            
            json.startArray("rename_extensions");
            size_t ext_limit = std::min(static_cast<size_t>(10), rename_extensions.size());
            for (size_t i = 0; i < ext_limit; ++i) {
                json.addStringValue(rename_extensions[i]);
            }
            json.endArray();

            json.endObject(); // operation_history
            json.endObject(); // 结束主JSON对象

        } catch (const std::exception& e) {
            setErrorResponse(response, HttpStatus::BAD_REQUEST, "Invalid PID parameter");
            return;
        }
    } else {
        // 获取所有重点进程的统计汇总
        auto focus_processes = process_monitor->getFocusProcesses();
        
        json.addNumber("focus_process_count", static_cast<uint64_t>(focus_processes.size()));
        
        // 全局文件监控统计
        json.addKey("global_file_stats")
            .startObject()
            .addNumber("total_events", static_cast<uint64_t>(file_stats.total_events))
            .addNumber("focus_process_events", static_cast<uint64_t>(file_stats.focus_process_events))
            .addNumber("focus_process_files", static_cast<uint64_t>(file_stats.focus_process_files))
            .addNumber("entropy_calculations", static_cast<uint64_t>(file_stats.entropy_calculations))
            //.addNumber("open_entropy_calculations", static_cast<uint64_t>(file_stats.open_entropy_calculations))
            //.addNumber("close_entropy_calculations", static_cast<uint64_t>(file_stats.close_entropy_calculations))
            .addNumber("write_entropy_calculations", static_cast<uint64_t>(file_stats.write_entropy_calculations))
            .addNumber("read_entropy_calculations", static_cast<uint64_t>(file_stats.read_entropy_calculations))
            .endObject();
        
        // 各进程统计
        json.startArray("processes");
        for (pid_t pid : focus_processes) {
            auto process_info = process_monitor->getProcess(pid);
            if (process_info && process_info->is_focus_process && process_info->focus_file_info) {
                double total_original = process_monitor->getFocusProcessTotalOriginalEntropy(pid);
                double total_final = process_monitor->getFocusProcessTotalFinalEntropy(pid);
                double total_change = process_monitor->getFocusProcessTotalEntropyChange(pid);
                auto file_entropy_info = process_monitor->getFocusProcessFileEntropyInfo(pid);
                
                json.startObject()
                    .addNumber("pid", static_cast<int64_t>(pid))
                    .addString("process_name", process_info->process_name)
                    .addNumber("total_original_entropy", total_original)
                    .addNumber("total_final_entropy", total_final)
                    .addNumber("total_entropy_change", total_change)
                    .addNumber("file_count", static_cast<uint64_t>(file_entropy_info.size()));
                
                // 增加熵值变化百分比
                if (total_original > 0.0) {
                    double change_percentage = (total_change / total_original) * 100.0;
                    json.addNumber("entropy_change_percentage", change_percentage);
                } else {
                    json.addNumber("entropy_change_percentage", 0.0);
                }
                
                json.addNumber("legacy_read_entropy", process_info->focus_file_info->total_read_entropy)
                    .addNumber("legacy_write_entropy", process_info->focus_file_info->total_write_entropy)
                    .addNumber("legacy_read_count", static_cast<uint64_t>(process_info->focus_file_info->read_entropy_count))
                    .addNumber("legacy_write_count", static_cast<uint64_t>(process_info->focus_file_info->write_entropy_count))
                    .endObject();
            }
        }
        json.endArray();
    }
    
    json.endObject();
    setJsonResponse(response, json.toString());
}








