#include "main_monitor.h"
#include "api_server.h"
#include <iostream>
#include <csignal>
#include <cstring>
#include <unistd.h>

// 函数声明
static void signalHandler(int signal);
static void setupSignalHandlers();
static void showVersion();
static void showHelp(const char* program_name);
static bool checkPermissions();
static bool checkSystemCompatibility();

// 全局变量
MainMonitor* g_monitor = nullptr;
ApiServer* g_api_server = nullptr;
std::atomic<bool> g_shutdown_requested{false};
std::atomic<bool> g_reload_requested{false};

// 信号处理函数 - 简化版，避免死锁
static void signalHandler(int signal) {
    // 预先声明变量，避免跨越case标签
    const char* msg;
    const char* reload_msg;
    ssize_t result; // 用于接收write的返回值，避免警告
    
    switch (signal) {
        case SIGINT:
        case SIGTERM:
            // 只设置标志，不在信号处理器中执行复杂操作
            g_shutdown_requested = true;
            // 使用信号安全的函数输出简单消息
            msg = "\n收到终止信号，正在关闭程序...\n";
            result = write(STDOUT_FILENO, msg, strlen(msg));
            (void)result; // 避免未使用变量警告
            break;
        case SIGHUP:
            // 只设置重新加载标志
            g_reload_requested = true;
            reload_msg = "\n收到重新加载信号...\n";
            result = write(STDOUT_FILENO, reload_msg, strlen(reload_msg));
            (void)result; // 避免未使用变量警告
            break;
        default:
            break;
    }
}

// 设置信号处理
static void setupSignalHandlers() {
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
    signal(SIGHUP, signalHandler);
    signal(SIGPIPE, SIG_IGN);  // 忽略SIGPIPE
}

// 显示版本信息
static void showVersion() {
    std::cout << "PSFSMON-L v1.0.0" << std::endl;
    std::cout << "Linux安全监控程序" << std::endl;
    std::cout << "支持的内核版本: >= 4.19.0" << std::endl;
    std::cout << "编译时间: " << __DATE__ << " " << __TIME__ << std::endl;
    std::cout << std::endl;
}

// 显示帮助信息
static void showHelp(const char* program_name) {
    std::cout << "用法: " << program_name << " [选项]" << std::endl;
    std::cout << std::endl;
    std::cout << "选项:" << std::endl;
    std::cout << "  -h, --help              显示此帮助信息" << std::endl;
    std::cout << "  -v, --version           显示版本信息" << std::endl;
    std::cout << "  -d, --daemon            以守护进程模式运行" << std::endl;
    std::cout << "  -c, --config FILE       指定配置文件路径" << std::endl;
    std::cout << "  -p, --port PORT         指定API服务器端口" << std::endl;
    std::cout << "  -l, --log-file FILE     指定日志文件路径" << std::endl;
    std::cout << "  --no-file-monitor       禁用文件监控" << std::endl;
    std::cout << "  --no-network-monitor    禁用网络监控" << std::endl;
    std::cout << "  --no-process-monitor    禁用进程监控" << std::endl;
    std::cout << "  --debug                 启用调试模式" << std::endl;
    std::cout << "  --verbose               详细输出" << std::endl;
    std::cout << std::endl;
    std::cout << "监控策略:" << std::endl;
    std::cout << "  系统仅使用Fanotify机制进行文件监控：" << std::endl;
    std::cout << "  - 内核 5.4.18+：全功能Fanotify支持（CREATE/DELETE/MOVE等）" << std::endl;
    std::cout << "  - 内核 5.4.x：  基础Fanotify支持（OPEN/CLOSE/READ/WRITE）" << std::endl;
    std::cout << "  - 重点进程：    文件熵值计算和详细操作记录" << std::endl;
    std::cout << "  - 特殊路径：    写操作保护和进程中止" << std::endl;
    std::cout << std::endl;
    std::cout << "示例:" << std::endl;
    std::cout << "  " << program_name << "                    # 直接运行" << std::endl;
    std::cout << "  " << program_name << " --daemon           # 守护进程模式" << std::endl;
    std::cout << "  " << program_name << " --port 9090        # 指定API端口" << std::endl;
    std::cout << "  " << program_name << " --config /etc/psfsmon/custom.conf" << std::endl;
    std::cout << std::endl;
    std::cout << "API接口:" << std::endl;
    std::cout << "  基础API:" << std::endl;
    std::cout << "    GET /api/health                        健康检查" << std::endl;
    std::cout << "    GET /api/stats                         获取统计信息" << std::endl;
    std::cout << "    GET /api/config                        获取配置信息" << std::endl;
    std::cout << "    PUT /api/config                        更新配置信息" << std::endl;
    std::cout << "    GET /api/monitor/status                获取监控状态" << std::endl;
    std::cout << "    GET /api/realtime                      获取实时数据" << std::endl;
    std::cout << "" << std::endl;
    std::cout << "  进程相关API:" << std::endl;
    std::cout << "    GET /api/process-tree                  获取进程树" << std::endl;
    std::cout << "    GET /api/processes                     获取所有进程基本信息" << std::endl;
    std::cout << "    GET /api/processes-list                获取所有进程详细信息" << std::endl;
    std::cout << "    GET /api/process?pid={pid}             获取进程信息" << std::endl;
    // 注意：根据需求10，已删除文件操作序列API
    // std::cout << "    GET /api/process/files?pid={pid}       获取重点进程文件操作详情" << std::endl;
    std::cout << "" << std::endl;
    std::cout << "  网络相关API:" << std::endl;
    std::cout << "    GET /api/network                       获取网络连接" << std::endl;
    std::cout << "    GET /api/network/stats                 获取网络统计" << std::endl;
    std::cout << "    GET /api/network/listening             获取监听端口" << std::endl;
    std::cout << "" << std::endl;
    std::cout << "  文件监控API:" << std::endl;
    std::cout << "    GET /api/file/events?limit={n}         获取文件事件" << std::endl;
    std::cout << "    GET /api/file/stats                    获取文件统计" << std::endl;
    std::cout << "" << std::endl;
    std::cout << "  管理API:" << std::endl;
    std::cout << "    GET /api/exclusions                    获取进程排除规则" << std::endl;
    std::cout << "    POST /api/exclusions                   添加进程排除规则" << std::endl;
    std::cout << "    DELETE /api/exclusions                 删除进程排除规则" << std::endl;
    std::cout << "    GET /api/focus-processes               获取重点进程列表" << std::endl;
    std::cout << "    POST /api/focus-processes              添加重点进程" << std::endl;
    std::cout << "    DELETE /api/focus-processes            删除重点进程" << std::endl;
    std::cout << "    GET /api/focus-processes/entropy       获取重点进程熵值信息" << std::endl;
    std::cout << "    GET /api/focus-processes/entropy-stats 获取重点进程详细熵值统计" << std::endl;
    std::cout << "    GET /api/focus-process/file-info       获取重点进程文件详细信息" << std::endl;
    std::cout << "    GET /api/protected-paths               获取受保护路径列表" << std::endl;
    std::cout << "    POST /api/protected-paths              添加受保护路径" << std::endl;
    std::cout << "    DELETE /api/protected-paths            删除受保护路径" << std::endl;
    
    std::cout << std::endl;
}

// 检查权限
static bool checkPermissions() {
    if (getuid() != 0) {
        std::cerr << "错误: 此程序需要root权限才能运行" << std::endl;
        std::cerr << "请使用sudo运行此程序" << std::endl;
        return false;
    }
    return true;
}

// 检查系统兼容性
static bool checkSystemCompatibility() {
    // 检查内核版本
    std::string kernel_version = Utils::getKernelVersion();
    std::cout << "当前内核版本: " << kernel_version << std::endl;
    
    // 检查必要的系统文件
    if (!Utils::fileExists("/proc/version")) {
        std::cerr << "错误: /proc 文件系统不可用" << std::endl;
        return false;
    }
    
    if (!Utils::fileExists("/sys/kernel")) {
        std::cerr << "错误: /sys 文件系统不可用" << std::endl;
        return false;
    }
    
    return true;
}

// 主函数
int main(int argc, char* argv[]) {
    
    // 处理帮助和版本选项（在解析前处理）
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "--help") == 0 || strcmp(argv[i], "-h") == 0) {
            showHelp(argv[0]);
            return 0;
        }
        if (strcmp(argv[i], "--version") == 0 || strcmp(argv[i], "-v") == 0) {
            showVersion();
            return 0;
        }
    }

	std::cout << "启动 PSFSMON-L Linux安全监控程序..." << std::endl;
    
    // 检查权限
    if (!checkPermissions()) {
        return 1;
    }
    
    // 检查系统兼容性
    if (!checkSystemCompatibility()) {
        return 1;
    }
    
    // 初始化配置管理器
    MonitorConfig config;
    ConfigManager& config_manager = ConfigManager::getInstance();
    
    // 解析命令行参数（获取配置文件路径等）
    if (!config_manager.parseCommandLine(argc, argv, config)) {
        return 1;
    }
    
    // 加载配置文件（命令行参数优先级更高）
    std::string config_file_to_load = config.config_file;
    
    // 首先尝试加载指定的配置文件
    if (!config_manager.loadConfig(config_file_to_load, config)) {
        std::cerr << "警告: 加载配置文件失败，使用默认配置" << std::endl;
    }
    
    // 重新解析命令行参数，只更新命令行指定的选项，不重置其他配置
    if (!config_manager.parseCommandLineOverlay(argc, argv, config)) {
        return 1;
    }
    
    // 显示使用的配置信息
    // 在daemon模式下，配置信息只记录到日志，不输出到控制台
    if ((config.verbose_mode || config.debug_mode) && !config.daemon_mode) {
        std::cout << "配置信息:" << std::endl;
        std::cout << "  配置文件: " << config.config_file << std::endl;
        std::cout << "  日志文件: " << config.log_file << std::endl;
        std::cout << "  API端口: " << config.api_port << std::endl;
        std::cout << "  守护进程模式: " << (config.daemon_mode ? "是" : "否") << std::endl;
        std::cout << "  调试模式: " << (config.debug_mode ? "是" : "否") << std::endl;
        std::cout << "  详细输出: " << (config.verbose_mode ? "是" : "否") << std::endl;
        std::cout << "    文件监控: " << (config.enable_file_monitoring ? "启用" : "禁用") << std::endl;
        std::cout << "    网络监控: " << (config.enable_network_monitoring ? "启用" : "禁用") << std::endl;
        std::cout << "    进程监控: " << (config.enable_process_monitoring ? "启用" : "禁用") << std::endl;
        std::cout << "    重点进程监控: " << (config.enable_focus_process_monitoring ? "启用" : "禁用") << std::endl;
        std::cout << "    受保护路径监控: " << (config.enable_protected_path_monitoring ? "启用" : "禁用") << std::endl;
        std::cout << "    终止违规进程: " << (config.terminate_violating_processes ? "是" : "否") << std::endl;
        std::cout << "    监控策略: 根据内核版本自动选择" << std::endl;
        std::cout << std::endl;
    }
    
    // 设置信号处理
    setupSignalHandlers();
    
    // 初始化日志系统
    LogManager& log_manager = LogManager::getInstance();
    
    // 设置日志级别
    LogManager::LogLevel file_log_level = LogManager::LOG_INFO;
    LogManager::LogLevel console_log_level = LogManager::LOG_INFO;
    
    if (config.log_level == "DEBUG") {
        file_log_level = LogManager::LOG_DEBUG;
        console_log_level = config.verbose_mode ? LogManager::LOG_DEBUG : LogManager::LOG_INFO;
    } else if (config.log_level == "INFO") {
        file_log_level = LogManager::LOG_INFO;
        console_log_level = LogManager::LOG_INFO;
    } else if (config.log_level == "WARNING") {
        file_log_level = LogManager::LOG_WARNING;
        console_log_level = LogManager::LOG_WARNING;
    } else if (config.log_level == "ERROR") {
        file_log_level = LogManager::LOG_ERROR;
        console_log_level = LogManager::LOG_ERROR;
    } else if (config.log_level == "FATAL") {
        file_log_level = LogManager::LOG_FATAL;
        console_log_level = LogManager::LOG_FATAL;
    }
    
    // 在verbose或debug模式下，降低控制台日志级别阈值
    if (config.verbose_mode && console_log_level > LogManager::LOG_INFO) {
        console_log_level = LogManager::LOG_INFO;
    }
    if (config.debug_mode && console_log_level > LogManager::LOG_DEBUG) {
        console_log_level = LogManager::LOG_DEBUG;
    }
    
    if (!log_manager.initialize(config.log_file, file_log_level, console_log_level)) {
        std::cerr << "错误: 无法初始化日志系统" << std::endl;
        return 1;
    }
    
    // 设置daemon模式（影响策略信息是否输出到控制台）
    log_manager.setDaemonMode(config.daemon_mode);
    
    // 在daemon模式下不输出到控制台
    if (!config.daemon_mode) {
        std::cout << "日志文件: " << config.log_file << std::endl;
        std::cout << "文件日志级别: " << config.log_level << ", 控制台日志级别: " << 
            (console_log_level == LogManager::LOG_DEBUG ? "DEBUG" :
             console_log_level == LogManager::LOG_INFO ? "INFO" :
             console_log_level == LogManager::LOG_WARNING ? "WARNING" :
             console_log_level == LogManager::LOG_ERROR ? "ERROR" : "FATAL") << std::endl;
    }
    
    log_manager.systemInfo("PSFSMON-L 正在启动...");
    
    // 显示内核兼容性信息
    std::string kernel_version = Utils::getKernelVersion();
    log_manager.info("检测到内核版本: " + kernel_version);
    log_manager.info("使用双Fanotify Group架构 - 需求13核心实现");
    
    try {
        // 创建主监控器
        g_monitor = new MainMonitor();
        if (!g_monitor->initialize(config)) {
            std::cerr << "错误: 无法初始化监控器" << std::endl;
            delete g_monitor;
            return 1;
        }
        
        // 启动监控器
        if (!g_monitor->start()) {
            std::cerr << "错误: 无法启动监控器" << std::endl;
            delete g_monitor;
            return 1;
        }
        
        std::cout << "监控器启动成功" << std::endl;
        log_manager.info("监控器启动成功");
        
        // 创建API服务器
        g_api_server = new ApiServer();
        if (!g_api_server->initialize(static_cast<uint16_t>(config.api_port), g_monitor)) {
            std::cerr << "错误: 无法初始化API服务器" << std::endl;
        } else {
            if (!g_api_server->start()) {
                std::cerr << "错误: 无法启动API服务器" << std::endl;
            } else {
                std::cout << "API服务器启动成功，端口: " << config.api_port << std::endl;
                log_manager.info("API服务器启动成功，端口: " + std::to_string(config.api_port));
            }
        }
        
        // 守护进程模式需要在输出信息前处理
        if (config.daemon_mode) {
            if (g_monitor->daemonize()) {
                log_manager.info("已切换到守护进程模式");
                log_manager.info("PSFSMON-L 启动完成，正在监控系统...");
            } else {
                std::cerr << "错误: 无法切换到守护进程模式" << std::endl;
                return 1;
            }
        } else {
            std::cout << "PSFSMON-L 启动完成，正在监控系统..." << std::endl;
            std::cout << "按 Ctrl+C 停止程序" << std::endl;
        }
        
        // 主循环 - 改进版本，正确处理信号
        while (!g_shutdown_requested) {
            // 检查重新加载请求
            if (g_reload_requested.exchange(false)) {
                std::cout << "正在重新加载配置..." << std::endl;
                if (g_monitor) {
                    ConfigManager& reload_config_manager = ConfigManager::getInstance();
                    MonitorConfig new_config = g_monitor->getConfig();
                    
                    if (reload_config_manager.loadConfig(new_config.config_file, new_config)) {
                        if (g_monitor->reloadConfig(new_config)) {
                            std::cout << "配置重新加载成功" << std::endl;
                            log_manager.systemInfo("配置重新加载成功");
                        } else {
                            std::cout << "配置重新加载失败" << std::endl;
                            log_manager.systemError("配置重新加载失败");
                        }
                    } else {
                        std::cout << "无法读取配置文件" << std::endl;
                        log_manager.systemError("无法读取配置文件");
                    }
                }
            }
            
            // 短暂休眠，避免CPU占用过高
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            
            // 检查监控器状态
            if (g_monitor && g_monitor->getStatus() == MonitorStatus::ERROR) {
                std::cerr << "监控器出现错误，正在退出..." << std::endl;
                log_manager.error("监控器出现错误，正在退出");
                break;
            }
        }
        
    } catch (const std::exception& e) {
        std::cerr << "异常: " << e.what() << std::endl;
        log_manager.error("程序异常: " + std::string(e.what()));
    }
    
    // 清理资源 - 改进版本，添加超时保护
    std::cout << "正在停止服务..." << std::endl;
    log_manager.info("正在停止服务...");
    
    // 先停止API服务器，避免新的请求
    if (g_api_server) {
        log_manager.info("正在停止API服务器...");
        auto start_time = std::chrono::steady_clock::now();
        g_api_server->stop();
        auto elapsed = std::chrono::steady_clock::now() - start_time;
        log_manager.info("API服务器停止耗时: " + std::to_string(
            std::chrono::duration_cast<std::chrono::milliseconds>(elapsed).count()) + "ms");
        delete g_api_server;
        g_api_server = nullptr;
    }
    
    // 再停止监控器
    if (g_monitor) {
        log_manager.info("正在停止监控器...");
        auto start_time = std::chrono::steady_clock::now();
        g_monitor->stop();
        auto elapsed = std::chrono::steady_clock::now() - start_time;
        log_manager.info("监控器停止耗时: " + std::to_string(
            std::chrono::duration_cast<std::chrono::milliseconds>(elapsed).count()) + "ms");
        delete g_monitor;
        g_monitor = nullptr;
    }
    
    std::cout << "PSFSMON-L 已停止" << std::endl;
    log_manager.info("PSFSMON-L 已停止");
    
    return 0;
} 