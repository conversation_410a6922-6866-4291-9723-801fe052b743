/*
 * 内核文件操作序列监控器实现
 */

#include "kernel_sequence_monitor.h"
#include "fast_simple_lcsk.h"
#include "file_monitor.h"
#include "process_monitor.h"
#include "main_monitor.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <unistd.h>
#include <sys/types.h>
#include <dirent.h>
#include <cstring>
#include <cerrno>

// 规则管理器实现 - 只保留方法实现，getInstance已在头文件中定义
void CmpRuleManager::AddOneCmpRule(std::shared_ptr<OneCmpRule> prule) {
    std::lock_guard<std::mutex> lock(m_CmpRuleManager_Mutex);
    
    bool findit = false;
    for (auto CmpRuleIter = mRulesList.begin(); CmpRuleIter != mRulesList.end(); ++CmpRuleIter) {
        auto tprul = CmpRuleIter->get();
        if ((tprul->RuleName == prule->RuleName) &&
            (tprul->Rule == prule->Rule) &&
            (tprul->k_Value == prule->k_Value) &&
            (tprul->len_Value == prule->len_Value) &&
            (tprul->d_Value == prule->d_Value) &&
            (tprul->w_Level == prule->w_Level)) {
            findit = true;
            break;
        }
    }
    
    if (!findit) {
        mRulesList.push_back(prule);
    }
}

bool CmpRuleManager::CatchCmpRule2TID(const std::string& kernel_tag_str) {
    if (kernel_tag_str.empty()) return false;
    
    bool ret = false;
    std::lock_guard<std::mutex> lock(m_CmpRuleManager_Mutex);
    
    for (const auto& rule : mRulesList) {
        std::vector<std::pair<int, int>> recon;
        
        // 使用LCSK++算法进行匹配
        LcsKppSparseFast(rule->Rule, kernel_tag_str, 1, &recon);
        size_t length = recon.size();
        
        if (length >= static_cast<size_t>(rule->len_Value)) {
            // 细化匹配
            std::string tmpcmpstr;
            for (const auto& p : recon) {
                if (p.first < static_cast<int>(rule->Rule.length())) {
                    tmpcmpstr.push_back(rule->Rule[p.first]);
                }
            }
            
            LcsKppSparseFast(rule->Rule, tmpcmpstr, rule->k_Value, &recon);
            size_t kdvalue_length = recon.size();
            size_t min_dkvalue_len = static_cast<size_t>(rule->k_Value) * static_cast<size_t>(rule->d_Value);
            
            if (kdvalue_length >= min_dkvalue_len) {
                ret = true;
                break;
            }
        }
    }
    
    return ret;
}

std::vector<std::pair<std::shared_ptr<OneCmpRule>, int>> CmpRuleManager::getMatchingRules(const std::string& kernel_tag_str) {
    std::vector<std::pair<std::shared_ptr<OneCmpRule>, int>> matching_rules;
    
    if (kernel_tag_str.empty()) return matching_rules;
    
    std::lock_guard<std::mutex> lock(m_CmpRuleManager_Mutex);
    
    for (const auto& rule : mRulesList) {
        std::vector<std::pair<int, int>> recon;
        
        // 使用LCSK++算法进行匹配
        LcsKppSparseFast(rule->Rule, kernel_tag_str, 1, &recon);
        size_t length = recon.size();
        
        if (length >= static_cast<size_t>(rule->len_Value)) {
            // 细化匹配
            std::string tmpcmpstr;
            for (const auto& p : recon) {
                if (p.first < static_cast<int>(rule->Rule.length())) {
                    tmpcmpstr.push_back(rule->Rule[p.first]);
                }
            }
            
            LcsKppSparseFast(rule->Rule, tmpcmpstr, rule->k_Value, &recon);
            size_t kdvalue_length = recon.size();
            size_t min_dkvalue_len = static_cast<size_t>(rule->k_Value) * static_cast<size_t>(rule->d_Value);
            
            if (kdvalue_length >= min_dkvalue_len) {
                // 计算匹配质量分数 (0-100)
                int match_score = static_cast<int>((static_cast<double>(kdvalue_length) / min_dkvalue_len) * 100);
                if (match_score > 100) match_score = 100;
                
                matching_rules.emplace_back(rule, match_score);
            }
        }
    }
    
    return matching_rules;
}

void CmpRuleManager::initializeDefaultRules() {
    // 添加默认规则（简化版本，基于ref_code/rules_about.cpp）
    AddOneCmpRule(std::make_shared<OneCmpRule>(
        "strict_rename",
        "!aA&!bB'!cC(!dD)!eE*!fF+!gG,!hH-!iI.!jJ/",
        9, 3, 2, CB_MIN_STRICT_RENAME_WATCH_LEVEL, COMPRESS_RULE | RENAME_RULE
    ));
    
    AddOneCmpRule(std::make_shared<OneCmpRule>(
        "strict_delete",
        "!aBA0!cDC2!eFE4!gHG6!iJI8!",
        12, 4, 2, CB_MIN_STRICT_DELETE_WATCH_LEVEL, COMPRESS_RULE | DELETE_RULE
    ));
    
    AddOneCmpRule(std::make_shared<OneCmpRule>(
        "sensitive_delete",
        "!aB0!cD2!eF4!gH6!iJ8!",
        12, 3, 3, CB_MIN_STRICT_DELETE_WATCH_LEVEL, COMPRESS_RULE | DELETE_RULE
    ));
}

const std::vector<std::shared_ptr<OneCmpRule>>& CmpRuleManager::getRules() const {
    return mRulesList;
}

// 内核序列监控器实现
KernelSequenceMonitor::~KernelSequenceMonitor() {
    stop();
}

bool KernelSequenceMonitor::start() {
    if (running_.load()) {
        return true;
    }
    
    running_.store(true);
    monitor_thread_ = std::thread(&KernelSequenceMonitor::monitorLoop, this);
    return true;
}

void KernelSequenceMonitor::stop() {
    running_.store(false);
    if (monitor_thread_.joinable()) {
        monitor_thread_.join();
    }
}

void KernelSequenceMonitor::monitorLoop() {
    while (running_.load()) {
        std::lock_guard<std::mutex> lock(data_mutex_);
        
        // 读取所有进程的序列信息
        std::ifstream proc_file(KERNEL_MONITOR_PROC_PATH);
        if (!proc_file.is_open()) {
            // 如果文件不存在，等待后重试
            std::this_thread::sleep_for(std::chrono::milliseconds(KERNEL_SEQUENCE_MONITOR_INTERVAL_MS));
            continue;
        }
        
        std::string line;
        while (std::getline(proc_file, line)) {
            pid_t pid, tid;
            std::string sequence;
            
            parseSequenceLine(line, pid, tid, sequence);
            if (pid > 0 && !sequence.empty()) {
                ProcessSequenceInfo& info = process_sequences_[pid];
                info.pid = pid;
                info.tid = tid;
                info.sequence_string = sequence;
                info.last_update_time = std::chrono::duration_cast<std::chrono::milliseconds>(
                    std::chrono::steady_clock::now().time_since_epoch()).count();
                
                // 获取匹配的规则
                auto matching_rules = CmpRuleManager::getInstance().getMatchingRules(sequence);
                info.has_match = !matching_rules.empty();
                
                if (info.has_match) {
                    // 获取进程名用于记录
                    std::string process_name = Utils::getProcessName(pid);
                    
                    // 记录所有匹配的规则信息
                    auto& rule_manager = RuleMatchManager::getInstance();
                    
                    for (const auto& rule_pair : matching_rules) {
                        const std::shared_ptr<OneCmpRule>& rule = rule_pair.first;
                        int match_score = rule_pair.second;
                        
                        // 使用规则的监控等级作为规则等级
                        int rule_level = rule->w_Level;
                        
                        // 记录规则匹配到重点进程的文件操作信息中
                        rule_manager.recordRuleMatch(pid, rule_level, sequence);
                        
                        // 记录匹配的规则名称
                        info.matched_rules.push_back(rule->RuleName);
                        
                        LogManager::getInstance().info("[KernelSequenceMonitor] 规则匹配详情: PID=" + 
                                                      std::to_string(pid) + 
                                                      ", 规则名=" + rule->RuleName + 
                                                      ", 规则等级=" + std::to_string(rule_level) +
                                                      ", 匹配分数=" + std::to_string(match_score) + "%");
                    }
                    
                    // 将匹配规则的进程添加到重点进程列表（如果不是重点进程）
                    auto& focus_manager = FocusProcessManager::getInstance();
                    if (!focus_manager.isFocusProcess(pid)) {
                        focus_manager.addFocusProcess(pid);
                        
                        // 通知ProcessMonitor更新进程状态
                        auto& process_monitor = ProcessMonitorManager::getInstance();
                        if (process_monitor.getProcessMonitor()) {
                            process_monitor.getProcessMonitor()->setFocusProcess(pid, true);
                        }
                        
                        LogManager::getInstance().info("[KernelSequenceMonitor] 规则匹配，自动添加为重点进程: PID=" + 
                                                      std::to_string(pid) + ", TID=" + std::to_string(tid) + 
                                                      ", 进程名=" + process_name + 
                                                      ", 匹配规则数=" + std::to_string(matching_rules.size()));
                    } else {
                        // 已经是重点进程，记录详细信息
                        LogManager::getInstance().info("[KernelSequenceMonitor] 重点进程规则匹配: PID=" + 
                                                      std::to_string(pid) + ", TID=" + std::to_string(tid) + 
                                                      ", 进程名=" + process_name + 
                                                      ", 匹配规则数=" + std::to_string(matching_rules.size()));
                    }
                    
                    // 计算并记录删除、重命名数量信息
                    auto stats = rule_manager.getRuleMatchStats(pid);
                    if (stats) {
                        // 从内核特征字符串计算文件操作次数
                        stats->calculateFileOperationsFromKernelString(sequence);
                        
                        LogManager::getInstance().info("[KernelSequenceMonitor] 文件操作统计: PID=" + 
                                                      std::to_string(pid) + 
                                                      ", 删除次数=" + std::to_string(stats->all_delete_times) +
                                                      ", 重命名次数=" + std::to_string(stats->all_rename_times) +
                                                      ", 总规则触发=" + std::to_string(stats->all_catch_count));
                    }
                }
            }
        }
        
        proc_file.close();
        
        // 清理过期的进程信息
        auto current_time = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now().time_since_epoch()).count();
        
        for (auto it = process_sequences_.begin(); it != process_sequences_.end();) {
            if (current_time - it->second.last_update_time > 30000) { // 30秒过期
                it = process_sequences_.erase(it);
            } else {
                ++it;
            }
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(KERNEL_SEQUENCE_MONITOR_INTERVAL_MS));
    }
}

void KernelSequenceMonitor::parseSequenceLine(const std::string& line, pid_t& pid, pid_t& tid, std::string& sequence) {
    pid = 0;
    tid = 0;
    sequence.clear();
    
    // 解析格式: "# SEQUENCE PID=%d TID=%d: %s"
    const char* prefix = "# SEQUENCE PID=";
    const char* tid_prefix = " TID=";
    const char* seq_prefix = ": ";
    
    size_t pid_pos = line.find(prefix);
    if (pid_pos == std::string::npos) return;
    
    size_t tid_pos = line.find(tid_prefix, pid_pos + strlen(prefix));
    if (tid_pos == std::string::npos) return;
    
    size_t seq_pos = line.find(seq_prefix, tid_pos + strlen(tid_prefix));
    if (seq_pos == std::string::npos) return;
    
    try {
        pid = std::stoi(line.substr(pid_pos + strlen(prefix), tid_pos - (pid_pos + strlen(prefix))));
        tid = std::stoi(line.substr(tid_pos + strlen(tid_prefix), seq_pos - (tid_pos + strlen(tid_prefix))));
        sequence = line.substr(seq_pos + strlen(seq_prefix));
    } catch (...) {
        pid = 0;
        tid = 0;
        sequence.clear();
    }
}

ProcessSequenceInfo KernelSequenceMonitor::getProcessSequence(pid_t pid) const {
    std::lock_guard<std::mutex> lock(data_mutex_);
    auto it = process_sequences_.find(pid);
    if (it != process_sequences_.end()) {
        return it->second;
    }
    return ProcessSequenceInfo();
}

std::vector<ProcessSequenceInfo> KernelSequenceMonitor::getAllProcessSequences() const {
    std::lock_guard<std::mutex> lock(data_mutex_);
    std::vector<ProcessSequenceInfo> result;
    for (const auto& pair : process_sequences_) {
        result.push_back(pair.second);
    }
    return result;
}

bool KernelSequenceMonitor::checkProcessForFocus(pid_t pid) {
    ProcessSequenceInfo info = getProcessSequence(pid);
    return info.has_match;
}

bool KernelSequenceMonitor::readKernelSequence(pid_t pid, std::string& sequence) {
    std::ifstream proc_file(KERNEL_MONITOR_PROC_PATH);
    if (!proc_file.is_open()) {
        return false;
    }
    
    std::string line;
    while (std::getline(proc_file, line)) {
        pid_t current_pid, current_tid;
        std::string current_sequence;
        
        parseSequenceLine(line, current_pid, current_tid, current_sequence);
        if (current_pid == pid) {
            sequence = current_sequence;
            proc_file.close();
            return true;
        }
    }
    
    proc_file.close();
    return false;
}
