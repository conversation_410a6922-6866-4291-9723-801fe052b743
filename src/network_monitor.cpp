#include "network_monitor.h"
#include "main_monitor.h"
#include <sys/socket.h>
#include <linux/netlink.h>
#include <linux/inet_diag.h>
#include <linux/sock_diag.h>
#include <linux/unix_diag.h>
#include <linux/rtnetlink.h>
#include <arpa/inet.h>
#include <netinet/tcp.h>
#include <ifaddrs.h>
#include <cstring>
#include <algorithm>
#include <sstream>
#include <dirent.h>
#include <fstream>
#include <chrono>

// NetworkMonitor完整实现
NetworkMonitor::NetworkMonitor() : running_(false), stop_requested_(false),
                                   update_interval_ms_(5000), process_monitor_(nullptr),
                                   total_network_events_(0),
                                   useNetlinkDiag_(false), netlink_fd_(-1), last_full_scan_(0) {
}

NetworkMonitor::~NetworkMonitor() {
    stop();
}

bool NetworkMonitor::initialize() {
    // 尝试初始化 netlink socket 诊断接口
    if (initializeNetlinkDiag()) {
        useNetlinkDiag_ = true;
        LogManager::getInstance().info("[NetworkMonitor] 使用 Netlink 诊断接口");
    } else {
        LogManager::getInstance().info("[NetworkMonitor] 使用 /proc 文件系统接口");
    }
    
    // 初始化网络接口信息
    updateNetworkInterfaces();
    
    LogManager::getInstance().info("[NetworkMonitor] 网络监控器初始化成功");
    return true;
}

bool NetworkMonitor::initializeNetlinkDiag() {
    netlink_fd_ = socket(AF_NETLINK, SOCK_RAW, NETLINK_SOCK_DIAG);
    if (netlink_fd_ < 0) {
        LogManager::getInstance().error("[NetworkMonitor] 无法创建 netlink socket: " + std::string(strerror(errno)));
        return false;
    }
    
    struct sockaddr_nl addr;
    memset(&addr, 0, sizeof(addr));
    addr.nl_family = AF_NETLINK;
    
    if (bind(netlink_fd_, (struct sockaddr*)&addr, sizeof(addr)) < 0) {
        LogManager::getInstance().error("[NetworkMonitor] netlink bind 失败: " + std::string(strerror(errno)));
        close(netlink_fd_);
        netlink_fd_ = -1;
        return false;
    }
    
    return true;
}

bool NetworkMonitor::start() {
    if (running_) {
        return true;
    }
    
    running_ = true;
    stop_requested_ = false;
    
    monitor_thread_ = std::thread(&NetworkMonitor::monitorLoop, this);
    
    LogManager::getInstance().info("[NetworkMonitor] 网络监控器已启动");
    return true;
}

void NetworkMonitor::stop() {
    if (!running_) {
        return;
    }
    
    stop_requested_ = true;
    running_ = false;
    
    if (netlink_fd_ != -1) {
        close(netlink_fd_);
        netlink_fd_ = -1;
    }
    
    if (monitor_thread_.joinable()) {
        monitor_thread_.join();
    }
    
    LogManager::getInstance().info("[NetworkMonitor] 网络监控器已停止");
}

void NetworkMonitor::updateNetworkConnections() {
    auto start_time = std::chrono::steady_clock::now();
    static int scan_count = 0;
    
    if (useNetlinkDiag_) {
        scanConnectionsWithNetlink();
    } else {
        scanTcpConnections();
        scanUdpConnections();
        scanUnixConnections();
    }
    
    // 进程套接字映射是昂贵操作，只在每10次扫描时执行一次（约50秒）
    scan_count++;
    if (scan_count >= 10) {
        updateProcessSocketMapping();
        scan_count = 0;
    }
    
    detectConnectionChanges();
    updateStatistics();
    
    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    LogManager::getInstance().debug("[NetworkMonitor] 网络扫描完成，耗时: " + std::to_string(duration.count()) + "ms");
}

void NetworkMonitor::scanConnectionsWithNetlink() {
    // 清空旧的连接数据，避免累积
    {
        std::lock_guard<std::mutex> lock(NetworkMonitor_connections_mutex_);
        current_connections_.clear();
    }
    
    // 扫描 TCP 连接
    scanNetlinkConnections(IPPROTO_TCP);
    // 扫描 UDP 连接  
    scanNetlinkConnections(IPPROTO_UDP);
}

void NetworkMonitor::scanNetlinkConnections(int protocol) {
    struct {
        struct nlmsghdr nlh;
        struct inet_diag_req_v2 req;
    } request;
    
    memset(&request, 0, sizeof(request));
    request.nlh.nlmsg_len = sizeof(request);
    request.nlh.nlmsg_type = SOCK_DIAG_BY_FAMILY;
    request.nlh.nlmsg_flags = NLM_F_REQUEST | NLM_F_DUMP;
    request.nlh.nlmsg_seq = time(nullptr);
    
    request.req.sdiag_family = AF_INET;
    request.req.sdiag_protocol = protocol;
    request.req.idiag_states = 0xFFFFFFFF; // 所有状态
    request.req.idiag_ext = (1 << (INET_DIAG_INFO - 1)) | (1 << (INET_DIAG_CONG - 1));
    
    if (send(netlink_fd_, &request, sizeof(request), 0) < 0) {
        LogManager::getInstance().error("[NetworkMonitor] netlink 发送请求失败");
        return;
    }
    
    char buffer[8192];
    int max_iterations = 1000; // 防止无限循环的安全机制
    int iteration_count = 0;
    
    while (iteration_count < max_iterations) {
        ssize_t len = recv(netlink_fd_, buffer, sizeof(buffer), MSG_DONTWAIT);
        if (len <= 0) {
            if (errno == EAGAIN || errno == EWOULDBLOCK) {
                break; // 没有更多数据
            }
            LogManager::getInstance().error("[NetworkMonitor] netlink 接收数据失败: " + std::string(strerror(errno)));
            break;
        }
        
        processNetlinkResponse(buffer, len);
        iteration_count++;
    }
    
    if (iteration_count >= max_iterations) {
        LogManager::getInstance().warning("[NetworkMonitor] netlink 接收循环达到最大迭代次数，可能存在数据过多或配置问题");
    }
}

void NetworkMonitor::processNetlinkResponse(const char* buffer, ssize_t len) {
    const struct nlmsghdr* nlh;
    
    for (nlh = (const struct nlmsghdr*)buffer; NLMSG_OK(nlh, len); nlh = NLMSG_NEXT(nlh, len)) {
        if (nlh->nlmsg_type == NLMSG_DONE) {
            break;
        }
        
        if (nlh->nlmsg_type == NLMSG_ERROR) {
            const struct nlmsgerr* err = (const struct nlmsgerr*)NLMSG_DATA(nlh);
            LogManager::getInstance().error("[NetworkMonitor] netlink 错误: " + std::to_string(err->error));
            break;
        }
        
        if (nlh->nlmsg_type == SOCK_DIAG_BY_FAMILY) {
            const struct inet_diag_msg* diag = (const struct inet_diag_msg*)NLMSG_DATA(nlh);
            processInetDiagMessage(diag, nlh);
        }
    }
}

void NetworkMonitor::processInetDiagMessage(const struct inet_diag_msg* diag, const struct nlmsghdr* nlh) {
    SocketInfo socket;
    socket.protocol = (diag->idiag_family == AF_INET) ? 
                     ((diag->id.idiag_sport || diag->id.idiag_dport) ? ProtocolType::TCP : ProtocolType::UDP) : 
                     ProtocolType::TCP;
    
    // 本地地址和端口
    if (diag->idiag_family == AF_INET) {
        socket.local_addr = inet_ntoa(*(struct in_addr*)&diag->id.idiag_src[0]);
        socket.remote_addr = inet_ntoa(*(struct in_addr*)&diag->id.idiag_dst[0]);
    } else {
        // IPv6 地址处理
        char addr_str[INET6_ADDRSTRLEN];
        inet_ntop(AF_INET6, diag->id.idiag_src, addr_str, sizeof(addr_str));
        socket.local_addr = addr_str;
        inet_ntop(AF_INET6, diag->id.idiag_dst, addr_str, sizeof(addr_str));
        socket.remote_addr = addr_str;
    }
    
    socket.local_port = ntohs(diag->id.idiag_sport);
    socket.remote_port = ntohs(diag->id.idiag_dport);
    socket.state = tcpStateToConnectionState(diag->idiag_state);
    socket.inode = diag->idiag_inode;
    socket.uid = diag->idiag_uid;
    socket.pid = 0; // 需要通过其他方式获取 PID
    
    // 获取流量统计信息
    parseNetlinkAttributes(nlh, socket);
    
    std::lock_guard<std::mutex> lock(NetworkMonitor_connections_mutex_);
    current_connections_.push_back(socket);
}

void NetworkMonitor::parseNetlinkAttributes(const struct nlmsghdr* nlh, SocketInfo& socket) {
    const struct inet_diag_msg* diag = (const struct inet_diag_msg*)NLMSG_DATA(nlh);
    const struct rtattr* attr;
    int len = nlh->nlmsg_len - NLMSG_LENGTH(sizeof(*diag));
    
    for (attr = (const struct rtattr*)(diag + 1); RTA_OK(attr, len); attr = RTA_NEXT(attr, len)) {
        switch (attr->rta_type) {
            case INET_DIAG_INFO: {
                // TCP 信息
                if (socket.protocol == ProtocolType::TCP) {
                    const struct tcp_info* info = (const struct tcp_info*)RTA_DATA(attr);
                    socket.tx_queue = info->tcpi_snd_cwnd;
                    socket.rx_queue = info->tcpi_rcv_space;
                    // socket.retransmits = info->tcpi_retransmits; // 暂时注释，SocketInfo中没有此字段
                }
                break;
            }
            case INET_DIAG_CONG: {
                // 拥塞控制信息
                const char* cong = (const char*)RTA_DATA(attr);
                (void)cong; // 暂时未使用，避免编译警告
                // 可以记录拥塞控制算法
                break;
            }
        }
    }
}

std::vector<NetworkConnection> NetworkMonitor::getProcessConnections(pid_t pid) const {
    std::vector<NetworkConnection> connections;

    std::lock_guard<std::mutex> lock(NetworkMonitor_connections_mutex_);
    for (const auto& socket : current_connections_) {
        if (socket.pid == pid) {
            NetworkConnection conn;
            conn.local_addr = socket.local_addr;
            conn.local_port = socket.local_port;
            conn.remote_addr = socket.remote_addr;
            conn.remote_port = socket.remote_port;
            conn.protocol = protocolTypeToString(socket.protocol);
            conn.state = connectionStateToString(socket.state);
            conn.created_time = Utils::getCurrentTimestamp();
            conn.tx_bytes = 0; // TODO: 从socket统计获取
            conn.rx_bytes = 0; // TODO: 从socket统计获取
            connections.push_back(conn);
        }
    }
    
    return connections;
}

std::vector<SocketInfo> NetworkMonitor::getAllConnections() const {
    std::lock_guard<std::mutex> lock(NetworkMonitor_connections_mutex_);
    return current_connections_;
}

NetworkStats NetworkMonitor::getNetworkStats() const {
    return current_stats_;
}

std::vector<SocketInfo> NetworkMonitor::getListeningPorts() const {
    std::vector<SocketInfo> listening;

    std::lock_guard<std::mutex> lock(NetworkMonitor_connections_mutex_);
    for (const auto& socket : current_connections_) {
        if (socket.state == ConnectionState::LISTEN) {
            listening.push_back(socket);
        }
    }
    
    return listening;
}

void NetworkMonitor::monitorLoop() {
    LogManager::getInstance().info("[NetworkMonitor] 网络监控循环已启动");
    
    while (!stop_requested_) {
        try {
            updateNetworkConnections();
            
            // 定期更新网络接口统计
            if (Utils::getCurrentTimestamp() - last_full_scan_ > 30) { // 30秒更新一次
                updateNetworkInterfaces();
                last_full_scan_ = Utils::getCurrentTimestamp();
            }
            
        } catch (const std::exception& e) {
            LogManager::getInstance().error("[NetworkMonitor] 监控循环异常: " + std::string(e.what()));
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(update_interval_ms_));
    }
    
    LogManager::getInstance().info("[NetworkMonitor] 网络监控循环已停止");
}

bool NetworkMonitor::scanTcpConnections() {
    std::vector<std::string> lines = readFileLines("/proc/net/tcp");

    std::lock_guard<std::mutex> lock(NetworkMonitor_connections_mutex_);
    // 清除旧的 TCP 连接
    current_connections_.erase(
        std::remove_if(current_connections_.begin(), current_connections_.end(),
                      [](const SocketInfo& s) { return s.protocol == ProtocolType::TCP; }),
        current_connections_.end());
    
    for (size_t i = 1; i < lines.size(); ++i) { // 跳过头部
        SocketInfo socket;
        if (parseTcpLine(lines[i], ProtocolType::TCP, socket)) {
            current_connections_.push_back(socket);
            total_network_events_++;
        }
    }
    
    return true;
}

bool NetworkMonitor::scanUdpConnections() {
    std::vector<std::string> lines = readFileLines("/proc/net/udp");

    std::lock_guard<std::mutex> lock(NetworkMonitor_connections_mutex_);
    // 清除旧的 UDP 连接
    current_connections_.erase(
        std::remove_if(current_connections_.begin(), current_connections_.end(),
                      [](const SocketInfo& s) { return s.protocol == ProtocolType::UDP; }),
        current_connections_.end());
    
    for (size_t i = 1; i < lines.size(); ++i) { // 跳过头部
        SocketInfo socket;
        if (parseTcpLine(lines[i], ProtocolType::UDP, socket)) {
            current_connections_.push_back(socket);
            total_network_events_++;
        }
    }
    
    return true;
}

bool NetworkMonitor::scanUnixConnections() {
    std::vector<std::string> lines = readFileLines("/proc/net/unix");

    std::lock_guard<std::mutex> lock(NetworkMonitor_connections_mutex_);
    // 清除旧的 Unix socket 连接
    current_connections_.erase(
        std::remove_if(current_connections_.begin(), current_connections_.end(),
                      [](const SocketInfo& s) { return s.protocol == ProtocolType::UNIX; }),
        current_connections_.end());
    
    for (size_t i = 1; i < lines.size(); ++i) { // 跳过头部
        SocketInfo socket;
        if (parseUnixLine(lines[i], socket)) {
            current_connections_.push_back(socket);
            total_network_events_++;
        }
    }
    
    return true;
}

bool NetworkMonitor::parseTcpLine(const std::string& line, ProtocolType protocol, SocketInfo& socket) {
    auto parts = splitString(trim(line), ' ');
    if (parts.size() < 10) {
        return false;
    }
    
    socket.protocol = protocol;
    socket.inode = 0;
    socket.pid = 0;
    
    try {
        // 解析本地和远程地址
        auto local = parseAddress(parts[1]);
        socket.local_addr = local.first;
        socket.local_port = local.second;
        
        auto remote = parseAddress(parts[2]);
        socket.remote_addr = remote.first;
        socket.remote_port = remote.second;
        
        // 解析连接状态
        int state = std::stoi(parts[3], nullptr, 16);
        socket.state = intToConnectionState(state);
        
        // 解析发送和接收队列
        auto queue = splitString(parts[4], ':');
        if (queue.size() >= 2) {
            socket.tx_queue = std::stoul(queue[0], nullptr, 16);
            socket.rx_queue = std::stoul(queue[1], nullptr, 16);
        }
        
        // 解析 inode
        if (parts.size() > 9) {
            socket.inode = std::stoul(parts[9]);
        }
        
        // 解析 UID
        if (parts.size() > 7) {
            socket.uid = std::stoi(parts[7]);
        }
        
    } catch (const std::exception& e) {
        LogManager::getInstance().debug("[NetworkMonitor] 解析连接行失败: " + line);
        return false;
    }
    
    return true;
}

bool NetworkMonitor::parseUnixLine(const std::string& line, SocketInfo& socket) {
    auto parts = splitString(trim(line), ' ');
    if (parts.size() < 7) {
        return false;
    }
    
    socket.protocol = ProtocolType::UNIX;
    socket.local_addr = "unix";
    socket.remote_addr = "";
    socket.local_port = 0;
    socket.remote_port = 0;
    
    try {
        // Unix socket 状态
        int state = std::stoi(parts[5], nullptr, 16);
        socket.state = (state == 1) ? ConnectionState::ESTABLISHED : ConnectionState::UNKNOWN;
        
        // Inode
        socket.inode = std::stoul(parts[6]);
        
        // 路径 (如果有)
        if (parts.size() > 7) {
            socket.unix_path = parts[7];
        }
        
    } catch (const std::exception& e) {
        return false;
    }
    
    return true;
}

void NetworkMonitor::updateProcessSocketMapping() {
    // 遍历 /proc/*/fd/ 目录，建立 inode 到 PID 的映射
    std::unordered_map<unsigned long, pid_t> inode_to_pid;
    
    DIR* proc_dir = opendir("/proc");
    if (!proc_dir) return;
    
    struct dirent* entry;
    while ((entry = readdir(proc_dir)) != nullptr) {
        if (!isdigit(entry->d_name[0])) continue;
        
        pid_t pid = std::stoi(entry->d_name);
        std::string fd_dir = "/proc/" + std::string(entry->d_name) + "/fd";
        
        DIR* fd_dir_handle = opendir(fd_dir.c_str());
        if (!fd_dir_handle) continue;
        
        struct dirent* fd_entry;
        while ((fd_entry = readdir(fd_dir_handle)) != nullptr) {
            if (!isdigit(fd_entry->d_name[0])) continue;
            
            std::string fd_path = fd_dir + "/" + fd_entry->d_name;
            char link_target[PATH_MAX];
            ssize_t len = readlink(fd_path.c_str(), link_target, sizeof(link_target) - 1);
            
            if (len > 0) {
                link_target[len] = '\0';
                std::string target(link_target);
                
                // 检查是否是 socket
                if (target.find("socket:[") == 0) {
                    size_t start = target.find('[') + 1;
                    size_t end = target.find(']');
                    if (start != std::string::npos && end != std::string::npos) {
                        unsigned long inode = std::stoul(target.substr(start, end - start));
                        inode_to_pid[inode] = pid;
                    }
                }
            }
        }
        closedir(fd_dir_handle);
    }
    closedir(proc_dir);
    
    // 更新连接的 PID 信息
    std::lock_guard<std::mutex> lock(NetworkMonitor_connections_mutex_);
    for (auto& socket : current_connections_) {
        auto it = inode_to_pid.find(socket.inode);
        if (it != inode_to_pid.end()) {
            socket.pid = it->second;
        }
    }
}

void NetworkMonitor::detectConnectionChanges() {
    // 检测新建立的连接
    std::lock_guard<std::mutex> lock(NetworkMonitor_connections_mutex_);
    for (const auto& socket : current_connections_) {
        if (socket.pid > 0 && process_monitor_) {
            // 将网络连接信息更新到进程监控器
            NetworkConnection conn;
            conn.local_addr = socket.local_addr;
            conn.local_port = socket.local_port;
            conn.remote_addr = socket.remote_addr;
            conn.remote_port = socket.remote_port;
            conn.protocol = protocolTypeToString(socket.protocol);
            conn.state = connectionStateToString(socket.state);
            conn.created_time = Utils::getCurrentTimestamp();
            
            // 更新进程的网络连接信息
            auto process = process_monitor_->getProcess(socket.pid);
            if (process) {
                std::vector<NetworkConnection> connections = {conn};
                process->updateNetworkConnections(connections);
            }
        }
    }
}

void NetworkMonitor::updateNetworkInterfaces() {
    struct ifaddrs* ifaddrs_ptr;
    if (getifaddrs(&ifaddrs_ptr) == -1) {
        LogManager::getInstance().error("[NetworkMonitor] getifaddrs 失败");
        return;
    }
    
    std::lock_guard<std::mutex> lock(NetworkMonitor_interfaces_mutex_);
    network_interfaces_.clear();
    
    for (struct ifaddrs* ifa = ifaddrs_ptr; ifa != nullptr; ifa = ifa->ifa_next) {
        if (ifa->ifa_addr == nullptr) continue;
        
        NetworkInterface iface;
        iface.name = ifa->ifa_name;
        iface.flags = ifa->ifa_flags;
        
        if (ifa->ifa_addr->sa_family == AF_INET) {
            struct sockaddr_in* addr_in = (struct sockaddr_in*)ifa->ifa_addr;
            iface.ipv4_addr = inet_ntoa(addr_in->sin_addr);
        } else if (ifa->ifa_addr->sa_family == AF_INET6) {
            struct sockaddr_in6* addr_in6 = (struct sockaddr_in6*)ifa->ifa_addr;
            char addr_str[INET6_ADDRSTRLEN];
            inet_ntop(AF_INET6, &addr_in6->sin6_addr, addr_str, sizeof(addr_str));
            iface.ipv6_addr = addr_str;
        }
        
        network_interfaces_.push_back(iface);
    }
    
    freeifaddrs(ifaddrs_ptr);
}

void NetworkMonitor::updateStatistics() {
    std::lock_guard<std::mutex> lock(NetworkMonitor_connections_mutex_);
    
    current_stats_.total_connections = current_connections_.size();
    current_stats_.tcp_connections = 0;
    current_stats_.udp_connections = 0;
    current_stats_.unix_connections = 0;
    current_stats_.listening_ports = 0;
    current_stats_.established_connections = 0;
    
    for (const auto& socket : current_connections_) {
        switch (socket.protocol) {
            case ProtocolType::TCP:
            case ProtocolType::TCP6:
                current_stats_.tcp_connections++;
                break;
            case ProtocolType::UDP:
            case ProtocolType::UDP6:
                current_stats_.udp_connections++;
                break;
            case ProtocolType::UNIX:
                current_stats_.unix_connections++;
                break;
            case ProtocolType::UNKNOWN:
                // 忽略未知协议
                break;
        }
        
        if (socket.state == ConnectionState::LISTEN) {
            current_stats_.listening_ports++;
        } else if (socket.state == ConnectionState::ESTABLISHED) {
            current_stats_.established_connections++;
        }
    }
    
    current_stats_.total_bytes_sent = 0;
    current_stats_.total_bytes_received = 0;
    current_stats_.last_update = Utils::getCurrentTimestamp();
}

std::pair<std::string, uint16_t> NetworkMonitor::parseAddress(const std::string& addr_str, bool is_ipv6) {
    size_t colon_pos = addr_str.find(':');
    if (colon_pos == std::string::npos) {
        return {"0.0.0.0", 0};
    }
    
    std::string addr_hex = addr_str.substr(0, colon_pos);
    std::string port_hex = addr_str.substr(colon_pos + 1);
    
    std::string ip = is_ipv6 ? hexToIpv6(addr_hex) : hexToIpv4(addr_hex);
    uint16_t port = hexToPort(port_hex);
    
    return {ip, port};
}

std::string NetworkMonitor::hexToIpv4(const std::string& hex_addr) {
    if (hex_addr.length() != 8) {
        return "0.0.0.0";
    }
    
    uint32_t addr = std::stoul(hex_addr, nullptr, 16);
    return std::to_string(addr & 0xFF) + "." +
           std::to_string((addr >> 8) & 0xFF) + "." +
           std::to_string((addr >> 16) & 0xFF) + "." +
           std::to_string((addr >> 24) & 0xFF);
}

std::string NetworkMonitor::hexToIpv6(const std::string& hex_addr) {
    if (hex_addr.length() != 32) {
        return "::";
    }
    
    std::string result;
    for (size_t i = 0; i < hex_addr.length(); i += 4) {
        if (i > 0) result += ":";
        result += hex_addr.substr(i, 4);
    }
    return result;
}

uint16_t NetworkMonitor::hexToPort(const std::string& hex_port) {
    return static_cast<uint16_t>(std::stoul(hex_port, nullptr, 16));
}

std::vector<std::string> NetworkMonitor::readFileLines(const std::string& filename) {
    std::vector<std::string> lines;
    std::ifstream file(filename);
    std::string line;
    
    while (std::getline(file, line)) {
        lines.push_back(line);
    }
    
    return lines;
}

std::string NetworkMonitor::trim(const std::string& str) {
    size_t start = str.find_first_not_of(" \t\n\r");
    if (start == std::string::npos) return "";
    
    size_t end = str.find_last_not_of(" \t\n\r");
    return str.substr(start, end - start + 1);
}

ConnectionState NetworkMonitor::intToConnectionState(int state) {
    switch (state) {
        case 1: return ConnectionState::ESTABLISHED;
        case 2: return ConnectionState::SYN_SENT;
        case 3: return ConnectionState::SYN_RECV;
        case 4: return ConnectionState::FIN_WAIT1;
        case 5: return ConnectionState::FIN_WAIT2;
        case 6: return ConnectionState::TIME_WAIT;
        case 7: return ConnectionState::CLOSE;
        case 8: return ConnectionState::CLOSE_WAIT;
        case 9: return ConnectionState::LAST_ACK;
        case 10: return ConnectionState::LISTEN;
        case 11: return ConnectionState::CLOSING;
        default: return ConnectionState::UNKNOWN;
    }
}

ConnectionState NetworkMonitor::tcpStateToConnectionState(int tcp_state) {
    return intToConnectionState(tcp_state);
}

std::string NetworkMonitor::connectionStateToString(ConnectionState state) const {
    switch (state) {
        case ConnectionState::ESTABLISHED: return "ESTABLISHED";
        case ConnectionState::SYN_SENT: return "SYN_SENT";
        case ConnectionState::SYN_RECV: return "SYN_RECV";
        case ConnectionState::FIN_WAIT1: return "FIN_WAIT1";
        case ConnectionState::FIN_WAIT2: return "FIN_WAIT2";
        case ConnectionState::TIME_WAIT: return "TIME_WAIT";
        case ConnectionState::CLOSE: return "CLOSE";
        case ConnectionState::CLOSE_WAIT: return "CLOSE_WAIT";
        case ConnectionState::LAST_ACK: return "LAST_ACK";
        case ConnectionState::LISTEN: return "LISTEN";
        case ConnectionState::CLOSING: return "CLOSING";
        default: return "UNKNOWN";
    }
}

std::string NetworkMonitor::protocolTypeToString(ProtocolType protocol) const {
    switch (protocol) {
        case ProtocolType::TCP: return "TCP";
        case ProtocolType::UDP: return "UDP";
        case ProtocolType::UNIX: return "UNIX";
        default: return "UNKNOWN";
    }
}

std::vector<std::string> NetworkMonitor::splitString(const std::string& str, char delimiter) {
    std::vector<std::string> tokens;
    std::stringstream ss(str);
    std::string token;
    
    while (std::getline(ss, token, delimiter)) {
        if (!token.empty()) {
            tokens.push_back(token);
        }
    }
    
    return tokens;
}

void NetworkMonitor::logError(const std::string& message) {
    LogManager::getInstance().error("[NetworkMonitor] " + message);
}

// NetworkMonitorManager 单例实现
NetworkMonitorManager& NetworkMonitorManager::getInstance() {
    static NetworkMonitorManager instance;
    return instance;
}

bool NetworkMonitorManager::initialize() {
    network_monitor_.reset(new NetworkMonitor());
    return network_monitor_->initialize();
}

bool NetworkMonitorManager::start() {
    return network_monitor_->start();
}

void NetworkMonitorManager::stop() {
    if (network_monitor_) {
        network_monitor_->stop();
    }
}

void NetworkMonitorManager::setProcessMonitor(ProcessMonitor* monitor) {
    if (network_monitor_) {
        network_monitor_->setProcessMonitor(monitor);
    }
}

uint64_t NetworkMonitor::getTotalEvents() const {
    return total_network_events_;
}

std::vector<NetworkInterface> NetworkMonitor::getNetworkInterfaces() const {
    std::lock_guard<std::mutex> lock(NetworkMonitor_interfaces_mutex_);
    return network_interfaces_;
}