#include "main_monitor.h"
#include "file_monitor.h"
#include "network_monitor.h"
#include "process_monitor.h"
#include "process_exclusion.h"
#include <cstring>
#include <cstdarg>
#include <iostream>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <cctype>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/utsname.h>
#include <unistd.h>
#include <fcntl.h>
#include <csignal>
#include <pwd.h>
#include <thread>

// MainMonitor静态成员
MainMonitor* MainMonitor::instance_ = nullptr;
std::atomic<bool> MainMonitor::shutdown_requested_{false};

// MainMonitor实现
MainMonitor::MainMonitor() : status_(MonitorStatus::STOPPED), 
                             status_thread_running_(false), start_time_(0), last_stats_update_(0) {
    instance_ = this;
}

MainMonitor::~MainMonitor() {
    stop();
    instance_ = nullptr;
}

bool MainMonitor::initialize(const MonitorConfig& config) {
    config_ = config;
    status_ = MonitorStatus::STARTING;
    start_time_ = Utils::getCurrentTimestamp();
    
    // 设置daemon模式
    LogManager::getInstance().setDaemonMode(config_.daemon_mode);
    
    // 初始化进程排除管理器（确保自身进程被排除）
    LogManager::getInstance().systemInfo("初始化进程排除管理器...");
    auto& exclusion_manager = ProcessExclusionManagerSingleton::getInstance();
    
    // 添加当前进程到排除列表（避免监控自己）
    exclusion_manager.addExclusionByPid(getpid());
    exclusion_manager.addExclusionByName("psfsmon");
    
    // 排除日志相关的路径（避免日志写入触发事件循环）
    if (!config_.log_file.empty()) {
        size_t pos = config_.log_file.find_last_of('/');
        if (pos != std::string::npos) {
            std::string log_dir = config_.log_file.substr(0, pos);
            exclusion_manager.addExclusionByPath(log_dir);
            LogManager::getInstance().systemInfo("排除日志目录监控: " + log_dir);
        }
    }
    
    LogManager::getInstance().systemInfo("进程排除管理器初始化完成 - 当前进程PID: " + std::to_string(getpid()));
    
    // 检查权限
    if (!checkPermissions()) {
        status_ = MonitorStatus::ERROR;
        return false;
    }
    
    // 检查系统兼容性
    if (!checkSystemCompatibility()) {
        status_ = MonitorStatus::ERROR;
        return false;
    }
    
    // 初始化各个监控器
    if (config_.enable_process_monitoring && !initializeProcessMonitor()) {
        LogManager::getInstance().systemError("无法初始化进程监控器");
        status_ = MonitorStatus::ERROR;
        return false;
    }
    
    if (config_.enable_file_monitoring && !initializeFileMonitor()) {
        LogManager::getInstance().systemError("无法初始化文件监控器");
        status_ = MonitorStatus::ERROR;
        return false;
    }
    
    if (config_.enable_network_monitoring && !initializeNetworkMonitor()) {
        LogManager::getInstance().systemError("无法初始化网络监控器");
        status_ = MonitorStatus::ERROR;
        return false;
    }
    
    // 初始化内核序列监控器
    if (!initializeKernelSequenceMonitor()) {
        LogManager::getInstance().systemWarning("无法初始化内核序列监控器，将继续运行其他监控器");
        // 不设置为ERROR状态，允许继续运行
    }
    
    LogManager::getInstance().systemInfo("监控器初始化完成");
    return true;
}

bool MainMonitor::start() {
    if (status_ != MonitorStatus::STARTING) {
        return false;
    }
    
    // 启动各个监控器
    if (config_.enable_process_monitoring && !startProcessMonitor()) {
        LogManager::getInstance().systemError("无法启动进程监控器");
        status_ = MonitorStatus::ERROR;
        return false;
    }
    
    if (config_.enable_file_monitoring && !startFileMonitor()) {
        LogManager::getInstance().systemError("无法启动文件监控器");
        status_ = MonitorStatus::ERROR;
        return false;
    }
    
    if (config_.enable_network_monitoring && !startNetworkMonitor()) {
        LogManager::getInstance().systemError("无法启动网络监控器");
        status_ = MonitorStatus::ERROR;
        return false;
    }
    
    // 启动内核序列监控器
    if (kernel_sequence_monitor_ && !startKernelSequenceMonitor()) {
        LogManager::getInstance().systemWarning("无法启动内核序列监控器");
    }
    
    // 创建PID文件
    if (!createPidFile()) {
        LogManager::getInstance().systemWarning("无法创建PID文件，但程序将继续运行");
    }
    
    // 启动状态监控线程
    status_thread_running_ = true;
    status_thread_ = std::thread(&MainMonitor::statusMonitorLoop, this);
    
    status_ = MonitorStatus::RUNNING;
    LogManager::getInstance().systemInfo("监控器启动成功");
    return true;
}

bool MainMonitor::reloadConfig(const MonitorConfig& new_config) {
    LogManager::getInstance().systemInfo("开始重新加载配置...");
    
    // 保存旧配置用于回滚
    MonitorConfig old_config = config_;
    
    try {
        // 更新配置
        config_ = new_config;
        
        // 添加调试日志，确认配置已更新
        LogManager::getInstance().systemInfo("配置已更新 - terminate_violating_processes: " + 
                                           std::string(config_.terminate_violating_processes ? "true" : "false"));
        
        // 重新初始化各个监控器（如果配置发生变化）
        bool success = true;
        
        // 重新初始化文件监控器（如果文件监控配置发生变化）
        if (config_.enable_file_monitoring) {
            if (file_monitor_) {
                // 停止旧的文件监控器
                file_monitor_->stop();
                file_monitor_.reset();
            }
            
            if (!initializeFileMonitor()) {
                LogManager::getInstance().systemError("重新加载配置时无法初始化文件监控器");
                success = false;
            } else if (!startFileMonitor()) {
                LogManager::getInstance().systemError("重新加载配置时无法启动文件监控器");
                success = false;
            } else {
                // 确保配置正确应用到 FileMonitor
                if (!file_monitor_->reloadConfig(&config_)) {
                    LogManager::getInstance().systemError("重新加载配置时无法应用配置到文件监控器");
                    success = false;
                }
            }
        } else {
            // 如果禁用了文件监控，停止文件监控器
            stopFileMonitor();
        }
        
        // 重新初始化网络监控器（如果网络监控配置发生变化）
        if (config_.enable_network_monitoring) {
            if (network_monitor_) {
                // 停止旧的网络监控器
                network_monitor_->stop();
                network_monitor_.reset();
            }
            
            if (!initializeNetworkMonitor()) {
                LogManager::getInstance().systemError("重新加载配置时无法初始化网络监控器");
                success = false;
            } else if (!startNetworkMonitor()) {
                LogManager::getInstance().systemError("重新加载配置时无法启动网络监控器");
                success = false;
            }
        } else {
            // 如果禁用了网络监控，停止网络监控器
            stopNetworkMonitor();
        }
        
        // 重新初始化进程监控器（如果进程监控配置发生变化）
        if (config_.enable_process_monitoring) {
            auto& process_manager = ProcessMonitorManager::getInstance();
            if (!process_manager.reinitialize()) {
                LogManager::getInstance().systemError("重新加载配置时无法重新初始化进程监控器");
                success = false;
            }
        }
        
        // 更新日志系统配置
        LogManager::getInstance().setDaemonMode(config_.daemon_mode);
        
        if (success) {
            LogManager::getInstance().systemInfo("配置重新加载成功");
            return true;
        } else {
            // 回滚配置
            LogManager::getInstance().systemError("配置重新加载失败，正在回滚...");
            config_ = old_config;
            
            // 重新初始化回滚的配置
            if (config_.enable_file_monitoring && !file_monitor_) {
                initializeFileMonitor();
                startFileMonitor();
            }
            if (config_.enable_network_monitoring && !network_monitor_) {
                initializeNetworkMonitor();
                startNetworkMonitor();
            }
            
            return false;
        }
        
    } catch (const std::exception& e) {
        LogManager::getInstance().systemError("配置重新加载时发生异常: " + std::string(e.what()));
        // 回滚配置
        config_ = old_config;
        return false;
    }
}

void MainMonitor::stop() {
    if (status_ == MonitorStatus::STOPPED || status_ == MonitorStatus::STOPPING) {
        return;
    }
    
    status_ = MonitorStatus::STOPPING;
    LogManager::getInstance().systemInfo("正在停止监控器...");
    
    // 停止状态监控线程
    status_thread_running_ = false;
    if (status_thread_.joinable()) {
        status_thread_.join();
    }
    
    // 停止各个监控器
    stopNetworkMonitor();
    stopFileMonitor();
    stopProcessMonitor();
    stopKernelSequenceMonitor();
    
    // 删除PID文件
    removePidFile();
    
    status_ = MonitorStatus::STOPPED;
    LogManager::getInstance().systemInfo("监控器已停止");
}

MonitorStats MainMonitor::getStats() const {
    std::lock_guard<std::mutex> lock(MainMonitor_stats_mutex_);
    MonitorStats stats = stats_;
    stats.uptime_seconds = Utils::getCurrentTimestamp() - start_time_;
    stats.start_time = start_time_;
    return stats;
}

ProcessMonitor* MainMonitor::getProcessMonitor() const {
    auto& process_manager = ProcessMonitorManager::getInstance();
    return process_manager.getProcessMonitor();
}

FileMonitor* MainMonitor::getFileMonitor() const {
    return file_monitor_.get();
}

NetworkMonitor* MainMonitor::getNetworkMonitor() const {
    return network_monitor_.get();
}

bool MainMonitor::checkPermissions() {
    return Utils::isRootUser();
}

bool MainMonitor::checkSystemCompatibility() {
    return Utils::fileExists("/proc/version") && Utils::fileExists("/sys/kernel");
}

bool MainMonitor::initializeProcessMonitor() {
    // 使用ProcessMonitorManager确保全局使用同一个ProcessMonitor实例
    auto& process_manager = ProcessMonitorManager::getInstance();
    return process_manager.initialize();
}

bool MainMonitor::initializeFileMonitor() {
    file_monitor_.reset(new FileMonitor());
    
    // 设置文件事件回调函数
    file_monitor_->setEventCallback([this](const FileEvent& event) {
        handleFileEvent(event);
    });
    
    return file_monitor_->initialize(&config_);
}

bool MainMonitor::initializeNetworkMonitor() {
    network_monitor_.reset(new NetworkMonitor());
    return network_monitor_->initialize();
}

bool MainMonitor::initializeKernelSequenceMonitor() {
    kernel_sequence_monitor_.reset(new KernelSequenceMonitor());
    
    // 初始化规则管理器
    CmpRuleManager::getInstance().initializeDefaultRules();
    
    return true;
}

bool MainMonitor::startKernelSequenceMonitor() {
    if (!kernel_sequence_monitor_) {
        return false;
    }
    
    return kernel_sequence_monitor_->start();
}

void MainMonitor::stopKernelSequenceMonitor() {
    if (kernel_sequence_monitor_) {
        kernel_sequence_monitor_->stop();
    }
}

bool MainMonitor::startProcessMonitor() {
    auto& process_manager = ProcessMonitorManager::getInstance();
    return process_manager.start();
}

bool MainMonitor::startFileMonitor() {
    if (!file_monitor_) {
        return false;
    }
    
    // 让FileMonitor自己通过setupMonitoringStrategy()统一处理监控策略
    // 避免重复监控设置
    LogManager::getInstance().systemInfo("启动文件监控器，使用智能监控策略");
    
    return file_monitor_->start();
}

bool MainMonitor::startNetworkMonitor() {
    if (!network_monitor_) {
        return false;
    }
    return network_monitor_->start();
}

void MainMonitor::stopProcessMonitor() {
    auto& process_manager = ProcessMonitorManager::getInstance();
    process_manager.stop();
}

void MainMonitor::stopFileMonitor() {
    if (file_monitor_) {
        file_monitor_->stop();
    }
}

void MainMonitor::stopNetworkMonitor() {
    if (network_monitor_) {
        network_monitor_->stop();
    }
}

void MainMonitor::handleFileEvent(const FileEvent& event) {
    // 更新统计信息
    {
        std::lock_guard<std::mutex> lock(MainMonitor_stats_mutex_);
        stats_.total_file_events++;
    }
    
    // 获取进程名
    std::string process_name = "unknown";
    auto& process_manager = ProcessMonitorManager::getInstance();
    auto* process_monitor = process_manager.getProcessMonitor();
    
    if (process_monitor) {
        auto process_info = process_monitor->getProcess(event.pid);
        if (process_info) {
            process_name = process_info->process_name;
        }
    }
    
    // 检查是否为重点进程
    if (process_monitor) {
        FocusProcessManager& focus_mgr = FocusProcessManager::getInstance();
        if (focus_mgr.isFocusProcess(event.pid) || focus_mgr.isFocusProcess(process_name)) {
            // 对于重点进程，记录基本文件操作统计
            if (event.type == FileEvent::DELETE) {
                process_monitor->recordFocusProcessDelete(event.pid, event.file_path);
            } else if (event.type == FileEvent::RENAME) {
                process_monitor->recordFocusProcessRename(event.pid, event.file_path, "");
            }
        }
    }
    
    // 检查受保护路径
    ProtectedPathManager& protected_mgr = ProtectedPathManager::getInstance();
    if (protected_mgr.isProtectedPath(event.file_path)) {
        if (event.type == FileEvent::WRITE || event.type == FileEvent::DELETE || event.type == FileEvent::RENAME) {
            protected_mgr.handleViolation(event.file_path, event.pid, process_name);
        }
    }
}

void MainMonitor::statusMonitorLoop() {
    while (status_thread_running_) {
        updatePerformanceStats();
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
}

void MainMonitor::updatePerformanceStats() {
    std::lock_guard<std::mutex> lock(MainMonitor_stats_mutex_);
    
    auto& process_manager = ProcessMonitorManager::getInstance();
    auto* process_monitor = process_manager.getProcessMonitor();
    if (process_monitor) {
        stats_.total_processes = process_monitor->getTotalProcesses();
        // 不再统计线程数量
    }
    
    // 获取文件监控统计
    if (config_.enable_file_monitoring && file_monitor_) {
        stats_.total_file_events = file_monitor_->getTotalEvents();
    }
    
    // 获取网络监控统计
    if (config_.enable_network_monitoring && network_monitor_) {
        stats_.total_network_events = network_monitor_->getTotalEvents();
    }
    
    last_stats_update_ = Utils::getCurrentTimestamp();
}

void MainMonitor::logError(const std::string& message) {
    LogManager::getInstance().systemError(message);
}

void MainMonitor::logInfo(const std::string& message) {
    LogManager::getInstance().systemInfo(message);
}

void MainMonitor::logDebug(const std::string& message) {
    LogManager::getInstance().systemDebug(message);
}

void MainMonitor::signalHandler(int signal) {
    (void)signal;
    // 简化实现
    shutdown_requested_ = true;
}

bool MainMonitor::daemonize() {
    // Fork子进程
    pid_t pid = fork();
    
    if (pid < 0) {
        // fork失败
        logError("fork失败: " + std::string(strerror(errno)));
        return false;
    }
    
    if (pid > 0) {
        // 父进程退出
        exit(0);
    }
    
    // 子进程继续执行
    
    // 创建新的会话
    if (setsid() < 0) {
        logError("setsid失败: " + std::string(strerror(errno)));
        return false;
    }
    
    // 忽略SIGHUP信号
    signal(SIGHUP, SIG_IGN);
    
    // 再次fork，确保daemon进程不会重新获得控制终端
    pid = fork();
    
    if (pid < 0) {
        logError("第二次fork失败: " + std::string(strerror(errno)));
        return false;
    }
    
    if (pid > 0) {
        // 第一个子进程退出
        exit(0);
    }
    
    // 更改工作目录到根目录
    if (chdir("/") < 0) {
        logError("chdir失败: " + std::string(strerror(errno)));
        return false;
    }
    
    // 设置文件掩码
    umask(0);
    
    // 关闭标准文件描述符
    close(STDIN_FILENO);
    close(STDOUT_FILENO);
    close(STDERR_FILENO);
    
    // 重定向标准文件描述符到/dev/null
    int null_fd = open("/dev/null", O_RDWR);
    if (null_fd >= 0) {
        dup2(null_fd, STDIN_FILENO);
        dup2(null_fd, STDOUT_FILENO);
        dup2(null_fd, STDERR_FILENO);
        if (null_fd > STDERR_FILENO) {
            close(null_fd);
        }
    }
    
    return true;
}

bool MainMonitor::createPidFile() {
    if (config_.pid_file.empty()) {
        return false;
    }
    
    try {
        // 创建PID文件目录（如果不存在）
        size_t pos = config_.pid_file.find_last_of('/');
        if (pos != std::string::npos) {
            std::string dir = config_.pid_file.substr(0, pos);
            if (!dir.empty()) {
                std::string mkdir_cmd = "mkdir -p " + dir;
                if (system(mkdir_cmd.c_str()) != 0) {
                    LogManager::getInstance().systemWarning("无法创建PID文件目录: " + dir);
                    return false;
                }
            }
        }
        
        // 写入PID文件
        std::ofstream pid_file(config_.pid_file);
        if (!pid_file.is_open()) {
            LogManager::getInstance().systemWarning("无法创建PID文件: " + config_.pid_file);
            return false;
        }
        
        pid_file << getpid() << std::endl;
        pid_file.close();
        
        LogManager::getInstance().systemInfo("PID文件已创建: " + config_.pid_file + " (PID: " + std::to_string(getpid()) + ")");
        return true;
        
    } catch (const std::exception& e) {
        LogManager::getInstance().systemWarning("创建PID文件时发生异常: " + std::string(e.what()));
        return false;
    }
}

void MainMonitor::removePidFile() {
    if (!config_.pid_file.empty()) {
        if (unlink(config_.pid_file.c_str()) == 0) {
            LogManager::getInstance().systemInfo("PID文件已删除: " + config_.pid_file);
        } else {
            LogManager::getInstance().systemWarning("无法删除PID文件: " + config_.pid_file);
        }
    }
}

// ConfigManager实现
ConfigManager& ConfigManager::getInstance() {
    static ConfigManager instance;
    return instance;
}

bool ConfigManager::loadConfig(const std::string& config_file, MonitorConfig& config) {
    // 不再先设置默认配置
    // config = getDefaultConfig();
    
    std::ifstream file(config_file);
    if (!file.is_open()) {
        // 配置文件不存在，尝试创建默认配置文件
        std::cout << "配置文件不存在，正在创建默认配置文件: " << config_file << std::endl;
        if (createDefaultConfigFile(config_file)) {
            std::cout << "默认配置文件创建成功" << std::endl;
        } else {
            std::cout << "警告：无法创建配置文件 " << config_file << "，使用内存中的默认配置" << std::endl;
        }
        return true;
    }
    
    std::string line;
    std::string current_section = "";
    
    while (std::getline(file, line)) {
        // 去除前后空格
        line.erase(0, line.find_first_not_of(" \t"));
        line.erase(line.find_last_not_of(" \t") + 1);
        
        // 跳过空行和注释
        if (line.empty() || line[0] == '#') {
            continue;
        }
        
        // 处理段落 [section]
        if (line[0] == '[' && line.back() == ']') {
            current_section = line.substr(1, line.length() - 2);
            continue;
        }
        
        // 处理键值对
        size_t eq_pos = line.find('=');
        if (eq_pos == std::string::npos) {
            continue;
        }
        
        std::string key = line.substr(0, eq_pos);
        std::string value = line.substr(eq_pos + 1);
        
        // 去除键值对的前后空格
        key.erase(0, key.find_first_not_of(" \t"));
        key.erase(key.find_last_not_of(" \t") + 1);
        value.erase(0, value.find_first_not_of(" \t"));
        value.erase(value.find_last_not_of(" \t") + 1);
        
        // 解析不同段落的配置
        try {
            if (current_section == "monitor") {
                if (key == "enable_file_monitoring") {
                    config.enable_file_monitoring = parseBool(value);
                } else if (key == "enable_network_monitoring") {
                    config.enable_network_monitoring = parseBool(value);
                } else if (key == "enable_process_monitoring") {
                    config.enable_process_monitoring = parseBool(value);
                } else if (key == "update_interval_ms") {
                    config.update_interval_ms = std::stoi(value);
                } else if (key == "max_files_per_thread") {
                    config.max_files_per_thread = std::stoi(value);
                }
            } else if (current_section == "daemon") {
                if (key == "daemon_mode") {
                    config.daemon_mode = parseBool(value);
                } else if (key == "pid_file") {
                    config.pid_file = value;
                }
            } else if (current_section == "api") {
                if (key == "api_port") {
                    config.api_port = std::stoi(value);
                } else if (key == "bind_address") {
                    config.bind_address = value;
                } else if (key == "api_auth_required") {
                    config.api_auth_required = parseBool(value);
                } else if (key == "allowed_api_clients") {
                    config.allowed_api_clients = value;
                }
            } else if (current_section == "logging") {
                if (key == "log_file") {
                    config.log_file = value;
                } else if (key == "log_level") {
                    config.log_level = value;
                } else if (key == "max_log_size") {
                    config.max_log_size = std::stoi(value);
                } else if (key == "max_log_files") {
                    config.max_log_files = std::stoi(value);
                }
            } else if (current_section == "file_monitor") {
                if (key == "use_fanotify_only") {
                    config.use_fanotify_only = parseBool(value);
                }
            } else if (current_section == "focus_process") {
                if (key == "enable_focus_process_monitoring") {
                    config.enable_focus_process_monitoring = parseBool(value);
                } else if (key == "calculate_file_entropy") {
                    config.calculate_file_entropy = parseBool(value);
                } else if (key == "focus_process_list") {
                    config.focus_process_list = value;
                }
            } else if (current_section == "protected_path") {
                if (key == "enable_protected_path_monitoring") {
                    config.enable_protected_path_monitoring = parseBool(value);
                } else if (key == "protected_paths") {
                    config.protected_paths = value;
                } else if (key == "terminate_violating_processes") {
                    config.terminate_violating_processes = parseBool(value);
                }
            } else if (current_section == "network_monitor") {
                if (key == "monitor_all_interfaces") {
                    config.monitor_all_interfaces = parseBool(value);
                } else if (key == "connection_update_interval") {
                    config.connection_update_interval = std::stoi(value);
                } else if (key == "use_netlink_diag") {
                    config.use_netlink_diag = parseBool(value);
                }
            } else if (current_section == "process_monitor") {
                if (key == "scan_interval_ms") {
                    config.process_scan_interval_ms = std::stoi(value);
                } else if (key == "full_scan_interval") {
                    config.full_scan_interval = std::stoi(value);
                } else if (key == "monitor_all_processes") {
                    config.monitor_all_processes = parseBool(value);
                } else if (key == "cpu_calc_window") {
                    config.cpu_calc_window = std::stoi(value);
                }
            } else if (current_section == "security") {
                if (key == "require_root") {
                    config.require_root = parseBool(value);
                }
            } else if (current_section == "performance") {
                if (key == "worker_threads") {
                    config.worker_threads = std::stoi(value);
                } else if (key == "max_event_queue_size") {
                    config.max_event_queue_size = std::stoi(value);
                } else if (key == "memory_limit_mb") {
                    config.memory_limit_mb = std::stoi(value);
                } else if (key == "enable_performance_stats") {
                    config.enable_performance_stats = parseBool(value);
                }
            } else if (current_section == "advanced") {
                if (key == "check_kernel_compatibility") {
                    config.check_kernel_compatibility = parseBool(value);
                } else if (key == "min_kernel_version") {
                    config.min_kernel_version = value;
                } else if (key == "enable_experimental_features") {
                    config.enable_experimental_features = parseBool(value);
                } else if (key == "debug_mode") {
                    config.debug_mode = parseBool(value);
                } else if (key == "verbose") {
                    config.verbose_mode = parseBool(value);
                }
            }
        } catch (const std::exception& e) {
            std::cerr << "警告：配置文件解析错误 [" << current_section << "] " 
                      << key << " = " << value << " (" << e.what() << ")" << std::endl;
        }
    }
    
    file.close();
    return true;
}

bool ConfigManager::parseBool(const std::string& value) {
    std::string lower_value = value;
    std::transform(lower_value.begin(), lower_value.end(), lower_value.begin(), ::tolower);
    
    if (lower_value == "true" || lower_value == "yes" || lower_value == "1" || lower_value == "on") {
        return true;
    } else if (lower_value == "false" || lower_value == "no" || lower_value == "0" || lower_value == "off") {
        return false;
    } else {
        throw std::invalid_argument("无效的布尔值: " + value);
    }
}

bool ConfigManager::parseCommandLine(int argc, char* argv[], MonitorConfig& config) {
    config = getDefaultConfig();
    
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "--help") == 0 || strcmp(argv[i], "-h") == 0) {
            // 帮助信息将在main函数中处理
            return false;
        } else if (strcmp(argv[i], "--version") == 0 || strcmp(argv[i], "-v") == 0) {
            // 版本信息将在main函数中处理
            return false;
        } else if (strcmp(argv[i], "--daemon") == 0 || strcmp(argv[i], "-d") == 0) {
            config.daemon_mode = true;
        } else if (strcmp(argv[i], "--port") == 0 || strcmp(argv[i], "-p") == 0) {
            if (i + 1 < argc) {
                try {
                    config.api_port = std::stoi(argv[++i]);
                } catch (const std::exception& e) {
                    std::cerr << "错误：无效的端口号 " << argv[i] << std::endl;
                    return false;
                }
            } else {
                std::cerr << "错误：--port 选项需要指定端口号" << std::endl;
                return false;
            }
        } else if (strcmp(argv[i], "--config") == 0 || strcmp(argv[i], "-c") == 0) {
            if (i + 1 < argc) {
                config.config_file = argv[++i];
            } else {
                std::cerr << "错误：--config 选项需要指定配置文件路径" << std::endl;
                return false;
            }
        } else if (strcmp(argv[i], "--log-file") == 0 || strcmp(argv[i], "-l") == 0) {
            if (i + 1 < argc) {
                config.log_file = argv[++i];
            } else {
                std::cerr << "错误：--log-file 选项需要指定日志文件路径" << std::endl;
                return false;
            }
        } else if (strcmp(argv[i], "--no-file-monitor") == 0) {
            config.enable_file_monitoring = false;
        } else if (strcmp(argv[i], "--no-network-monitor") == 0) {
            config.enable_network_monitoring = false;
        } else if (strcmp(argv[i], "--no-process-monitor") == 0) {
            config.enable_process_monitoring = false;
        } else if (strcmp(argv[i], "--debug") == 0) {
            config.debug_mode = true;
            config.log_level = "DEBUG";
        } else if (strcmp(argv[i], "--verbose") == 0) {
            config.verbose_mode = true;
            if (config.log_level == "INFO") {
                config.log_level = "DEBUG";  // 详细模式自动启用调试日志级别
            }
        } else if (argv[i][0] == '-') {
            std::cerr << "错误：未知选项 " << argv[i] << std::endl;
            std::cerr << "使用 --help 查看所有可用选项" << std::endl;
            return false;
        }
    }
    
    return true;
}

bool ConfigManager::parseCommandLineOverlay(int argc, char* argv[], MonitorConfig& config) {
    // 这个函数不会重置配置，只会覆盖命令行指定的选项
    
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "--help") == 0 || strcmp(argv[i], "-h") == 0) {
            // 帮助信息将在main函数中处理
            return false;
        } else if (strcmp(argv[i], "--version") == 0 || strcmp(argv[i], "-v") == 0) {
            // 版本信息将在main函数中处理
            return false;
        } else if (strcmp(argv[i], "--daemon") == 0 || strcmp(argv[i], "-d") == 0) {
            config.daemon_mode = true;
        } else if (strcmp(argv[i], "--port") == 0 || strcmp(argv[i], "-p") == 0) {
            if (i + 1 < argc) {
                try {
                    config.api_port = std::stoi(argv[++i]);
                } catch (const std::exception& e) {
                    std::cerr << "错误：无效的端口号 " << argv[i] << std::endl;
                    return false;
                }
            } else {
                std::cerr << "错误：--port 选项需要指定端口号" << std::endl;
                return false;
            }
        } else if (strcmp(argv[i], "--config") == 0 || strcmp(argv[i], "-c") == 0) {
            if (i + 1 < argc) {
                config.config_file = argv[++i];
            } else {
                std::cerr << "错误：--config 选项需要指定配置文件路径" << std::endl;
                return false;
            }
        } else if (strcmp(argv[i], "--log-file") == 0 || strcmp(argv[i], "-l") == 0) {
            if (i + 1 < argc) {
                config.log_file = argv[++i];
            } else {
                std::cerr << "错误：--log-file 选项需要指定日志文件路径" << std::endl;
                return false;
            }
        } else if (strcmp(argv[i], "--no-file-monitor") == 0) {
            config.enable_file_monitoring = false;
        } else if (strcmp(argv[i], "--no-network-monitor") == 0) {
            config.enable_network_monitoring = false;
        } else if (strcmp(argv[i], "--no-process-monitor") == 0) {
            config.enable_process_monitoring = false;
        } else if (strcmp(argv[i], "--debug") == 0) {
            config.debug_mode = true;
            config.log_level = "DEBUG";
        } else if (strcmp(argv[i], "--verbose") == 0) {
            config.verbose_mode = true;
            if (config.log_level == "INFO") {
                config.log_level = "DEBUG";  // 详细模式自动启用调试日志级别
            }
        } else if (argv[i][0] == '-') {
            std::cerr << "错误：未知选项 " << argv[i] << std::endl;
            std::cerr << "使用 --help 查看所有可用选项" << std::endl;
            return false;
        }
    }
    
    return true;
}

MonitorConfig ConfigManager::getDefaultConfig() {
    MonitorConfig config;
    config.log_file = "/var/log/psfsmon/psfsmon.log";
    config.config_file = "/etc/psfsmon/psfsmon.conf";
    return config;
}

bool ConfigManager::createDefaultConfigFile(const std::string& config_file) {
    // 创建目录
    size_t pos = config_file.find_last_of('/');
    if (pos != std::string::npos) {
        std::string dir = config_file.substr(0, pos);
        std::string mkdir_cmd = "mkdir -p " + dir;
        if (system(mkdir_cmd.c_str()) != 0) {
            std::cerr << "无法创建配置目录: " << dir << std::endl;
            return false;
        }
    }
    
    std::ofstream file(config_file);
    if (!file.is_open()) {
        std::cerr << "无法创建配置文件: " << config_file << std::endl;
        return false;
    }
    
    MonitorConfig default_config = getDefaultConfig();
    
    file << "# PSFSMON-L 配置文件" << std::endl;
    file << "# 此文件在首次运行时自动生成，您可以根据需要修改配置" << std::endl;
    file << std::endl;
    
    file << "[monitor]" << std::endl;
    file << "# 启用各种监控功能" << std::endl;
    file << "enable_file_monitoring = " << (default_config.enable_file_monitoring ? "true" : "false") << std::endl;
    file << "enable_network_monitoring = " << (default_config.enable_network_monitoring ? "true" : "false") << std::endl;
    file << "enable_process_monitoring = " << (default_config.enable_process_monitoring ? "true" : "false") << std::endl;
    file << "update_interval_ms = " << default_config.update_interval_ms << std::endl;
    file << "max_files_per_thread = " << default_config.max_files_per_thread << std::endl;
    file << std::endl;
    
    file << "[daemon]" << std::endl;
    file << "# 守护进程配置" << std::endl;
    file << "daemon_mode = " << (default_config.daemon_mode ? "true" : "false") << std::endl;
    file << "pid_file = " << default_config.pid_file << std::endl;
    file << std::endl;
    
    file << "[api]" << std::endl;
    file << "# API服务器配置" << std::endl;
    file << "api_port = " << default_config.api_port << std::endl;
    file << "bind_address = " << default_config.bind_address << std::endl;
    file << "api_auth_required = " << (default_config.api_auth_required ? "true" : "false") << std::endl;
    file << "allowed_api_clients = " << default_config.allowed_api_clients << std::endl;
    file << std::endl;
    
    file << "[logging]" << std::endl;
    file << "# 日志配置" << std::endl;
    file << "log_file = " << default_config.log_file << std::endl;
    file << "log_level = " << default_config.log_level << std::endl;
    file << "max_log_size = " << default_config.max_log_size << std::endl;
    file << "max_log_files = " << default_config.max_log_files << std::endl;
    file << std::endl;
    
    file << "[file_monitor]" << std::endl;
    file << "# Fanotify文件监控配置 - 直接监控受保护路径" << std::endl;
    file << "# 根据内核版本自动选择监控事件类型" << std::endl;
    
    file << "# 注意：监控策略现在基于内核版本自动选择" << std::endl;
    file << "# - 内核 5.4.18+：使用Fanotify高性能模式" << std::endl;
    file << "# - 内核 5.4.x：  使用Fanotify完整模式" << std::endl;
    file << "# - 内核 4.19.x： 使用Fanotify基础模式" << std::endl;
    file << "use_fanotify_only = " << (default_config.use_fanotify_only ? "true" : "false") << std::endl;
    file << std::endl;
    
    file << "[focus_process]" << std::endl;
    file << "# 重点进程监控配置" << std::endl;
    file << "enable_focus_process_monitoring = " << (default_config.enable_focus_process_monitoring ? "true" : "false") << std::endl;
    file << "calculate_file_entropy = " << (default_config.calculate_file_entropy ? "true" : "false") << std::endl;
    file << "focus_process_list = " << default_config.focus_process_list << std::endl;
    file << std::endl;
    
    file << "[protected_path]" << std::endl;
    file << "# 受保护路径监控配置" << std::endl;
    file << "enable_protected_path_monitoring = " << (default_config.enable_protected_path_monitoring ? "true" : "false") << std::endl;
    file << "protected_paths = " << default_config.protected_paths << std::endl;
    file << "terminate_violating_processes = " << (default_config.terminate_violating_processes ? "true" : "false") << std::endl;
    file << std::endl;
    
    file << "[network_monitor]" << std::endl;
    file << "# 网络监控配置" << std::endl;
    file << "monitor_all_interfaces = " << (default_config.monitor_all_interfaces ? "true" : "false") << std::endl;
    file << "connection_update_interval = " << default_config.connection_update_interval << std::endl;
    file << "use_netlink_diag = " << (default_config.use_netlink_diag ? "true" : "false") << std::endl;
    file << std::endl;
    
    file << "[process_monitor]" << std::endl;
    file << "# 进程监控配置" << std::endl;
    file << "scan_interval_ms = " << default_config.process_scan_interval_ms << std::endl;
    file << "full_scan_interval = " << default_config.full_scan_interval << std::endl;
    file << "monitor_all_processes = " << (default_config.monitor_all_processes ? "true" : "false") << std::endl;
    file << "cpu_calc_window = " << default_config.cpu_calc_window << std::endl;
    file << std::endl;
    
    file << "[security]" << std::endl;
    file << "# 安全配置" << std::endl;
    file << "require_root = " << (default_config.require_root ? "true" : "false") << std::endl;
    file << std::endl;
    
    file << "[performance]" << std::endl;
    file << "# 性能配置" << std::endl;
    file << "worker_threads = " << default_config.worker_threads << std::endl;
    file << "max_event_queue_size = " << default_config.max_event_queue_size << std::endl;
    file << "memory_limit_mb = " << default_config.memory_limit_mb << std::endl;
    file << "enable_performance_stats = " << (default_config.enable_performance_stats ? "true" : "false") << std::endl;
    file << std::endl;
    
    file << "[advanced]" << std::endl;
    file << "# 高级配置" << std::endl;
    file << "check_kernel_compatibility = " << (default_config.check_kernel_compatibility ? "true" : "false") << std::endl;
    file << "min_kernel_version = " << default_config.min_kernel_version << std::endl;
    file << "enable_experimental_features = " << (default_config.enable_experimental_features ? "true" : "false") << std::endl;
    file << "debug_mode = " << (default_config.debug_mode ? "true" : "false") << std::endl;
    file << "verbose = " << (default_config.verbose_mode ? "true" : "false") << std::endl;
    
    file.close();
    return true;
}

// LogManager实现
LogManager& LogManager::getInstance() {
    static LogManager instance;
    return instance;
}

bool LogManager::initialize(const std::string& log_file, LogLevel file_level, LogLevel console_level) {
    log_file_ = log_file;
    file_log_level_ = file_level;
    console_log_level_ = console_level;
    
    // 创建日志目录
    size_t pos = log_file.find_last_of('/');
    if (pos != std::string::npos) {
        std::string dir = log_file.substr(0, pos);
        mkdir(dir.c_str(), 0755);
    }
    
    log_stream_.open(log_file_, std::ios::app);
    initialized_ = log_stream_.is_open();
    
    if (initialized_) {
        log(LOG_INFO, LOG_SYSTEM, "日志系统初始化成功 - 文件级别: " + logLevelToString(file_level) + 
            ", 控制台级别: " + logLevelToString(console_level));
    }
    
    return initialized_;
}

void LogManager::log(LogLevel level, LogType type, const std::string& message) {
    if (!initialized_) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(LogManager_log_mutex_);
    
    std::string timestamp = getCurrentTimestamp();
    std::string level_str = logLevelToString(level);
    std::string type_str = logTypeToString(type);
    
    // 构建完整的日志消息
    std::string full_message = "[" + timestamp + "] [" + level_str + "] [" + type_str + "] " + message;
    
    // 输出到文件（总是输出，如果满足文件日志级别）
    if (shouldOutputToFile(level, type)) {
        log_stream_ << full_message << std::endl;
        log_stream_.flush();
    }
    
    // 输出到控制台（根据规则决定）
    if (shouldOutputToConsole(level, type)) {
        // 控制台输出简化格式（去掉类型标识，保持简洁）
        std::string console_message = "[" + timestamp + "] [" + level_str + "] " + message;
        std::cout << console_message << std::endl;
    }
}

void LogManager::log(LogLevel level, LogType type, const char* format, ...) {
    if (!initialized_) {
        return;
    }
    
    va_list args;
    va_start(args, format);
    
    char buffer[4096];
    vsnprintf(buffer, sizeof(buffer), format, args);
    
    va_end(args);
    
    log(level, type, std::string(buffer));
}

void LogManager::log(LogLevel level, const char* format, ...) {
    if (!initialized_) {
        return;
    }
    
    va_list args;
    va_start(args, format);
    
    char buffer[4096];
    vsnprintf(buffer, sizeof(buffer), format, args);
    
    va_end(args);
    
    log(level, LOG_GENERAL, std::string(buffer));
}

bool LogManager::shouldOutputToConsole(LogLevel level, LogType type) {
    // 首先检查日志级别
    if (level < console_log_level_) {
        return false;
    }
    
    // 根据日志类型决定是否输出到控制台
    switch (type) {
        case LOG_SYSTEM:
            // 系统信息：总是输出到控制台（如果满足级别要求）
            return true;
            
        case LOG_STRATEGY:
            // 策略信息：非daemon模式时输出到控制台
            return !daemon_mode_;
            
        case LOG_MONITOR:
        case LOG_NETWORK:
        case LOG_API:
            // 监控、网络、API信息：不输出到控制台
            return false;
            
        case LOG_GENERAL:
        default:
            // 一般信息：根据级别决定
            // 只有WARNING及以上级别的一般信息才输出到控制台
            return level >= LOG_WARNING;
    }
}

bool LogManager::shouldOutputToFile(LogLevel level, LogType type) {
    // 文件输出：所有类型的日志都输出到文件，只要满足文件日志级别
    (void)type;  // 防止未使用参数警告
    return level >= file_log_level_;
}

void LogManager::flush() {
    if (initialized_) {
        std::lock_guard<std::mutex> lock(LogManager_log_mutex_);
        log_stream_.flush();
        std::cout.flush();
    }
}

LogManager::~LogManager() {
    if (initialized_) {
        log_stream_.close();
    }
}

std::string LogManager::getCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return ss.str();
}

std::string LogManager::logLevelToString(LogLevel level) {
    switch (level) {
        case LOG_DEBUG:   return "DEBUG";
        case LOG_INFO:    return "INFO";
        case LOG_WARNING: return "WARN";
        case LOG_ERROR:   return "ERROR";
        case LOG_FATAL:   return "FATAL";
        default:          return "UNKNOWN";
    }
}

std::string LogManager::logTypeToString(LogType type) {
    switch (type) {
        case LOG_SYSTEM:   return "SYS";
        case LOG_MONITOR:  return "MON";
        case LOG_STRATEGY: return "STR";
        case LOG_NETWORK:  return "NET";
        case LOG_API:      return "API";
        case LOG_GENERAL:  return "GEN";
        default:           return "UNK";
    }
}

// Utils类实现 - 从utils.cpp合并
uint64_t Utils::getCurrentTimestamp() {
    return std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
}

std::string Utils::formatTimestamp(uint64_t timestamp) {
    std::time_t time = timestamp;
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time), "%Y-%m-%d %H:%M:%S");
    return ss.str();
}

std::string Utils::formatDuration(uint64_t seconds) {
    uint64_t days = seconds / 86400;
    uint64_t hours = (seconds % 86400) / 3600;
    uint64_t mins = (seconds % 3600) / 60;
    uint64_t secs = seconds % 60;
    
    std::stringstream ss;
    if (days > 0) {
        ss << days << "天 ";
    }
    if (hours > 0) {
        ss << hours << "小时 ";
    }
    if (mins > 0) {
        ss << mins << "分钟 ";
    }
    ss << secs << "秒";
    return ss.str();
}

std::string Utils::formatBytes(uint64_t bytes) {
    const char* units[] = {"B", "KB", "MB", "GB", "TB"};
    double size = bytes;
    int unit = 0;
    
    while (size >= 1024.0 && unit < 4) {
        size /= 1024.0;
        unit++;
    }
    
    std::stringstream ss;
    ss << std::fixed << std::setprecision(2) << size << " " << units[unit];
    return ss.str();
}

bool Utils::fileExists(const std::string& path) {
    struct stat buffer;
    return (stat(path.c_str(), &buffer) == 0);
}

bool Utils::directoryExists(const std::string& path) {
    struct stat buffer;
    return (stat(path.c_str(), &buffer) == 0 && S_ISDIR(buffer.st_mode));
}

std::string Utils::readFileContent(const std::string& path) {
    std::ifstream file(path);
    if (!file.is_open()) {
        return "";
    }
    
    std::stringstream buffer;
    buffer << file.rdbuf();
    return buffer.str();
}

std::string Utils::trim(const std::string& str) {
    size_t start = str.find_first_not_of(" \t\n\r");
    if (start == std::string::npos) {
        return "";
    }
    
    size_t end = str.find_last_not_of(" \t\n\r");
    return str.substr(start, end - start + 1);
}

std::vector<std::string> Utils::split(const std::string& str, char delimiter) {
    std::vector<std::string> result;
    std::stringstream ss(str);
    std::string item;
    
    while (std::getline(ss, item, delimiter)) {
        result.push_back(item);
    }
    
    return result;
}

pid_t Utils::getCurrentPid() {
    return getpid();
}

std::string Utils::getCurrentUser() {
    struct passwd* pw = getpwuid(getuid());
    return pw ? pw->pw_name : "unknown";
}

bool Utils::isRootUser() {
    return getuid() == 0;
}

bool Utils::processExists(pid_t pid) {
    if (pid <= 0) {
        return false;
    }

    // 方法1：使用kill(pid, 0)检查进程是否存在
    // 这个调用不会发送信号，只检查进程是否存在
    bool kill_check = (kill(pid, 0) == 0);
    
    // 方法2：检查/proc/[pid]目录是否存在
    std::string proc_path = "/proc/" + std::to_string(pid);
    bool proc_check = fileExists(proc_path);
    
    // 方法3：检查/proc/[pid]/stat文件是否存在且可读
    std::string stat_path = proc_path + "/stat";
    bool stat_check = fileExists(stat_path);
    
    // 综合判断：三种方法都通过才认为进程存在
    // 这样可以避免在高负载情况下的误判
    return kill_check && proc_check && stat_check;
}

// 新增：更严格的进程存在检查，用于死进程清理
bool Utils::processExistsStrict(pid_t pid) {
    if (pid <= 0) {
        return false;
    }

    // 严格检查：除了基本检查外，还要验证进程状态
    if (!processExists(pid)) {
        return false;
    }
    
    // 检查进程状态文件内容
    std::string stat_path = "/proc/" + std::to_string(pid) + "/stat";
    std::string stat_content = readFileContent(stat_path);
    if (stat_content.empty()) {
        return false;
    }
    
    // 解析进程状态，确保不是僵尸进程
    auto parts = split(stat_content, ' ');
    if (parts.size() >= 3) {
        std::string state = parts[2];
        // 如果是僵尸进程，也认为是"已死"
        if (state == "Z") {
            return false;
        }
    }
    
    return true;
}

std::string Utils::getProcessName(pid_t pid) {
    // 方法1：尝试从cmdline获取完整的可执行文件名（支持长名称）
    char cmdline_path[256];
    snprintf(cmdline_path, sizeof(cmdline_path), "/proc/%d/cmdline", pid);

    std::ifstream cmdline_file(cmdline_path);
    if (cmdline_file.is_open()) {
        std::string cmdline;
        std::getline(cmdline_file, cmdline, '\0'); // 读取到第一个null字符

        if (!cmdline.empty()) {
            // 提取可执行文件的基本名称（去掉路径）
            size_t last_slash = cmdline.find_last_of('/');
            std::string executable_name;
            if (last_slash != std::string::npos) {
                executable_name = cmdline.substr(last_slash + 1);
            } else {
                executable_name = cmdline;
            }

            // 确保名称长度不超过512字符（增加限制）
            if (executable_name.length() > 512) {
                executable_name = executable_name.substr(0, 512);
            }

            // 如果获取到了有效的可执行文件名，返回它
            if (!executable_name.empty()) {
                return trim(executable_name);
            }
        }
    }

    // 方法2：备选方案 - 使用comm文件（限制15字符，但更稳定）
    char comm_path[256];
    snprintf(comm_path, sizeof(comm_path), "/proc/%d/comm", pid);

    std::ifstream comm_file(comm_path);
    if (comm_file.is_open()) {
        std::string name;
        std::getline(comm_file, name);
        return trim(name);
    }

    return "unknown";
}

std::string Utils::getKernelVersion() {
    struct utsname buffer;
    if (uname(&buffer) != 0) {
        return "unknown";
    }
    return std::string(buffer.release);
}

uint32_t Utils::getCpuCores() {
    return std::thread::hardware_concurrency();
}

uint64_t Utils::getPageSize() {
    return sysconf(_SC_PAGESIZE);
}
