#include "api_server.h"
#include <sstream>

// JsonBuilder工具类实现

ApiServer::JsonBuilder::JsonBuilder() {
    json_.clear();
    first_element_stack_.push_back(true);
}

ApiServer::JsonBuilder& ApiServer::JsonBuilder::startObject() {
    addCommaIfNeeded();
    json_ += "{";
    first_element_stack_.push_back(true);
    return *this;
}

ApiServer::JsonBuilder& ApiServer::JsonBuilder::endObject() {
    json_ += "}";
    if (!first_element_stack_.empty()) {
        first_element_stack_.pop_back();
    }
    if (!first_element_stack_.empty()) {
        first_element_stack_.back() = false;
    }
    return *this;
}

ApiServer::JsonBuilder& ApiServer::JsonBuilder::startArray(const std::string& key) {
    addCommaIfNeeded();
    if (!key.empty()) {
        json_ += "\"" + escapeJsonString(key) + "\":[";
    } else {
        json_ += "[";
    }
    first_element_stack_.push_back(true);
    return *this;
}

ApiServer::JsonBuilder& ApiServer::JsonBuilder::endArray() {
    json_ += "]";
    if (!first_element_stack_.empty()) {
        first_element_stack_.pop_back();
    }
    if (!first_element_stack_.empty()) {
        first_element_stack_.back() = false;
    }
    return *this;
}

ApiServer::JsonBuilder& ApiServer::JsonBuilder::addString(const std::string& key, const std::string& value) {
    addCommaIfNeeded();
    json_ += "\"" + escapeJsonString(key) + "\":\"" + escapeJsonString(value) + "\"";
    if (!first_element_stack_.empty()) {
        first_element_stack_.back() = false;
    }
    return *this;
}

ApiServer::JsonBuilder& ApiServer::JsonBuilder::addNumber(const std::string& key, int64_t value) {
    addCommaIfNeeded();
    json_ += "\"" + escapeJsonString(key) + "\":" + std::to_string(value);
    if (!first_element_stack_.empty()) {
        first_element_stack_.back() = false;
    }
    return *this;
}

ApiServer::JsonBuilder& ApiServer::JsonBuilder::addNumber(const std::string& key, uint64_t value) {
    addCommaIfNeeded();
    json_ += "\"" + escapeJsonString(key) + "\":" + std::to_string(value);
    if (!first_element_stack_.empty()) {
        first_element_stack_.back() = false;
    }
    return *this;
}

ApiServer::JsonBuilder& ApiServer::JsonBuilder::addNumber(const std::string& key, double value) {
    addCommaIfNeeded();
    json_ += "\"" + escapeJsonString(key) + "\":" + std::to_string(value);
    if (!first_element_stack_.empty()) {
        first_element_stack_.back() = false;
    }
    return *this;
}

ApiServer::JsonBuilder& ApiServer::JsonBuilder::addBool(const std::string& key, bool value) {
    addCommaIfNeeded();
    json_ += "\"" + escapeJsonString(key) + "\":" + (value ? "true" : "false");
    if (!first_element_stack_.empty()) {
        first_element_stack_.back() = false;
    }
    return *this;
}

ApiServer::JsonBuilder& ApiServer::JsonBuilder::addStringValue(const std::string& value) {
    addCommaIfNeeded();
    json_ += "\"" + escapeJsonString(value) + "\"";
    if (!first_element_stack_.empty()) {
        first_element_stack_.back() = false;
    }
    return *this;
}

ApiServer::JsonBuilder& ApiServer::JsonBuilder::addNumberValue(int64_t value) {
    addCommaIfNeeded();
    json_ += std::to_string(value);
    if (!first_element_stack_.empty()) {
        first_element_stack_.back() = false;
    }
    return *this;
}

ApiServer::JsonBuilder& ApiServer::JsonBuilder::addNumberValue(uint64_t value) {
    addCommaIfNeeded();
    json_ += std::to_string(value);
    if (!first_element_stack_.empty()) {
        first_element_stack_.back() = false;
    }
    return *this;
}

ApiServer::JsonBuilder& ApiServer::JsonBuilder::addNumberValue(double value) {
    addCommaIfNeeded();
    json_ += std::to_string(value);
    if (!first_element_stack_.empty()) {
        first_element_stack_.back() = false;
    }
    return *this;
}

ApiServer::JsonBuilder& ApiServer::JsonBuilder::addKey(const std::string& key) {
    addCommaIfNeeded();
    json_ += "\"" + escapeJsonString(key) + "\":";
    if (!first_element_stack_.empty()) {
        first_element_stack_.back() = false;
    }
    return *this;
}

ApiServer::JsonBuilder& ApiServer::JsonBuilder::addObjectKey(const std::string& key) {
    addCommaIfNeeded();
    json_ += "\"" + escapeJsonString(key) + "\":{";
    first_element_stack_.push_back(true);
    return *this;
}

ApiServer::JsonBuilder& ApiServer::JsonBuilder::addNull(const std::string& key) {
    addCommaIfNeeded();
    json_ += "\"" + escapeJsonString(key) + "\":null";
    if (!first_element_stack_.empty()) {
        first_element_stack_.back() = false;
    }
    return *this;
}

ApiServer::JsonBuilder& ApiServer::JsonBuilder::addBoolValue(bool value) {
    addCommaIfNeeded();
    json_ += (value ? "true" : "false");
    if (!first_element_stack_.empty()) {
        first_element_stack_.back() = false;
    }
    return *this;
}

ApiServer::JsonBuilder& ApiServer::JsonBuilder::addNullValue() {
    addCommaIfNeeded();
    json_ += "null";
    if (!first_element_stack_.empty()) {
        first_element_stack_.back() = false;
    }
    return *this;
}

void ApiServer::JsonBuilder::addCommaIfNeeded() {
    if (!first_element_stack_.empty() && !first_element_stack_.back()) {
        json_ += ",";
    }
}

std::string ApiServer::JsonBuilder::escapeJsonString(const std::string& str) {
    std::string escaped;
    for (char c : str) {
        switch (c) {
            case '"': escaped += "\\\""; break;
            case '\\': escaped += "\\\\"; break;
            case '\b': escaped += "\\b"; break;
            case '\f': escaped += "\\f"; break;
            case '\n': escaped += "\\n"; break;
            case '\r': escaped += "\\r"; break;
            case '\t': escaped += "\\t"; break;
            default: escaped += c; break;
        }
    }
    return escaped;
}

std::string ApiServer::JsonBuilder::toString() const {
    return json_;
} 