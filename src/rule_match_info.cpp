/*
 * 规则匹配信息实现文件
 * 实现RuleMatchStats和RuleMatchManager的功能
 */

#include "rule_match_info.h"
#include <sstream>
#include <iomanip>
#include <ctime>
#include <set>

// RuleMatchStats方法实现
std::string RuleMatchStats::getSummary() const {
    std::ostringstream oss;
    
    oss << "Rule Match Summary:\n";
    oss << "  Total rule matches: " << all_catch_count << "\n";
    oss << "  Base rules - Delete: " << base_delete_rule_counts 
        << ", Rename: " << base_rename_rule_counts << "\n";
    oss << "  Change rules - Delete: " << change_delete_rule_counts
        << ", Rename: " << change_rename_rule_counts << "\n";
    oss << "  Patch rules - Delete: " << patch_delete_rule_counts
        << ", Rename: " << patch_rename_rule_counts << "\n";
    oss << "  File operations - Delete: " << all_delete_times
        << ", Rename: " << all_rename_times << "\n";
    oss << "  Last match time: " << last_rule_catch_ticket << "\n";
    
    return oss.str();
}

std::string RuleMatchStats::toJson() const {
    std::ostringstream oss;
    
    oss << "{";
    oss << "\"base_delete_rule_counts\":" << base_delete_rule_counts << ",";
    oss << "\"base_rename_rule_counts\":" << base_rename_rule_counts << ",";
    oss << "\"change_delete_rule_counts\":" << change_delete_rule_counts << ",";
    oss << "\"change_rename_rule_counts\":" << change_rename_rule_counts << ",";
    oss << "\"patch_delete_rule_counts\":" << patch_delete_rule_counts << ",";
    oss << "\"patch_rename_rule_counts\":" << patch_rename_rule_counts << ",";
    oss << "\"last_rule_catch_ticket\":" << last_rule_catch_ticket << ",";
    oss << "\"all_catch_count\":" << all_catch_count << ",";
    oss << "\"delete_rename_more_times_rule_count\":" << delete_rename_more_times_rule_count << ",";
    oss << "\"all_delete_times\":" << all_delete_times << ",";
    oss << "\"all_rename_times\":" << all_rename_times << ",";
    oss << "\"all_watcher_catch_count\":" << all_watcher_catch_count << ",";
    oss << "\"source_ext_name_count\":" << source_ext_name_count << ",";
    oss << "\"target_ext_name_count\":" << target_ext_name_count << ",";
    oss << "\"rename_ext_name_count\":" << rename_ext_name_count << ",";
    oss << "\"pid_info\":" << pid_info << ",";
    oss << "\"pid_value\":" << pid_value << ",";
    oss << "\"most_target_ext_name\":\"" << most_target_ext_name << "\",";
    oss << "\"most_rename_ext_name\":\"" << most_rename_ext_name << "\"";
    oss << "}";
    
    return oss.str();
}

// RuleMatchManager单例实现
RuleMatchManager& RuleMatchManager::getInstance() {
    static RuleMatchManager instance;
    return instance;
}

bool RuleMatchManager::initialize() {
    std::lock_guard<std::mutex> lock(rule_match_mutex_);
    process_rule_stats_.clear();
    return true;
}

void RuleMatchManager::recordRuleMatch(pid_t pid, int rule_level, const std::string& kernel_string) {
    std::lock_guard<std::mutex> lock(rule_match_mutex_);
    
    // 获取或创建进程的统计信息
    auto it = process_rule_stats_.find(pid);
    if (it == process_rule_stats_.end()) {
        it = process_rule_stats_.emplace(pid, std::make_shared<RuleMatchStats>()).first;
    }
    
    auto& stats = *it->second;
    
    // 增加规则匹配计数
    stats.incrementRuleCount(rule_level);
    
    // 如果有内核特征字符串，计算文件操作次数
    if (!kernel_string.empty()) {
        stats.calculateFileOperationsFromKernelString(kernel_string);
    }
    
    // 设置进程ID
    stats.pid_value = static_cast<uint32_t>(pid);
}

std::shared_ptr<RuleMatchStats> RuleMatchManager::getRuleMatchStats(pid_t pid) const {
    std::lock_guard<std::mutex> lock(rule_match_mutex_);
    
    auto it = process_rule_stats_.find(pid);
    if (it != process_rule_stats_.end()) {
        return it->second;
    }
    
    return nullptr;
}

std::vector<std::pair<pid_t, RuleMatchStats>> RuleMatchManager::getAllRuleMatchStats() const {
    std::lock_guard<std::mutex> lock(rule_match_mutex_);
    
    std::vector<std::pair<pid_t, RuleMatchStats>> result;
    result.reserve(process_rule_stats_.size());
    
    for (const auto& pair : process_rule_stats_) {
        result.emplace_back(pair.first, *pair.second);
    }
    
    return result;
}

void RuleMatchManager::cleanupDeadProcesses(const std::set<pid_t>& alive_pids) {
    std::lock_guard<std::mutex> lock(rule_match_mutex_);
    
    auto it = process_rule_stats_.begin();
    while (it != process_rule_stats_.end()) {
        if (alive_pids.find(it->first) == alive_pids.end()) {
            // 进程已不存在，清理其统计信息
            it = process_rule_stats_.erase(it);
        } else {
            ++it;
        }
    }
}

void RuleMatchManager::resetProcessStats(pid_t pid) {
    std::lock_guard<std::mutex> lock(rule_match_mutex_);
    
    auto it = process_rule_stats_.find(pid);
    if (it != process_rule_stats_.end()) {
        it->second->reset();
    }
}

void RuleMatchManager::resetAllStats() {
    std::lock_guard<std::mutex> lock(rule_match_mutex_);
    process_rule_stats_.clear();
}
