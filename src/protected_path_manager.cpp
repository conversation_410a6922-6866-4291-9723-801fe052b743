/*
 * PSFSMON-L 受保护路径管理器实现
 * 
 * 【需求10支持】
 * 本模块实现受保护路径管理功能，包括：
 * - 受保护路径的添加/删除/查询
 * - KILL模式和LOG模式的违规处理
 * - 线程安全的路径状态管理
 * - 与FileMonitor的受保护路径监控Group协作
 */

#include "file_monitor.h"
#include "main_monitor.h"
#include <sstream>
#include <algorithm>
#include <signal.h>
#include <cstring>

// ProtectedPathManager 实现
ProtectedPathManager& ProtectedPathManager::getInstance() {
    static ProtectedPathManager instance;
    return instance;
}

void ProtectedPathManager::addProtectedPath(const std::string& path) {
    std::lock_guard<std::mutex> lock(ProtectedPathManager_protected_paths_mutex_);
    protected_paths_.insert(path);
}

void ProtectedPathManager::removeProtectedPath(const std::string& path) {
    std::lock_guard<std::mutex> lock(ProtectedPathManager_protected_paths_mutex_);
    protected_paths_.erase(path);
}

bool ProtectedPathManager::isProtectedPath(const std::string& path) const {
    std::lock_guard<std::mutex> lock(ProtectedPathManager_protected_paths_mutex_);
    
    // 记录调试信息
    LogManager::getInstance().debug("[ProtectedPathManager] 检查路径: " + path);
    LogManager::getInstance().debug("[ProtectedPathManager] 当前受保护路径数量: " + std::to_string(protected_paths_.size()));
    
    // 检查直接匹配
    if (protected_paths_.find(path) != protected_paths_.end()) {
        LogManager::getInstance().debug("[ProtectedPathManager] 直接匹配成功: " + path);
        return true;
    }
    
    // 检查路径前缀匹配
    for (const auto& protected_path : protected_paths_) {
        LogManager::getInstance().debug("[ProtectedPathManager] 检查前缀匹配: " + path + " vs " + protected_path);
        if (isPathUnderProtected(path, protected_path)) {
            LogManager::getInstance().debug("[ProtectedPathManager] 前缀匹配成功: " + path + " 在 " + protected_path + " 下");
            return true;
        }
    }
    
    LogManager::getInstance().debug("[ProtectedPathManager] 路径不匹配: " + path);
    return false;
}

void ProtectedPathManager::handleViolation(const std::string& path, pid_t pid, const std::string& process_name) {
    std::string message = "受保护路径违规: 进程 " + process_name + " (PID: " +
                         std::to_string(pid) + ") 尝试访问受保护路径: " + path;

    LogManager::getInstance().warning("[ProtectedPathManager] " + message);
    LogManager::getInstance().debug(std::string("[ProtectedPathManager] 当前模式: ") + (terminate_violating_processes_ ? "KILL" : "LOG"));

    // 记录违规统计
    violation_count_++;
    last_violation_time_ = time(nullptr);

    if (terminate_violating_processes_) {
        // KILL模式：快速响应机制
        LogManager::getInstance().info("[ProtectedPathManager] 执行KILL模式 - 尝试终止进程 " +
                                     process_name + " (PID: " + std::to_string(pid) + ")");

        // 先尝试SIGTERM，给进程清理机会
        if (kill(pid, SIGTERM) == 0) {
            LogManager::getInstance().info("[ProtectedPathManager] 发送SIGTERM信号到违规进程 " +
                                         process_name + " (PID: " + std::to_string(pid) + ")");

            // 等待100ms后检查进程是否还存在
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            if (kill(pid, 0) == 0) {
                // 进程仍存在，使用SIGKILL强制终止
                if (kill(pid, SIGKILL) == 0) {
                    LogManager::getInstance().info("[ProtectedPathManager] 强制终止违规进程 " +
                                                 process_name + " (PID: " + std::to_string(pid) + ")");
                } else {
                    LogManager::getInstance().error("[ProtectedPathManager] 强制终止违规进程失败: " +
                                                  std::string(strerror(errno)));
                }
            } else {
                LogManager::getInstance().info("[ProtectedPathManager] 违规进程已自行退出: " +
                                             process_name + " (PID: " + std::to_string(pid) + ")");
            }
        } else {
            LogManager::getInstance().error("[ProtectedPathManager] 终止违规进程失败: " +
                                          std::string(strerror(errno)));
        }
    } else {
        // LOG模式：仅记录日志
        LogManager::getInstance().info("[ProtectedPathManager] LOG模式 - 记录违规行为: " + message);
    }
}

std::vector<std::string> ProtectedPathManager::getProtectedPaths() const {
    std::lock_guard<std::mutex> lock(ProtectedPathManager_protected_paths_mutex_);
    return std::vector<std::string>(protected_paths_.begin(), protected_paths_.end());
}

void ProtectedPathManager::loadFromConfig(const std::string& config_str) {
    std::lock_guard<std::mutex> lock(ProtectedPathManager_protected_paths_mutex_);
    
    if (config_str.empty()) {
        return;
    }
    
    // 解析配置字符串，格式: "path1,path2,path3"
    std::istringstream iss(config_str);
    std::string token;
    
    while (std::getline(iss, token, ',')) {
        token = Utils::trim(token);
        if (!token.empty()) {
            protected_paths_.insert(token);
        }
    }
}

void ProtectedPathManager::clear() {
    std::lock_guard<std::mutex> lock(ProtectedPathManager_protected_paths_mutex_);
    protected_paths_.clear();
}

bool ProtectedPathManager::reloadConfig(const std::string& config_str, bool terminate_violating_processes) {
    LogManager::getInstance().info("[ProtectedPathManager] 开始重新加载配置...");

    try {
        std::lock_guard<std::mutex> lock(ProtectedPathManager_protected_paths_mutex_);
        
        // 清空现有配置
        protected_paths_.clear();
        
        // 重新加载路径配置
        if (!config_str.empty()) {
            // 解析配置字符串，格式: "path1,path2,path3"
            std::istringstream iss(config_str);
            std::string token;
            
            while (std::getline(iss, token, ',')) {
                token = Utils::trim(token);
                if (!token.empty()) {
                    protected_paths_.insert(token);
                }
            }
        }
        
        // 更新终止违规进程设置
        terminate_violating_processes_ = terminate_violating_processes;
        
        LogManager::getInstance().info("[ProtectedPathManager] 配置重新加载成功 - 受保护路径数量: " + 
                                      std::to_string(protected_paths_.size()) + 
                                      ", 终止违规进程: " + (terminate_violating_processes ? "是" : "否"));
        
        return true;
        
    } catch (const std::exception& e) {
        LogManager::getInstance().error("[ProtectedPathManager] 配置重新加载时发生异常: " + std::string(e.what()));
        return false;
    }
}

void ProtectedPathManager::setTerminateViolatingProcesses(bool terminate) {
    terminate_violating_processes_ = terminate;
    LogManager::getInstance().debug(std::string("[ProtectedPathManager] 设置终止违规进程模式: ") + (terminate ? "KILL" : "LOG"));
}

bool ProtectedPathManager::shouldTerminateViolatingProcesses() const {
    return terminate_violating_processes_;
}

bool ProtectedPathManager::isPathUnderProtected(const std::string& path, const std::string& protected_path) const {
    // 检查path是否在protected_path目录下
    if (path.length() <= protected_path.length()) {
        return false;
    }
    
    // 检查前缀匹配
    if (path.substr(0, protected_path.length()) != protected_path) {
        return false;
    }
    
    // 确保是目录边界
    char separator = path[protected_path.length()];
    return separator == '/' || separator == '\0';
} 