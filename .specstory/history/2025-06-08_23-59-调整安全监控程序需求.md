<!-- Generated by SpecStory -->

# 调整安全监控程序需求 (2025-06-09 07:59:19)

_**User**_

下面是你上一个会话任务，但是中间异常中断了，请继续相关工作。

当前项目目录下的代码，主体结构已经完成，功能比较完整和正确。
但是，我的需求发生了重大变化，当前项目虽然主体依然可用，但是细节调整也是非常大的，请按下面的描述和需求变更，梳理和更改当前的程序代码。
我需要你核对下面的需求，特别仔细的检查和细化文件监控能力，更改部份已经完成的功能代码，以体现最新版本的需求变更。
所有需求如下（包括更改的需求）：
1、我需要编写一个运行在Linux平台下面的安全监控程序；
2、这个程序的主要功能是实时监控所有系统中所运行的进程并收集它们的状态，并具备汇报这些信息的能力；
3、这个程序需要以C++语言进行编写，低层的库可以使用C语言。C++语言的标准，由于系统编译器的版本问题，要控制在可以支持C++11标准的状态；
4、这个程序可能需要在不同的CPU上运行，例如x64/amd64, aarch64/arm64等等，但是不会跨平台编译，也就是说，它一定会在所在的平台下，用本地编译器进行编译；
5、这个程序要在启动时收集所有的已有进程的信息，管理它们，具备形成进程树的能力；
6、针对每个进程，要可以管理它创建或clone的所有线程、子线程；
7、收集的信息要归属并管理在进程所属并管理的每个线程下面，但是要在进程这一层次有一个汇总；
8、收集的信息主要包括3类，1是进程的基本信息例如名称、CPU使用状态、内存使用状态、路径等等；2是在一般情况下，不再收集进程的每个线程所操作的文件信息，也不再区分是进程的哪个线程操作了什么文件，不再需要通过各种方式找到文件操作的具体线程；3是进程的基本网络操作信息，例如打开了多少连接，连接了什么IP地址，每个IP收发的基本数据量是多少、整体收发数据量等等；
9、因为OS的限制，我们不能使用eBPF机制来收集这些信息；这个要求非常重要；
10、我们要使用Fanotify机制来进行特殊情况下的文件信息的获取，不再需要整合Inotify机制到本程序中，已有的整合要全部去除；Fanotify机制要根据OS的支持能力不同，尽可能多的监控文件变化操作，特别是读、写、删除和重命名操作，包括目录删除，相关的支持文档，在项目目录下的目录中，你可以自行查阅；
11、对于进程信息、网络通信信息，你可以使用合适的方法，但是要避免依赖其它大规模的底层依赖库，因为我们的操作系统版本并不高，而且可能是多个CPU类型的，它的基础版本只有5.4.18，可能很多功能都不具备，例如Sysdig和它所使用的一些基础库是无法编译通过的；
12、程序可以直接执行，以直观的方式进行输出，也可以以服务的方式进行安装和执行，通过对外接口形式进行输出；
13、程序对外的接口，可以向调用者灵活的提供所输出的信息，例如进程树、每个进程的信息汇总，每个进程的详细信息例如它所管理的每个线程的所有的文件的操作序列字符串等等；
14、每个进程不再管理所属的每个线程的文件信息；
15、程序要支持CMake和标准Makefile这2种编译方式，或者使用auto config系列工具编译；
16、支持忽略进程设置，被设置为忽略进程的，不再监控其相关信息（所有信息，包括文件操作信息）；
17、支持重点进程设置，被设置为重点进程的，除进程基本信息外，还要依靠和使用Fanotify机制，尽可能多的监控其文件操作相关信息，也只有被设置为重点进程，才会监控文件操作信息；监控重点是读、写操作，要在读操作前，计算文件熵值；要在写操作后，再次计算文件熵值；记录的文件熵值，按读/写区分，统一累加记录到进程级别进行汇总；如果系统支持，使用Fanotify机制获取删除的文件数量、重命名的文件数量、重命名的扩展名列表、操作文件路径历史记录等信息，记录到重点进程的相关数据结构中（也可以是类属性中）供查询；提供接口API支持这一功能；该功能是变化最大的需求；
18、支持特殊路径设置，被设置为特殊路径的，在发生针对特殊路径的写操作时，包括删除和重命名，相关的进程会被中止，并将相关记录在日志中；
19、保持程序的可读性，如果单个源代码文件过大，例如超过了1500行，则可以在逻辑上拆分它，并在设计文件中给出简单的说明；
20、同步更改相关的需求文档、测试代码和脚本。

---

_**Assistant**_



---

我需要先查看当前项目的目录结构，了解现有代码基础，然后根据您的需求变更来调整代码。

---

<details>
            <summary>Listed current directory • **23** results</summary>
        
| Name |
|-------|
| 📁 `refdocs` |
| 📁 `obj` |
| 📁 `.git` |
| 📁 `.idea` |
| 📁 `include` |
| 📁 `src` |
| 📁 `cmake-build-debug` |
| 📄 `CMakeLists.txt` |
| 📄 `Makefile` |
| 📄 `DEBUG_QUICK_START.md` |
| 📁 `docs` |
| 📄 `.gdbinit` |
| 📁 `scripts` |
| 📄 `.gitignore` |
| 📁 `test` |
| 📁 `ref_code` |
| 📄 `PROJECT_REVIEW_SUMMARY.md` |
| 📄 `README.md` |
| 📄 `REQUIREMENTS.md` |
| 📄 `API_REFERENCE.md` |
| 📁 `.specstory` |
| 📁 `config` |
| 📄 `.cursorindexingignore` |

</details>

---

让我查看主要的源代码目录结构：

---

<details>
            <summary>Listed directory src • **11** results</summary>
        
| Name |
|-------|
| 📄 `file_monitor.cpp` |
| 📄 `psfsmon_core.cpp` |
| 📄 `kernel_compat.cpp` |
| 📄 `main.cpp` |
| 📄 `main_monitor.cpp` |
| 📄 `process_monitor.cpp` |
| 📄 `api_server.cpp` |
| 📄 `network_monitor.cpp` |
| 📄 `path_protection.cpp` |
| 📄 `process_exclusion.cpp` |
| 📄 `utils.cpp` |

</details>

---

<details>
            <summary>Listed directory include • **9** results</summary>
        
| Name |
|-------|
| 📄 `psfsmon.h` |
| 📄 `process_monitor.h` |
| 📄 `file_monitor.h` |
| 📄 `kernel_compat.h` |
| 📄 `main_monitor.h` |
| 📄 `api_server.h` |
| 📄 `network_monitor.h` |
| 📄 `process_exclusion.h` |
| 📄 `path_protection.h` |

</details>

---

现在让我查看当前的需求文档，了解现有功能和需要变更的部分：

---

Read file: REQUIREMENTS.md

---

让我继续查看需求文档的其余部分：

---

Read file: REQUIREMENTS.md

---

现在让我查看当前的文件监控实现，了解需要修改的部分：

---

Read file: src/file_monitor.cpp

---

现在让我查看主监控文件中关于线程文件信息管理的部分：

---

<details>
            <summary>Grep search for "ThreadFileInfo" • **3** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///home/<USER>/coder/psfsmonL/REQUIREMENTS.md` | L169 | `struct ThreadFileInfo {` |
| `file:///home/<USER>/coder/psfsmonL/REQUIREMENTS.md` | L236 | `std::unordered_map<pid_t, std::vector<ThreadFileInfo>> thread_files;` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L9842 | `+ struct ThreadFileInfo {` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L9899 | `+     std::unordered_map<pid_t, std::vector<ThreadFileInfo>> thread_files;` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_01-25-linux-security-monitoring-program-requirements.md` | L236 | `+ struct ThreadFileInfo {` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_01-25-linux-security-monitoring-program-requirements.md` | L293 | `+     std::unordered_map<pid_t, std::vector<ThreadFileInfo>> thread_files;` |

</details>

---

<details>
            <summary>Grep search for "file_operations" • **9** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///home/<USER>/coder/psfsmonL/src/process_monitor.cpp` | L759 | `for (auto& file_op : thread->file_operations) {` |
| `file:///home/<USER>/coder/psfsmonL/src/process_monitor.cpp` | L780 | `thread->file_operations[thread->file_op_index] = new_op;` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L879 | `.startArray("file_operations");` |
| `file:///home/<USER>/coder/psfsmonL/src/psfsmon_core.cpp` | L11 | `auto it = std::find_if(file_operations.begin(), file_operations.end(),` |
| `file:///home/<USER>/coder/psfsmonL/src/psfsmon_core.cpp` | L16 | `if (it != file_operations.end()) {` |
| `file:///home/<USER>/coder/psfsmonL/src/psfsmon_core.cpp` | L23 | `file_operations[file_op_index] = op;` |
| `file:///home/<USER>/coder/psfsmonL/src/psfsmon_core.cpp` | L24 | `file_operations[file_op_index].access_count = 1;` |
| `file:///home/<USER>/coder/psfsmonL/src/psfsmon_core.cpp` | L38 | `if (file_operations[idx].file_id != 0) {  // 有效记录` |
| `file:///home/<USER>/coder/psfsmonL/src/psfsmon_core.cpp` | L39 | `result.push_back(file_operations[idx]);` |
| `file:///home/<USER>/coder/psfsmonL/src/psfsmon_core.cpp` | L60 | `if (file_operations[i].file_id != 0 &&` |
| `file:///home/<USER>/coder/psfsmonL/src/psfsmon_core.cpp` | L61 | `file_operations[i].last_access_time > 0 &&` |
| `file:///home/<USER>/coder/psfsmonL/src/psfsmon_core.cpp` | L62 | `(current_time - file_operations[i].last_access_time) > max_age_seconds) {` |
| `file:///home/<USER>/coder/psfsmonL/src/psfsmon_core.cpp` | L65 | `file_operations[i] = FileOperation();` |
| `file:///home/<USER>/coder/psfsmonL/REQUIREMENTS.md` | L171 | `std::vector<FileOperationRecord> file_operations; // 文件操作记录` |
| `file:///home/<USER>/coder/psfsmonL/REQUIREMENTS.md` | L561 | `"file_operations": [` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-26_10-58-%E9%A1%B9%E7%9B%AE%E5%AE%9E%E7%8E%B0%E7%BB%86%E8%8A%82%E4%B8%8Eapi%E6%8E%A5%E5%8F%A3%E6%A3%80%E6%9F%A5.md` | L518 | `823 |     auto file_operations = file_monitor->getProcessFileOperati` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-26_10-58-%E9%A1%B9%E7%9B%AE%E5%AE%9E%E7%8E%B0%E7%BB%86%E8%8A%82%E4%B8%8Eapi%E6%8E%A5%E5%8F%A3%E6%A3%80%E6%9F%A5.md` | L2043 | `{"pid":1,"operation_count":0,"file_operations":[]]}helloub22@helloub22-virtual-machine:~/coder/psfsmonL$` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-26_10-58-%E9%A1%B9%E7%9B%AE%E5%AE%9E%E7%8E%B0%E7%BB%86%E8%8A%82%E4%B8%8Eapi%E6%8E%A5%E5%8F%A3%E6%A3%80%E6%9F%A5.md` | L2084 | `"file_operations": []` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-06-08_23-19-%E9%9C%80%E6%B1%82%E5%8F%98%E6%9B%B4%E4%B8%8E%E7%A8%8B%E5%BA%8F%E4%BB%A3%E7%A0%81%E8%B0%83%E6%95%B4.md` | L236 | `-     std::vector<FileOperation> file_operations;` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-06-08_23-19-%E9%9C%80%E6%B1%82%E5%8F%98%E6%9B%B4%E4%B8%8E%E7%A8%8B%E5%BA%8F%E4%BB%A3%E7%A0%81%E8%B0%83%E6%95%B4.md` | L242 | `-         file_operations.resize(Constants::MAX_FILES_PER_THREAD);` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-24_22-57-linux%E5%AE%89%E5%85%A8%E7%9B%91%E6%8E%A7%E7%A8%8B%E5%BA%8F%E5%BC%80%E5%8F%91%E8%AE%A8%E8%AE%BA.md` | L295 | `+     std::vector<FileOperation> file_operations;` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-24_22-57-linux%E5%AE%89%E5%85%A8%E7%9B%91%E6%8E%A7%E7%A8%8B%E5%BA%8F%E5%BC%80%E5%8F%91%E8%AE%A8%E8%AE%BA.md` | L301 | `+         file_operations.resize(Constants::MAX_FILES_PER_THREAD);` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-24_22-57-linux%E5%AE%89%E5%85%A8%E7%9B%91%E6%8E%A7%E7%A8%8B%E5%BA%8F%E5%BC%80%E5%8F%91%E8%AE%A8%E8%AE%BA.md` | L1657 | `+     auto it = std::find_if(file_operations.begin(), file_operations.end(),` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-24_22-57-linux%E5%AE%89%E5%85%A8%E7%9B%91%E6%8E%A7%E7%A8%8B%E5%BA%8F%E5%BC%80%E5%8F%91%E8%AE%A8%E8%AE%BA.md` | L1662 | `+     if (it != file_operations.end()) {` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-24_22-57-linux%E5%AE%89%E5%85%A8%E7%9B%91%E6%8E%A7%E7%A8%8B%E5%BA%8F%E5%BC%80%E5%8F%91%E8%AE%A8%E8%AE%BA.md` | L1669 | `+         file_operations[file_op_index] = op;` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-24_22-57-linux%E5%AE%89%E5%85%A8%E7%9B%91%E6%8E%A7%E7%A8%8B%E5%BA%8F%E5%BC%80%E5%8F%91%E8%AE%A8%E8%AE%BA.md` | L1670 | `+         file_operations[file_op_index].access_count = 1;` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-24_22-57-linux%E5%AE%89%E5%85%A8%E7%9B%91%E6%8E%A7%E7%A8%8B%E5%BA%8F%E5%BC%80%E5%8F%91%E8%AE%A8%E8%AE%BA.md` | L1684 | `+         if (file_operations[idx].file_id != 0) {  // 有效记录` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-24_22-57-linux%E5%AE%89%E5%85%A8%E7%9B%91%E6%8E%A7%E7%A8%8B%E5%BA%8F%E5%BC%80%E5%8F%91%E8%AE%A8%E8%AE%BA.md` | L1685 | `+             result.push_back(file_operations[idx]);` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_01-25-linux-security-monitoring-program-requirements.md` | L238 | `+     std::vector<FileOperationRecord> file_operations; // 文件操作记录` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_01-25-linux-security-monitoring-program-requirements.md` | L525 | `+         "file_operations": [` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L295 | `+     std::vector<FileOperation> file_operations;` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L301 | `+         file_operations.resize(Constants::MAX_FILES_PER_THREAD);` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L1657 | `+     auto it = std::find_if(file_operations.begin(), file_operations.end(),` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L1662 | `+     if (it != file_operations.end()) {` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L1669 | `+         file_operations[file_op_index] = op;` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L1670 | `+         file_operations[file_op_index].access_count = 1;` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L1684 | `+         if (file_operations[idx].file_id != 0) {  // 有效记录` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L1685 | `+             result.push_back(file_operations[idx]);` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L9844 | `+     std::vector<FileOperationRecord> file_operations; // 文件操作记录` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L10131 | `+         "file_operations": [` |

</details>

---

现在让我查看头文件，了解当前的数据结构定义：

---

Read file: include/psfsmon.h

---

现在我需要查看file_monitor.h文件，了解当前的文件监控头文件定义：

---

Read file: include/file_monitor.h

---

让我继续查看file_monitor.h的其余部分：

---

Read file: include/file_monitor.h

---

现在让我查看当前的进程监控实现，了解线程文件管理的部分：

---

<details>
            <summary>Grep search for "addFileOperation" • **16** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///home/<USER>/coder/psfsmonL/include/process_monitor.h` | L56 | `void addFileOperationToFocusProcess(pid_t pid, const FileIdentifier& file_id, char op_char,` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L1394 | `process_monitor_->addFileOperation(event.pid, event.tid, file_id, op_char);` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L1453 | `process_monitor_->addFileOperation(delete_event.pid, delete_event.tid, file_id, 'P');` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L1484 | `process_monitor_->addFileOperation(rename_event.pid, rename_event.tid, file_id, 'N');` |
| `file:///home/<USER>/coder/psfsmonL/src/process_monitor.cpp` | L99 | `void ProcessMonitor::addFileOperationToThread(pid_t pid, pid_t tid, const FileOperation& operation) {` |
| `file:///home/<USER>/coder/psfsmonL/src/process_monitor.cpp` | L104 | `thread->addFileOperation(operation);` |
| `file:///home/<USER>/coder/psfsmonL/src/process_monitor.cpp` | L733 | `void ProcessMonitor::addFileOperation(pid_t pid, pid_t tid, const FileIdentifier& file_id, char op_char) {` |
| `file:///home/<USER>/coder/psfsmonL/src/psfsmon_core.cpp` | L7 | `void ThreadInfo::addFileOperation(const FileOperation& op) {` |
| `file:///home/<USER>/coder/psfsmonL/test/test_process_lifecycle.cpp` | L160 | `monitor_->addFileOperation(getpid(), getpid(), file_id, 'O');` |
| `file:///home/<USER>/coder/psfsmonL/test/test_process_lifecycle.cpp` | L161 | `monitor_->addFileOperation(getpid(), getpid(), file_id, 'R');` |
| `file:///home/<USER>/coder/psfsmonL/test/test_process_lifecycle.cpp` | L162 | `monitor_->addFileOperation(getpid(), getpid(), file_id, 'W');` |
| `file:///home/<USER>/coder/psfsmonL/test/rename_delete_guide.md` | L149 | `process_monitor_->addFileOperation(delete_event.pid, delete_event.tid, file_id, 'P');` |
| `file:///home/<USER>/coder/psfsmonL/test/rename_delete_guide.md` | L152 | `process_monitor_->addFileOperation(rename_event.pid, rename_event.tid, file_id, 'N');` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-27_04-01-%E6%B5%8B%E8%AF%95c%2B%2B%E6%96%87%E4%BB%B6%E7%BC%96%E8%AF%91%E4%B8%8E%E6%B8%85%E7%90%86.md` | L196 | `file_monitor.cpp:(.text+0x794e): undefined reference to `ProcessMonitor::addFileOperation(int, int, FileIdentifier const&, ch` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-27_04-01-%E6%B5%8B%E8%AF%95c%2B%2B%E6%96%87%E4%BB%B6%E7%BC%96%E8%AF%91%E4%B8%8E%E6%B8%85%E7%90%86.md` | L201 | `/usr/bin/ld: file_monitor.cpp:(.text+0x7f75): undefined reference to `ProcessMonitor::addFileOperation(int, int, FileIdentifi` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-27_04-01-%E6%B5%8B%E8%AF%95c%2B%2B%E6%96%87%E4%BB%B6%E7%BC%96%E8%AF%91%E4%B8%8E%E6%B8%85%E7%90%86.md` | L204 | `/usr/bin/ld: file_monitor.cpp:(.text+0x81ad): undefined reference to `ProcessMonitor::addFileOperation(int, int, FileIdentifi` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-06-08_23-19-%E9%9C%80%E6%B1%82%E5%8F%98%E6%9B%B4%E4%B8%8E%E7%A8%8B%E5%BA%8F%E4%BB%A3%E7%A0%81%E8%B0%83%E6%95%B4.md` | L246 | `-     void addFileOperation(const FileOperation& op);` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-06-08_23-19-%E9%9C%80%E6%B1%82%E5%8F%98%E6%9B%B4%E4%B8%8E%E7%A8%8B%E5%BA%8F%E4%BB%A3%E7%A0%81%E8%B0%83%E6%95%B4.md` | L618 | `-     void addFileOperationToThread(pid_t pid, pid_t tid, const FileOperation& operation);` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-06-08_23-19-%E9%9C%80%E6%B1%82%E5%8F%98%E6%9B%B4%E4%B8%8E%E7%A8%8B%E5%BA%8F%E4%BB%A3%E7%A0%81%E8%B0%83%E6%95%B4.md` | L626 | `-     void addFileOperation(pid_t pid, pid_t tid, const FileIdentifier& file_id, char op_char);` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-06-08_23-19-%E9%9C%80%E6%B1%82%E5%8F%98%E6%9B%B4%E4%B8%8E%E7%A8%8B%E5%BA%8F%E4%BB%A3%E7%A0%81%E8%B0%83%E6%95%B4.md` | L628 | `+     void addFileOperationToFocusProcess(pid_t pid, const FileIdentifier& file_id, char op_char,` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-24_22-57-linux%E5%AE%89%E5%85%A8%E7%9B%91%E6%8E%A7%E7%A8%8B%E5%BA%8F%E5%BC%80%E5%8F%91%E8%AE%A8%E8%AE%BA.md` | L305 | `+     void addFileOperation(const FileOperation& op);` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-24_22-57-linux%E5%AE%89%E5%85%A8%E7%9B%91%E6%8E%A7%E7%A8%8B%E5%BA%8F%E5%BC%80%E5%8F%91%E8%AE%A8%E8%AE%BA.md` | L679 | `+     void addFileOperationToThread(pid_t pid, pid_t tid, const FileOperation& operation);` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-24_22-57-linux%E5%AE%89%E5%85%A8%E7%9B%91%E6%8E%A7%E7%A8%8B%E5%BA%8F%E5%BC%80%E5%8F%91%E8%AE%A8%E8%AE%BA.md` | L1653 | `+ void ThreadInfo::addFileOperation(const FileOperation& op) {` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-24_22-57-linux%E5%AE%89%E5%85%A8%E7%9B%91%E6%8E%A7%E7%A8%8B%E5%BA%8F%E5%BC%80%E5%8F%91%E8%AE%A8%E8%AE%BA.md` | L3594 | `+ void ProcessMonitor::addFileOperationToThread(pid_t pid, pid_t tid, const FileOperation& operation) {` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-24_22-57-linux%E5%AE%89%E5%85%A8%E7%9B%91%E6%8E%A7%E7%A8%8B%E5%BA%8F%E5%BC%80%E5%8F%91%E8%AE%A8%E8%AE%BA.md` | L3599 | `+             thread->addFileOperation(operation);` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-24_22-57-linux%E5%AE%89%E5%85%A8%E7%9B%91%E6%8E%A7%E7%A8%8B%E5%BA%8F%E5%BC%80%E5%8F%91%E8%AE%A8%E8%AE%BA.md` | L4216 | `+         process_monitor_->addFileOperationToThread(event.pid, event.tid, op);` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_11-21-%E9%A1%B9%E7%9B%AE%E5%AE%9E%E7%8E%B0%E7%BB%86%E8%8A%82%E8%AE%A8%E8%AE%BA.md` | L1499 | `file_monitor.cpp:(.text+0x95a9): undefined reference to `ProcessMonitor::addFileOperation(int, int, FileIdentifier const&, char)'` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_11-21-%E9%A1%B9%E7%9B%AE%E5%AE%9E%E7%8E%B0%E7%BB%86%E8%8A%82%E8%AE%A8%E8%AE%BA.md` | L1501 | `file_monitor.cpp:(.text+0x97ea): undefined reference to `ProcessMonitor::addFileOperation(int, int, FileIdentifier const&, char)'` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_01-25-linux-security-monitoring-program-requirements.md` | L1583 | `+         process_monitor_->addFileOperation(event.pid, event.tid, file_id,` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_01-25-linux-security-monitoring-program-requirements.md` | L1621 | `+             process_monitor_->addFileOperation(complete_event.pid, complete_event.tid,` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_01-25-linux-security-monitoring-program-requirements.md` | L1654 | `现在让我检查进程监控器是否有addFileOperation方法，如果没有需要添加：` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_01-25-linux-security-monitoring-program-requirements.md` | L1659 | `<summary>Grep search for "addFileOperation" • **2** files</summary>` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_01-25-linux-security-monitoring-program-requirements.md` | L1663 | `| `file:///home/<USER>/coder/psfsmonL/include/psfsmon.h` | L80 | `void addFileOperation(const FileOperation& op);` |` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_01-25-linux-security-monitoring-program-requirements.md` | L1664 | `| `file:///home/<USER>/coder/psfsmonL/include/process_monitor.h` | L50 | `void addFileOperationToThread(pid_t pid, pid_t tid, const FileOperation& operation);` |` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_01-25-linux-security-monitoring-program-requirements.md` | L2147 | `752 |         process_monitor_->addFileOperation(event.pid, event.tid, file_id, op_char);` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_01-25-linux-security-monitoring-program-requirements.md` | L2153 | `src/../include/process_monitor.h:54:94: note:   initializing argument 4 of ‘void ProcessMonitor::addFileOperation(pi` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_01-25-linux-security-monitoring-program-requirements.md` | L2155 | `54 |     void addFileOperation(pid_t pid, pid_t tid, const FileIdentifier& file_id, FileOperation op_char);` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_01-25-linux-security-monitoring-program-requirements.md` | L2169 | `src/../include/process_monitor.h:54:94: note:   initializing argument 4 of ‘void ProcessMonitor::addFileOperation(pi` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_01-25-linux-security-monitoring-program-requirements.md` | L2171 | `54 |     void addFileOperation(pid_t pid, pid_t tid, const FileIdentifier& file_id, FileOperation op_char);` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_00-19-%E7%BC%96%E8%AF%91%E4%B8%8E%E6%B5%8B%E8%AF%95%E7%A8%8B%E5%BA%8F.md` | L1416 | `-         process_monitor_->addFileOperationToThread(event.pid, event.tid, op);` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_00-19-%E7%BC%96%E8%AF%91%E4%B8%8E%E6%B5%8B%E8%AF%95%E7%A8%8B%E5%BA%8F.md` | L1419 | `+             process_monitor_->addFileOperationToThread(event.pid, event.tid, op);` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L305 | `+     void addFileOperation(const FileOperation& op);` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L679 | `+     void addFileOperationToThread(pid_t pid, pid_t tid, const FileOperation& operation);` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L1653 | `+ void ThreadInfo::addFileOperation(const FileOperation& op) {` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L3594 | `+ void ProcessMonitor::addFileOperationToThread(pid_t pid, pid_t tid, const FileOperation& operation) {` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L3599 | `+             thread->addFileOperation(operation);` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L4216 | `+         process_monitor_->addFileOperationToThread(event.pid, event.tid, op);` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L8261 | `-         process_monitor_->addFileOperationToThread(event.pid, event.tid, op);` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L8264 | `+             process_monitor_->addFileOperationToThread(event.pid, event.tid, op);` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L11189 | `+         process_monitor_->addFileOperation(event.pid, event.tid, file_id,` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L11227 | `+             process_monitor_->addFileOperation(complete_event.pid, complete_event.tid,` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L11260 | `现在让我检查进程监控器是否有addFileOperation方法，如果没有需要添加：` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L11265 | `<summary>Grep search for "addFileOperation" • **2** files</summary>` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L11269 | `| `file:///home/<USER>/coder/psfsmonL/include/psfsmon.h` | L80 | `void addFileOperation(const FileOperation& op);` |` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L11270 | `| `file:///home/<USER>/coder/psfsmonL/include/process_monitor.h` | L50 | `void addFileOperationToThread(pid_t pid, pid_t tid, const FileOperation& operation);` |` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L11753 | `752 |         process_monitor_->addFileOperation(event.pid, event.tid, file_id, op_char);` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L11759 | `src/../include/process_monitor.h:54:94: note:   initializing argument 4 of ‘void ProcessMonitor::addFileOperation(pi` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L11761 | `54 |     void addFileOperation(pid_t pid, pid_t tid, const FileIdentifier& file_id, FileOperation op_char);` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L11775 | `src/../include/process_monitor.h:54:94: note:   initializing argument 4 of ‘void ProcessMonitor::addFileOperation(pi` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L11777 | `54 |     void addFileOperation(pid_t pid, pid_t tid, const FileIdentifier& file_id, FileOperation op_char);` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-31_02-17-%E7%A7%BB%E5%8A%A8%E6%96%87%E4%BB%B6%E5%B9%B6%E7%BC%96%E8%AF%91%E6%B5%8B%E8%AF%95.md` | L6840 | `/home/<USER>/coder/psfsmonL/src/file_monitor.cpp:1429: undefined reference to `ProcessMonitor::addFileOperation(` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-31_02-17-%E7%A7%BB%E5%8A%A8%E6%96%87%E4%BB%B6%E5%B9%B6%E7%BC%96%E8%AF%91%E6%B5%8B%E8%AF%95.md` | L7001 | `+     static void addFileOperation(int, int, const void*, char) {}` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-31_02-17-%E7%A7%BB%E5%8A%A8%E6%96%87%E4%BB%B6%E5%B9%B6%E7%BC%96%E8%AF%91%E6%B5%8B%E8%AF%95.md` | L7608 | `/home/<USER>/coder/psfsmonL/src/file_monitor.cpp:1429: undefined reference to `ProcessMonitor::addFileOperation(int, int, FileIdentifier cons` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-31_02-17-%E7%A7%BB%E5%8A%A8%E6%96%87%E4%BB%B6%E5%B9%B6%E7%BC%96%E8%AF%91%E6%B5%8B%E8%AF%95.md` | L7611 | `/home/<USER>/coder/psfsmonL/src/file_monitor.cpp:1488: undefined reference to `ProcessMonitor::addFileOperation(int, int, FileIdentifier cons` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-31_02-17-%E7%A7%BB%E5%8A%A8%E6%96%87%E4%BB%B6%E5%B9%B6%E7%BC%96%E8%AF%91%E6%B5%8B%E8%AF%95.md` | L7613 | `/usr/bin/ld: /home/<USER>/coder/psfsmonL/src/file_monitor.cpp:1519: undefined reference to `ProcessMonitor::addFileOperation(int, int, FileId` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-31_02-17-%E7%A7%BB%E5%8A%A8%E6%96%87%E4%BB%B6%E5%B9%B6%E7%BC%96%E8%AF%91%E6%B5%8B%E8%AF%95.md` | L9963 | `file_monitor.cpp:(.text+0x87c2): undefined reference to `ProcessMonitor::addFileOperation(int, int, FileIdentifier const&, char)'` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-31_02-17-%E7%A7%BB%E5%8A%A8%E6%96%87%E4%BB%B6%E5%B9%B6%E7%BC%96%E8%AF%91%E6%B5%8B%E8%AF%95.md` | L9967 | `/usr/bin/ld: file_monitor.cpp:(.text+0x8de9): undefined reference to `ProcessMonitor::addFileOperation(int, int, FileIdentifier const&, char)'` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-31_02-17-%E7%A7%BB%E5%8A%A8%E6%96%87%E4%BB%B6%E5%B9%B6%E7%BC%96%E8%AF%91%E6%B5%8B%E8%AF%95.md` | L9969 | `/usr/bin/ld: file_monitor.cpp:(.text+0x9021): undefined reference to `ProcessMonitor::addFileOperation(int, int, FileIdentifier const&, char)'` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-31_07-23-%E5%AE%8C%E6%88%90%E6%B5%8B%E8%AF%95%E7%A8%8B%E5%BA%8F%E5%B9%B6%E8%A7%A3%E5%86%B3%E9%97%AE%E9%A2%98.md` | L1861 | `/usr/bin/ld: file_monitor.cpp:(.text+0x9be4): undefined reference to `ProcessMonitor::addFileOperation(int, int, FileIdentifier const&, char)'` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-31_07-23-%E5%AE%8C%E6%88%90%E6%B5%8B%E8%AF%95%E7%A8%8B%E5%BA%8F%E5%B9%B6%E8%A7%A3%E5%86%B3%E9%97%AE%E9%A2%98.md` | L1862 | `/usr/bin/ld: file_monitor.cpp:(.text+0x9cec): undefined reference to `ProcessMonitor::addFileOperation(int, int, FileIdentifier const&, char)'` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-31_07-23-%E5%AE%8C%E6%88%90%E6%B5%8B%E8%AF%95%E7%A8%8B%E5%BA%8F%E5%B9%B6%E8%A7%A3%E5%86%B3%E9%97%AE%E9%A2%98.md` | L1864 | `file_monitor.cpp:(.text+0x9f4f): undefined reference to `ProcessMonitor::addFileOperation(int, int, FileIdentifier const&, char)'` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-27_04-37-%E8%BF%9B%E7%A8%8B%E4%B8%8E%E7%BA%BF%E7%A8%8B%E7%9B%91%E6%8E%A7%E5%8A%9F%E8%83%BD%E6%A0%B8%E6%9F%A5.md` | L134 | `<summary>Grep search for "void ThreadInfo::addFileOperation" • **3** files</summary>` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-27_04-37-%E8%BF%9B%E7%A8%8B%E4%B8%8E%E7%BA%BF%E7%A8%8B%E7%9B%91%E6%8E%A7%E5%8A%9F%E8%83%BD%E6%A0%B8%E6%9F%A5.md` | L138 | `| `file:///home/<USER>/coder/psfsmonL/src/psfsmon_core.cpp` | L5 | `void ThreadInfo::addFileOperation(const FileOperation& op) {` |` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-27_04-37-%E8%BF%9B%E7%A8%8B%E4%B8%8E%E7%BA%BF%E7%A8%8B%E7%9B%91%E6%8E%A7%E5%8A%9F%E8%83%BD%E6%A0%B8%E6%9F%A5.md` | L139 | `⟪ 167 characters skipped ⟫AE%BA.md` | L1653 | `+ void ThreadInfo::addFileOperation(const FileOperation& op) {` |` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-27_04-37-%E8%BF%9B%E7%A8%8B%E4%B8%8E%E7%BA%BF%E7%A8%8B%E7%9B%91%E6%8E%A7%E5%8A%9F%E8%83%BD%E6%A0%B8%E6%9F%A5.md` | L140 | `⟪ 136 characters skipped ⟫B9%B6.md` | L1653 | `+ void ThreadInfo::addFileOperation(const FileOperation& op) {` |` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-27_04-37-%E8%BF%9B%E7%A8%8B%E4%B8%8E%E7%BA%BF%E7%A8%8B%E7%9B%91%E6%8E%A7%E5%8A%9F%E8%83%BD%E6%A0%B8%E6%9F%A5.md` | L418 | `+         monitor_->addFileOperation(getpid(), getpid(), file_id, 'O');` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-27_04-37-%E8%BF%9B%E7%A8%8B%E4%B8%8E%E7%BA%BF%E7%A8%8B%E7%9B%91%E6%8E%A7%E5%8A%9F%E8%83%BD%E6%A0%B8%E6%9F%A5.md` | L419 | `+         monitor_->addFileOperation(getpid(), getpid(), file_id, 'R');` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-27_04-37-%E8%BF%9B%E7%A8%8B%E4%B8%8E%E7%BA%BF%E7%A8%8B%E7%9B%91%E6%8E%A7%E5%8A%9F%E8%83%BD%E6%A0%B8%E6%9F%A5.md` | L420 | `+         monitor_->addFileOperation(getpid(), getpid(), file_id, 'W');` |

</details>

---

基于对当前代码的分析，我现在明确了需要根据您的需求变更进行的主要调整。让我按照需求来系统性地更改代码：

## 核心需求变更总结：
1. **不再收集进程每个线程的文件操作信息**
2. **不再区分是哪个线程操作了什么文件**
3. **去除Inotify机制整合，只使用Fanotify**
4. **只有重点进程才监控文件操作信息**
5. **文件操作监控要计算熵值**
6. **支持特殊路径设置，写操作时中止相关进程**

让我开始更新代码。首先修改主要的头文件：

---



---

让我查看具体的部分并更精确地修改：

---

Read file: include/psfsmon.h

---

现在我看到了代码结构。让我首先实现FocusProcessFileInfo的方法，然后更新进程监控头文件：

---

```diff
  // ThreadInfo 实现
  void ThreadInfo::addFileOperation(const FileOperation& op) {
-     std::lock_guard<std::mutex> lock(file_ops_mutex);
-     
-     // 查找是否已存在该文件ID的操作记录
-     auto it = std::find_if(file_operations.begin(), file_operations.end(),
-                           [&op](const FileOperation& existing) {
-                               return existing.file_id == op.file_id && existing.file_id != 0;
-                           });
-     
-     if (it != file_operations.end()) {
-         // 更新现有记录
-         it->operations += op.operations;
-         it->last_access_time = op.last_access_time;
-         it->access_count++;
-     } else {
-         // 使用循环缓冲区添加新记录
-         file_operations[file_op_index] = op;
-         file_operations[file_op_index].access_count = 1;
-         file_op_index = (file_op_index + 1) % Constants::MAX_FILES_PER_THREAD;
-     }
+     // 该方法已废弃，不再使用
+     // 文件操作现在只对重点进程在进程级别进行管理
+ }
+ 
+ void ThreadInfo::cleanupOldOperations(uint64_t max_age_seconds) {
+     // 该方法已废弃，不再使用
  }
  
  std::vector<FileOperation> ThreadInfo::getFileOperations() const {
-     std::lock_guard<std::mutex> lock(file_ops_mutex);
-     
-     std::vector<FileOperation> result;
-     result.reserve(Constants::MAX_FILES_PER_THREAD);
-     
-     // 从当前索引开始按时间顺序收集操作
-     for (size_t i = 0; i < Constants::MAX_FILES_PER_THREAD; ++i) {
-         size_t idx = (file_op_index + i) % Constants::MAX_FILES_PER_THREAD;
-         if (file_operations[idx].file_id != 0) {  // 有效记录
-             result.push_back(file_operations[idx]);
-         }
-     }
-     
-     // 按最后访问时间排序
-     std::sort(result.begin(), result.end(),
-               [](const FileOperation& a, const FileOperation& b) {
-                   return a.last_access_time > b.last_access_time;
-               });
-     
-     return result;
- }
- 
- void ThreadInfo::cleanupOldFileOperations(uint64_t max_age_seconds) {
-     std::lock_guard<std::mutex> lock(file_ops_mutex);
-     
-     uint64_t current_time = std::chrono::duration_cast<std::chrono::seconds>(
-         std::chrono::system_clock::now().time_since_epoch()).count();
-     
-     // 清理过期的文件操作记录
-     for (size_t i = 0; i < Constants::MAX_FILES_PER_THREAD; ++i) {
-         if (file_operations[i].file_id != 0 && 
-             file_operations[i].last_access_time > 0 &&
-             (current_time - file_operations[i].last_access_time) > max_age_seconds) {
-             
-             // 清零以标记为空闲
-             file_operations[i] = FileOperation();
-         }
-     }
+     // 该方法已废弃，返回空向量
+     return std::vector<FileOperation>();
  }
  
```

```diff
      rename_count++;
      
-     // 提取并记录扩展名
+     // 提取新路径的扩展名
      size_t dot_pos = new_path.find_last_of('.');
      if (dot_pos != std::string::npos && dot_pos < new_path.length() - 1) {
-         std::string extension = new_path.substr(dot_pos + 1);
-         
+         std::string extension = new_path.substr(dot_pos);
          // 检查扩展名是否已存在
-         auto it = std::find(rename_extensions.begin(), rename_extensions.end(), extension);
-         if (it == rename_extensions.end()) {
+         if (std::find(rename_extensions.begin(), rename_extensions.end(), extension) == rename_extensions.end()) {
              rename_extensions.push_back(extension);
-             
-             // 限制扩展名列表大小
+             // 保持扩展名列表在合理大小内
              if (rename_extensions.size() > Constants::MAX_EXTENSION_LIST) {
                  rename_extensions.erase(rename_extensions.begin());
```

```diff
  
  void FocusProcessFileInfo::recordPathOperation(const std::string& path) {
-     // 注意：此方法假设已经持有mutex锁
+     // 注意：此方法假设已经在锁保护下调用
      path_history.push_back(path);
      
-     // 限制路径历史大小
+     // 保持路径历史在合理大小内
      if (path_history.size() > Constants::MAX_PATH_HISTORY) {
          path_history.erase(path_history.begin());
```

```diff
      std::lock_guard<std::mutex> lock(mutex);
      
-     // 清理过长的路径历史（保留最近的一半）
+     // 保持路径历史和扩展名列表在合理大小内
      if (path_history.size() > Constants::MAX_PATH_HISTORY / 2) {
-         size_t keep_count = Constants::MAX_PATH_HISTORY / 2;
          path_history.erase(path_history.begin(), 
-                           path_history.end() - keep_count);
+                           path_history.begin() + (path_history.size() - Constants::MAX_PATH_HISTORY / 2));
      }
      
-     // 清理过长的扩展名列表（保留最近的一半）
      if (rename_extensions.size() > Constants::MAX_EXTENSION_LIST / 2) {
-         size_t keep_count = Constants::MAX_EXTENSION_LIST / 2;
          rename_extensions.erase(rename_extensions.begin(),
-                                rename_extensions.end() - keep_count);
+                                rename_extensions.begin() + (rename_extensions.size() - Constants::MAX_EXTENSION_LIST / 2));
      }
  } 
```

---

现在更新进程监控头文件，移除线程级文件操作管理，添加重点进程文件操作管理：

---

Read file: include/process_monitor.h

---

现在我需要更新进程监控实现文件，移除线程级文件操作管理，添加重点进程文件操作管理：

---

Read file: src/process_monitor.cpp

---

现在让我查看addFileOperation方法，需要用新的重点进程文件监控方法替换它：

---

Read file: src/process_monitor.cpp

---

现在我需要使用search_replace来精确替换旧的文件操作管理方法，改为重点进程文件监控方法：

---



---

现在替换旧的addFileOperation方法为重点进程文件操作管理方法：

---



---

现在需要移除performIncrementalUpdate中对过期文件操作的清理，因为不再管理线程级文件操作：

---



---

现在更新文件监控器，只对重点进程进行文件监控，并且集成熵值计算和特殊路径保护：

---

Read file: src/file_monitor.cpp

---

我需要更新文件监控器的事件处理部分，让它只对重点进程进行监控，并添加熵值计算和特殊路径保护。首先找到handleFileEvent方法：

---



---

现在需要修复handleMoveEvent中对addFileOperation的调用，改为使用新的重点进程文件操作方法：

---



---



---

现在需要修复FileMonitor::getProcessName方法，它在上面的代码中被调用但可能是静态方法调用错误：

---

<details>
            <summary>Grep search for "std::string FileMonitor::getProcessName" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L751 | `std::string FileMonitor::getProcessName(pid_t pid) {` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L751 | `std::string FileMonitor::getProcessName(pid_t pid) {` |

</details>

---



让我查看这个方法的定义：

---

Read file: src/file_monitor.cpp

---

我需要修复调用getProcessName的方式，因为它不是静态方法。让我修改handleFileEvent方法：

---



---

现在我需要在FileMonitorManager中添加getProcessNameFromPid方法：

---



---

现在需要在头文件中声明这个方法：

---



---

现在需要更新API服务器，移除对线程文件操作的支持，改为支持重点进程文件监控信息的查询。让我查看API服务器的相关部分：

---

<details>
            <summary>Grep search for "file_operations" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L879 | `.startArray("file_operations");` |

</details>

---

让我查看这个部分的上下文：

---

Read file: src/api_server.cpp

---

我需要更新这个API方法，改为支持重点进程文件监控信息。让我替换handleGetProcessFiles方法：

---



---

让我先查找这个方法的确切位置：

---

<details>
            <summary>Grep search for "handleGetProcessFiles" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L123 | `handleGetProcessFiles(req, resp);` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L831 | `void ApiServer::handleGetProcessFiles(const HttpRequest& request, HttpResponse& response) {` |

</details>

---

Read file: src/api_server.cpp

---

现在我可以精确替换这个方法：

---



---

现在需要添加重点进程管理和特殊路径管理的API接口。让我查看API服务器的路由注册部分：

---

<details>
            <summary>Grep search for "registerRoute" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L66 | `http_server_->registerRoute(HttpMethod::GET, "/api/health",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L71 | `http_server_->registerRoute(HttpMethod::GET, "/api/stats",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L77 | `http_server_->registerRoute(HttpMethod::GET, "/api/processes",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L82 | `http_server_->registerRoute(HttpMethod::GET, "/api/processes-list",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L87 | `http_server_->registerRoute(HttpMethod::GET, "/api/process-tree",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L93 | `http_server_->registerRoute(HttpMethod::GET, "/api/network",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L98 | `http_server_->registerRoute(HttpMethod::GET, "/api/network/stats",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L103 | `http_server_->registerRoute(HttpMethod::GET, "/api/network/listening",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L109 | `http_server_->registerRoute(HttpMethod::GET, "/api/process",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L115 | `http_server_->registerRoute(HttpMethod::GET, "/api/process/threads",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L121 | `http_server_->registerRoute(HttpMethod::GET, "/api/process/files",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L127 | `http_server_->registerRoute(HttpMethod::GET, "/api/exclusions",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L132 | `http_server_->registerRoute(HttpMethod::POST, "/api/exclusions",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L137 | `http_server_->registerRoute(HttpMethod::DELETE, "/api/exclusions",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L143 | `http_server_->registerRoute(HttpMethod::GET, "/api/protected-paths",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L148 | `http_server_->registerRoute(HttpMethod::POST, "/api/protected-paths",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L153 | `http_server_->registerRoute(HttpMethod::DELETE, "/api/protected-paths",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L159 | `http_server_->registerRoute(HttpMethod::GET, "/api/monitor/status",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L165 | `http_server_->registerRoute(HttpMethod::GET, "/api/config",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L170 | `http_server_->registerRoute(HttpMethod::PUT, "/api/config",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L176 | `http_server_->registerRoute(HttpMethod::GET, "/api/file/events",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L181 | `http_server_->registerRoute(HttpMethod::GET, "/api/file/stats",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L187 | `http_server_->registerRoute(HttpMethod::GET, "/api/realtime",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L1560 | `void HttpServer::registerRoute(HttpMethod method, const std::string& path, ApiHandler handler) {` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L66 | `http_server_->registerRoute(HttpMethod::GET, "/api/health",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L71 | `http_server_->registerRoute(HttpMethod::GET, "/api/stats",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L77 | `http_server_->registerRoute(HttpMethod::GET, "/api/processes",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L82 | `http_server_->registerRoute(HttpMethod::GET, "/api/processes-list",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L87 | `http_server_->registerRoute(HttpMethod::GET, "/api/process-tree",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L93 | `http_server_->registerRoute(HttpMethod::GET, "/api/network",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L98 | `http_server_->registerRoute(HttpMethod::GET, "/api/network/stats",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L103 | `http_server_->registerRoute(HttpMethod::GET, "/api/network/listening",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L109 | `http_server_->registerRoute(HttpMethod::GET, "/api/process",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L115 | `http_server_->registerRoute(HttpMethod::GET, "/api/process/threads",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L121 | `http_server_->registerRoute(HttpMethod::GET, "/api/process/files",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L127 | `http_server_->registerRoute(HttpMethod::GET, "/api/exclusions",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L132 | `http_server_->registerRoute(HttpMethod::POST, "/api/exclusions",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L137 | `http_server_->registerRoute(HttpMethod::DELETE, "/api/exclusions",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L143 | `http_server_->registerRoute(HttpMethod::GET, "/api/protected-paths",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L148 | `http_server_->registerRoute(HttpMethod::POST, "/api/protected-paths",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L153 | `http_server_->registerRoute(HttpMethod::DELETE, "/api/protected-paths",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L159 | `http_server_->registerRoute(HttpMethod::GET, "/api/monitor/status",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L165 | `http_server_->registerRoute(HttpMethod::GET, "/api/config",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L170 | `http_server_->registerRoute(HttpMethod::PUT, "/api/config",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L176 | `http_server_->registerRoute(HttpMethod::GET, "/api/file/events",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L181 | `http_server_->registerRoute(HttpMethod::GET, "/api/file/stats",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L187 | `http_server_->registerRoute(HttpMethod::GET, "/api/realtime",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L1560 | `void HttpServer::registerRoute(HttpMethod method, const std::string& path, ApiHandler handler) {` |

</details>

---

我需要添加重点进程管理的API路由。让我查看路由注册的部分：

---

Read file: src/api_server.cpp

---

现在我需要在路由注册部分添加重点进程管理和特殊路径管理的API。让我在合适的位置添加：

---



---

现在我需要在API服务器的末尾添加这些新的处理方法。让我查看文件的末尾部分：

---

Read file: src/api_server.cpp

---

现在我需要在API服务器中添加新的处理方法。让我在合适的位置添加这些方法：

---



---

现在需要在API服务器头文件中声明这些新方法：

---

Read file: include/api_server.h

---

我需要在API服务器头文件中添加新的方法声明：

---



---

现在需要在SpecialPathManager中添加getSpecialPaths方法，因为API中用到了但可能还没有实现：

---



---

看起来有重复了，让我检查一下：

---

<details>
            <summary>Grep search for "getSpecialPaths" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L201 | `std::vector<std::string> SpecialPathManager::getSpecialPaths() const {` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L206 | `std::vector<std::string> SpecialPathManager::getSpecialPaths() const {` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L201 | `std::vector<std::string> SpecialPathManager::getSpecialPaths() const {` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L206 | `std::vector<std::string> SpecialPathManager::getSpecialPaths() const {` |

</details>

---

确实有重复，让我删除重复的方法：

---



---

现在让我更新需求文档，反映这些重大的需求变更：

---

```diff
- # PSFSMON-L 功能需求文档
+ # PSFSMON-L 功能需求文档 (更新版本)
  
  ## 1. 项目概述
```

```diff
  
  ### 1.2 项目目标
- PSFSMON-L 是一个Linux系统安全监控程序，提供实时的进程、文件系统和网络监控功能。该程序主要功能是监控系统中所有运行的进程并收集它们的状态信息，包括文件操作、网络连接等行为分析。
+ PSFSMON-L 是一个Linux系统安全监控程序，提供实时的进程、文件系统和网络监控功能。该程序主要功能是监控系统中所有运行的进程并收集它们的状态信息，**重点关注重点进程的文件操作行为分析**。
  
  ### 1.3 应用场景
  - 系统安全监控
  - 恶意软件检测
- - 文件访问审计
+ - 重点进程文件访问审计
  - 进程行为分析
  - 合规性监控
```

```diff
  - **最低内核版本**: 5.4.18
  - **必需特性**: 
-   - Fanotify支持
-   - Inotify支持（如需要）
+   - **仅使用Fanotify支持**（不再使用Inotify）
    - /proc文件系统
    - /sys文件系统
```

```diff
    - 查询当前排除规则列表
  
- #### 3.1.4 进程数据结构
- ```cpp
- struct ProcessInfo {
-     pid_t pid;                    // 进程ID
-     pid_t ppid;                   // 父进程ID
-     std::string name;             // 进程名称
-     std::string cmdline;          // 命令行参数
-     std::string exe_path;         // 可执行文件路径
-     uid_t uid;                    // 用户ID
-     gid_t gid;                    // 组ID
-     
-     // 资源使用情况
-     double cpu_usage;             // CPU使用率
-     size_t memory_usage;          // 内存使用量（字节）
-     size_t virtual_memory;        // 虚拟内存大小
-     
-     // 时间信息
-     time_t start_time;            // 进程启动时间
-     time_t last_update;           // 最后更新时间
-     
-     // 状态信息
-     ProcessState state;           // 进程状态
-     int priority;                 // 进程优先级
-     int nice_value;               // Nice值
- };
- ```
- 
- ### 3.2 文件系统监控功能
- 
- #### 3.2.1 基础文件监控
+ #### 3.1.4 重点进程管理 (新增)
+ - **需求ID**: REQ-PROC-004
+ - **描述**: 支持设置重点进程，只有重点进程才进行详细的文件监控
+ - **功能点**:
+   - 按进程ID设置重点进程
+   - 按进程名称设置重点进程
+   - 动态添加和删除重点进程
+   - 查询当前重点进程列表
+   - 重点进程文件操作详细监控
+ 
+ ### 3.2 文件系统监控功能 (重大变更)
+ 
+ #### 3.2.1 仅使用Fanotify机制
  - **需求ID**: REQ-FILE-001
- - **描述**: 监控文件系统的访问和修改操作
+ - **描述**: **完全移除Inotify机制，仅使用Fanotify进行文件监控**
  - **功能点**:
-   - 使用Fanotify和Inotify机制协同监控
-   - 记录文件操作序列
-   - 跟踪文件的打开、读取、写入、删除等操作
-   - 为每个文件创建唯一标识符
- 
- #### 3.2.2 路径保护功能
+   - 使用Fanotify机制监控文件系统操作
+   - 根据内核版本自动选择可用的Fanotify事件类型
+   - 支持文件的打开、读取、写入、删除、重命名等操作监控
+   - **不再区分是进程的哪个线程操作了什么文件**
+ 
+ #### 3.2.2 重点进程文件监控 (核心变更)
  - **需求ID**: REQ-FILE-002
- - **描述**: 保护关键系统文件和目录，防止恶意访问
+ - **描述**: **只有被设置为重点进程的进程才会监控其文件操作信息**
  - **功能点**:
-   - 支持三种保护级别：
-     - `read_only`: 只读保护，禁止写入、删除
-     - `write_protect`: 写保护，禁止删除、重命名
-     - `full_protect`: 完全保护，禁止所有操作
-   - 支持递归保护（保护目录及其子目录）
-   - 支持违规进程终止功能
-   - 动态添加和删除保护规则
-   - 查询当前保护规则列表
- 
- #### 3.2.3 监控机制设计
- **重要发现**: Fanotify在Linux 5.1+版本已支持文件删除和重命名监控：
- - `FAN_DELETE`: 文件删除事件（Linux 5.1+）
- - `FAN_MOVED_FROM`/`FAN_MOVED_TO`: 文件移动事件
- - `FAN_CREATE`: 文件创建事件
- 
- **监控策略**:
- - **主要使用Fanotify**: 利用其进程上下文信息和权限拦截能力
- - **Inotify作为补充**: 仅在Fanotify无法覆盖的场景下使用
- - **版本兼容性**: 检测内核版本，动态选择可用的事件类型
- 
- #### 3.2.4 文件数据结构
- ```cpp
- struct FileIdentifier {
-     dev_t device_id;              // 设备ID
-     ino_t inode_number;           // Inode号
-     uint64_t generation;          // 生成号（如果可用）
-     
-     std::string to_string() const {
-         return std::to_string(device_id) + ":" + std::to_string(inode_number);
-     }
- };
- 
- enum FileOperation {
-     OPEN = 'O',                   // 打开文件
-     READ = 'R',                   // 读取文件
-     WRITE = 'W',                  // 写入文件
-     CLOSE = 'C',                  // 关闭文件
-     DELETE = 'D',                 // 删除文件
-     RENAME = 'N',                 // 重命名文件
-     RENAME_DELETE = 'P',          // 重命名删除（目标已存在）
-     TRUNCATE = 'T',               // 截断文件
-     CHMOD = 'M',                  // 修改权限
-     CHOWN = 'U'                   // 修改所有者
- };
- 
- struct FileOperationRecord {
-     FileIdentifier file_id;       // 文件唯一标识
-     std::string operation_sequence; // 操作序列字符串
-     time_t first_access;          // 首次访问时间
-     time_t last_access;           // 最后访问时间
-     size_t access_count;          // 访问次数
- };
- 
- struct ThreadFileInfo {
-     pthread_t thread_id;          // 线程ID
-     std::vector<FileOperationRecord> file_operations; // 文件操作记录
-     static const size_t MAX_FILES = 50; // 最大文件数量限制
-     
-     void add_operation(const FileIdentifier& file_id, FileOperation op);
-     void cleanup_old_records();  // 清理超过限制的旧记录
- };
- ```
- 
- ### 3.3 网络监控功能
+   - 重点进程文件操作监控重点是读、写操作
+   - **在读操作前，计算文件熵值**
+   - **在写操作后，再次计算文件熵值**
+   - 记录的文件熵值，按读/写区分，统一累加记录到进程级别
+   - 获取删除的文件数量、重命名的文件数量
+   - 记录重命名的扩展名列表
+   - 记录操作文件路径历史记录
+   - 提供接口API支持查询这些信息
+ 
+ #### 3.2.3 特殊路径保护功能 (新增)
+ - **需求ID**: REQ-FILE-003
+ - **描述**: 支持特殊路径设置，在发生针对特殊路径的写操作时中止相关进程
+ - **功能点**:
+   - 支持添加和删除特殊路径
+   - 监控对特殊路径的写操作（包括删除和重命名）
+   - **相关的进程会被中止**
+   - 将相关记录写入日志
+   - 提供API接口管理特殊路径
+ 
+ #### 3.2.4 文件熵值计算 (新增)
+ - **需求ID**: REQ-FILE-004
+ - **描述**: 为重点进程的文件操作计算Shannon熵值
+ - **功能点**:
+   - 实现Shannon熵值计算算法
+   - 读操作前计算文件熵值
+   - 写操作后计算文件熵值
+   - 按读/写操作分别累加熵值到进程级别
+   - 提供平均熵值计算
+   - 支持熵值统计查询
+ 
+ ### 3.3 网络监控功能 (保持不变)
  
  #### 3.3.1 网络连接监控
```

```diff
    - 统计收发数据量
  
- #### 3.3.2 网络数据结构
+ ### 3.4 数据结构设计 (重大调整)
+ 
+ #### 3.4.1 重点进程文件监控信息
  ```cpp
- struct NetworkConnection {
-     int socket_fd;                // 套接字文件描述符
-     int protocol;                 // 协议类型（TCP/UDP）
-     std::string local_addr;       // 本地地址
-     uint16_t local_port;          // 本地端口
-     std::string remote_addr;      // 远程地址
-     uint16_t remote_port;         // 远程端口
-     ConnectionState state;        // 连接状态
-     
-     // 流量统计
-     uint64_t bytes_sent;          // 发送字节数
-     uint64_t bytes_received;      // 接收字节数
-     uint64_t packets_sent;        // 发送包数
-     uint64_t packets_received;    // 接收包数
-     
-     time_t established_time;      // 连接建立时间
-     time_t last_activity;         // 最后活动时间
- };
- 
- struct ProcessNetworkInfo {
-     pid_t pid;                    // 进程ID
-     std::vector<NetworkConnection> connections; // 连接列表
-     
-     // 汇总统计
-     uint64_t total_bytes_sent;    // 总发送字节数
-     uint64_t total_bytes_received; // 总接收字节数
-     size_t active_connections;    // 活跃连接数
-     std::set<std::string> connected_ips; // 连接的IP地址集合
+ struct FocusProcessFileInfo {
+     // 文件熵值信息
+     double total_read_entropy;     // 读操作累计文件熵值
+     double total_write_entropy;    // 写操作累计文件熵值
+     size_t read_entropy_count;     // 读操作熵值计数
+     size_t write_entropy_count;    // 写操作熵值计数
+     
+     // 文件操作统计
+     size_t delete_count;           // 删除文件数量
+     size_t rename_count;           // 重命名文件数量
+     std::vector<std::string> rename_extensions;  // 重命名扩展名列表
+     std::vector<std::string> path_history;       // 操作文件路径历史记录
+     
+     mutable std::mutex mutex;      // 线程安全锁
+     
+     // 方法
+     void addReadEntropy(double entropy);
+     void addWriteEntropy(double entropy);
+     void recordDelete(const std::string& path);
+     void recordRename(const std::string& old_path, const std::string& new_path);
+     double getAverageReadEntropy() const;
+     double getAverageWriteEntropy() const;
+     void cleanup();
  };
  ```
  
- #### 3.3.3 网络监控实现方法
- - **套接字信息**: 通过/proc/net/tcp、/proc/net/udp等文件获取
- - **流量统计**: 通过/proc/net/dev或netlink套接字获取
- - **进程关联**: 通过/proc/[pid]/fd/目录关联套接字到进程
- 
- ### 3.4 数据管理和存储
- 
- #### 3.4.1 内存管理策略
+ #### 3.4.2 进程信息结构 (调整)
  ```cpp
- class ProcessDataManager {
- private:
-     std::unordered_map<pid_t, ProcessInfo> processes;
-     std::unordered_map<pid_t, std::vector<ThreadFileInfo>> thread_files;
-     std::unordered_map<pid_t, ProcessNetworkInfo> network_info;
-     
-     // 配置参数
-     static const size_t MAX_FILES_PER_THREAD = 50;
-     static const size_t MAX_THREADS_PER_PROCESS = 1000;
-     
- public:
-     void update_process_info(pid_t pid, const ProcessInfo& info);
-     void add_file_operation(pid_t pid, pthread_t tid, 
-                            const FileIdentifier& file_id, FileOperation op);
-     void update_network_info(pid_t pid, const ProcessNetworkInfo& net_info);
-     void cleanup_dead_processes();
- };
- ```
- 
- #### 3.4.2 配置管理
- ```cpp
- struct MonitorConfig {
-     // 文件监控配置
-     size_t max_files_per_thread = 50;    // 每线程最大文件数（可配置，最大100）
-     bool enable_file_monitoring = true;   // 启用文件监控
-     
-     // 网络监控配置
-     bool enable_network_monitoring = true; // 启用网络监控
-     uint32_t network_update_interval = 5;  // 网络信息更新间隔（秒）
-     
-     // 特殊进程配置
-     std::set<pid_t> excluded_pids;        // 排除的进程ID
-     std::set<std::string> excluded_names; // 排除的进程名称
-     
-     // 特殊路径配置
-     std::set<std::string> protected_paths; // 受保护的路径
-     bool terminate_on_protected_write = true; // 写保护路径时终止进程
-     
-     // 日志配置
-     std::string log_file_path = "/var/log/psfsmon.log";
-     LogLevel log_level = LogLevel::INFO;
+ struct ProcessInfo {
+     // 基础信息
+     pid_t pid;
+     pid_t ppid;
+     std::string process_name;
+     std::string executable_path;
+     // ... 其他基础信息
+     
+     // 线程信息 (不再管理文件操作)
+     std::unordered_map<pid_t, std::shared_ptr<ThreadInfo>> threads;
+     
+     // 网络连接
+     std::vector<NetworkConnection> network_connections;
+     
+     // 重点进程标记和文件监控信息 (新增)
+     bool is_focus_process;
+     std::unique_ptr<FocusProcessFileInfo> focus_file_info;
+     
+     // 方法
+     void setFocusProcess(bool focus);
+     FocusProcessFileInfo* getFocusFileInfo() const;
  };
  ```
  
- ## 4. 系统架构设计
- 
- ### 4.1 模块架构
- ```
- ┌─────────────────────────────────────────────────────────────┐
- │                    PSFSMON-L 主程序                          │
- ├─────────────────────────────────────────────────────────────┤
- │  API接口层  │  配置管理  │  日志系统  │  信号处理  │  守护进程  │
- ├─────────────────────────────────────────────────────────────┤
- │           数据管理和汇总层 (ProcessDataManager)              │
- ├─────────────────────────────────────────────────────────────┤
- │ 进程监控模块 │ 文件监控模块 │ 网络监控模块 │ 线程监控模块    │
- ├─────────────────────────────────────────────────────────────┤
- │  Fanotify   │   Inotify   │  /proc/net  │   /proc/[pid]    │
- │   接口      │    接口     │    接口     │      接口        │
- └─────────────────────────────────────────────────────────────┘
- ```
- 
- ### 4.2 线程模型
+ #### 3.4.3 线程信息结构 (简化)
  ```cpp
- class PSFSMonitor {
- private:
-     std::thread process_monitor_thread;    // 进程监控线程
-     std::thread file_monitor_thread;       // 文件监控线程
-     std::thread network_monitor_thread;    // 网络监控线程
-     std::thread api_server_thread;         // API服务线程
-     
-     std::atomic<bool> running{true};       // 运行状态标志
-     
- public:
-     void start();
-     void stop();
-     void run_as_daemon();
+ struct ThreadInfo {
+     pid_t tid;
+     std::string thread_name;
+     double cpu_usage;
+     uint64_t memory_usage;
+     std::string state;
+     uint64_t created_time;
+     
+     // 不再管理文件操作信息
  };
  ```
  
- ## 5. API接口功能
- 
- ### 5.1 基础API
- - **需求ID**: REQ-API-001
- - **描述**: 提供基础的系统状态查询接口
+ ## 4. API接口功能 (更新)
+ 
+ ### 4.1 重点进程管理API (新增)
+ - **需求ID**: REQ-API-006
+ - **描述**: 提供重点进程管理接口
  - **接口列表**:
-   - `GET /api/health` - 健康检查
-   - `GET /api/stats` - 系统统计信息
- 
- ### 5.2 进程相关API
- - **需求ID**: REQ-API-002
- - **描述**: 提供进程信息查询接口
+   - `GET /api/focus-processes` - 获取当前重点进程列表
+   - `POST /api/focus-processes` - 添加重点进程
+   - `DELETE /api/focus-processes` - 删除重点进程
+ 
+ ### 4.2 特殊路径管理API (新增)
+ - **需求ID**: REQ-API-007
+ - **描述**: 提供特殊路径保护管理接口
  - **接口列表**:
-   - `GET /api/processes` - 获取所有进程基本信息
-   - `GET /api/processes-list` - 获取包含父进程名称的进程列表
-   - `GET /api/process-tree` - 获取进程树结构
-   - `GET /api/process?pid={pid}` - 获取特定进程详细信息
- 
- ### 5.3 网络相关API
- - **需求ID**: REQ-API-003
- - **描述**: 提供网络连接信息查询接口
+   - `GET /api/special-paths` - 获取当前特殊路径列表
+   - `POST /api/special-paths` - 添加特殊路径
+   - `DELETE /api/special-paths` - 删除特殊路径
+ 
+ ### 4.3 进程文件监控API (更新)
+ - **需求ID**: REQ-API-008
+ - **描述**: 提供重点进程文件监控信息查询接口
  - **接口列表**:
-   - `GET /api/network` - 获取网络连接信息
- 
- ### 5.4 排除进程管理API
- - **需求ID**: REQ-API-004
- - **描述**: 提供进程排除规则管理接口
- - **接口列表**:
-   - `GET /api/exclusions` - 获取当前排除规则
-   - `POST /api/exclusions` - 添加排除规则
-   - `DELETE /api/exclusions` - 删除排除规则
- 
- ### 5.5 受保护目录管理API
- - **需求ID**: REQ-API-005
- - **描述**: 提供路径保护规则管理接口
- - **接口列表**:
-   - `GET /api/protected-paths` - 获取当前保护规则
-   - `POST /api/protected-paths` - 添加保护规则
-   - `DELETE /api/protected-paths` - 删除保护规则
- 
- ### 5.6 REST API接口设计
- ```cpp
- // API端点定义
- class APIServer {
- public:
-     // 进程相关接口
-     GET  /api/v1/processes                    // 获取所有进程列表
-     GET  /api/v1/processes/{pid}              // 获取特定进程信息
-     GET  /api/v1/processes/{pid}/threads      // 获取进程线程信息
-     GET  /api/v1/processes/{pid}/files        // 获取进程文件操作
-     GET  /api/v1/processes/{pid}/network      // 获取进程网络信息
-     
-     // 系统相关接口
-     GET  /api/v1/system/process-tree          // 获取进程树
-     GET  /api/v1/system/statistics            // 获取系统统计信息
-     GET  /api/v1/system/status                // 获取监控状态
-     
-     // 配置相关接口
-     GET  /api/v1/config                       // 获取当前配置
-     PUT  /api/v1/config                       // 更新配置
-     POST /api/v1/config/exclude-process       // 添加排除进程
-     POST /api/v1/config/protect-path          // 添加保护路径
- };
- ```
- 
- ## 6. 关键技术难点和解决方案
- 
- ### 6.1 Fanotify线程信息获取问题
- **问题**: Fanotify事件中无法直接获取触发操作的具体线程ID
- 
- **解决方案**:
- 1. **进程级关联**: 使用Fanotify提供的PID信息，关联到进程级别
- 2. **时间窗口匹配**: 在短时间窗口内，将文件操作关联到最近活跃的线程
- 3. **文件描述符跟踪**: 通过/proc/[pid]/fd/目录跟踪文件描述符的线程归属
- 4. **系统调用跟踪**: 在必要时使用ptrace或其他机制获取更详细的线程信息
- 
- ```cpp
- class ThreadFileMapper {
- private:
-     struct ThreadActivity {
-         pthread_t thread_id;
-         time_t last_activity;
-         std::set<int> open_fds;
-     };
-     
-     std::unordered_map<pid_t, std::vector<ThreadActivity>> process_threads;
-     
- public:
-     pthread_t guess_thread_for_file_operation(pid_t pid, 
-                                              const FileIdentifier& file_id,
-                                              time_t operation_time);
-     void update_thread_activity(pid_t pid, pthread_t tid);
- };
- ```
- 
- ### 6.2 性能优化策略
- 1. **事件批处理**: 批量处理Fanotify事件，减少系统调用开销
- 2. **内存池**: 使用内存池管理频繁分配的小对象
- 3. **读写锁**: 使用读写锁保护共享数据结构
- 4. **异步处理**: 将耗时操作放到后台线程处理
- 
- ### 6.3 资源限制管理
- ```cpp
- class ResourceLimiter {
- private:
-     size_t max_processes = 10000;         // 最大进程数
-     size_t max_files_total = 500000;      // 总最大文件数
-     size_t max_memory_usage = 1024 * 1024 * 1024; // 最大内存使用（1GB）
-     
- public:
-     bool can_add_process(pid_t pid);
-     bool can_add_file_record(pid_t pid, pthread_t tid);
-     void cleanup_if_needed();
- };
- ```
- 
- ## 7. 安全和保护机制
- 
- ### 7.1 特殊进程排除
- ```cpp
- class ProcessExclusionManager {
- private:
-     std::set<pid_t> excluded_pids;
-     std::set<std::string> excluded_names;
-     std::regex excluded_patterns;
-     
- public:
-     bool is_excluded(pid_t pid);
-     bool is_excluded(const std::string& process_name);
-     void add_exclusion(pid_t pid);
-     void add_exclusion(const std::string& name_pattern);
-     void load_exclusions_from_config();
- };
- ```
- 
- ### 7.2 路径保护机制
- ```cpp
- class PathProtectionManager {
- private:
-     std::set<std::string> protected_paths;
-     std::set<std::string> protected_patterns;
-     
- public:
-     bool is_protected_path(const std::string& path);
-     void add_protected_path(const std::string& path);
-     void handle_protected_path_violation(pid_t pid, 
-                                        const std::string& path,
-                                        FileOperation operation);
-     
- private:
-     void terminate_violating_process(pid_t pid);
-     void log_violation(pid_t pid, const std::string& path, FileOperation op);
- };
- ```
- 
- ### 7.3 日志和审计
- ```cpp
- class AuditLogger {
- public:
-     void log_process_start(pid_t pid, const ProcessInfo& info);
-     void log_process_exit(pid_t pid);
-     void log_file_operation(pid_t pid, pthread_t tid, 
-                            const FileIdentifier& file_id, FileOperation op);
-     void log_network_connection(pid_t pid, const NetworkConnection& conn);
-     void log_security_violation(pid_t pid, const std::string& details);
-     void log_system_event(const std::string& event, LogLevel level);
- };
- ```
- 
- ## 8. 编译和构建系统
- 
- ### 8.1 支持的构建系统
- 1. **CMake** (推荐)
- 2. **GNU Makefile**
- 3. **GNU Autotools** (configure/make)
- 
- ### 8.2 CMakeLists.txt 关键配置
- ```cmake
- cmake_minimum_required(VERSION 3.5)
- project(psfsmon-l)
- 
- set(CMAKE_CXX_STANDARD 11)
- set(CMAKE_CXX_STANDARD_REQUIRED ON)
- 
- # 检查必需的系统特性
- include(CheckIncludeFiles)
- check_include_files("sys/fanotify.h" HAVE_FANOTIFY)
- check_include_files("sys/inotify.h" HAVE_INOTIFY)
- 
- if(NOT HAVE_FANOTIFY)
-     message(FATAL_ERROR "Fanotify support is required")
- endif()
- 
- # 编译选项
- add_compile_options(-Wall -Wextra -O2)
- 
- # 链接库
- target_link_libraries(psfsmon-l pthread)
- ```
- 
- ### 8.3 依赖检查
- ```cpp
- // 运行时检查内核特性支持
- class SystemCapabilityChecker {
- public:
-     static bool check_fanotify_support();
-     static bool check_fanotify_delete_support(); // Linux 5.1+
-     static bool check_inotify_support();
-     static std::string get_kernel_version();
-     static bool is_root_user();
- };
- ```
- 
- ## 9. 运行模式和接口
- 
- ### 9.1 运行模式
- 1. **直接运行模式**: 前台运行，直接输出到控制台
- 2. **守护进程模式**: 后台运行，通过API提供服务
- 3. **调试模式**: 详细日志输出，用于开发调试
- 
- ### 9.2 命令行参数
- ```bash
- psfsmon [选项]
- 
- 选项:
-   -d, --daemon          以守护进程模式运行
-   -c, --config FILE     指定配置文件路径
-   -p, --port PORT       API服务端口（默认8080）
-   -l, --log-level LEVEL 日志级别（debug/info/warn/error）
-   -h, --help            显示帮助信息
-   -v, --version         显示版本信息
-   --no-file-monitor     禁用文件监控
-   --no-network-monitor  禁用网络监控
- ```
- 
- ### 9.3 输出格式
- ```json
- {
-   "timestamp": "2024-01-15T10:30:00Z",
-   "process": {
-     "pid": 1234,
-     "name": "example_process",
-     "exe_path": "/usr/bin/example",
-     "cpu_usage": 15.5,
-     "memory_usage": 1048576,
-     "threads": [
-       {
-         "tid": 1235,
-         "file_operations": [
-           {
-             "file_id": "2049:12345",
-             "operations": "ORWWWRWWWD",
-             "first_access": "2024-01-15T10:25:00Z",
-             "last_access": "2024-01-15T10:29:00Z"
-           }
-         ]
-       }
-     ],
-     "network": {
-       "connections": [
-         {
-           "protocol": "TCP",
-           "local_addr": "192.168.1.100:8080",
-           "remote_addr": "192.168.1.200:443",
-           "state": "ESTABLISHED",
-           "bytes_sent": 2048,
-           "bytes_received": 4096
-         }
-       ]
-     }
-   }
- }
- ```
- 
- ## 10. 非功能性需求
- 
- ### 10.1 性能需求
- - **需求ID**: REQ-PERF-001
- - **描述**: 系统性能要求
- - **要求**:
-   - API响应时间 < 100ms（正常负载下）
-   - 支持并发请求处理
-   - 内存使用控制在合理范围内
-   - CPU使用率不超过系统总量的10%
- 
- ### 10.2 兼容性需求
- - **需求ID**: REQ-COMPAT-001
- - **描述**: 系统兼容性要求
- - **要求**:
-   - 支持Linux内核5.4.18+
-   - 支持x64/amd64、aarch64/arm64架构
-   - 使用C++11标准确保兼容性
-   - 避免大型第三方库依赖
- 
- ### 10.3 安全需求
- - **需求ID**: REQ-SEC-001
- - **描述**: 系统安全要求
- - **要求**:
-   - 需要root权限运行
-   - 保护关键系统文件
-   - 防止恶意进程绕过监控
-   - 提供进程终止功能
- 
- ### 10.4 可用性需求
- - **需求ID**: REQ-USAB-001
- - **描述**: 系统可用性要求
- - **要求**:
-   - 提供清晰的API文档
-   - 支持JSON格式数据交换
-   - 提供详细的错误信息
-   - 支持测试工具和示例
- 
- ## 11. 数据格式规范
- 
- ### 11.1 进程信息格式
- ```json
- {
-   "pid": 1234,
-   "ppid": 1,
-   "parent_name": "systemd",
-   "name": "nginx",
-   "state": "SLEEPING",
-   "cpu_usage": 2.5,
-   "memory_usage": 32768,
-   "virtual_memory": 65536,
-   "executable_path": "/usr/sbin/nginx",
-   "command_line": "nginx: master process",
-   "cwd": "/",
-   "uid": 0,
-   "gid": 0,
-   "start_time": 1640992000,
-   "num_threads": 4,
-   "num_children": 2
- }
- ```
- 
- ### 11.2 排除规则格式
- ```json
- {
-   "type": "name|pid|path|pattern",
-   "value": "排除值"
- }
- ```
- 
- ### 11.3 保护路径格式
- ```json
- {
-   "path": "/path/to/protect",
-   "level": "read_only|write_protect|full_protect",
-   "recursive": true|false,
-   "terminate_violator": true|false
- }
- ```
- 
- ### 11.4 错误响应格式
- ```json
- {
-   "error": "错误描述信息",
-   "timestamp": 1640995200
- }
- ```
- 
- ## 12. 测试需求
- 
- ### 12.1 功能测试
- - **需求ID**: REQ-TEST-001
- - **描述**: 功能测试要求
- - **要求**:
-   - 所有API接口功能测试
-   - 边界条件测试
-   - 错误处理测试
-   - 数据格式验证测试
- 
- ### 12.2 性能测试
- - **需求ID**: REQ-TEST-002
- - **描述**: 性能测试要求
- - **要求**:
-   - API响应时间测试
-   - 并发请求测试
-   - 系统资源使用测试
-   - 长时间运行稳定性测试
- 
- ### 12.3 安全测试
- - **需求ID**: REQ-TEST-003
- - **描述**: 安全测试要求
- - **要求**:
-   - 权限验证测试
-   - 路径保护功能测试
-   - 进程终止功能测试
-   - 恶意输入防护测试
- 
- ## 13. 部署需求
- 
- ### 13.1 系统要求
- - Linux内核版本 >= 5.4.18
- - root权限
- - 支持Fanotify和Inotify机制
- - C++11兼容编译器
- 
- ### 13.2 安装包结构
- ```
- /usr/local/bin/psfsmon           # 主程序
- /etc/psfsmon/                    # 配置目录
-   ├── psfsmon.conf              # 主配置文件
-   ├── excluded_processes.conf   # 排除进程配置
-   └── protected_paths.conf      # 保护路径配置
- /var/log/psfsmon/               # 日志目录
- /lib/systemd/system/psfsmon.service # systemd服务文件
- ```
- 
- ### 13.3 系统服务集成
- ```ini
- [Unit]
- Description=Process Security File System Monitor
- After=network.target
- 
- [Service]
- Type=forking
- ExecStart=/usr/local/bin/psfsmon --daemon
- ExecReload=/bin/kill -HUP $MAINPID
- PIDFile=/var/run/psfsmon.pid
- Restart=always
- User=root
- 
- [Install]
- WantedBy=multi-user.target
- ```
- 
- ### 13.4 依赖要求
- - 最小化外部依赖
- - 避免大型第三方库
- - 支持静态链接
- 
- ### 13.5 配置要求
- - 支持配置文件
- - 支持命令行参数
- - 支持环境变量配置
- 
- ## 14. 维护需求
- 
- ### 14.1 日志需求
- - 详细的操作日志
- - 错误日志记录
- - 性能监控日志
- - 安全事件日志
- 
- ### 14.2 监控需求
- - 系统健康状态监控
- - 性能指标监控
- - 错误率监控
- - 资源使用监控
- 
- ### 14.3 更新需求
- - 支持在线配置更新
- - 支持热重载功能
- - 支持版本升级
- - 支持回滚机制
- 
- ## 15. 开发里程碑
- 
- ### 15.1 第一阶段 (基础框架)
- - [x] 项目结构搭建
- - [x] 基础进程监控功能
- - [x] 配置管理系统
- - [x] 日志系统
- 
- ### 15.2 第二阶段 (核心监控)
- - [x] Fanotify文件监控实现
- - [x] 网络监控功能
- - [x] 线程管理功能
- - [x] 数据存储和管理
- 
- ### 15.3 第三阶段 (高级功能)
- - [x] API服务实现
- - [x] 安全保护机制
- - [x] 性能优化
- - [x] 完整测试覆盖
- 
- ### 15.4 第四阶段 (发布准备)
- - [x] 文档完善
- - [ ] 安装包制作
- - [ ] 系统集成
- - [ ] 用户手册
- 
- ## 16. 风险评估和缓解
- 
- ### 16.1 技术风险
- - **内核版本兼容性**: 通过运行时特性检测缓解
- - **性能影响**: 通过优化和资源限制缓解
- - **内存泄漏**: 通过严格的内存管理和测试缓解
- 
- ### 16.2 安全风险
- - **权限滥用**: 通过最小权限原则和审计日志缓解
- - **信息泄露**: 通过访问控制和数据加密缓解
- - **拒绝服务**: 通过资源限制和监控缓解
- 
- ## 17. 总结
- 
- 本需求文档详细定义了Linux安全监控程序的所有功能需求、技术规格和实现方案。基于对Fanotify能力的确认（Linux 5.1+支持删除和重命名监控），我们主要依赖Fanotify实现文件监控，大大简化了系统架构。
- 
- 关键技术决策：
- 1. **主要使用Fanotify**: 利用其完整的文件事件支持和进程上下文
- 2. **C++11标准**: 确保在较老系统上的兼容性
- 3. **模块化设计**: 便于维护和扩展
- 4. **多种构建系统支持**: 适应不同的部署环境
- 5. **完整的API接口**: 支持灵活的数据查询和集成
- 
- 该文档将作为后续开发工作的基础参考，确保项目按照明确的需求和规格进行实施。 
+   - `GET /api/process/files?pid={pid}` - 获取重点进程文件监控信息
+   - 返回内容包括：
+     - 是否为重点进程
+     - 读/写操作熵值统计
+     - 删除/重命名操作统计
+     - 重命名扩展名列表
+     - 文件路径操作历史
+ 
+ ### 4.4 现有API保持不变
+ - 进程相关API
+ - 网络相关API
+ - 排除进程管理API
+ - 受保护目录管理API
+ 
+ ## 5. 关键技术变更
+ 
+ ### 5.1 文件监控机制变更
+ - **移除**: 完全移除Inotify机制及相关代码
+ - **保留**: 仅使用Fanotify机制
+ - **优化**: 根据内核版本自动选择可用的Fanotify事件类型
+ 
+ ### 5.2 线程级文件操作管理移除
+ - **移除**: 不再收集进程每个线程的文件操作信息
+ - **移除**: 不再区分是哪个线程操作了什么文件
+ - **简化**: ThreadInfo结构不再包含文件操作管理
+ 
+ ### 5.3 重点进程文件监控机制
+ - **新增**: 只有重点进程才进行详细文件监控
+ - **新增**: 文件熵值计算和统计
+ - **新增**: 进程级文件操作统计和历史记录
+ 
+ ### 5.4 特殊路径保护机制
+ - **新增**: 特殊路径设置和管理
+ - **新增**: 违规进程自动中止功能
+ - **新增**: 违规行为日志记录
+ 
+ ## 6. 编译和部署要求
+ 
+ ### 6.1 编译方式支持
+ - CMake编译系统
+ - 标准Makefile编译
+ - autotools编译系统支持
+ 
+ ### 6.2 代码组织要求
+ - 单个源代码文件不超过1500行
+ - 逻辑清晰的模块化设计
+ - 完整的设计文档说明
+ 
+ ### 6.3 测试和文档
+ - 同步更新测试代码
+ - 同步更新相关脚本
+ - 保持代码可读性和可维护性
+ 
+ ## 7. 性能和安全要求
+ 
+ ### 7.1 性能要求
+ - 避免依赖大规模底层依赖库
+ - 兼容Linux内核5.4.18及以上版本
+ - 支持多CPU架构本地编译
+ 
+ ### 7.2 安全要求
+ - 特殊路径保护机制
+ - 违规进程自动中止
+ - 完整的审计日志记录
+ - 重点进程行为深度分析
+ 
+ 这个更新版本的需求文档反映了项目的重大架构调整，从通用的文件监控转向了重点进程的深度监控，提高了安全监控的针对性和效率。 
```

---

我来查看当前项目的状态，然后继续完成Linux安全监控程序的开发工作。

---

<details>
            <summary>Listed current directory • **23** results</summary>
        
| Name |
|-------|
| 📄 `REQUIREMENTS.md` |
| 📁 `refdocs` |
| 📁 `obj` |
| 📁 `.git` |
| 📁 `.idea` |
| 📁 `include` |
| 📁 `src` |
| 📁 `cmake-build-debug` |
| 📄 `CMakeLists.txt` |
| 📄 `Makefile` |
| 📄 `DEBUG_QUICK_START.md` |
| 📁 `docs` |
| 📄 `.gdbinit` |
| 📁 `scripts` |
| 📄 `.gitignore` |
| 📁 `test` |
| 📁 `ref_code` |
| 📄 `PROJECT_REVIEW_SUMMARY.md` |
| 📄 `README.md` |
| 📄 `API_REFERENCE.md` |
| 📁 `.specstory` |
| 📁 `config` |
| 📄 `.cursorindexingignore` |

</details>

---

Read file: REQUIREMENTS.md

---

Read file: REQUIREMENTS.md

---

现在我来查看当前的源代码结构，了解项目的实现状态：

---

<details>
            <summary>Listed directory src • **11** results</summary>
        
| Name |
|-------|
| 📄 `file_monitor.cpp` |
| 📄 `api_server.cpp` |
| 📄 `process_monitor.cpp` |
| 📄 `psfsmon_core.cpp` |
| 📄 `kernel_compat.cpp` |
| 📄 `main.cpp` |
| 📄 `main_monitor.cpp` |
| 📄 `network_monitor.cpp` |
| 📄 `path_protection.cpp` |
| 📄 `process_exclusion.cpp` |
| 📄 `utils.cpp` |

</details>

---

<details>
            <summary>Listed directory include • **9** results</summary>
        
| Name |
|-------|
| 📄 `api_server.h` |
| 📄 `file_monitor.h` |
| 📄 `psfsmon.h` |
| 📄 `process_monitor.h` |
| 📄 `kernel_compat.h` |
| 📄 `main_monitor.h` |
| 📄 `network_monitor.h` |
| 📄 `process_exclusion.h` |
| 📄 `path_protection.h` |

</details>

---





---

