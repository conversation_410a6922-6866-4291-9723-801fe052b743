<!-- Generated by SpecStory -->

# Untitled (2025-05-25 12:32:45)

_**User**_

 当前程序的需求如下，请对照核对开发需求文档，如果没有描述清楚的，修改文档以适应需求。同时，项目已经可以编译成功，但是在监控上还存在细节问题。我给你一个基本可以正常工作的程序做参考。@@fanotify_monitor.c 
 尝试修改并编译、测试当前项目。
1、我需要编写一个运行在Linux平台下面的安全监控程序；
2、这个程序的主要功能是实时监控所有系统中所运行的进程并收集它们的状态，并具备汇报这些信息的能力；
3、这个程序需要以C++语言进行编写，低层的库可以使用C语言。C++语言的标准，由于系统编译器的版本问题，要控制在可以支持C++11标准的状态；
4、这个程序可能需要在不同的CPU上运行，例如x64/amd64, aarch64/arm64等等，但是不会跨平台编译，也就是说，它一定会在所在的平台下，用本地编译器进行编译；
5、这个程序要在启动时收集所有的已有进程的信息，管理它们，具备形成进程树的能力；
6、针对每个进程，要可以管理它创建或clone的所有线程、子线程；
7、收集的信息要归属并管理在进程所属并管理的每个线程下面，但是要在进程这一层次有一个汇总；
8、收集的信息主要包括3类，1是进程的基本信息例如名称、CPU使用状态、内存使用状态、路径等等；2是进程的每个线程所操作的文件信息，包括操作了哪些文件（要创建文件的唯一标识符），针对每个文件执行了打开、读、写、删除、重命名、以重命名方式进行删除（也就是说重命名时目标文件对象已经存在）、关闭等操作的序列信息（可以用特殊的字符来标记，例如使用O表示打开，W表示写，R表示读，N表示重命名，D表示删除，P表示以重命名方式进行删除等等，最终形成的文件操作序列是一个字符串，例如对ID为0x1175205F标识的文件，它的操作序列是“ORWWWWWRWWWD”）；3是进程的基本网络操作信息，例如打开了多少连接，连接了什么IP地址，每个IP收发的基本数据量是多少、整体收发数据量等等；
9、因为OS的限制，我们不能使用eBPF机制来收集这些信息；这个要求非常重要；
10、我们要使用Fanotify机制来进行文件信息的获取，如果Fanotify机制可以进行相关监控，不需要整合Inotify机制到本程序中，除非有非常必要的理由；
11、对于进程信息、网络通信信息，你可以使用合适的方法，但是要避免依赖其它大规模的底层依赖库，因为我们的操作系统版本并不高，而且可能是多个CPU类型的，它的基础版本只有5.4.18，可能很多功能都不具备，例如Sysdig和它所使用的一些基础库是无法编译通过的；
12、程序可以直接执行，以直观的方式进行输出，也可以以服务的方式进行安装和执行，通过对外接口形式进行输出；
13、程序对外的接口，可以向调用者灵活的提供所输出的信息，例如进程树、每个进程的信息汇总，每个进程的详细信息例如它所管理的每个线程的所有的文件的操作序列字符串等等；
14、每个进程所管理的每个线程的文件信息有一个常量上限，现在定义为：每个线程只记录最近操作的50个文件的信息；50这个值以常量形式存在且可更改，但是最大不超过100；
15、程序要支持CMake和标准Makefile这2种编译方式，或者使用auto config系列工具编译；
16、支持特殊进程设置，被设置为特殊进程的，不再监控其相关信息；
17、支持特殊路径设置，被设置为特殊路径的，在发生针对特殊路径的写操作时，包括删除和重命名，相关的进程会被中止，并将相关记录在日志中；
18、注意到Fanotify机制中无法获取当前操作的进程的线程的具体值的问题，请使用其它折中的方法来解决这一难题；
19、保持程序的可读性，如果单个源代码文件过大，例如超过了1500行，则可以在逻辑上拆分它，并在设计文件中给出简单的说明；




---

_**Assistant**_



---

我需要先了解当前项目的结构，然后基于您提供的参考代码和需求来修改和完善程序。

---

<details>
            <summary>Listed current directory • **18** results</summary>
        
| Name |
|-------|
| 📁 `ref_code` |
| 📁 `bin` |
| 📁 `obj` |
| 📄 `CMakeLists.txt` |
| 📁 `src` |
| 📁 `include` |
| 📁 `docs` |
| 📄 `README.md` |
| 📁 `scripts` |
| 📁 `config` |
| 📄 `configure` |
| 📄 `Makefile` |
| 📁 `build` |
| 📁 `tests` |
| 📄 `.gitignore` |
| 📄 `.cursorindexingignore` |
| 📁 `.specstory` |
| 📁 `.git` |

</details>

---

Read file: README.md

---

<details>
            <summary>Listed directory src • **10** results</summary>
        
| Name |
|-------|
| 📄 `file_monitor.cpp` |
| 📄 `network_monitor.cpp` |
| 📄 `path_protection.cpp` |
| 📄 `process_exclusion.cpp` |
| 📄 `process_monitor.cpp` |
| 📄 `main_monitor.cpp` |
| 📄 `main.cpp` |
| 📄 `api_server.cpp` |
| 📄 `utils.cpp` |
| 📄 `psfsmon_core.cpp` |

</details>

---

<details>
            <summary>Listed directory include • **8** results</summary>
        
| Name |
|-------|
| 📄 `process_exclusion.h` |
| 📄 `path_protection.h` |
| 📄 `network_monitor.h` |
| 📄 `file_monitor.h` |
| 📄 `process_monitor.h` |
| 📄 `psfsmon.h` |
| 📄 `main_monitor.h` |
| 📄 `api_server.h` |

</details>

---

现在让我查看主要的头文件以了解当前的架构：

---

Read file: include/psfsmon.h

---

让我继续查看这个文件的剩余部分：

---

Read file: include/psfsmon.h

---

现在让我查看文件监控器的实现，这是我们需要重点改进的部分：

---

Read file: include/file_monitor.h

---

Read file: include/file_monitor.h

---

现在让我查看当前编译时遇到的具体问题：

---

```bash
cd /home/<USER>/coder/psfsmonL && make clean && make
```

```
Cleaning up...
rm -rf obj bin
Clean completed!
mkdir -p obj
Compiling src/api_server.cpp...
g++ -std=c++11 -Wall -Wextra -O2 -pthread -DNDEBUG -Iinclude -c src/api_server.c
pp -o obj/api_server.o
Compiling src/file_monitor.cpp...
g++ -std=c++11 -Wall -Wextra -O2 -pthread -DNDEBUG -Iinclude -c src/file_monitor
.cpp -o obj/file_monitor.o
Compiling src/main.cpp...
g++ -std=c++11 -Wall -Wextra -O2 -pthread -DNDEBUG -Iinclude -c src/main.cpp -o
obj/main.o
Compiling src/main_monitor.cpp...
g++ -std=c++11 -Wall -Wextra -O2 -pthread -DNDEBUG -Iinclude -c src/main_monitor
.cpp -o obj/main_monitor.o
Compiling src/network_monitor.cpp...
g++ -std=c++11 -Wall -Wextra -O2 -pthread -DNDEBUG -Iinclude -c src/network_moni
tor.cpp -o obj/network_monitor.o
src/network_monitor.cpp: In member function ‘void NetworkMonitor::parseNetlinkAt
tributes(const nlmsghdr*, SocketInfo&)’:
src/network_monitor.cpp:240:29: warning: unused variable ‘cong’ [-Wunused-variab
le]
  240 |                 const char* cong = (const char*)RTA_DATA(attr);
      |                             ^~~~
Compiling src/path_protection.cpp...
g++ -std=c++11 -Wall -Wextra -O2 -pthread -DNDEBUG -Iinclude -c src/path_protect
ion.cpp -o obj/path_protection.o
Compiling src/process_exclusion.cpp...
g++ -std=c++11 -Wall -Wextra -O2 -pthread -DNDEBUG -Iinclude -c src/process_excl
usion.cpp -o obj/process_exclusion.o
Compiling src/process_monitor.cpp...
g++ -std=c++11 -Wall -Wextra -O2 -pthread -DNDEBUG -Iinclude -c src/process_moni
tor.cpp -o obj/process_monitor.o
Compiling src/psfsmon_core.cpp...
g++ -std=c++11 -Wall -Wextra -O2 -pthread -DNDEBUG -Iinclude -c src/psfsmon_core
.cpp -o obj/psfsmon_core.o
Compiling src/utils.cpp...
g++ -std=c++11 -Wall -Wextra -O2 -pthread -DNDEBUG -Iinclude -c src/utils.cpp -o
 obj/utils.o
mkdir -p bin
Linking bin/psfsmon...
g++ obj/api_server.o obj/file_monitor.o obj/main.o obj/main_monitor.o obj/networ
k_monitor.o obj/path_protection.o obj/process_exclusion.o obj/process_monitor
.o obj/psfsmon_core.o obj/utils.o -o bin/psfsmon -pthread -lrt -ldl
                                                                             Bui
ld completed successfully!

```

---

编译成功了！现在让我测试一下程序是否能正常运行：

---

```bash
sudo ./bin/psfsmon --help
```

```
helloub22@helloub22-virtual-machine:~/coder/psfsmonL$ sudo ./bin/psfsmon --help
[sudo] helloub22 的密码： 
启动 PSFSMON-L Linux安全监控程序...
当前内核版本: 6.8.0-57-generic
用法: ./bin/psfsmon [选项]

选项:
  -h, --help              显示此帮助信息
  -v, --version           显示版本信息
  -d, --daemon            以守护进程模式运行
  -c, --config FILE       指定配置文件路径
  -p, --port PORT         指定API服务器端口
  -l, --log-file FILE     指定日志文件路径
  --no-file-monitor       禁用文件监控
  --no-network-monitor    禁用网络监控
  --no-process-monitor    禁用进程监控
  --debug                 启用调试模式
  --verbose               详细输出

示例:
  ./bin/psfsmon                    # 直接运行
  ./bin/psfsmon --daemon           # 守护进程模式
  ./bin/psfsmon --port 9090        # 指定API端口
  ./bin/psfsmon --config /etc/psfsmon/custom.conf

API接口:
  GET /api/process-tree                    获取进程树
  GET /api/process/{pid}                   获取进程信息
  GET /api/process/{pid}/threads           获取进程线程
  GET /api/process/{pid}/files             获取文件操作序列
  GET /api/network                         获取网络连接
  GET /api/stats                           获取统计信息
  GET /api/health                          健康检查

helloub22@helloub22-virtual-machine:~/coder/psfsmonL$
```

---

很好！程序可以正常编译和运行。现在让我查看当前的文件监控实现，看看是否需要改进以更好地符合您的需求，特别是参考您提供的fanotify_monitor.c：

---

Read file: src/file_monitor.cpp

---

让我继续查看文件监控的实现，特别是事件处理部分：

---

Read file: src/file_monitor.cpp

---

现在让我看看实际的测试运行情况：

---

```bash
sudo timeout 10s ./bin/psfsmon --verbose
```

```
helloub22@helloub22-virtual-machine:~/coder/psfsmonL$ sudo timeout 10s ./bin/psfsmon --verbose
启动 PSFSMON-L Linux安全监控程序...
当前内核版本: 6.8.0-57-generic
[2025-05-25 12:38:20] [INFO] 日志系统初始化成功
日志文件: /var/log/psfsmon/psfsmon.log
[2025-05-25 12:38:20] [INFO] PSFSMON-L 正在启动...
[2025-05-25 12:38:20] [INFO] [ProcessMonitor] 进程监控器初始化成功
[2025-05-25 12:38:20] [INFO] [FileMonitor] 启用 Fanotify 扩展事件支持 (Linux 5.1+)
[2025-05-25 12:38:20] [ERROR] [FileMonitor] fanotify_mark (MOUNT) 失败: Invalid argument
[2025-05-25 12:38:20] [ERROR] [FileMonitor] fanotify_mark 失败: Invalid argument
[2025-05-25 12:38:20] [ERROR] [FileMonitor] Fanotify 初始化失败
[2025-05-25 12:38:20] [ERROR] 无法初始化文件监控器
错误: 无法初始化监控器
[2025-05-25 12:38:20] [INFO] 正在停止监控器...
[2025-05-25 12:38:20] [INFO] 监控器已停止
helloub22@helloub22-virtual-machine:~/coder/psfsmonL$
```

---

看起来当前的fanotify初始化有问题。让我基于您提供的参考代码来改进文件监控器。根据需求分析，我需要：

1. 修复fanotify初始化问题
2. 改进fanotify事件处理，更好地遵循您的参考代码
3. 添加权限检查和特殊路径保护功能
4. 确保线程ID获取的准确性

让我创建一个改进的文件监控器：

---

```diff
  #include <fstream>
  #include <sstream>
+ #include <linux/fanotify.h>
+ #include <sys/utsname.h>
  
  // FileMonitor 实现
```

```diff
  
  bool FileMonitor::initializeFanotify() {
-     // 创建 fanotify 文件描述符
-     fanotify_fd_ = fanotify_init(FAN_CLASS_NOTIF | FAN_REPORT_TID, 
-                                 O_RDONLY | O_LARGEFILE);
+     // 参考fanotify_monitor.c的实现方式
+     uint64_t fan_mask = FAN_OPEN | FAN_CLOSE | FAN_ACCESS | FAN_MODIFY;
+     unsigned int init_flags = FAN_CLASS_NOTIF;
+     
+     // 创建 fanotify 文件描述符 - 参考原始代码的初始化方式
+     fanotify_fd_ = fanotify_init(init_flags | FAN_CLOEXEC | FAN_NONBLOCK,
+                                O_RDONLY | O_LARGEFILE);
      if (fanotify_fd_ == -1) {
-         logError("fanotify_init 失败: " + std::string(strerror(errno)));
+         logError("fanotify_init 失败: " + std::string(strerror(errno)) + 
+                 " (错误码: " + std::to_string(errno) + ")");
          return false;
      }
      
-     // 标记根文件系统进行监控
-     uint64_t mask = FanotifyEvents::EVENTS;
-     
-     // 检查是否支持扩展事件（Linux 5.1+）
-     if (FanotifyEvents::EXTENDED_EVENTS != 0) {
-         mask |= FanotifyEvents::EXTENDED_EVENTS;
-         logInfo("启用 Fanotify 扩展事件支持 (Linux 5.1+)");
-     }
-     
-     // 监控根目录（递归）
-     if (fanotify_mark(fanotify_fd_, FAN_MARK_ADD | FAN_MARK_MOUNT,
-                      mask, AT_FDCWD, "/") == -1) {
-         logError("fanotify_mark (MOUNT) 失败: " + std::string(strerror(errno)));
-         
-         // 如果MOUNT失败，尝试只监控根目录
-         if (fanotify_mark(fanotify_fd_, FAN_MARK_ADD,
-                          mask, AT_FDCWD, "/") == -1) {
-             logError("fanotify_mark 失败: " + std::string(strerror(errno)));
-             close(fanotify_fd_);
-             fanotify_fd_ = -1;
-             return false;
+     // 添加事件监控 - 参考原始代码的标记添加方式
+     fan_mask |= FAN_ONDIR;
+     fan_mask |= FAN_EVENT_ON_CHILD;
+     
+     unsigned int mark_flags = FAN_MARK_ADD;
+     
+     // 尝试先使用挂载点监控
+     mark_flags |= FAN_MARK_MOUNT;
+     
+     if (fanotify_mark(fanotify_fd_, mark_flags, fan_mask, AT_FDCWD, "/") != 0) {
+         logError("无法添加监控标记到根目录(MOUNT): " + std::string(strerror(errno)) + 
+                 " (错误码: " + std::to_string(errno) + ")");
+         
+         // 尝试使用更基本的标志 - 参考原始代码的降级处理
+         mark_flags = FAN_MARK_ADD;
+         fan_mask = FAN_OPEN | FAN_CLOSE | FAN_ACCESS | FAN_MODIFY;
+         
+         if (fanotify_mark(fanotify_fd_, mark_flags, fan_mask, AT_FDCWD, "/") != 0) {
+             logError("无法添加基本监控标记到根目录: " + std::string(strerror(errno)) + 
+                     " (错误码: " + std::to_string(errno) + ")");
+             
+             // 尝试监控常用目录 - 参考原始代码的目录监控策略
+             const char *common_dirs[] = {
+                 "/home", "/var", "/tmp", "/opt", "/usr/local"
+             };
+             
+             bool success = false;
+             for (size_t i = 0; i < sizeof(common_dirs) / sizeof(common_dirs[0]); i++) {
+                 if (fanotify_mark(fanotify_fd_, mark_flags, fan_mask, AT_FDCWD, common_dirs[i]) == 0) {
+                     logInfo("成功添加监控: " + std::string(common_dirs[i]));
+                     success = true;
+                 }
+             }
+             
+             if (!success) {
+                 logError("无法监控任何目录");
+                 close(fanotify_fd_);
+                 fanotify_fd_ = -1;
+                 return false;
+             }
          } else {
-             logInfo("Fanotify 使用目录监控模式");
+             logInfo("Fanotify 使用基本目录监控模式");
          }
      } else {
```

---

现在我需要改进fanotify的事件处理部分，让它更接近参考代码的实现：

---



---

现在让我们重新编译并测试：

---

```bash
make clean && make
```

```
Cleaning up...
rm -rf obj bin
Clean completed!
mkdir -p obj
Compiling src/api_server.cpp...
g++ -std=c++11 -Wall -Wextra -O2 -pthread -DNDEBUG -Iinclude -c src/api_server.cpp -o obj/api_serv
er.o
Compiling src/file_monitor.cpp...
g++ -std=c++11 -Wall -Wextra -O2 -pthread -DNDEBUG -Iinclude -c src/file_monitor.cpp -o obj/file_m
onitor.o
Compiling src/main.cpp...
g++ -std=c++11 -Wall -Wextra -O2 -pthread -DNDEBUG -Iinclude -c src/main.cpp -o obj/main.o
Compiling src/main_monitor.cpp...
g++ -std=c++11 -Wall -Wextra -O2 -pthread -DNDEBUG -Iinclude -c src/main_monitor.cpp -o obj/main_m
onitor.o
Compiling src/network_monitor.cpp...
g++ -std=c++11 -Wall -Wextra -O2 -pthread -DNDEBUG -Iinclude -c src/network_monitor.cpp -o obj/net
work_monitor.o
src/network_monitor.cpp: In member function ‘void NetworkMonitor::parseNetlinkAttributes(const nlm
sghdr*, SocketInfo&)’:
src/network_monitor.cpp:240:29: warning: unused variable ‘cong’ [-Wunused-variable]
  240 |                 const char* cong = (const char*)RTA_DATA(attr);
      |                             ^~~~
Compiling src/path_protection.cpp...
g++ -std=c++11 -Wall -Wextra -O2 -pthread -DNDEBUG -Iinclude -c src/path_protection.cpp -o obj/pat
h_protection.o
Compiling src/process_exclusion.cpp...
g++ -std=c++11 -Wall -Wextra -O2 -pthread -DNDEBUG -Iinclude -c src/process_exclusion.cpp -o obj/p
rocess_exclusion.o
Compiling src/process_monitor.cpp...
g++ -std=c++11 -Wall -Wextra -O2 -pthread -DNDEBUG -Iinclude -c src/process_monitor.cpp -o obj/pro
cess_monitor.o
Compiling src/psfsmon_core.cpp...
g++ -std=c++11 -Wall -Wextra -O2 -pthread -DNDEBUG -Iinclude -c src/psfsmon_core.cpp -o obj/psfsmo
n_core.o
Compiling src/utils.cpp...
g++ -std=c++11 -Wall -Wextra -O2 -pthread -DNDEBUG -Iinclude -c src/utils.cpp -o obj/utils.o
mkdir -p bin
Linking bin/psfsmon...
g++ obj/api_server.o obj/file_monitor.o obj/main.o obj/main_monitor.o obj/network_monitor.o obj/pa
th_protection.o obj/process_exclusion.o obj/process_m
onitor.o obj/psfsmon_core.o obj/utils.o -o bin/psfsmon -pthread -lrt -ldl
                                                     Build completed successfully!

```

---

编译成功！现在让我们测试改进后的程序：

---

```bash
sudo timeout 10s ./bin/psfsmon --verbose
```

```
helloub22@helloub22-virtual-machine:~/coder/psfsmonL$ sudo timeout 10s ./bin/psfsmon --verbose
启动 PSFSMON-L Linux安全监控程序...
当前内核版本: 6.8.0-57-generic
[2025-05-25 12:39:46] [INFO] 日志系统初始化成功
日志文件: /var/log/psfsmon/psfsmon.log
[2025-05-25 12:39:46] [INFO] PSFSMON-L 正在启动...
[2025-05-25 12:39:46] [INFO] [ProcessMonitor] 进程监控器初始化成功
[2025-05-25 12:39:46] [INFO] [FileMonitor] Fanotify 使用挂载点监控模式
[2025-05-25 12:39:46] [INFO] [FileMonitor] Fanotify 初始化成功
[2025-05-25 12:39:46] [INFO] [FileMonitor] Inotify 初始化成功
[2025-05-25 12:39:46] [INFO] [FileMonitor] 文件监控器初始化成功
[2025-05-25 12:39:46] [INFO] [NetworkMonitor] 使用 Netlink 诊断接口
[2025-05-25 12:39:46] [INFO] [NetworkMonitor] 网络监控器初始化成功
[2025-05-25 12:39:46] [INFO] 监控器初始化完成
[2025-05-25 12:39:46] [INFO] [ProcessMonitor] 进程监控器已启动
[2025-05-25 12:39:46] [INFO] [FileMonitor] 文件监控器已启动
[2025-05-25 12:39:46] [INFO] [FileMonitor] 事件处理线程已启动
[2025-05-25 12:39:46] [INFO] [FileMonitor] Inotify 事件循环已启动
[2025-05-25 12:39:46] [INFO] [NetworkMonitor] 网络监控器已启动
[2025-05-25 12:39:46] [INFO] 监控器启动成功
监控器启动成功
[2025-05-25 12:39:46] [INFO] 监控器启动成功
[2025-05-25 12:39:46] [INFO] [ApiServer] API服务器初始化成功，端口: 8080
[2025-05-25 12:39:46] [INFO] [HttpServer] HTTP服务器已启动，端口: 8080
[2025-05-25 12:39:46] [INFO] [ApiServer] API服务器已启动
API服务器启动成功，端口: 8080
[2025-05-25 12:39:46] [INFO] [FileMonitor] Fanotify 事件循环已启动
[2025-05-25 12:39:46] [INFO] API服务器启动成功，端口: 8080
PSFSMON-L 启动完成，正在监控系统...
按 Ctrl+C 停止程序
[2025-05-25 12:39:46] [INFO] [ProcessExclusionManager] 初始化进程排除管理器
[2025-05-25 12:39:46] [INFO] [ProcessExclusionManager] 添加默认系统进程排除规则
[2025-05-25 12:39:46] [INFO] [ProcessExclusionManager] 进程排除管理器初始化完成，总计 44 个排除规则
[2025-05-25 12:39:46] [INFO] [NetworkMonitor] 网络监控循环已启动
[2025-05-25 12:39:46] [INFO] [PathProtectionManager] 初始化路径保护管理器
[2025-05-25 12:39:46] [INFO] [PathProtectionManager] 添加默认系统保护路径
[2025-05-25 12:39:46] [INFO] [PathProtectionManager] 添加保护路径: /etc/passwd (级别: full_protect)
[2025-05-25 12:39:46] [INFO] [PathProtectionManager] 添加保护路径: /etc/shadow (级别: full_protect)
[2025-05-25 12:39:46] [INFO] [PathProtectionManager] 添加保护路径: /etc/group (级别: full_protect)
[2025-05-25 12:39:46] [INFO] [PathProtectionManager] 添加保护路径: /etc/sudoers (级别: full_protect)
[2025-05-25 12:39:46] [INFO] [PathProtectionManager] 添加保护路径: /etc/fstab (级别: full_protect)
[2025-05-25 12:39:46] [INFO] [PathProtectionManager] 添加保护路径: /etc/hosts (级别: full_protect)
[2025-05-25 12:39:46] [INFO] [PathProtectionManager] 添加保护路径: /boot/vmlinuz (级别: full_protect)
[2025-05-25 12:39:46] [INFO] [PathProtectionManager] 添加保护路径: /boot/initrd (级别: full_protect)
[2025-05-25 12:39:46] [INFO] [PathProtectionManager] 添加保护路径: /usr/bin/sudo (级别: full_protect)
[2025-05-25 12:39:46] [INFO] [PathProtectionManager] 添加保护路径: /usr/bin/su (级别: full_protect)
[2025-05-25 12:39:46] [INFO] [PathProtectionManager] 添加保护路径: /sbin/init (级别: full_protect)
[2025-05-25 12:39:46] [INFO] [PathProtectionManager] 更新保护路径: /etc/passwd
[2025-05-25 12:39:46] [INFO] [PathProtectionManager] 更新保护路径: /etc/shadow
[2025-05-25 12:39:46] [INFO] [PathProtectionManager] 更新保护路径: /etc/group
[2025-05-25 12:39:46] [INFO] [PathProtectionManager] 更新保护路径: /etc/sudoers
[2025-05-25 12:39:46] [INFO] [PathProtectionManager] 添加保护路径: /etc/ssh/ (级别: read_only)
[2025-05-25 12:39:46] [INFO] [PathProtectionManager] 添加保护路径: /etc/ssl/ (级别: read_only)
[2025-05-25 12:39:46] [INFO] [PathProtectionManager] 添加保护路径: /boot/ (级别: read_only)
[2025-05-25 12:39:46] [INFO] [PathProtectionManager] 添加保护路径: /usr/bin/ (级别: read_only)
[2025-05-25 12:39:46] [INFO] [PathProtectionManager] 添加保护路径: /usr/sbin/ (级别: read_only)
[2025-05-25 12:39:46] [INFO] [PathProtectionManager] 添加保护路径: /sbin/ (级别: read_only)
[2025-05-25 12:39:46] [INFO] [PathProtectionManager] 添加保护路径: /lib/ (级别: read_only)
[2025-05-25 12:39:46] [INFO] [PathProtectionManager] 添加保护路径: /lib64/ (级别: read_only)
[2025-05-25 12:39:46] [INFO] [PathProtectionManager] 添加保护路径: /usr/lib/ (级别: read_only)
[2025-05-25 12:39:46] [INFO] [PathProtectionManager] 添加保护路径: /usr/lib64/ (级别: read_only)
[2025-05-25 12:39:46] [INFO] [PathProtectionManager] 路径保护管理器初始化完成，保护 21 个路径
```

---

太好了！程序现在能够正常启动并初始化监控器了。让我创建一个测试来验证文件监控功能：

---

```bash
cd /home/<USER>/coder/psfsmonL
```

```
helloub22@helloub22-virtual-machine:~/coder/psfsmonL$ 

```

---

```bash
sudo killall -9 psfsmon; sudo ./bin/psfsmon --verbose &
```

---

现在让我们创建一些测试文件来验证监控功能：

---

```bash
cd /home/<USER>/coder/psfsmonL && echo "测试文件内容" > test_file.txt && cat test_file.txt && rm test_file.txt
```

```
helloub22@helloub22-virtual-machine:~/coder/psfsmonL$ cd /home/<USER>/coder/psfsmonL && echo "测试文件内容" > tes
helloub22@helloub22-virtual-machine:~/coder/psfsmonL$ cd /home/<USER>/coder/psfsmonL && echo "测试文件内容" > tes
t_file.txt && cat test_file.txt && rm test_file.txt
测试文件内容
helloub22@helloub22-virtual-machine:~/coder/psfsmonL$
```

---

让我检查程序是否正在记录事件。首先查看API状态：

---

```bash
curl -s http://localhost:8080/api/stats 2>/dev/null || echo "API服务器未启动"
```

```
helloub22@helloub22-virtual-machine:~/coder/psfsmonL$ curl -s http://localhost:8080/api/stats 2>/dev/null || echo "A
helloub22@helloub22-virtual-machine:~/coder/psfsmonL$ curl -s http://localhost:8080/api/stats 2>/dev/null || echo "A
PI服务器未启动"
API服务器未启动
helloub22@helloub22-virtual-machine:~/coder/psfsmonL$
```

---

看起来API服务器可能没有启动。让我检查一下程序状态：

---

```bash
ps aux | grep psfsmon
```

```
helloub22@helloub22-virtual-machine:~/coder/psfsmonL$ ps aux | grep psfsmon
您的 131072x1 的屏幕尺寸可能并非真实尺寸。程序运行可能不正常
root        4934  0.0  0.0  17108  5504 pts/1    S+   12:40   0:00 sudo killall -9 psfsmon
helloub+    5011  0.0  0.0  12192  2304 pts/2    S+   12:40   0:00 grep --color=auto psfsmon
helloub22@helloub22-virtual-machine:~/coder/psfsmonL$
```

---

看起来程序没有在后台运行。让我直接以前台方式启动程序来查看详细的运行情况：

---

```bash
sudo ./bin/psfsmon --verbose
```

---



---

