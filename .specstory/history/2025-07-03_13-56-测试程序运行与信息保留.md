<!-- Generated by SpecStory -->

# 测试程序运行与信息保留 (2025-07-03 21:56:58)

_**User**_

　类似的调用都是有问题的，我们不能等待程序退出，我们必须在程序运行的同时，往下面走自己的代码。
　被测试程序也不应该使用类似代码移除自身信息，因为我们会找不到它们了。
　
　最后，被测试程序，还要等待足够的时间，避免项目服务因为进程退出而删除相关统计信息，这样我们的主测试程序就无法读取它们并进程统计性测试了。

---

_**Assistant**_



---

我理解您指出的问题。让我先查看相关的测试代码，了解当前的实现情况，然后进行修正。

---

<details>
            <summary>Searched codebase "测试程序启动子进程但不等待退出的代码" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `test/test_api_comprehensive.cpp` | L1658-1704 |
| `test/test_api_comprehensive.cpp` | L738-768 |
| `test/entropy_test_program.cpp` | L364-408 |
| `test/test_api_comprehensive.cpp` | L1030-1070 |
| `test/test_entropy_calculation.sh` | L76-156 |
| `test/test_config_reload.sh` | L94-164 |
| `test/test_api_comprehensive.cpp` | L1776-1812 |
| `test/test_api_comprehensive.cpp` | L923-956 |
| `test/test_entropy_simple.sh` | L1-76 |
| `test/test_config_reload_fix.sh` | L1-93 |
| `test/test_config_reload_fix.sh` | L93-171 |
| `test/test_api_comprehensive.cpp` | L768-788 |
| `test/entropy_test_program.cpp` | L492-553 |
| `test/entropy_test_program.cpp` | L196-219 |
| `test/test_api_comprehensive.cpp` | L956-988 |
| `test/test_large_file_entropy.sh` | L1-92 |
| `test/test_entropy_calculation.sh` | L1-76 |
| `test/entropy_test_program.cpp` | L57-104 |
| `test/entropy_test_program.cpp` | L155-196 |
| `test/protected_path_violator.cpp` | L83-136 |
| `test/test_large_file_entropy.sh` | L92-140 |
| `test/test_entropy_calculation.sh` | L156-171 |
| `test/test_api_comprehensive.cpp` | L1356-1395 |
| `test/test_api_with_curl.sh` | L297-333 |
| `test/test_config_reload.sh` | L164-241 |

</details>

---



---

<details>
            <summary>Grep search for "system.*&" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L1016 | `kernel_supports_filesystem_mark_ = (major > 4 || (major == 4 && minor >= 20));` |
| `file:///home/<USER>/coder/psfsmonL/src/api_handlers_basic.cpp` | L23 | `void ApiServer::handleGetSystemStats(const HttpRequest& request, HttpResponse& response) {` |

</details>

---



---

Read file: test/test_api_comprehensive.cpp

---



---

Read file: test/entropy_test_program.cpp

---



---

<details>
            <summary>Grep search for "unregister\|移除" • **0** files</summary>
        
No results found
</details>

---



---

Read file: test/protected_path_violator.cpp

---



---

我理解您指出的问题。这些测试代码确实存在三个关键问题：

1. **启动子进程时等待退出**：使用`system()`会等待子进程完成，导致测试无法并发进行
2. **被测试程序自动移除注册信息**：程序退出时会调用`unregisterFromFocusProcess()`，导致测试找不到它们
3. **程序运行时间不够**：程序退出过早，导致监控服务删除统计信息

让我修正这些问题：

---



---



---

Read file: test/test_api_comprehensive.cpp

---



---



---



---

<details>
            <summary>Grep search for "system.*entropy_test_program" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///home/<USER>/coder/psfsmonL/test/test_api_comprehensive.cpp` | L877 | `if (system("pkill -f entropy_test_program") != 0) {` |
| `file:///home/<USER>/coder/psfsmonL/test/test_api_comprehensive.cpp` | L1015 | `if (system("pkill -f entropy_test_program") != 0) {` |
| `file:///home/<USER>/coder/psfsmonL/test/test_api_comprehensive.cpp` | L877 | `if (system("pkill -f entropy_test_program") != 0) {` |
| `file:///home/<USER>/coder/psfsmonL/test/test_api_comprehensive.cpp` | L1015 | `if (system("pkill -f entropy_test_program") != 0) {` |

</details>

---



---



---



---



---



---

<details>
            <summary>Grep search for "unregisterFromFocusProcess" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///home/<USER>/coder/psfsmonL/test/entropy_test_program.cpp` | L196 | `bool unregisterFromFocusProcess() {` |
| `file:///home/<USER>/coder/psfsmonL/test/entropy_test_program.cpp` | L482 | `unregisterFromFocusProcess();` |
| `file:///home/<USER>/coder/psfsmonL/test/entropy_test_program.cpp` | L530 | `unregisterFromFocusProcess();` |

</details>

---



---

Read file: test/entropy_test_program.cpp

---



---



---



---



---



---

Read file: test/protected_path_violator.cpp

---



---



---



---



---



---



---



---



---



---



---



---



---



---

<details>
            <summary>Grep search for "entropy.*测试.*完成" • **0** files</summary>
        
No results found
</details>

---



---

Read file: test/test_api_comprehensive.cpp

---



---

Read file: test/test_api_comprehensive.cpp

---



---



---



---

我已经完成了对测试代码的修正，解决了您指出的三个关键问题：

## 修改内容总结：

### 1. **修复子进程启动等待问题**
- **原问题**：使用`system()`命令会等待子进程退出
- **解决方案**：改用`fork()+exec()`启动子进程，不等待退出
- **修改位置**：
  - `test_api_comprehensive.cpp` 中启动 `protected_path_violator` 的地方
  - `test_api_comprehensive.cpp` 中启动 `entropy_test_program` 的地方

### 2. **移除被测试程序的自清理功能**
- **原问题**：程序退出时调用`unregisterFromFocusProcess()`，导致测试找不到它们
- **解决方案**：移除自动清理调用，改为注释说明由主测试程序负责清理
- **修改位置**：
  - `entropy_test_program.cpp` 中的两处 `unregisterFromFocusProcess()` 调用
  - `protected_path_violator.cpp` 中的受保护路径自动删除代码

### 3. **延长被测试程序运行时间**
- **原问题**：程序退出过早，导致监控服务删除统计信息
- **解决方案**：
  - `entropy_test_program` 快速模式运行30秒，完整模式运行60秒
  - `protected_path_violator` 等待时间从30秒增加到60秒
- **好处**：确保主测试程序有足够时间读取和验证统计信息

### 4. **添加主测试程序清理功能**
- 在测试完成时负责清理所有子进程
- 清理相关的配置（如受保护路径、重点进程注册）
- 在正常完成和异常情况下都进行清理

## 核心改进：

1. **真正的并发测试**：主测试程序现在可以在子进程运行的同时继续执行测试逻辑
2. **数据持久性**：子进程保持注册状态直到被主程序清理，确保统计数据可用
3. **充足的测试窗口**：延长的运行时间为主测试程序提供了充足的时间进行各种检查和验证

这些修改确保了测试的可靠性和准确性，避免了因进程管理问题导致的测试失败。

---

