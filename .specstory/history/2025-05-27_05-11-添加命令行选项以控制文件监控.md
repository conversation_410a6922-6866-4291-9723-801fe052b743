<!-- Generated by SpecStory -->

# 添加命令行选项以控制文件监控 (2025-05-27 13:11:26)

_**User**_

　现在，当前项目的基本功能已经完成了。
　但是，我想在文件监控时，添加一个命令行选项，即no-file-inotify-monitor选项，这个选项的意思是，我有fanotify的监控就足够了，在这个选项出现时，不再使用inotify监控。而且，这个选项默认就是启用的。
　当然，相应的代码也需要更改。
　请在当前项目框架基础上，完成上面我所定义的选项功能，保证inotify可以不启用，也可以在特殊情况下使用命令行选项功能启用。

---

_**Assistant**_



---

我来帮你添加 `--no-file-inotify-monitor` 选项功能。首先让我探索一下当前项目的结构，了解文件监控和配置相关的代码。

---

<details>
            <summary>Listed current directory • **18** results</summary>
        
| Name |
|-------|
| 📁 `test` |
| 📁 `.git` |
| 📄 `.gitignore` |
| 📁 `.idea` |
| 📄 `README.md` |
| 📁 `docs` |
| 📄 `REQUIREMENTS.md` |
| 📄 `API_REFERENCE.md` |
| 📄 `CMakeLists.txt` |
| 📁 `src` |
| 📁 `.specstory` |
| 📁 `ref_code` |
| 📁 `include` |
| 📁 `scripts` |
| 📁 `config` |
| 📄 `configure` |
| 📄 `Makefile` |
| 📄 `.cursorindexingignore` |

</details>

---

<details>
            <summary>Listed directory include • **8** results</summary>
        
| Name |
|-------|
| 📄 `psfsmon.h` |
| 📄 `process_monitor.h` |
| 📄 `file_monitor.h` |
| 📄 `api_server.h` |
| 📄 `network_monitor.h` |
| 📄 `process_exclusion.h` |
| 📄 `path_protection.h` |
| 📄 `main_monitor.h` |

</details>

---

让我查看配置相关的头文件，首先从主头文件开始：

---

Read file: include/psfsmon.h

---

让我查看更多关于配置的内容：

---

Read file: include/psfsmon.h

---

现在我需要查看文件监控相关的代码，了解 inotify 和 fanotify 的实现：

---

Read file: include/file_monitor.h

---

现在让我查看 ConfigManager 相关的代码：

---

<details>
            <summary>Searched codebase "ConfigManager class parseCommandLine" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/main_monitor.cpp` | L427-453 |
| `src/main.cpp` | L142-198 |
| `include/main_monitor.h` | L123-173 |
| `src/main_monitor.cpp` | L453-502 |
| `src/main_monitor.cpp` | L405-427 |
| `src/main_monitor.cpp` | L264-311 |
| `src/main_monitor.cpp` | L311-331 |
| `src/main_monitor.cpp` | L369-405 |
| `src/main_monitor.cpp` | L331-351 |
| `src/main_monitor.cpp` | L351-369 |
| `src/process_exclusion.cpp` | L160-215 |
| `src/main.cpp` | L198-239 |
| `docs/CONFIG_GUIDE.md` | L1-92 |
| `docs/CONFIG_GUIDE.md` | L144-223 |
| `README.md` | L94-166 |
| `src/main_monitor.cpp` | L201-264 |
| `include/process_exclusion.h` | L1-47 |
| `src/main.cpp` | L53-81 |
| `src/main.cpp` | L239-286 |
| `include/psfsmon.h` | L221-264 |
| `include/psfsmon.h` | L174-221 |
| `ref_code/fanotify_monitor.c` | L473-509 |
| `src/main_monitor.cpp` | L1-68 |
| `test/test_command_line_options.sh` | L1-77 |
| `include/main_monitor.h` | L1-44 |

</details>

---

现在我需要查看文件监控的实现代码，了解具体的 inotify 和 fanotify 逻辑：

---

<details>
            <summary>Searched codebase "file_monitor.cpp" • **1** results</summary>
        
| File |
|------|
| `undefined` |

</details>

---

Read file: src/file_monitor.cpp

---

现在我需要查看更多关于 inotify 和 fanotify 的配置选项，来理解需要如何修改：

---

Read file: src/file_monitor.cpp

---

现在我需要查看 FileMonitor 的 start 方法和初始化逻辑，了解如何控制 inotify 的启动：

---

Read file: src/file_monitor.cpp

---

现在我理解了代码结构。基于我的分析，我需要：

1. 在 `MonitorConfig` 结构中添加一个新的配置选项来控制 inotify 的启用/禁用
2. 修改命令行解析逻辑来处理新的选项
3. 修改文件监控器的初始化逻辑，根据配置决定是否启用 inotify
4. 更新帮助信息和相关文档

让我开始实现这些更改：

## 1. 首先修改配置结构

---



---



---

## 2. 修改命令行解析逻辑

---



---

现在我需要找到 parseCommandLine 函数来添加新的选项解析：

---

<details>
            <summary>Grep search for "--no-file-monitor" • **17** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///home/<USER>/coder/psfsmonL/README.md` | L118 | `- `--no-file-monitor`: 禁用文件监控` |
| `file:///home/<USER>/coder/psfsmonL/README.md` | L132 | `sudo ./bin/psfsmon --no-file-monitor --no-network-monitor` |
| `file:///home/<USER>/coder/psfsmonL/src/main_monitor.cpp` | L466 | `} else if (strcmp(argv[i], "--no-file-monitor") == 0) {` |
| `file:///home/<USER>/coder/psfsmonL/src/main.cpp` | L63 | `std::cout << "  --no-file-monitor       禁用文件监控" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/REQUIREMENTS.md` | L544 | `--no-file-monitor     禁用文件监控` |
| `file:///home/<USER>/coder/psfsmonL/docs/CONFIG_GUIDE.md` | L169 | `sudo ./bin/psfsmon --no-file-monitor         # 禁用文件监控` |
| `file:///home/<USER>/coder/psfsmonL/docs/CONFIG_GUIDE.md` | L198 | `sudo ./bin/psfsmon --no-file-monitor --no-network-monitor` |
| `file:///home/<USER>/coder/psfsmonL/test/test_command_line_options.sh` | L64 | `echo "命令: timeout 3 ./bin/psfsmon --no-file-monitor --no-network-monitor --port 9997 --verbose"` |
| `file:///home/<USER>/coder/psfsmonL/test/test_command_line_options.sh` | L66 | `timeout 3 ./bin/psfsmon --no-file-monitor --no-network-monitor --port 9997 --verbose 2>&1 | head -10 || true` |
| `file:///home/<USER>/coder/psfsmonL/test/test_command_line_options.sh` | L148 | `echo "✓ --no-file-monitor 文件监控禁用正常"` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_04-32-%E9%92%88%E5%AF%B9fs%E4%BF%AE%E6%AD%A3.md` | L224 | `--no-file-monitor       禁用文件监控` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-26_10-58-%E9%A1%B9%E7%9B%AE%E5%AE%9E%E7%8E%B0%E7%BB%86%E8%8A%82%E4%B8%8Eapi%E6%8E%A5%E5%8F%A3%E6%A3%80%E6%9F%A5.md` | L1393 | `--no-file-monitor       禁用文件监控` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-26_10-58-%E9%A1%B9%E7%9B%AE%E5%AE%9E%E7%8E%B0%E7%BB%86%E8%8A%82%E4%B8%8Eapi%E6%8E%A5%E5%8F%A3%E6%A3%80%E6%9F%A5.md` | L2787 | `--no-file-monitor       禁用文件监控` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_10-27-%E4%BF%AE%E5%A4%8D%E9%A1%B9%E7%9B%AE%E7%BC%96%E8%AF%91%E9%94%99%E8%AF%AF%E4%B8%8E%E6%96%87%E6%A1%A3%E6%9B%B4%E6%96%B0.md` | L296 | `--no-file-monitor       禁用文件监控` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_01-51-%E5%AE%89%E5%85%A8%E7%9B%91%E6%8E%A7%E7%A8%8B%E5%BA%8F%E9%9C%80%E6%B1%82%E6%A0%B8%E5%AF%B9.md` | L2320 | `--no-file-monitor       禁用文件监控` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-24_22-57-linux%E5%AE%89%E5%85%A8%E7%9B%91%E6%8E%A7%E7%A8%8B%E5%BA%8F%E5%BC%80%E5%8F%91%E8%AE%A8%E8%AE%BA.md` | L2406 | `+     std::cout << "  --no-file-monitor       禁用文件监控" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-24_22-57-linux%E5%AE%89%E5%85%A8%E7%9B%91%E6%8E%A7%E7%A8%8B%E5%BA%8F%E5%BC%80%E5%8F%91%E8%AE%A8%E8%AE%BA.md` | L3374 | `+         } else if (strcmp(argv[i], "--no-file-monitor") == 0) {` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-24_22-57-linux%E5%AE%89%E5%85%A8%E7%9B%91%E6%8E%A7%E7%A8%8B%E5%BA%8F%E5%BC%80%E5%8F%91%E8%AE%A8%E8%AE%BA.md` | L5998 | `--no-file-monitor       禁用文件监控` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-24_22-57-linux%E5%AE%89%E5%85%A8%E7%9B%91%E6%8E%A7%E7%A8%8B%E5%BA%8F%E5%BC%80%E5%8F%91%E8%AE%A8%E8%AE%BA.md` | L6655 | `--no-file-monitor       禁用文件监控` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_08-24-%E8%B0%83%E8%AF%95%E7%A8%8B%E5%BA%8F%E4%B8%8E%E6%B5%8B%E8%AF%95%E5%8A%9F%E8%83%BD.md` | L443 | `+ sudo ./bin/psfsmon --no-file-monitor --port 9092` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_08-24-%E8%B0%83%E8%AF%95%E7%A8%8B%E5%BA%8F%E4%B8%8E%E6%B5%8B%E8%AF%95%E5%8A%9F%E8%83%BD.md` | L715 | `--no-file-monitor       禁用文件监控` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_08-24-%E8%B0%83%E8%AF%95%E7%A8%8B%E5%BA%8F%E4%B8%8E%E6%B5%8B%E8%AF%95%E5%8A%9F%E8%83%BD.md` | L807 | `+ sudo ./bin/psfsmon --no-file-monitor --port 9091` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_08-24-%E8%B0%83%E8%AF%95%E7%A8%8B%E5%BA%8F%E4%B8%8E%E6%B5%8B%E8%AF%95%E5%8A%9F%E8%83%BD.md` | L1183 | `+ - [x] --no-file-monitor 禁用文件监控` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_01-25-linux-security-monitoring-program-requirements.md` | L483 | `+   --no-file-monitor     禁用文件监控` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_04-32-untitled.md` | L224 | `--no-file-monitor       禁用文件监控` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-25_00-19-%E7%BC%96%E8%AF%91%E4%B8%8E%E6%B5%8B%E8%AF%95%E7%A8%8B%E5%BA%8F.md` | L135 | `--no-file-monitor       禁用文件监控` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L2406 | `+     std::cout << "  --no-file-monitor       禁用文件监控" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L3374 | `+         } else if (strcmp(argv[i], "--no-file-monitor") == 0) {` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L5996 | `--no-file-monitor       禁用文件监控` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L6653 | `--no-file-monitor       禁用文件监控` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L6980 | `--no-file-monitor       禁用文件监控` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L10089 | `+   --no-file-monitor     禁用文件监控` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L14732 | `--no-file-monitor       禁用文件监控` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L15159 | `--no-file-monitor       禁用文件监控` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L16010 | `+ sudo ./bin/psfsmon --no-file-monitor --port 9092` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L16282 | `--no-file-monitor       禁用文件监控` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L16374 | `+ sudo ./bin/psfsmon --no-file-monitor --port 9091` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L16750 | `+ - [x] --no-file-monitor 禁用文件监控` |
| `file:///home/<USER>/coder/psfsmonL/src/main.cpp` | L63 | `std::cout << "  --no-file-monitor       禁用文件监控" << std::endl;` |

</details>

---

Read file: src/main_monitor.cpp

---

现在我需要在 parseCommandLine 函数中添加新的选项：

---



---

## 3. 需要在配置文件加载中也添加对新选项的支持

---



---

## 4. 现在需要修改文件监控器，根据配置决定是否启用 inotify

首先我需要看一下 FileMonitor 如何被初始化的，需要传递配置：

---

<details>
            <summary>Searched codebase "FileMonitor initialize config" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/main_monitor.cpp` | L311-331 |
| `src/main_monitor.cpp` | L1-68 |
| `src/main_monitor.cpp` | L351-369 |
| `include/main_monitor.h` | L1-44 |
| `src/main_monitor.cpp` | L136-201 |
| `include/psfsmon.h` | L221-264 |
| `src/main_monitor.cpp` | L369-405 |
| `config/psfsmon.conf` | L1-102 |
| `include/file_monitor.h` | L122-175 |
| `include/main_monitor.h` | L44-123 |
| `include/file_monitor.h` | L59-122 |
| `src/main_monitor.cpp` | L453-502 |
| `docs/CONFIG_GUIDE.md` | L1-92 |
| `src/main_monitor.cpp` | L201-264 |
| `src/file_monitor.cpp` | L220-304 |
| `src/file_monitor.cpp` | L1250-1307 |
| `include/main_monitor.h` | L123-173 |
| `src/main_monitor.cpp` | L331-351 |
| `include/psfsmon.h` | L174-221 |
| `CMakeLists.txt` | L1-87 |
| `src/file_monitor.cpp` | L586-639 |
| `src/file_monitor.cpp` | L1176-1250 |
| `src/main_monitor.cpp` | L264-311 |
| `src/main.cpp` | L198-239 |
| `src/file_monitor.cpp` | L639-697 |

</details>

---

现在我发现了一个问题。从搜索结果来看，目前项目使用的是 `FileMonitorManager` 单例模式来管理 `FileMonitor`，而不是直接传递配置。我需要查看 `FileMonitorManager` 的实现，了解如何传递配置。

---

Read file: include/file_monitor.h

---

我看到 `FileMonitorManager` 没有接受配置参数的方法。我需要修改它来支持配置传递。首先，我需要添加一个方法来设置配置，然后修改 `FileMonitor` 的 `initialize` 方法来支持配置参数。

## 5. 修改 FileMonitor 来支持配置

首先修改头文件，为 `FileMonitor` 添加配置参数：

---



---

现在修改 `FileMonitorManager` 来支持配置传递：

---



---

现在需要在 `FileMonitor` 类中添加配置存储：

---



---

## 6. 修改 FileMonitor 实现以支持配置

---



---



---

## 7. 修改 FileMonitorManager 实现

---

Read file: src/file_monitor.cpp

---



---



## 8. 修改 MainMonitor 来传递配置

---



---

## 9. 更新配置文件示例

---



---

## 10. 更新详细输出信息

---



---

## 11. 更新文档

---



---



---

让我先查看文档的内容：

---

Read file: docs/CONFIG_GUIDE.md

---



---



---

## 12. 更新 README.md

---



---



---

## 13. 更新测试脚本

---



---

让我查看测试脚本的内容：

---

Read file: test/test_command_line_options.sh

---



---

我来帮您在项目中添加 `--no-file-inotify-monitor` 命令行选项。首先让我探索一下项目结构，了解当前的配置和监控实现。

---

<details>
            <summary>Listed current directory • **18** results</summary>
        
| Name |
|-------|
| 📄 `README.md` |
| 📁 `test` |
| 📁 `.git` |
| 📄 `.gitignore` |
| 📁 `.idea` |
| 📁 `docs` |
| 📄 `REQUIREMENTS.md` |
| 📄 `API_REFERENCE.md` |
| 📄 `CMakeLists.txt` |
| 📁 `src` |
| 📁 `.specstory` |
| 📁 `ref_code` |
| 📁 `include` |
| 📁 `scripts` |
| 📁 `config` |
| 📄 `configure` |
| 📄 `Makefile` |
| 📄 `.cursorindexingignore` |

</details>

---

<details>
            <summary>Listed directory src • **10** results</summary>
        
| Name |
|-------|
| 📄 `main.cpp` |
| 📄 `main_monitor.cpp` |
| 📄 `file_monitor.cpp` |
| 📄 `process_monitor.cpp` |
| 📄 `psfsmon_core.cpp` |
| 📄 `api_server.cpp` |
| 📄 `network_monitor.cpp` |
| 📄 `path_protection.cpp` |
| 📄 `process_exclusion.cpp` |
| 📄 `utils.cpp` |

</details>

---

<details>
            <summary>Listed directory include • **8** results</summary>
        
| Name |
|-------|
| 📄 `file_monitor.h` |
| 📄 `psfsmon.h` |
| 📄 `process_monitor.h` |
| 📄 `api_server.h` |
| 📄 `network_monitor.h` |
| 📄 `process_exclusion.h` |
| 📄 `path_protection.h` |
| 📄 `main_monitor.h` |

</details>

---

现在让我查看主要的配置文件和监控相关代码，了解当前的实现：

---

Read file: include/psfsmon.h

---

很好！我看到在 `MonitorConfig` 结构中已经有一个 `enable_inotify_monitoring` 配置选项。现在让我查看文件监控的实现：

---

Read file: src/file_monitor.cpp

---

现在让我查看主程序中的命令行解析部分：

---

<details>
            <summary>Searched codebase "命令行解析 parseCommandLine" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/main_monitor.cpp` | L429-455 |
| `src/main.cpp` | L143-199 |
| `include/main_monitor.h` | L123-173 |
| `src/main_monitor.cpp` | L455-502 |
| `src/api_server.cpp` | L1615-1655 |
| `src/main.cpp` | L53-80 |
| `src/main_monitor.cpp` | L407-429 |
| `src/main_monitor.cpp` | L264-311 |
| `src/process_monitor.cpp` | L272-340 |
| `README.md` | L94-166 |
| `src/network_monitor.cpp` | L378-427 |
| `src/api_server.cpp` | L935-973 |
| `src/process_exclusion.cpp` | L160-215 |
| `src/api_server.cpp` | L1702-1757 |
| `include/api_server.h` | L223-249 |
| `src/main.cpp` | L199-235 |
| `src/api_server.cpp` | L1085-1132 |
| `include/process_monitor.h` | L77-94 |
| `ref_code/fanotify_monitor.c` | L473-509 |
| `test/test_command_line_options.sh` | L1-77 |
| `ref_code/fanotify_monitor.c` | L1-73 |
| `ref_code/fanotify_monitor.c` | L509-557 |
| `include/api_server.h` | L128-185 |
| `src/main.cpp` | L1-53 |
| `src/main.cpp` | L105-143 |

</details>

---

现在我看到了当前的实现情况。我发现：

1. 在 `MonitorConfig` 结构中已经有 `enable_inotify_monitoring` 配置选项
2. 在 `parseCommandLine` 函数中已经有 `--enable-inotify` 选项
3. 在 `file_monitor.cpp` 中已经有根据配置禁用inotify的逻辑

但是我注意到用户想要的是 `--no-file-inotify-monitor` 选项，而且希望默认禁用inotify。让我检查当前的默认配置：

---

<details>
            <summary>Grep search for "enable_inotify_monitoring.*=" • **6** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///home/<USER>/coder/psfsmonL/src/main_monitor.cpp` | L360 | `config.enable_inotify_monitoring = parseBool(value);` |
| `file:///home/<USER>/coder/psfsmonL/src/main_monitor.cpp` | L475 | `config.enable_inotify_monitoring = true;` |
| `file:///home/<USER>/coder/psfsmonL/docs/CONFIG_GUIDE.md` | L59 | `enable_inotify_monitoring = false` |
| `file:///home/<USER>/coder/psfsmonL/config/psfsmon.conf` | L56 | `enable_inotify_monitoring = false` |
| `file:///home/<USER>/coder/psfsmonL/src/main_monitor.cpp` | L360 | `config.enable_inotify_monitoring = parseBool(value);` |
| `file:///home/<USER>/coder/psfsmonL/src/main_monitor.cpp` | L475 | `config.enable_inotify_monitoring = true;` |
| `file:///home/<USER>/coder/psfsmonL/config/psfsmon.conf` | L56 | `enable_inotify_monitoring = false` |
| `file:///home/<USER>/coder/psfsmonL/docs/CONFIG_GUIDE.md` | L59 | `enable_inotify_monitoring = false` |

</details>

---

让我查看默认配置的设置：

---

Read file: src/main_monitor.cpp

---

我需要查看 `getDefaultConfig()` 函数的完整实现：

---

<details>
            <summary>Grep search for "MonitorConfig ConfigManager::getDefaultConfig" • **4** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///home/<USER>/coder/psfsmonL/src/main_monitor.cpp` | L494 | `MonitorConfig ConfigManager::getDefaultConfig() {` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/2025-05-24_22-57-linux%E5%AE%89%E5%85%A8%E7%9B%91%E6%8E%A7%E7%A8%8B%E5%BA%8F%E5%BC%80%E5%8F%91%E8%AE%A8%E8%AE%BA.md` | L3386 | `+ MonitorConfig ConfigManager::getDefaultConfig() {` |
| `file:///home/<USER>/coder/psfsmonL/.specstory/history/Linux_X64%E4%B8%8B%E7%BC%96%E8%AF%91%E6%88%90%E5%8A%9F%E5%B9%B6%E5%90%88%E5%B9%B6.md` | L3386 | `+ MonitorConfig ConfigManager::getDefaultConfig() {` |
| `file:///home/<USER>/coder/psfsmonL/src/main_monitor.cpp` | L494 | `MonitorConfig ConfigManager::getDefaultConfig() {` |

</details>

---

Read file: src/main_monitor.cpp

---

我看到 `getDefaultConfig()` 函数很简单，只设置了两个字段。让我查看 `MonitorConfig` 结构的构造函数或默认值：

---

Read file: include/psfsmon.h

---

很好！我看到在 `MonitorConfig` 的构造函数中，`enable_inotify_monitoring` 已经默认设置为 `false`。现在我需要根据用户的需求添加 `--no-file-inotify-monitor` 选项。

根据用户的要求，我需要：
1. 添加 `--no-file-inotify-monitor` 命令行选项，用于禁用inotify监控
2. 默认情况下禁用inotify（这已经实现了）
3. 可以通过命令行选项启用inotify

但是我注意到当前的逻辑有点混乱。让我重新整理一下：

用户希望：
- 默认禁用inotify（仅使用fanotify）
- 添加 `--no-file-inotify-monitor` 选项来明确禁用inotify
- 在特殊情况下可以启用inotify

当前实现：
- 默认已经禁用inotify（`enable_inotify_monitoring(false)`）
- 有 `--enable-inotify` 选项来启用inotify

我认为当前的实现已经满足了用户的需求，但为了完全符合用户的要求，我会添加 `--no-file-inotify-monitor` 选项。让我修改代码：

---



---

现在我需要更新帮助信息，添加新的选项：

---



---





---

