<!-- Generated by SpecStory -->

# 清理项目中的废弃代码 (2025-06-30 21:23:06)

_**User**_

@file_monitor.cpp 将项目中所有已废弃的变量、函数，全部删除。然后，我们才会发现现有程序存在的问题。全面梳理代码以完成这一工作。

---

_**Assistant**_



---

我需要全面梳理 `file_monitor.cpp` 中的废弃代码。让我先分析当前代码结构，识别所有已废弃的变量和函数。

---

<details>
            <summary>Searched codebase "废弃 已废弃 兼容性保留 deprecated" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `include/kernel_compat.h` | L1-80 |
| `include/main_monitor.h` | L220-256 |
| `src/kernel_compat.cpp` | L1-80 |
| `src/file_monitor.cpp` | L392-432 |
| `src/kernel_compat.cpp` | L197-241 |
| `include/main_monitor.h` | L179-220 |
| `src/kernel_compat.cpp` | L241-277 |
| `src/file_monitor.cpp` | L257-296 |
| `src/kernel_compat.cpp` | L80-155 |
| `ref_docs/Fanotify框架说明.txt` | L948-994 |
| `src/kernel_compat.cpp` | L155-197 |
| `include/file_monitor.h` | L204-248 |
| `include/api_server.h` | L127-186 |
| `src/file_monitor.cpp` | L52-118 |
| `src/file_monitor.cpp` | L1-52 |
| `include/api_server.h` | L224-255 |
| `include/file_monitor.h` | L298-344 |
| `ref_docs/Fanotify框架说明.txt` | L994-1043 |
| `src/api_handlers_basic.cpp` | L121-139 |
| `include/file_monitor.h` | L137-180 |
| `src/file_monitor.cpp` | L912-963 |
| `include/file_monitor.h` | L1-43 |
| `src/main_monitor.cpp` | L128-200 |
| `src/file_monitor.cpp` | L963-1013 |
| `src/file_monitor.cpp` | L1036-1081 |

</details>

---



---

现在我来全面梳理并删除 `file_monitor.cpp` 中所有已废弃的变量和函数。基于代码分析，我识别出以下废弃内容：

1. **废弃变量**：
   - `fanotify_fd_` - 已明确标注为废弃
   - `event_queue_`, `event_queue_mutex_`, `event_condition_` - 已被双线程架构替代

2. **废弃函数**：
   - `processEvent()` - 标注为"兼容性保留（已废弃）"
   - `processFileEvent()` - 标注为"兼容性保留（已废弃）"
   - `handleFocusProcessEvent()` - 被新的双线程处理替代
   - `handleProtectedPathCheck()` - 被新的双线程处理替代
   - `calculateAndRecordEntropy()` - 被新的熵值计算方法替代

让我进行彻底的清理：

---

```diff
      void calculateReadEntropy(FileEvent& event);
      void calculateWriteEntropy(FileEvent& event);
-     
-     // 兼容性保留（已废弃）
-     void processEvent();
-     bool processFileEvent(struct fanotify_event_metadata* metadata);
-     void handleFocusProcessEvent(const FileEvent& event);
-     bool handleProtectedPathCheck(const FileEvent& event);
      
      // 基础fanotify初始化方法 - 根据需求13简化实现
```

```diff
      
      // 成员变量
-     int fanotify_fd_;                    // 主fanotify文件描述符（已废弃，保持兼容性）
      int protected_fanotify_fd_;          // 受保护路径fanotify文件描述符
      int focus_fanotify_fd_;              // 重点进程fanotify文件描述符
```

```diff
      // 事件处理
      std::function<void(const FileEvent&)> event_callback_;
-     std::queue<FileEvent> event_queue_;
-     std::mutex event_queue_mutex_;
-     std::condition_variable event_condition_;
-     
- 
      
      // 统计信息
```

```diff
      
      // 重点进程熵值监控
-     void calculateAndRecordEntropy(FileEvent& event);
      void updateFocusProcessEntropy(pid_t pid, const FileEvent& event);
  
```

---



---

现在清理 `file_monitor.cpp` 中对应的废弃实现：

---

```diff
  // FileMonitor 实现
  FileMonitor::FileMonitor() 
-     : fanotify_fd_(-1), running_(false), initialized_(false),
-       config_(nullptr) {
+     : running_(false), initialized_(false), config_(nullptr) {
      
      // 显式初始化所有关键成员变量
```

---



---



---



---



---



---



---



---



---



---



---



---



---



---



---



---



---



---

<details>
            <summary>Grep search for "void FileMonitor::processEvent\(\)" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L519 | `void FileMonitor::processEvent() {` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L519 | `void FileMonitor::processEvent() {` |

</details>

---

