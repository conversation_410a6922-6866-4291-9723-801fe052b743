<!-- Generated by SpecStory -->

# 更新安全监控程序需求 (2025-06-09 08:25:52)

_**User**_

	下面是你上一个会话任务，但是中间异常中断了，请继续相关工作。

	当前项目目录下的代码，主体结构已经完成，功能比较完整和正确。
	但是，我的需求发生了重大变化，当前项目虽然主体依然可用，但是细节调整也是非常大的，请按下面的描述和需求变更，梳理和更改当前的程序代码。
	我需要你核对下面的需求，特别仔细的检查和细化文件监控能力，更改部份已经完成的功能代码，以体现最新版本的需求变更。
	所有需求如下（包括更改的需求）：
	1、我需要编写一个运行在Linux平台下面的安全监控程序；
	2、这个程序的主要功能是实时监控所有系统中所运行的进程并收集它们的状态，并具备汇报这些信息的能力；
	3、这个程序需要以C++语言进行编写，低层的库可以使用C语言。C++语言的标准，由于系统编译器的版本问题，要控制在可以支持C++11标准的状态；
	4、这个程序可能需要在不同的CPU上运行，例如x64/amd64, aarch64/arm64等等，但是不会跨平台编译，也就是说，它一定会在所在的平台下，用本地编译器进行编译；
	5、这个程序要在启动时收集所有的已有进程的信息，管理它们，具备形成进程树的能力；
	6、针对每个进程，要可以管理它创建或clone的所有线程、子线程；
	7、收集的信息要归属并管理在进程所属并管理的每个线程下面，但是要在进程这一层次有一个汇总；
	8、收集的信息主要包括3类，1是进程的基本信息例如名称、CPU使用状态、内存使用状态、路径等等；2是在一般情况下，不再收集进程的每个线程所操作的文件信息，也不再区分是进程的哪个线程操作了什么文件，不再需要通过各种方式找到文件操作的具体线程；3是进程的基本网络操作信息，例如打开了多少连接，连接了什么IP地址，每个IP收发的基本数据量是多少、整体收发数据量等等；
	9、因为OS的限制，我们不能使用eBPF机制来收集这些信息；这个要求非常重要；
	10、我们要使用Fanotify机制来进行特殊情况下的文件信息的获取，不再需要整合Inotify机制到本程序中，已有的整合要全部去除；Fanotify机制要根据OS的支持能力不同，尽可能多的监控文件变化操作，特别是读、写、删除和重命名操作，包括目录删除，相关的支持文档，在项目目录下的目录中，你可以自行查阅；
	11、对于进程信息、网络通信信息，你可以使用合适的方法，但是要避免依赖其它大规模的底层依赖库，因为我们的操作系统版本并不高，而且可能是多个CPU类型的，它的基础版本只有5.4.18，可能很多功能都不具备，例如Sysdig和它所使用的一些基础库是无法编译通过的；
	12、程序可以直接执行，以直观的方式进行输出，也可以以服务的方式进行安装和执行，通过对外接口形式进行输出；
	13、程序对外的接口，可以向调用者灵活的提供所输出的信息，例如进程树、每个进程的信息汇总，每个进程的详细信息例如它所管理的每个线程的所有的文件的操作序列字符串等等；
	14、每个进程不再管理所属的每个线程的文件信息；
	15、程序要支持CMake和标准Makefile这2种编译方式，或者使用auto config系列工具编译；
	16、支持忽略进程设置，被设置为忽略进程的，不再监控其相关信息（所有信息，包括文件操作信息）；
	17、支持重点进程设置，被设置为重点进程的，除进程基本信息外，还要依靠和使用Fanotify机制，尽可能多的监控其文件操作相关信息，也只有被设置为重点进程，才会监控文件操作信息；监控重点是读、写操作，要在读操作前，计算文件熵值；要在写操作后，再次计算文件熵值；记录的文件熵值，按读/写区分，统一累加记录到进程级别进行汇总；如果系统支持，使用Fanotify机制获取删除的文件数量、重命名的文件数量、重命名的扩展名列表、操作文件路径历史记录等信息，记录到重点进程的相关数据结构中（也可以是类属性中）供查询；提供接口API支持这一功能；该功能是变化最大的需求；
	18、支持特殊路径设置，被设置为特殊路径的，在发生针对特殊路径的写操作时，包括删除和重命名，相关的进程会被中止，并将相关记录在日志中；
	19、保持程序的可读性，如果单个源代码文件过大，例如超过了1500行，则可以在逻辑上拆分它，并在设计文件中给出简单的说明；
	20、同步更改相关的需求文档、测试代码和脚本。

---

_**Assistant**_



---

我来继续之前的工作，首先查看当前项目的结构，了解已完成的代码情况。

---

<details>
            <summary>Listed current directory • **23** results</summary>
        
| Name |
|-------|
| 📄 `REQUIREMENTS.md` |
| 📁 `refdocs` |
| 📁 `obj` |
| 📁 `.git` |
| 📁 `.idea` |
| 📁 `include` |
| 📁 `src` |
| 📁 `cmake-build-debug` |
| 📄 `CMakeLists.txt` |
| 📄 `Makefile` |
| 📄 `DEBUG_QUICK_START.md` |
| 📁 `docs` |
| 📄 `.gdbinit` |
| 📁 `scripts` |
| 📄 `.gitignore` |
| 📁 `test` |
| 📁 `ref_code` |
| 📄 `PROJECT_REVIEW_SUMMARY.md` |
| 📄 `README.md` |
| 📄 `API_REFERENCE.md` |
| 📁 `.specstory` |
| 📁 `config` |
| 📄 `.cursorindexingignore` |

</details>

---

让我查看主要的源代码目录结构：

---

<details>
            <summary>Listed directory src • **11** results</summary>
        
| Name |
|-------|
| 📄 `api_server.cpp` |
| 📄 `psfsmon_core.cpp` |
| 📄 `file_monitor.cpp` |
| 📄 `process_monitor.cpp` |
| 📄 `kernel_compat.cpp` |
| 📄 `main.cpp` |
| 📄 `main_monitor.cpp` |
| 📄 `network_monitor.cpp` |
| 📄 `path_protection.cpp` |
| 📄 `process_exclusion.cpp` |
| 📄 `utils.cpp` |

</details>

---

<details>
            <summary>Listed directory include • **9** results</summary>
        
| Name |
|-------|
| 📄 `api_server.h` |
| 📄 `file_monitor.h` |
| 📄 `psfsmon.h` |
| 📄 `process_monitor.h` |
| 📄 `kernel_compat.h` |
| 📄 `main_monitor.h` |
| 📄 `network_monitor.h` |
| 📄 `process_exclusion.h` |
| 📄 `path_protection.h` |

</details>

---

现在让我查看当前的需求文档，了解之前的实现情况：

---

Read file: REQUIREMENTS.md

---

现在查看项目总结文档，了解当前实现状况：

---

Read file: PROJECT_REVIEW_SUMMARY.md

---

让我查看当前主要模块的实现情况：

---

Read file: include/psfsmon.h

---

现在让我查看文件监控模块的实现：

---

Read file: include/file_monitor.h

---

现在让我查看文件监控实现的核心代码：

---

Read file: src/file_monitor.cpp

---

让我查看需求中提到的关键问题，特别是Inotify的使用情况：

---

<details>
            <summary>Grep search for "inotify" • **16** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///home/<USER>/coder/psfsmonL/include/kernel_compat.h` | L48 | `bool needsInotifySupport() const;` |
| `file:///home/<USER>/coder/psfsmonL/include/kernel_compat.h` | L53 | `FANOTIFY_WITH_INOTIFY,   // Fanotify + Inotify 混合 (5.4, 4.19)` |
| `file:///home/<USER>/coder/psfsmonL/include/kernel_compat.h` | L54 | `INOTIFY_FALLBACK         // 主要使用 Inotify (极老内核)` |
| `file:///home/<USER>/coder/psfsmonL/include/file_monitor.h` | L6 | `#include <sys/inotify.h>` |
| `file:///home/<USER>/coder/psfsmonL/include/file_monitor.h` | L41 | `// Inotify事件类型` |
| `file:///home/<USER>/coder/psfsmonL/include/file_monitor.h` | L42 | `namespace InotifyEvents {` |
| `file:///home/<USER>/coder/psfsmonL/src/main_monitor.cpp` | L589 | `file << "# - 内核 5.4.x：  使用Fanotify+Inotify混合模式" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/src/main_monitor.cpp` | L590 | `file << "# - 内核 4.19.x： 使用Fanotify+Inotify基础模式" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/src/main.cpp` | L72 | `std::cout << "  - 内核 5.4.x：  使用Fanotify+Inotify混合模式" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/src/main.cpp` | L73 | `std::cout << "  - 内核 4.19.x： 使用Fanotify+Inotify基础模式" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L11 | `*    - Fanotify + Inotify 混合模式` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L13 | `*    - Inotify补充处理CREATE/DELETE/MOVE事件` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L17 | `*    - Fanotify + Inotify 混合模式` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L1183 | `// 这里返回false，移动检测将依赖Inotify` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L1191 | `// 这里返回false，移动检测将依赖Inotify` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L1196 | `FileEvent::EventType FileMonitor::inotifyMaskToEventType(uint32_t mask) {` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L1275 | `// 检查 Inotify 支持` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L1276 | `fd = inotify_init();` |
| `file:///home/<USER>/coder/psfsmonL/src/kernel_compat.cpp` | L97 | `bool KernelCompat::needsInotifySupport() const {` |
| `file:///home/<USER>/coder/psfsmonL/src/kernel_compat.cpp` | L98 | `// 如果Fanotify不支持CREATE/DELETE/MOVE事件，需要Inotify补充` |
| `file:///home/<USER>/coder/psfsmonL/src/kernel_compat.cpp` | L110 | `// 5.4 和 4.19 需要Fanotify+Inotify混合模式` |
| `file:///home/<USER>/coder/psfsmonL/src/kernel_compat.cpp` | L111 | `return MonitorStrategy::FANOTIFY_WITH_INOTIFY;` |
| `file:///home/<USER>/coder/psfsmonL/src/kernel_compat.cpp` | L114 | `// 极老内核回退到Inotify` |
| `file:///home/<USER>/coder/psfsmonL/src/kernel_compat.cpp` | L115 | `return MonitorStrategy::INOTIFY_FALLBACK;` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L748 | `.addNumber("inotify_events", static_cast<uint64_t>(file_monitor->getInotifyEvents()))` |
| `file:///home/<USER>/coder/psfsmonL/ref_code/fsmon.h` | L71 | `extern FileMonitorBackend fmb_inotify;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_kernel_compat.cpp` | L57 | `case KernelCompat::MonitorStrategy::FANOTIFY_WITH_INOTIFY:` |
| `file:///home/<USER>/coder/psfsmonL/test/test_kernel_compat.cpp` | L58 | `std::cout << "Fanotify + Inotify 混合模式" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_kernel_compat.cpp` | L60 | `case KernelCompat::MonitorStrategy::INOTIFY_FALLBACK:` |
| `file:///home/<USER>/coder/psfsmonL/test/test_kernel_compat.cpp` | L61 | `std::cout << "主要使用Inotify (兼容模式)" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_kernel_compat.cpp` | L65 | `std::cout << "\n🔍 是否需要Inotify支持: " << (kernel_compat.needsInotifySupport() ? "是" : "否") << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_kernel_compat.cpp` | L87 | `std::cout << "✅ 需要Inotify: " << (monitor.needsInotifySupport() ? "是" : "否") << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_thread_detection.cpp` | L49 | `case KernelCompat::MonitorStrategy::FANOTIFY_WITH_INOTIFY:` |
| `file:///home/<USER>/coder/psfsmonL/test/test_thread_detection.cpp` | L50 | `std::cout << "Fanotify+Inotify混合模式" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_thread_detection.cpp` | L51 | `std::cout << "   • Fanotify处理主要事件，Inotify补充不支持的功能" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_thread_detection.cpp` | L53 | `case KernelCompat::MonitorStrategy::INOTIFY_FALLBACK:` |
| `file:///home/<USER>/coder/psfsmonL/test/test_thread_detection.cpp` | L54 | `std::cout << "Inotify兼容模式" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_thread_detection.cpp` | L55 | `std::cout << "   • 使用Inotify作为主要监控方式" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_log_system.cpp` | L115 | `case KernelCompat::MonitorStrategy::FANOTIFY_WITH_INOTIFY:` |
| `file:///home/<USER>/coder/psfsmonL/test/test_log_system.cpp` | L116 | `strategy_name = "Fanotify+Inotify混合模式";` |
| `file:///home/<USER>/coder/psfsmonL/test/test_log_system.cpp` | L118 | `case KernelCompat::MonitorStrategy::INOTIFY_FALLBACK:` |
| `file:///home/<USER>/coder/psfsmonL/test/test_log_system.cpp` | L119 | `strategy_name = "Inotify兼容模式";` |
| `file:///home/<USER>/coder/psfsmonL/test/test_monitoring_exclusion_demo.cpp` | L177 | `case KernelCompat::MonitorStrategy::FANOTIFY_WITH_INOTIFY:` |
| `file:///home/<USER>/coder/psfsmonL/test/test_monitoring_exclusion_demo.cpp` | L178 | `strategy_name = "Fanotify+Inotify混合模式";` |
| `file:///home/<USER>/coder/psfsmonL/test/test_monitoring_exclusion_demo.cpp` | L180 | `case KernelCompat::MonitorStrategy::INOTIFY_FALLBACK:` |
| `file:///home/<USER>/coder/psfsmonL/test/test_monitoring_exclusion_demo.cpp` | L181 | `strategy_name = "Inotify兼容模式";` |
| `file:///home/<USER>/coder/psfsmonL/test/test_low_kernel_monitoring.cpp` | L10 | `* 测试重点：验证在Fanotify功能受限、Inotify的进程线程获取能力有限的情况下，` |
| `file:///home/<USER>/coder/psfsmonL/test/test_low_kernel_monitoring.cpp` | L71 | `int inotify_events = 0;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_low_kernel_monitoring.cpp` | L158 | `case KernelCompat::MonitorStrategy::FANOTIFY_WITH_INOTIFY:` |
| `file:///home/<USER>/coder/psfsmonL/test/test_low_kernel_monitoring.cpp` | L159 | `std::cout << "Fanotify+Inotify混合模式" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_low_kernel_monitoring.cpp` | L161 | `case KernelCompat::MonitorStrategy::INOTIFY_FALLBACK:` |
| `file:///home/<USER>/coder/psfsmonL/test/test_low_kernel_monitoring.cpp` | L162 | `std::cout << "Inotify兼容模式" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_low_kernel_monitoring.cpp` | L178 | `std::cout << "⚠️  警告: 当前内核Fanotify功能受限，需要Inotify补充" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_low_kernel_monitoring.cpp` | L572 | `case KernelCompat::MonitorStrategy::FANOTIFY_WITH_INOTIFY:` |
| `file:///home/<USER>/coder/psfsmonL/test/test_low_kernel_monitoring.cpp` | L573 | `std::cout << "Fanotify+Inotify混合模式" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_low_kernel_monitoring.cpp` | L575 | `case KernelCompat::MonitorStrategy::INOTIFY_FALLBACK:` |
| `file:///home/<USER>/coder/psfsmonL/test/test_low_kernel_monitoring.cpp` | L576 | `std::cout << "Inotify兼容模式" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_low_kernel_monitoring.cpp` | L642 | `std::cout << "\n⚠️  注意: 当前内核Fanotify功能受限，部分操作依赖Inotify" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/final_capture_test.cpp` | L544 | `std::cout << "   - Inotify只能监控文件系统事件，无法提供进程信息" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/final_capture_test.cpp` | L621 | `std::cout << "   💡 技术: 使用fanotify（能提供进程PID），而非inotify" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L46 | `bool needs_inotify_fallback = false;     // 是否需要inotify回退` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L64 | `uint64_t inotify_events = 0;            // Inotify事件数` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L137 | `std::cout << "   Inotify回退: " << (config_.needs_inotify_fallback ? "是" : "否") << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L163 | `uint64_t initial_inotify_events_ = 0;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L202 | `config_.needs_inotify_fallback = true;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L203 | `std::cout << "⚠️  较老内核版本，可能需要inotify回退支持" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L262 | `return getFileStats(initial_file_events_, initial_fanotify_events_, initial_inotify_events_);` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L265 | `bool getFileStats(uint64_t& total_events, uint64_t& fanotify_events, uint64_t& inotify_events) {` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L287 | `inotify_events = stats_json.value("inotify_events", 0ULL);` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L302 | `std::cout << "   初始Inotify事件: " << initial_inotify_events_ << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L393 | `uint64_t final_total_events, final_fanotify_events, final_inotify_events;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L394 | `if (!getFileStats(final_total_events, final_fanotify_events, final_inotify_events)) {` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L402 | `stats_.inotify_events = final_inotify_events - initial_inotify_events_;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L429 | `std::cout << "   Inotify回退需求: " << (config_.needs_inotify_fallback ? "是" : "否") << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L446 | `std::cout << "   Inotify捕获事件: " << stats_.inotify_events << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L476 | `if (stats_.fanotify_events > stats_.inotify_events) {` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L479 | `std::cout << "   • Inotify使用较多，可能需要优化Fanotify配置" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L498 | `<< "fanotify_events,inotify_events,execution_rate,capture_efficiency,test_duration_ms" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L505 | `<< stats_.inotify_events << ","` |
| `file:///home/<USER>/coder/psfsmonL/test/test_self_exclusion.cpp` | L118 | `case KernelCompat::MonitorStrategy::FANOTIFY_WITH_INOTIFY:` |
| `file:///home/<USER>/coder/psfsmonL/test/test_self_exclusion.cpp` | L119 | `std::cout << "Fanotify+Inotify混合模式";` |
| `file:///home/<USER>/coder/psfsmonL/test/test_self_exclusion.cpp` | L121 | `case KernelCompat::MonitorStrategy::INOTIFY_FALLBACK:` |
| `file:///home/<USER>/coder/psfsmonL/test/test_self_exclusion.cpp` | L122 | `std::cout << "Inotify兼容模式";` |
| `file:///home/<USER>/coder/psfsmonL/test/test_self_exclusion.cpp` | L159 | `std::cout << "  3. 在inotify事件中也可以通过路径过滤实现自身排除" << std::endl;` |

</details>

---

基于我对代码的分析，我发现当前实现确实还在使用Inotify机制，但根据用户的新需求，需要完全移除Inotify并仅使用Fanotify机制。现在让我开始按照需求进行代码更新。

首先，我需要更新核心数据结构和配置，移除Inotify相关内容，并添加新的重点进程和特殊路径功能。

---

```diff
  #include "kernel_compat.h"
  #include <sys/fanotify.h>
- #include <sys/inotify.h>
  #include <unordered_set>
  #include <queue>
```

```diff
  #endif
  }
- 
- // Inotify事件类型
- namespace InotifyEvents {
-     const uint32_t EVENTS = IN_CREATE | IN_DELETE | IN_DELETE_SELF | 
-                            IN_MOVE | IN_MOVED_FROM | IN_MOVED_TO | 
-                            IN_ATTRIB | IN_MODIFY | IN_OPEN | IN_CLOSE;
- }
  
  // 文件事件信息
```

```diff
      std::string new_path;       // 新路径
      bool target_exists;         // 目标是否已存在
+     
+     // 重点进程文件熵值信息
+     double entropy_before;      // 操作前熵值（读操作）
+     double entropy_after;       // 操作后熵值（写操作）
+     bool is_focus_process;      // 是否为重点进程
      
      FileEvent() : type(OPEN), file_id(0), pid(0), tid(0), 
-                   timestamp(0), uid(0), gid(0), target_exists(false) {}
+                   timestamp(0), uid(0), gid(0), target_exists(false),
+                   entropy_before(0.0), entropy_after(0.0), is_focus_process(false) {}
  };
  
```

```diff
      // 获取所有重点进程
      std::vector<pid_t> getFocusProcesses() const;
+     std::vector<std::string> getFocusProcessNames() const;
      
      // 清空重点进程列表
      void clear();
      
+     // 从配置字符串加载重点进程（逗号分隔）
+     void loadFromConfig(const std::string& config_str);
+     
  private:
      FocusProcessManager() = default;
```

```diff
      void clear();
      
+     // 从配置字符串加载特殊路径（逗号分隔）
+     void loadFromConfig(const std::string& config_str);
+     
+     // 设置是否中止违规进程
+     void setTerminateViolatingProcesses(bool terminate);
+     
  private:
      SpecialPathManager() = default;
```

```diff
      uint64_t getFanotifyEvents() const { return fanotify_events_; }
      
-     // 检查内核支持
-     static bool checkKernelSupport();
-     
-     // 辅助函数
-     static std::string eventTypeToString(FileEvent::EventType type);
-     static char eventTypeToChar(FileEvent::EventType type);
- 
- private:
-     // 内核兼容性管理
-     KernelCompat& kernel_compat_;
+     // 检查是否运行中
+     bool isRunning() const { return running_; }
+     
+     // 获取最近的事件列表
+     std::vector<FileEvent> getRecentEvents(size_t limit = 100) const;
+     
+     // 获取监控的路径列表
+     std::vector<std::string> getWatchPaths() const;
      
-     // Fanotify相关
-     int fanotify_fd_;
-     bool fanotify_initialized_;
-     std::thread fanotify_thread_;
-     void fanotifyEventLoop();
-     bool initializeFanotify();
-     void closeFanotify();
-     
-     // 事件处理
-     std::function<void(const FileEvent&)> event_callback_;
-     std::queue<FileEvent> event_queue_;
-     std::mutex event_queue_mutex_;
-     std::condition_variable event_condition_;
-     std::thread event_processor_thread_;
-     void processEvents();
-     
-     // 事件去重和合并
-     std::unordered_map<std::string, uint64_t> recent_events_; // path -> timestamp
-     std::mutex recent_events_mutex_;
-     bool isDuplicateEvent(const FileEvent& event);
-     void addRecentEvent(const FileEvent& event);
-     void cleanupRecentEvents();
+     // 事件统计信息
+     struct EventStats {
+         uint64_t total_events = 0;
+         uint64_t fanotify_events = 0;
+         uint64_t focus_process_events = 0;
+         uint64_t special_path_violations = 0;
+         uint64_t entropy_calculations = 0;
+         std::chrono::steady_clock::time_point start_time;
+         
+         EventStats() : start_time(std::chrono::steady_clock::now()) {}
+     };
+     
+     EventStats getEventStats() const;
+     
+ private:
+     // 内部方法
+     void processEvent();
+     bool processFileEvent(struct fanotify_event_metadata* metadata);
+     void handleFocusProcessEvent(const FileEvent& event);
+     void handleSpecialPathCheck(const FileEvent& event);
      
-     // 文件路径管理
-     std::unordered_set<std::string> monitored_paths_;
-     std::mutex paths_mutex_;
+     // 辅助方法
      std::string getPathFromFd(int fd);
-     uint64_t generateFileId(const std::string& path, dev_t dev, ino_t ino);
      std::string getProcessName(pid_t pid);
      std::string getExecutablePath(pid_t pid);
-     
-     // 进程信息获取 - 根据内核版本使用不同策略
-     pid_t getPidFromFanotifyEvent(const struct fanotify_event_metadata* event);
-     pid_t getTidFromEvent(const struct fanotify_event_metadata* event);
+     uint64_t generateFileId(const std::string& path, dev_t dev, ino_t ino);
+     pid_t getTidFromEvent(const struct fanotify_event_metadata* metadata);
      pid_t getTidFromPid(pid_t pid);
      pid_t getTidFromSyscallTrace(pid_t pid, const std::string& file_path);
      bool getProcessInfo(pid_t pid, uint32_t& uid, uint32_t& gid);
      
-     // 重点进程文件监控处理
-     void handleFocusProcessFileEvent(const FileEvent& event);
-     
-     // 特殊路径保护处理
-     void handleSpecialPathAccess(const FileEvent& event);
-     
-     // 线程识别配置
-     ThreadDetectionMode thread_detection_mode_;
-     
-     // 控制变量
-     std::atomic<bool> running_;
-     std::atomic<bool> stop_requested_;
-     
-     // 配置
-     const MonitorConfig* config_;
-     
-     // 统计信息
-     std::atomic<uint64_t> total_events_;
-     std::atomic<uint64_t> fanotify_events_;
-     
-     // 日志
-     void logError(const std::string& message);
-     void logWarning(const std::string& message);
-     void logDebug(const std::string& message);
-     void logInfo(const std::string& message);
-     
-     // 事件类型转换
+     // 事件处理
+     void addRecentEvent(const FileEvent& event);
+     bool isDuplicateEvent(const FileEvent& event) const;
      FileEvent::EventType fanotifyMaskToEventType(uint64_t mask);
-     bool isMoveFromEvent(uint64_t mask);
-     bool isMoveToEvent(uint64_t mask);
+     char eventTypeToChar(FileEvent::EventType type);
+     std::string eventTypeToString(FileEvent::EventType type);
      
-     // 目录监控辅助
-     void addDirectoryRecursive(const std::string& path);
-     bool isDirectory(const std::string& path);
-     
-     // 内核版本检查
-     static bool checkKernelVersion(int major, int minor);
-     static std::string getKernelVersion();
- };
- 
- // 文件监控管理器
- class FileMonitorManager {
- public:
-     static FileMonitorManager& getInstance();
-     
-     bool initialize();
-     bool start();
-     void stop();
-     
-     void setProcessMonitor(class ProcessMonitor* monitor) { process_monitor_ = monitor; }
+     // 路径处理
+     bool addRecursiveWatch(const std::string& path);
+     bool checkKernelVersion(int major, int minor);
+     
+     // 成员变量
+     int fanotify_fd_;
+     bool running_;
+     bool initialized_;
+     std::thread monitor_thread_;
+     
+     ThreadDetectionMode thread_detection_mode_;
+     KernelCompat kernel_compat_;
+     
+     // 监控路径管理
+     std::unordered_set<std::string> watch_paths_;
+     mutable std::mutex paths_mutex_;
      
      // 事件处理
-     void handleFileEvent(const FileEvent& event);
-     
-     // 工具函数
-     uint64_t generateFileIdFromStat(const std::string& path, const struct stat& st);
-     std::string getProcessNameFromPid(pid_t pid);
-     
- private:
-     FileMonitorManager() = default;
-     ~FileMonitorManager() = default;
-     
-     std::unique_ptr<FileMonitor> file_monitor_;
-     class ProcessMonitor* process_monitor_;
+     std::function<void(const FileEvent&)> event_callback_;
+     std::queue<FileEvent> event_queue_;
+     std::mutex event_queue_mutex_;
+     std::condition_variable event_condition_;
+     
+     // 去重处理
+     std::vector<FileEvent> recent_events_;
+     mutable std::mutex recent_events_mutex_;
+     uint32_t dedup_window_ms_;
      
-     // 移动事件处理（如果内核支持）
-     void handleMoveEvent(const FileEvent& event);
-     void processPendingMoves();
+     // 统计信息
+     std::atomic<uint64_t> total_events_{0};
+     std::atomic<uint64_t> fanotify_events_{0};
+     std::atomic<uint64_t> focus_process_events_{0};
+     std::atomic<uint64_t> special_path_violations_{0};
+     std::atomic<uint64_t> entropy_calculations_{0};
      
-     // 排除复制和赋值
-     FileMonitorManager(const FileMonitorManager&) = delete;
-     FileMonitorManager& operator=(const FileMonitorManager&) = delete;
+     // 配置信息
+     const MonitorConfig* config_;
+     
+     // 重点进程熵值监控
+     void calculateAndRecordEntropy(FileEvent& event);
+     void updateFocusProcessEntropy(pid_t pid, const FileEvent& event);
  };
  
```

---

现在我需要更新内核兼容性模块，移除Inotify相关内容：

---

```diff
  #define KERNEL_COMPAT_H
  
- #include <sys/utsname.h>
  #include <string>
- 
- // 内核版本层次定义
- enum class KernelVersion {
-     KERNEL_4_19,     // 4.19.x - 基础功能
-     KERNEL_5_4,      // 5.4.x - 中等功能
-     KERNEL_5_4_PLUS  // 5.4.18+ - 全功能
- };
- 
- // Fanotify 功能标志
- struct FanotifyFeatures {
-     bool supports_fan_report_tid;      // >= 4.20 right
-     bool supports_fan_create_delete;   // >= 5.1 right
-     bool supports_fan_move;            // >= 5.1 right
-     bool supports_fan_report_fid;      // >= 5.1 right
-     bool supports_fan_open_exec;       // >= 5.0 right
-     bool supports_mount_mark;          // >= 2.6.37 wrong, should be 5.1
-     bool supports_filesystem_mark;     // >= 3.8 wrong, should be 4.20
- };
+ #include <cstdint>
  
- // 内核兼容性管理类
+ // 内核兼容性管理器
  class KernelCompat {
  public:
-     static KernelCompat& getInstance();
+     // 监控策略枚举
+     enum class MonitorStrategy {
+         FANOTIFY_FULL,         // 完整Fanotify支持 (内核 >= 5.4.18)
+         FANOTIFY_BASIC         // 基础Fanotify支持 (较老内核)
+     };
+     
+     KernelCompat();
+     ~KernelCompat() = default;
      
-     // 检测当前内核版本
-     bool detectKernelVersion();
+     // 初始化内核兼容性检查
+     bool initialize();
      
      // 获取内核版本信息
-     KernelVersion getKernelVersion() const { return kernel_version_; }
-     std::string getKernelString() const { return kernel_string_; }
-     
-     // 获取内核版本数字
+     std::string getKernelVersion() const;
      int getMajorVersion() const { return major_version_; }
      int getMinorVersion() const { return minor_version_; }
      int getPatchVersion() const { return patch_version_; }
      
-     // 检查特定功能支持
-     const FanotifyFeatures& getFanotifyFeatures() const { return fanotify_features_; }
-     
-     // 便捷检查方法
-     bool supportsReportTid() const { return fanotify_features_.supports_fan_report_tid; }
-     bool supportsCreateDelete() const { return fanotify_features_.supports_fan_create_delete; }
-     bool supportsMove() const { return fanotify_features_.supports_fan_move; }
-     bool needsInotifySupport() const;
+     // 功能支持检查
+     bool supportsFanotify() const;
+     bool supportsReportTid() const;
+     bool supportsExtendedEvents() const;  // CREATE/DELETE/MOVE
+     bool supportsAttribEvents() const;
+     bool supportsOpenExec() const;
      
      // 获取推荐的监控策略
-     enum class MonitorStrategy {
-         FANOTIFY_ONLY,           // 仅使用 Fanotify (5.4.18+)
-         FANOTIFY_WITH_INOTIFY,   // Fanotify + Inotify 混合 (5.4, 4.19)
-         INOTIFY_FALLBACK         // 主要使用 Inotify (极老内核)
-     };
      MonitorStrategy getRecommendedStrategy() const;
      
+     // 获取支持的Fanotify事件掩码
+     uint64_t getSupportedFanotifyEvents() const;
+     
+     // 检查特定功能是否可用
+     bool canUseFeature(const std::string& feature) const;
+     
+     // 获取兼容性报告
+     std::string getCompatibilityReport() const;
+     
+     // 静态辅助方法
+     static bool checkMinimumKernel(int min_major, int min_minor, int min_patch = 0);
+     static std::string parseKernelVersion();
+     
  private:
-     KernelCompat() = default;
-     void initializeFeatures();
-     
-     KernelVersion kernel_version_;
-     std::string kernel_string_;
+     // 内核版本信息
      int major_version_;
      int minor_version_;
      int patch_version_;
-     FanotifyFeatures fanotify_features_;
-     bool initialized_;
- };
- 
- // 内核版本相关的常量定义
- namespace KernelConsts {
-     // Fanotify 标志定义 (兼容性)
-     #ifndef FAN_REPORT_TID
-     #define FAN_REPORT_TID 0x00000100
-     #endif
-     
-     // 对于可能已经定义的宏，我们使用常量而不是重新定义
-     static const uint64_t COMPAT_FAN_CREATE = 0x00000004;
-     static const uint64_t COMPAT_FAN_DELETE = 0x00000008;
-     static const uint64_t COMPAT_FAN_MOVE = 0x00000010;
-     
-     #ifndef FAN_REPORT_FID
-     #define FAN_REPORT_FID 0x00000200
-     #endif
-     
-     // 版本检查宏
-     #define KERNEL_VERSION(a,b,c) (((a) << 16) + ((b) << 8) + (c))
-     
-     // 最低支持版本
-     static const int MIN_KERNEL_4_19 = KERNEL_VERSION(4, 19, 0);
-     static const int MIN_KERNEL_5_4 = KERNEL_VERSION(5, 4, 0);
-     static const int MIN_KERNEL_5_4_18 = KERNEL_VERSION(5, 4, 18);
- }
+     std::string full_version_;
+     
+     // 功能支持标志
+     bool supports_fanotify_;
+     bool supports_report_tid_;
+     bool supports_extended_events_;
+     bool supports_attrib_events_;
+     bool supports_open_exec_;
+     
+     // 内部方法
+     void detectKernelVersion();
+     void checkFanotifySupport();
+     void checkFeatureSupport();
+     bool testFanotifyAccess() const;
+     uint64_t detectSupportedEvents() const;
+ };
  
  #endif // KERNEL_COMPAT_H 
```

---

现在我需要更新内核兼容性实现：

---

```diff
  #include "../include/kernel_compat.h"
- #include "../include/psfsmon.h"
- #include "../include/main_monitor.h"
- #include <cstdio>
+ #include <sys/utsname.h>
+ #include <sys/fanotify.h>
+ #include <unistd.h>
+ #include <fcntl.h>
+ #include <sstream>
+ #include <iostream>
  #include <cstring>
  
- // 日志辅助函数
- static void logError(const std::string& message) {
-     LogManager::getInstance().error("[KernelCompat] " + message);
- }
- 
- static void logInfo(const std::string& message) {
-     LogManager::getInstance().info("[KernelCompat] " + message);
- }
- 
- static void logWarning(const std::string& message) {
-     LogManager::getInstance().warning("[KernelCompat] " + message);
+ // 如果系统头文件没有定义FAN_REPORT_TID，则手动定义
+ #ifndef FAN_REPORT_TID
+ #define FAN_REPORT_TID 0x00000100
+ #endif
+ 
+ #ifndef FAN_CREATE
+ #define FAN_CREATE 0x00000004
+ #endif
+ 
+ #ifndef FAN_DELETE
+ #define FAN_DELETE 0x00000008
+ #endif
+ 
+ #ifndef FAN_MOVE
+ #define FAN_MOVE 0x00000010
+ #endif
+ 
+ #ifndef FAN_ATTRIB
+ #define FAN_ATTRIB 0x00000040
+ #endif
+ 
+ #ifndef FAN_OPEN_EXEC
+ #define FAN_OPEN_EXEC 0x00001000
+ #endif
+ 
+ KernelCompat::KernelCompat() 
+     : major_version_(0), minor_version_(0), patch_version_(0),
+       supports_fanotify_(false), supports_report_tid_(false),
+       supports_extended_events_(false), supports_attrib_events_(false),
+       supports_open_exec_(false) {
+ }
+ 
+ bool KernelCompat::initialize() {
+     detectKernelVersion();
+     checkFanotifySupport();
+     checkFeatureSupport();
+     return supports_fanotify_;
  }
  
- KernelCompat& KernelCompat::getInstance() {
-     static KernelCompat instance;
-     return instance;
+ std::string KernelCompat::getKernelVersion() const {
+     return full_version_;
  }
  
- bool KernelCompat::detectKernelVersion() {
+ void KernelCompat::detectKernelVersion() {
      struct utsname uts;
      if (uname(&uts) != 0) {
-         logError("无法获取内核版本信息");
-         return false;
+         return;
      }
      
-     kernel_string_ = uts.release;
+     full_version_ = uts.release;
      
-     // 解析内核版本 (格式: x.y.z)
-     int parsed = sscanf(uts.release, "%d.%d.%d", &major_version_, &minor_version_, &patch_version_);
-     if (parsed < 2) {
-         logError("无法解析内核版本: " + kernel_string_);
-         return false;
-     }
-     if (parsed == 2) {
-         patch_version_ = 0;  // 如果没有patch版本，设为0
+     // 解析版本号
+     std::string version_str = uts.release;
+     std::istringstream iss(version_str);
+     std::string token;
+     
+     // 获取主版本号
+     if (std::getline(iss, token, '.')) {
+         major_version_ = std::stoi(token);
      }
      
-     // 计算版本号用于比较
-     int version_code = KERNEL_VERSION(major_version_, minor_version_, patch_version_);
-     
-     // 确定内核版本层次
-     if (version_code >= KernelConsts::MIN_KERNEL_5_4_18) {
-         kernel_version_ = KernelVersion::KERNEL_5_4_PLUS;
-         logInfo("检测到内核版本: " + kernel_string_ + " (5.4.18+层次) - 支持全功能");
-     } else if (version_code >= KernelConsts::MIN_KERNEL_5_4) {
-         kernel_version_ = KernelVersion::KERNEL_5_4;
-         logInfo("检测到内核版本: " + kernel_string_ + " (5.4层次) - 支持中等功能");
-     } else if (version_code >= KernelConsts::MIN_KERNEL_4_19) {
-         kernel_version_ = KernelVersion::KERNEL_4_19;
-         logInfo("检测到内核版本: " + kernel_string_ + " (4.19层次) - 支持基础功能");
-     } else {
-         logWarning("内核版本过旧: " + kernel_string_ + " - 可能存在兼容性问题");
-         kernel_version_ = KernelVersion::KERNEL_4_19;  // 回退到最基础支持
-     }
-     
-     // 初始化功能特性
-     initializeFeatures();
-     initialized_ = true;
-     
+     // 获取次版本号
+     if (std::getline(iss, token, '.')) {
+         minor_version_ = std::stoi(token);
+     }
+     
+     // 获取修订版本号
+     if (std::getline(iss, token, '-')) {
+         patch_version_ = std::stoi(token);
+     }
+ }
+ 
+ void KernelCompat::checkFanotifySupport() {
+     // 测试Fanotify是否可用
+     supports_fanotify_ = testFanotifyAccess();
+ }
+ 
+ void KernelCompat::checkFeatureSupport() {
+     if (!supports_fanotify_) {
+         return;
+     }
+     
+     // 检查TID报告支持（内核 >= 4.20）
+     if (major_version_ > 4 || (major_version_ == 4 && minor_version_ >= 20)) {
+         supports_report_tid_ = true;
+     }
+     
+     // 检查扩展事件支持（内核 >= 5.1）
+     if (major_version_ > 5 || (major_version_ == 5 && minor_version_ >= 1)) {
+         supports_extended_events_ = true;
+         supports_attrib_events_ = true;
+     }
+     
+     // 检查OPEN_EXEC支持（内核 >= 5.0）
+     if (major_version_ >= 5) {
+         supports_open_exec_ = true;
+     }
+ }
+ 
+ bool KernelCompat::testFanotifyAccess() const {
+     int fd = fanotify_init(FAN_CLASS_NOTIF, O_RDONLY);
+     if (fd < 0) {
+         return false;
+     }
+     close(fd);
      return true;
  }
  
- void KernelCompat::initializeFeatures() {
-     // 根据内核版本设置功能支持
-     int version_code = KERNEL_VERSION(major_version_, minor_version_, patch_version_);
-     
-     // FAN_REPORT_TID 支持 (>= 4.20)
-     fanotify_features_.supports_fan_report_tid = (version_code >= KERNEL_VERSION(4, 20, 0));
-     
-     // FAN_CREATE, FAN_DELETE, FAN_MOVE 支持 (>= 5.1)
-     fanotify_features_.supports_fan_create_delete = (version_code >= KERNEL_VERSION(5, 1, 0));
-     fanotify_features_.supports_fan_move = (version_code >= KERNEL_VERSION(5, 1, 0));
-     
-     // FAN_REPORT_FID 支持 (>= 5.1)
-     fanotify_features_.supports_fan_report_fid = (version_code >= KERNEL_VERSION(5, 1, 0));
-     
-     // FAN_OPEN_EXEC 支持 (>= 5.0)
-     fanotify_features_.supports_fan_open_exec = (version_code >= KERNEL_VERSION(5, 0, 0));
-     
-     // 基础fanotify支持
-     fanotify_features_.supports_mount_mark = (version_code >= KERNEL_VERSION(5, 1, 0));
-     fanotify_features_.supports_filesystem_mark = (version_code >= KERNEL_VERSION(4, 20, 0));
-     
-     // 记录功能支持情况
-     logInfo("Fanotify功能支持:");
-     logInfo("  - FAN_REPORT_TID: " + std::string(fanotify_features_.supports_fan_report_tid ? "是" : "否"));
-     logInfo("  - FAN_CREATE/DELETE: " + std::string(fanotify_features_.supports_fan_create_delete ? "是" : "否"));
-     logInfo("  - FAN_MOVE: " + std::string(fanotify_features_.supports_fan_move ? "是" : "否"));
-     logInfo("  - FAN_OPEN_EXEC: " + std::string(fanotify_features_.supports_fan_open_exec ? "是" : "否"));
+ bool KernelCompat::supportsFanotify() const {
+     return supports_fanotify_;
+ }
+ 
+ bool KernelCompat::supportsReportTid() const {
+     return supports_report_tid_;
+ }
+ 
+ bool KernelCompat::supportsExtendedEvents() const {
+     return supports_extended_events_;
+ }
+ 
+ bool KernelCompat::supportsAttribEvents() const {
+     return supports_attrib_events_;
+ }
+ 
+ bool KernelCompat::supportsOpenExec() const {
+     return supports_open_exec_;
+ }
+ 
+ KernelCompat::MonitorStrategy KernelCompat::getRecommendedStrategy() const {
+     if (!supports_fanotify_) {
+         return MonitorStrategy::FANOTIFY_BASIC;
+     }
+     
+     // 内核 >= 5.4.18 使用完整Fanotify功能
+     if (major_version_ > 5 || 
+         (major_version_ == 5 && minor_version_ > 4) ||
+         (major_version_ == 5 && minor_version_ == 4 && patch_version_ >= 18)) {
+         return MonitorStrategy::FANOTIFY_FULL;
+     }
+     
+     // 其他情况使用基础Fanotify
+     return MonitorStrategy::FANOTIFY_BASIC;
+ }
+ 
+ uint64_t KernelCompat::getSupportedFanotifyEvents() const {
+     uint64_t events = FAN_OPEN | FAN_CLOSE | FAN_ACCESS | FAN_MODIFY;
+     
+     if (supports_open_exec_) {
+         events |= FAN_OPEN_EXEC;
+     }
+     
+     if (supports_attrib_events_) {
+         events |= FAN_ATTRIB;
+     }
+     
+     if (supports_extended_events_) {
+         events |= FAN_CREATE | FAN_DELETE | FAN_MOVE;
+     }
+     
+     return events;
+ }
+ 
+ bool KernelCompat::canUseFeature(const std::string& feature) const {
+     if (feature == "fanotify") {
+         return supports_fanotify_;
+     } else if (feature == "report_tid") {
+         return supports_report_tid_;
+     } else if (feature == "extended_events") {
+         return supports_extended_events_;
+     } else if (feature == "attrib_events") {
+         return supports_attrib_events_;
+     } else if (feature == "open_exec") {
+         return supports_open_exec_;
+     }
+     return false;
  }
  
- bool KernelCompat::needsInotifySupport() const {
-     // 如果Fanotify不支持CREATE/DELETE/MOVE事件，需要Inotify补充
-     return !fanotify_features_.supports_fan_create_delete || !fanotify_features_.supports_fan_move;
- }
- 
- KernelCompat::MonitorStrategy KernelCompat::getRecommendedStrategy() const {
-     switch (kernel_version_) {
-         case KernelVersion::KERNEL_5_4_PLUS:
-             // 5.4.18+ 功能完整，可以仅使用Fanotify
-             return MonitorStrategy::FANOTIFY_ONLY;
-             
-         case KernelVersion::KERNEL_5_4:
-         case KernelVersion::KERNEL_4_19:
-             // 5.4 和 4.19 需要Fanotify+Inotify混合模式
-             return MonitorStrategy::FANOTIFY_WITH_INOTIFY;
-             
-         default:
-             // 极老内核回退到Inotify
-             return MonitorStrategy::INOTIFY_FALLBACK;
+ std::string KernelCompat::getCompatibilityReport() const {
+     std::ostringstream report;
+     
+     report << "内核兼容性报告:\n";
+     report << "  内核版本: " << full_version_ << "\n";
+     report << "  主版本: " << major_version_ << "." << minor_version_ << "." << patch_version_ << "\n";
+     report << "  Fanotify支持: " << (supports_fanotify_ ? "是" : "否") << "\n";
+     report << "  TID报告支持: " << (supports_report_tid_ ? "是" : "否") << "\n";
+     report << "  扩展事件支持: " << (supports_extended_events_ ? "是" : "否") << "\n";
+     report << "  属性事件支持: " << (supports_attrib_events_ ? "是" : "否") << "\n";
+     report << "  OPEN_EXEC支持: " << (supports_open_exec_ ? "是" : "否") << "\n";
+     
+     MonitorStrategy strategy = getRecommendedStrategy();
+     report << "  推荐策略: ";
+     switch (strategy) {
+         case MonitorStrategy::FANOTIFY_FULL:
+             report << "完整Fanotify模式";
+             break;
+         case MonitorStrategy::FANOTIFY_BASIC:
+             report << "基础Fanotify模式";
+             break;
+     }
+     report << "\n";
+     
+     return report.str();
+ }
+ 
+ bool KernelCompat::checkMinimumKernel(int min_major, int min_minor, int min_patch) {
+     struct utsname uts;
+     if (uname(&uts) != 0) {
+         return false;
+     }
+     
+     std::string version_str = uts.release;
+     std::istringstream iss(version_str);
+     std::string token;
+     
+     int major = 0, minor = 0, patch = 0;
+     
+     if (std::getline(iss, token, '.')) {
+         major = std::stoi(token);
+     }
+     
+     if (std::getline(iss, token, '.')) {
+         minor = std::stoi(token);
+     }
+     
+     if (std::getline(iss, token, '-')) {
+         patch = std::stoi(token);
+     }
+     
+     if (major > min_major) return true;
+     if (major < min_major) return false;
+     if (minor > min_minor) return true;
+     if (minor < min_minor) return false;
+     return patch >= min_patch;
+ }
+ 
+ std::string KernelCompat::parseKernelVersion() {
+     struct utsname uts;
+     if (uname(&uts) != 0) {
+         return "unknown";
      }
+     return uts.release;
  } 
```

---

现在我需要更新配置结构，去除Inotify相关配置并添加新的重点进程和特殊路径配置：

---

Read file: include/psfsmon.h

---

我需要修复配置结构中的一个错误（max_files_per_thread已被移除）：

---



---

现在我需要大幅更新文件监控器的实现，移除所有Inotify相关代码，并实现新的重点进程熵值监控功能：

---



---

文件太长，我需要重新创建它：

---

删除原有的文件监控实现，准备重写为仅使用Fanotify的版本

---



---

文件被创建了，现在我需要更新其他相关文件，特别是进程监控模块，让它支持重点进程的熵值管理：

---

Read file: include/psfsmon.h

---

我需要在源文件中实现FocusProcessFileInfo和ProcessInfo的方法：

---

```diff
  #include "../include/psfsmon.h"
+ #include "../include/main_monitor.h"
  #include <algorithm>
  #include <cstring>
  #include <chrono>
  #include <cmath>
+ 
+ // 日志管理器单例声明
+ extern class LogManager& getLogManager();
  
  // ThreadInfo 实现
```

```diff
      std::lock_guard<std::mutex> lock(threads_mutex);
      auto it = threads.find(tid);
-     return (it != threads.end()) ? it->second : nullptr;
+     if (it != threads.end()) {
+         return it->second;
+     }
+     return nullptr;
  }
  
```

```diff
      if (focus && !focus_file_info) {
          focus_file_info = std::make_unique<FocusProcessFileInfo>();
-     } else if (!focus) {
+     } else if (!focus && focus_file_info) {
          focus_file_info.reset();
      }
  }
  
  FocusProcessFileInfo* ProcessInfo::getFocusFileInfo() const {
-     return is_focus_process ? focus_file_info.get() : nullptr;
+     if (is_focus_process && focus_file_info) {
+         return focus_file_info.get();
+     }
+     return nullptr;
  }
  
```

```diff
      summary["virtual_memory"] = std::to_string(virtual_memory);
      summary["start_time"] = std::to_string(start_time);
-     summary["is_focus_process"] = is_focus_process ? "true" : "false";
      
-     // 线程统计
      {
          std::lock_guard<std::mutex> lock(threads_mutex);
          summary["thread_count"] = std::to_string(threads.size());
      }
      
-     // 网络连接统计
+     summary["children_count"] = std::to_string(children.size());
+     
      {
          std::lock_guard<std::mutex> lock(network_mutex);
          summary["network_connections"] = std::to_string(network_connections.size());
-         
-         uint64_t total_tx = 0, total_rx = 0;
-         for (const auto& conn : network_connections) {
-             total_tx += conn.tx_bytes;
-             total_rx += conn.rx_bytes;
-         }
-         summary["total_tx_bytes"] = std::to_string(total_tx);
-         summary["total_rx_bytes"] = std::to_string(total_rx);
-     }
-     
-     // 重点进程信息
+     }
+     
+     summary["is_focus_process"] = is_focus_process ? "true" : "false";
+     
+     // 如果是重点进程，添加文件监控信息
      if (is_focus_process && focus_file_info) {
-         summary["read_entropy_avg"] = std::to_string(focus_file_info->getAverageReadEntropy());
-         summary["write_entropy_avg"] = std::to_string(focus_file_info->getAverageWriteEntropy());
-         summary["delete_count"] = std::to_string(focus_file_info->delete_count);
-         summary["rename_count"] = std::to_string(focus_file_info->rename_count);
-         summary["path_history_size"] = std::to_string(focus_file_info->path_history.size());
-         summary["rename_extensions_count"] = std::to_string(focus_file_info->rename_extensions.size());
+         std::lock_guard<std::mutex> lock(focus_file_info->mutex);
+         summary["focus_total_read_entropy"] = std::to_string(focus_file_info->total_read_entropy);
+         summary["focus_total_write_entropy"] = std::to_string(focus_file_info->total_write_entropy);
+         summary["focus_read_entropy_count"] = std::to_string(focus_file_info->read_entropy_count);
+         summary["focus_write_entropy_count"] = std::to_string(focus_file_info->write_entropy_count);
+         summary["focus_average_read_entropy"] = std::to_string(focus_file_info->getAverageReadEntropy());
+         summary["focus_average_write_entropy"] = std::to_string(focus_file_info->getAverageWriteEntropy());
+         summary["focus_delete_count"] = std::to_string(focus_file_info->delete_count);
+         summary["focus_rename_count"] = std::to_string(focus_file_info->rename_count);
+         summary["focus_path_history_count"] = std::to_string(focus_file_info->path_history.size());
+         summary["focus_rename_extensions_count"] = std::to_string(focus_file_info->rename_extensions.size());
      }
      
```

```diff
      rename_count++;
      
-     // 提取新路径的扩展名
-     size_t dot_pos = new_path.find_last_of('.');
-     if (dot_pos != std::string::npos && dot_pos < new_path.length() - 1) {
-         std::string extension = new_path.substr(dot_pos);
-         // 检查扩展名是否已存在
-         if (std::find(rename_extensions.begin(), rename_extensions.end(), extension) == rename_extensions.end()) {
-             rename_extensions.push_back(extension);
-             // 保持扩展名列表在合理大小内
-             if (rename_extensions.size() > Constants::MAX_EXTENSION_LIST) {
-                 rename_extensions.erase(rename_extensions.begin());
+     // 记录扩展名变化
+     size_t old_dot = old_path.find_last_of('.');
+     size_t new_dot = new_path.find_last_of('.');
+     
+     if (old_dot != std::string::npos && new_dot != std::string::npos) {
+         std::string old_ext = old_path.substr(old_dot);
+         std::string new_ext = new_path.substr(new_dot);
+         
+         if (old_ext != new_ext) {
+             std::string ext_change = old_ext + " -> " + new_ext;
+             if (std::find(rename_extensions.begin(), rename_extensions.end(), ext_change) == rename_extensions.end()) {
+                 rename_extensions.push_back(ext_change);
+                 
+                 // 限制扩展名列表大小
+                 if (rename_extensions.size() > Constants::MAX_EXTENSION_LIST) {
+                     rename_extensions.erase(rename_extensions.begin());
+                 }
              }
          }
      }
      
-     recordPathOperation(old_path);
-     recordPathOperation(new_path);
+     recordPathOperation(old_path + " -> " + new_path);
  }
  
  void FocusProcessFileInfo::recordPathOperation(const std::string& path) {
-     // 注意：此方法假设已经在锁保护下调用
      path_history.push_back(path);
      
-     // 保持路径历史在合理大小内
+     // 限制路径历史记录大小
      if (path_history.size() > Constants::MAX_PATH_HISTORY) {
          path_history.erase(path_history.begin());
```

```diff
      std::lock_guard<std::mutex> lock(mutex);
      
-     // 保持路径历史和扩展名列表在合理大小内
+     // 清理过期的路径历史记录（保留最近的一半）
      if (path_history.size() > Constants::MAX_PATH_HISTORY / 2) {
-         path_history.erase(path_history.begin(), 
-                           path_history.begin() + (path_history.size() - Constants::MAX_PATH_HISTORY / 2));
+         size_t remove_count = path_history.size() - Constants::MAX_PATH_HISTORY / 2;
+         path_history.erase(path_history.begin(), path_history.begin() + remove_count);
      }
      
+     // 清理扩展名列表（保留最近的一半）
      if (rename_extensions.size() > Constants::MAX_EXTENSION_LIST / 2) {
-         rename_extensions.erase(rename_extensions.begin(),
-                                rename_extensions.begin() + (rename_extensions.size() - Constants::MAX_EXTENSION_LIST / 2));
+         size_t remove_count = rename_extensions.size() - Constants::MAX_EXTENSION_LIST / 2;
+         rename_extensions.erase(rename_extensions.begin(), rename_extensions.begin() + remove_count);
      }
  } 
```

---

现在我需要更新API服务器，移除Inotify相关的API并添加新的重点进程和特殊路径管理API：

---

<details>
            <summary>Grep search for "inotify_events" • **3** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L748 | `.addNumber("inotify_events", static_cast<uint64_t>(file_monitor->getInotifyEvents()))` |
| `file:///home/<USER>/coder/psfsmonL/test/test_low_kernel_monitoring.cpp` | L71 | `int inotify_events = 0;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L64 | `uint64_t inotify_events = 0;            // Inotify事件数` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L163 | `uint64_t initial_inotify_events_ = 0;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L262 | `return getFileStats(initial_file_events_, initial_fanotify_events_, initial_inotify_events_);` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L265 | `bool getFileStats(uint64_t& total_events, uint64_t& fanotify_events, uint64_t& inotify_events) {` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L287 | `inotify_events = stats_json.value("inotify_events", 0ULL);` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L302 | `std::cout << "   初始Inotify事件: " << initial_inotify_events_ << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L393 | `uint64_t final_total_events, final_fanotify_events, final_inotify_events;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L394 | `if (!getFileStats(final_total_events, final_fanotify_events, final_inotify_events)) {` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L402 | `stats_.inotify_events = final_inotify_events - initial_inotify_events_;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L446 | `std::cout << "   Inotify捕获事件: " << stats_.inotify_events << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L476 | `if (stats_.fanotify_events > stats_.inotify_events) {` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L498 | `<< "fanotify_events,inotify_events,execution_rate,capture_efficiency,test_duration_ms" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L505 | `<< stats_.inotify_events << ","` |

</details>

---

Read file: src/api_server.cpp

---

我需要更新API服务器中的inotify引用，并添加新的重点进程和特殊路径管理API：

---



---

现在我需要添加重点进程和特殊路径管理的API端点。让我查看API服务器的路由注册部分：

---

<details>
            <summary>Grep search for "registerRoute.*exclusions" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L127 | `http_server_->registerRoute(HttpMethod::GET, "/api/exclusions",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L132 | `http_server_->registerRoute(HttpMethod::POST, "/api/exclusions",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L137 | `http_server_->registerRoute(HttpMethod::DELETE, "/api/exclusions",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L127 | `http_server_->registerRoute(HttpMethod::GET, "/api/exclusions",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L132 | `http_server_->registerRoute(HttpMethod::POST, "/api/exclusions",` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L137 | `http_server_->registerRoute(HttpMethod::DELETE, "/api/exclusions",` |

</details>

---

Read file: src/api_server.cpp

---

我看到API路由已经注册好了，现在我需要实现这些新的API处理函数。首先查看是否已经有现有的重点进程和特殊路径API实现：

---

<details>
            <summary>Grep search for "handleGetFocusProcesses|handleAddFocusProcess|handleGetSpecialPaths" • **3** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///home/<USER>/coder/psfsmonL/include/api_server.h` | L198 | `void handleGetFocusProcesses(const HttpRequest& request, HttpResponse& response);` |
| `file:///home/<USER>/coder/psfsmonL/include/api_server.h` | L199 | `void handleAddFocusProcess(const HttpRequest& request, HttpResponse& response);` |
| `file:///home/<USER>/coder/psfsmonL/include/api_server.h` | L203 | `void handleGetSpecialPaths(const HttpRequest& request, HttpResponse& response);` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L161 | `handleGetFocusProcesses(req, resp);` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L166 | `handleAddFocusProcess(req, resp);` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L177 | `handleGetSpecialPaths(req, resp);` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L1824 | `void ApiServer::handleGetFocusProcesses(const HttpRequest& request, HttpResponse& response) {` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L1870 | `void ApiServer::handleAddFocusProcess(const HttpRequest& request, HttpResponse& response) {` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L1984 | `void ApiServer::handleGetSpecialPaths(const HttpRequest& request, HttpResponse& response) {` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L161 | `handleGetFocusProcesses(req, resp);` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L166 | `handleAddFocusProcess(req, resp);` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L177 | `handleGetSpecialPaths(req, resp);` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L1824 | `void ApiServer::handleGetFocusProcesses(const HttpRequest& request, HttpResponse& response) {` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L1870 | `void ApiServer::handleAddFocusProcess(const HttpRequest& request, HttpResponse& response) {` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L1984 | `void ApiServer::handleGetSpecialPaths(const HttpRequest& request, HttpResponse& response) {` |

</details>

---

看来已经有实现了，让我检查一下这些实现：

---

Read file: src/api_server.cpp

---



现在我需要更新进程监控器，让它支持重点进程管理。让我查看进程监控器的头文件：

---

Read file: include/process_monitor.h

---

我看到进程监控器已经有了重点进程管理的方法声明。现在让我检查实现是否完整：

---

<details>
            <summary>Grep search for "setFocusProcess.*pid.*focus" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///home/<USER>/coder/psfsmonL/src/process_monitor.cpp` | L100 | `void ProcessMonitor::setFocusProcess(pid_t pid, bool focus) {` |

</details>

---

Read file: src/process_monitor.cpp

---

现在我需要更新主监控器，让它能够处理文件监控器的回调，将重点进程的文件操作信息传递给进程监控器。让我查看主监控器的实现：

---

Read file: src/main_monitor.cpp

---

我需要查看文件监控器管理器的实现，看看它是如何处理文件事件的：

---

<details>
            <summary>Grep search for "FileMonitorManager.*handleFileEvent" • **0** files</summary>
        
No results found
</details>

---

让我查看文件监控器的实现，看看它是否有FileMonitorManager类：

---

<details>
            <summary>Grep search for "class FileMonitorManager" • **0** files</summary>
        
No results found
</details>

---

看来我的新实现中没有FileMonitorManager类。我需要更新主监控器，让它直接使用FileMonitor并设置回调函数。让我修改主监控器的文件监控初始化：

---



---



---



---



---

现在我需要在主监控器头文件中添加文件事件处理方法和文件监控器成员变量：

---

Read file: include/main_monitor.h

---

我需要在主监控器头文件中添加文件事件处理方法：

---



---

现在我需要在主监控器的实现中添加文件事件处理方法：

---



---

现在我需要更新需求文档，反映最新的变更：

---

```diff
- # PSFSMON-L 功能需求文档 (更新版本)
+ # PSFSMON-L 功能需求文档 (最新版本 - 仅使用Fanotify)
  
  ## 1. 项目概述
```

```diff
  
  ### 1.2 项目目标
- PSFSMON-L 是一个Linux系统安全监控程序，提供实时的进程、文件系统和网络监控功能。该程序主要功能是监控系统中所有运行的进程并收集它们的状态信息，**重点关注重点进程的文件操作行为分析**。
+ PSFSMON-L 是一个Linux系统安全监控程序，提供实时的进程、文件系统和网络监控功能。该程序主要功能是监控系统中所有运行的进程并收集它们的状态信息，**重点关注重点进程的文件操作行为分析和熵值计算**。
  
  ### 1.3 应用场景
```

```diff
  - 进程行为分析
  - 合规性监控
+ - 特殊路径保护
  
  ## 2. 技术规格要求
```

```diff
  - **最低内核版本**: 5.4.18
  - **必需特性**: 
-   - **仅使用Fanotify支持**（不再使用Inotify）
+   - **仅使用Fanotify支持**（完全移除Inotify机制）
    - /proc文件系统
    - /sys文件系统
```

```diff
    - 查询当前排除规则列表
  
- #### 3.1.4 重点进程管理 (新增)
+ #### 3.1.4 重点进程管理 (核心功能)
  - **需求ID**: REQ-PROC-004
  - **描述**: 支持设置重点进程，只有重点进程才进行详细的文件监控
```

```diff
    - 查询当前重点进程列表
    - 重点进程文件操作详细监控
+   - 重点进程文件熵值计算和统计
  
- ### 3.2 文件系统监控功能 (重大变更)
+ ### 3.2 文件系统监控功能 (重大变更 - 仅使用Fanotify)
  
  #### 3.2.1 仅使用Fanotify机制
```

```diff
    - 支持文件的打开、读取、写入、删除、重命名等操作监控
    - **不再区分是进程的哪个线程操作了什么文件**
+   - 进程级文件操作汇总
  
  #### 3.2.2 重点进程文件监控 (核心变更)
```

```diff
    - 记录的文件熵值，按读/写区分，统一累加记录到进程级别
    - 获取删除的文件数量、重命名的文件数量
-   - 记录重命名的扩展名列表
+   - 记录重命名的扩展名变化列表
    - 记录操作文件路径历史记录
    - 提供接口API支持查询这些信息
```

```diff
      size_t delete_count;           // 删除文件数量
      size_t rename_count;           // 重命名文件数量
-     std::vector<std::string> rename_extensions;  // 重命名扩展名列表
+     std::vector<std::string> rename_extensions;  // 重命名扩展名变化列表
      std::vector<std::string> path_history;       // 操作文件路径历史记录
      
```

```diff
  ```
  
- ## 4. API接口功能 (更新)
+ #### 3.4.4 文件事件结构 (增强)
+ ```cpp
+ struct FileEvent {
+     enum EventType {
+         OPEN, CLOSE, READ, WRITE, CREATE, DELETE, RENAME, RENAME_DELETE, ATTRIB_CHANGE
+     };
+     
+     EventType type;
+     uint64_t file_id;
+     std::string file_path;
+     pid_t pid;
+     pid_t tid;
+     uint64_t timestamp;
+     uint32_t uid;
+     uint32_t gid;
+     
+     // 重命名操作信息
+     std::string old_path;
+     std::string new_path;
+     bool target_exists;
+     
+     // 重点进程熵值信息 (新增)
+     double entropy_before;      // 操作前熵值（读操作）
+     double entropy_after;       // 操作后熵值（写操作）
+     bool is_focus_process;      // 是否为重点进程
+ };
+ ```
+ 
+ ## 4. API接口设计 (更新)
  
  ### 4.1 重点进程管理API (新增)
- - **需求ID**: REQ-API-006
- - **描述**: 提供重点进程管理接口
- - **接口列表**:
-   - `GET /api/focus-processes` - 获取当前重点进程列表
-   - `POST /api/focus-processes` - 添加重点进程
-   - `DELETE /api/focus-processes` - 删除重点进程
+ 
+ #### 4.1.1 获取重点进程列表
+ - **端点**: `GET /api/focus-processes`
+ - **描述**: 获取当前所有重点进程的列表
+ - **响应**: 
+ ```json
+ {
+     "count": 2,
+     "focus_processes": [
+         {
+             "pid": 1234,
+             "name": "important_app",
+             "executable_path": "/usr/bin/important_app",
+             "is_focus_process": true,
+             "read_entropy_count": 150,
+             "write_entropy_count": 75,
+             "delete_count": 5,
+             "rename_count": 3,
+             "path_history_count": 200
+         }
+     ]
+ }
+ ```
+ 
+ #### 4.1.2 添加重点进程
+ - **端点**: `POST /api/focus-processes`
+ - **描述**: 添加新的重点进程
+ - **请求体**:
+ ```json
+ {
+     "type": "pid",  // 或 "name"
+     "value": "1234" // PID或进程名
+ }
+ ```
+ 
+ #### 4.1.3 移除重点进程
+ - **端点**: `DELETE /api/focus-processes?type=pid&value=1234`
+ - **描述**: 移除重点进程
  
  ### 4.2 特殊路径管理API (新增)
- - **需求ID**: REQ-API-007
- - **描述**: 提供特殊路径保护管理接口
- - **接口列表**:
-   - `GET /api/special-paths` - 获取当前特殊路径列表
-   - `POST /api/special-paths` - 添加特殊路径
-   - `DELETE /api/special-paths` - 删除特殊路径
+ 
+ #### 4.2.1 获取特殊路径列表
+ - **端点**: `GET /api/special-paths`
+ - **描述**: 获取当前所有特殊保护路径
+ 
+ #### 4.2.2 添加特殊路径
+ - **端点**: `POST /api/special-paths`
+ - **请求体**:
+ ```json
+ {
+     "path": "/etc/passwd"
+ }
+ ```
+ 
+ #### 4.2.3 移除特殊路径
+ - **端点**: `DELETE /api/special-paths?path=/etc/passwd`
  
- ### 4.3 进程文件监控API (更新)
- - **需求ID**: REQ-API-008
- - **描述**: 提供重点进程文件监控信息查询接口
- - **接口列表**:
-   - `GET /api/process/files?pid={pid}` - 获取重点进程文件监控信息
-   - 返回内容包括：
-     - 是否为重点进程
-     - 读/写操作熵值统计
-     - 删除/重命名操作统计
-     - 重命名扩展名列表
-     - 文件路径操作历史
- 
- ### 4.4 现有API保持不变
- - 进程相关API
- - 网络相关API
- - 排除进程管理API
- - 受保护目录管理API
- 
- ## 5. 关键技术变更
- 
- ### 5.1 文件监控机制变更
- - **移除**: 完全移除Inotify机制及相关代码
- - **保留**: 仅使用Fanotify机制
- - **优化**: 根据内核版本自动选择可用的Fanotify事件类型
- 
- ### 5.2 线程级文件操作管理移除
- - **移除**: 不再收集进程每个线程的文件操作信息
- - **移除**: 不再区分是哪个线程操作了什么文件
- - **简化**: ThreadInfo结构不再包含文件操作管理
- 
- ### 5.3 重点进程文件监控机制
- - **新增**: 只有重点进程才进行详细文件监控
- - **新增**: 文件熵值计算和统计
- - **新增**: 进程级文件操作统计和历史记录
- 
- ### 5.4 特殊路径保护机制
- - **新增**: 特殊路径设置和管理
- - **新增**: 违规进程自动中止功能
- - **新增**: 违规行为日志记录
- 
- ## 6. 编译和部署要求
- 
- ### 6.1 编译方式支持
- - CMake编译系统
- - 标准Makefile编译
- - autotools编译系统支持
- 
- ### 6.2 代码组织要求
- - 单个源代码文件不超过1500行
- - 逻辑清晰的模块化设计
- - 完整的设计文档说明
- 
- ### 6.3 测试和文档
- - 同步更新测试代码
- - 同步更新相关脚本
- - 保持代码可读性和可维护性
- 
- ## 7. 性能和安全要求
- 
- ### 7.1 性能要求
- - 避免依赖大规模底层依赖库
- - 兼容Linux内核5.4.18及以上版本
- - 支持多CPU架构本地编译
+ ### 4.3 文件监控统计API (更新)
+ 
+ #### 4.3.1 文件监控统计
+ - **端点**: `GET /api/file/stats`
+ - **响应**:
+ ```json
+ {
+     "total_events": 15420,
+     "fanotify_events": 15420,
+     "focus_process_events": 1250,
+     "special_path_violations": 3,
+     "entropy_calculations": 800
+ }
+ ```
+ 
+ ### 4.4 重点进程文件信息API (新增)
+ 
+ #### 4.4.1 获取重点进程文件操作详情
+ - **端点**: `GET /api/process/files?pid=1234`
+ - **描述**: 获取指定重点进程的文件操作详细信息
+ - **响应**:
+ ```json
+ {
+     "pid": 1234,
+     "is_focus_process": true,
+     "read_entropy_total": 1250.75,
+     "write_entropy_total": 890.25,
+     "read_entropy_count": 150,
+     "write_entropy_count": 75,
+     "read_entropy_average": 8.34,
+     "write_entropy_average": 11.87,
+     "delete_count": 5,
+     "rename_count": 3,
+     "rename_extensions": [".tmp -> .bak", ".log -> .old"],
+     "path_history": [
+         "/home/<USER>/file1.txt",
+         "/tmp/temp_file -> /home/<USER>/backup.txt",
+         "..."
+     ]
+ }
+ ```
+ 
+ ## 5. 配置文件更新
+ 
+ ### 5.1 新增配置项
+ ```ini
+ [file_monitor]
+ # 仅使用Fanotify（移除use_inotify选项）
+ use_fanotify_only=true
+ 
+ # 重点进程监控
+ enable_focus_process_monitoring=true
+ calculate_file_entropy=true
+ focus_process_list=nginx,apache2,mysql
+ 
+ # 特殊路径保护
+ enable_special_path_protection=true
+ special_paths=/etc/passwd,/etc/shadow,/boot
+ terminate_violating_processes=true
+ ```
+ 
+ ## 6. 实现变更总结
+ 
+ ### 6.1 移除的功能
+ - **完全移除Inotify机制**
+ - 线程级文件操作管理
+ - 混合监控策略（Fanotify+Inotify）
+ 
+ ### 6.2 新增的功能
+ - **重点进程文件熵值计算**
+ - **特殊路径保护机制**
+ - 重点进程管理API
+ - 文件操作扩展名变化跟踪
+ - 进程级文件操作汇总
+ 
+ ### 6.3 优化的功能
+ - 简化的监控策略（仅Fanotify）
+ - 更高效的事件处理
+ - 更精确的重点进程监控
+ - 更完善的API接口
+ 
+ ## 7. 性能和兼容性
+ 
+ ### 7.1 性能优势
+ - 移除Inotify减少了系统开销
+ - 重点进程监控减少了不必要的计算
+ - 更高效的事件去重机制
+ 
+ ### 7.2 兼容性要求
+ - 要求内核版本 >= 5.4.18（支持完整Fanotify功能）
+ - 对于较老内核，使用基础Fanotify功能
  
- ### 7.2 安全要求
+ ### 7.3 安全增强
  - 特殊路径保护机制
- - 违规进程自动中止
- - 完整的审计日志记录
- - 重点进程行为深度分析
+ - 重点进程行为分析
+ - 文件熵值异常检测
  
- 这个更新版本的需求文档反映了项目的重大架构调整，从通用的文件监控转向了重点进程的深度监控，提高了安全监控的针对性和效率。 
+ 这个更新版本的需求文档反映了项目的重大变更，特别是移除Inotify机制、专注于重点进程监控和文件熵值计算的新方向。 
```

---

我看到您想要继续之前被中断的Linux安全监控程序(PSFSMON-L)的工作。让我先检查一下当前的代码库状态，了解已经完成的工作和需要继续的部分。

---

<details>
            <summary>Listed current directory • **23** results</summary>
        
| Name |
|-------|
| 📄 `REQUIREMENTS.md` |
| 📁 `src` |
| 📁 `refdocs` |
| 📁 `obj` |
| 📁 `.git` |
| 📁 `.idea` |
| 📁 `include` |
| 📁 `cmake-build-debug` |
| 📄 `CMakeLists.txt` |
| 📄 `Makefile` |
| 📄 `DEBUG_QUICK_START.md` |
| 📁 `docs` |
| 📄 `.gdbinit` |
| 📁 `scripts` |
| 📄 `.gitignore` |
| 📁 `test` |
| 📁 `ref_code` |
| 📄 `PROJECT_REVIEW_SUMMARY.md` |
| 📄 `README.md` |
| 📄 `API_REFERENCE.md` |
| 📁 `.specstory` |
| 📁 `config` |
| 📄 `.cursorindexingignore` |

</details>

---

Read file: PROJECT_REVIEW_SUMMARY.md

---

```bash
cd /home/<USER>/coder/psfsmonL && make clean
```

```
helloub22@helloub22-virtual-machine:~/coder/psfsmonL$ cd /home/<USER>/coder/psfsmonL && make clean
src/file_monitor.cpp:23:10: fatal error: ../include/utils.h: 没有那个文件或目录
   23 | #include "../include/utils.h"
      |          ^~~~~~~~~~~~~~~~~~~~
compilation terminated.
src/file_monitor.cpp:23:10: fatal error: ../include/utils.h: 没有那个文件或目录
   23 | #include "../include/utils.h"
      |          ^~~~~~~~~~~~~~~~~~~~
compilation terminated.
src/file_monitor.cpp:23:10: fatal error: ../include/utils.h: 没有那个文件或目录
   23 | #include "../include/utils.h"
      |          ^~~~~~~~~~~~~~~~~~~~
compilation terminated.
src/file_monitor.cpp:23:10: fatal error: ../include/utils.h: 没有那个文件或目录
   23 | #include "../include/utils.h"
      |          ^~~~~~~~~~~~~~~~~~~~
compilation terminated.
src/file_monitor.cpp:23:10: fatal error: ../include/utils.h: 没有那个文件或目录
   23 | #include "../include/utils.h"
      |          ^~~~~~~~~~~~~~~~~~~~
compilation terminated.
Cleaning up...
rm -rf obj bin
Clean completed!
helloub22@helloub22-virtual-machine:~/coder/psfsmonL$
```

---

我看到项目在清理过程中出现了编译错误，提示缺少 `utils.h` 文件。让我检查一下当前的文件结构和这个问题。

---

<details>
            <summary>Listed directory include • **9** results</summary>
        
| Name |
|-------|
| 📄 `main_monitor.h` |
| 📄 `file_monitor.h` |
| 📄 `psfsmon.h` |
| 📄 `kernel_compat.h` |
| 📄 `api_server.h` |
| 📄 `process_monitor.h` |
| 📄 `network_monitor.h` |
| 📄 `process_exclusion.h` |
| 📄 `path_protection.h` |

</details>

---

Read file: src/file_monitor.cpp

---

我看到问题了。`file_monitor.cpp` 文件引用了 `utils.h`，但这个文件在 `include` 目录中不存在。让我检查一下其他源文件是否也有类似的问题，然后创建缺失的 `utils.h` 文件。

---

<details>
            <summary>Grep search for "#include.*utils\.h" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L22 | `#include "../include/utils.h"` |

</details>

---

<details>
            <summary>Grep search for "getLogManager" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L47 | `extern class LogManager& getLogManager();` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L51 | `getLogManager().error("[FileMonitor] " + message);` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L55 | `getLogManager().warning("[FileMonitor] " + message);` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L59 | `getLogManager().info("[FileMonitor] " + message);` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L63 | `getLogManager().debug("[FileMonitor] " + message);` |
| `file:///home/<USER>/coder/psfsmonL/src/psfsmon_core.cpp` | L8 | `extern class LogManager& getLogManager();` |

</details>

---

<details>
            <summary>Grep search for "class LogManager" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///home/<USER>/coder/psfsmonL/include/main_monitor.h` | L179 | `class LogManager {` |
| `file:///home/<USER>/coder/psfsmonL/include/main_monitor.h` | L179 | `class LogManager {` |

</details>

---

现在我明白了问题所在。`file_monitor.cpp` 引用了 `utils.h`，但这个文件不存在。从代码来看，它主要需要日志功能，而 `LogManager` 已经在 `main_monitor.h` 中定义了。让我创建一个简单的 `utils.h` 文件，或者更好的方法是移除对 `utils.h` 的引用，因为所需的功能已经通过其他头文件提供了。

---

Read file: include/main_monitor.h

---

很好！我看到 `main_monitor.h` 中已经定义了 `LogManager` 类和 `Utils` 类。现在我需要移除 `file_monitor.cpp` 中对 `utils.h` 的引用，因为所需的功能已经通过 `main_monitor.h` 提供了。

---



---

现在让我尝试重新编译项目，看看是否还有其他问题。

---

```bash
make
```

```
helloub22@helloub22-virtual-machine:~/coder/psfsmonL$ make
mkdir -p obj/valgrind
mkdir -p obj/profile
mkdir -p obj/sanitizer
mkdir -p obj/debug
Compiling (release) src/api_server.cpp...
g++ -std=c++11 -Wall -Wextra -Wpedantic -O2 -pthread -DNDEBUG -Iinclude -c src/api_server.cpp -o obj/api_server.o
In file included from src/../include/api_server.h:4,
                 from src/api_server.cpp:1:
src/../include/psfsmon.h: In constructor ‘MonitorConfig::MonitorConfig()’:
src/../include/psfsmon.h:277:10: warning: ‘MonitorConfig::enable_experimental_features’ will be initialized after [-Wreorder]
  277 |     bool enable_experimental_features; // 启用实验性功能
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~
src/../include/psfsmon.h:239:10: warning:   ‘bool MonitorConfig::use_fanotify_only’ [-Wreorder]
  239 |     bool use_fanotify_only;            // 仅使用Fanotify（默认true）
      |          ^~~~~~~~~~~~~~~~~
src/../include/psfsmon.h:279:5: warning:   when initialized here [-Wreorder]
  279 |     MonitorConfig() :
      |     ^~~~~~~~~~~~~
src/api_server.cpp: In member function ‘void ApiServer::handleGetConfig(const HttpRequest&, HttpResponse&)’:
src/api_server.cpp:655:73: error: ‘struct MonitorConfig’ has no member named ‘max_files_per_thread’
  655 |         .addNumber("max_files_per_thread", static_cast<uint64_t>(config.max_files_per_thread))
      |                                                                         ^~~~~~~~~~~~~~~~~~~~
make: *** [Makefile:116：obj/api_server.o] 错误 1
helloub22@helloub22-virtual-machine:~/coder/psfsmonL$
```

---

我看到有两个问题：
1. 一个警告：`MonitorConfig` 构造函数中成员初始化顺序问题
2. 一个错误：`MonitorConfig` 结构体中没有 `max_files_per_thread` 成员

让我先检查 `psfsmon.h` 中的 `MonitorConfig` 结构定义。

---

Read file: include/psfsmon.h

---





---

