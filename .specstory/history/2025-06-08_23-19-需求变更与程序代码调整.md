<!-- Generated by SpecStory -->

# 需求变更与程序代码调整 (2025-06-09 07:19:59)

_**User**_

当前项目目录下的代码，主体结构已经完成，功能比较完整和正确。
但是，我的需求发生了重大变化，当前项目虽然主体依然可用，但是细节调整也是非常大的，请按下面的描述和需求变更，梳理和更改当前的程序代码。
我需要你核对下面的需求，特别仔细的检查和细化文件监控能力，更改部份已经完成的功能代码，以体现最新版本的需求变更。
所有需求如下（包括更改的需求）：
1、我需要编写一个运行在Linux平台下面的安全监控程序；
2、这个程序的主要功能是实时监控所有系统中所运行的进程并收集它们的状态，并具备汇报这些信息的能力；
3、这个程序需要以C++语言进行编写，低层的库可以使用C语言。C++语言的标准，由于系统编译器的版本问题，要控制在可以支持C++11标准的状态；
4、这个程序可能需要在不同的CPU上运行，例如x64/amd64, aarch64/arm64等等，但是不会跨平台编译，也就是说，它一定会在所在的平台下，用本地编译器进行编译；
5、这个程序要在启动时收集所有的已有进程的信息，管理它们，具备形成进程树的能力；
6、针对每个进程，要可以管理它创建或clone的所有线程、子线程；
7、收集的信息要归属并管理在进程所属并管理的每个线程下面，但是要在进程这一层次有一个汇总；
8、收集的信息主要包括3类，1是进程的基本信息例如名称、CPU使用状态、内存使用状态、路径等等；2是在一般情况下，不再收集进程的每个线程所操作的文件信息，也不再区分是进程的哪个线程操作了什么文件，不再需要通过各种方式找到文件操作的具体线程；3是进程的基本网络操作信息，例如打开了多少连接，连接了什么IP地址，每个IP收发的基本数据量是多少、整体收发数据量等等；
9、因为OS的限制，我们不能使用eBPF机制来收集这些信息；这个要求非常重要；
10、我们要使用Fanotify机制来进行特殊情况下的文件信息的获取，不再需要整合Inotify机制到本程序中，已有的整合要全部去除；Fanotify机制要根据OS的支持能力不同，尽可能多的监控文件变化操作，特别是读、写、删除和重命名操作，包括目录删除，相关的支持文档，在项目目录下的@/refdocs 目录中，你可以自行查阅；
11、对于进程信息、网络通信信息，你可以使用合适的方法，但是要避免依赖其它大规模的底层依赖库，因为我们的操作系统版本并不高，而且可能是多个CPU类型的，它的基础版本只有5.4.18，可能很多功能都不具备，例如Sysdig和它所使用的一些基础库是无法编译通过的；
12、程序可以直接执行，以直观的方式进行输出，也可以以服务的方式进行安装和执行，通过对外接口形式进行输出；
13、程序对外的接口，可以向调用者灵活的提供所输出的信息，例如进程树、每个进程的信息汇总，每个进程的详细信息例如它所管理的每个线程的所有的文件的操作序列字符串等等；
14、每个进程不再管理所属的每个线程的文件信息；
15、程序要支持CMake和标准Makefile这2种编译方式，或者使用auto config系列工具编译；
16、支持忽略进程设置，被设置为忽略进程的，不再监控其相关信息（所有信息，包括文件操作信息）；
17、支持重点进程设置，被设置为重点进程的，除进程基本信息外，还要依靠和使用Fanotify机制，尽可能多的监控其文件操作相关信息，也只有被设置为重点进程，才会监控文件操作信息；监控重点是读、写操作，要在读操作前，计算文件熵值；要在写操作后，再次计算文件熵值；记录的文件熵值，按读/写区分，统一累加记录到进程级别进行汇总；如果系统支持，使用Fanotify机制获取删除的文件数量、重命名的文件数量、重命名的扩展名列表、操作文件路径历史记录等信息，记录到重点进程的相关数据结构中（也可以是类属性中）供查询；提供接口API支持这一功能；该功能是变化最大的需求；
18、支持特殊路径设置，被设置为特殊路径的，在发生针对特殊路径的写操作时，包括删除和重命名，相关的进程会被中止，并将相关记录在日志中；
19、保持程序的可读性，如果单个源代码文件过大，例如超过了1500行，则可以在逻辑上拆分它，并在设计文件中给出简单的说明；
20、同步更改相关的需求文档、测试代码和脚本。

---

_**Assistant**_



---

我需要先查看当前项目的结构，然后根据您的需求变更进行梳理和修改。让我开始分析当前代码：

---

<details>
            <summary>Listed current directory • **23** results</summary>
        
| Name |
|-------|
| 📁 `refdocs` |
| 📁 `obj` |
| 📁 `.git` |
| 📁 `.idea` |
| 📁 `include` |
| 📁 `src` |
| 📁 `cmake-build-debug` |
| 📄 `CMakeLists.txt` |
| 📄 `Makefile` |
| 📄 `DEBUG_QUICK_START.md` |
| 📁 `docs` |
| 📄 `.gdbinit` |
| 📁 `scripts` |
| 📄 `.gitignore` |
| 📁 `test` |
| 📁 `ref_code` |
| 📄 `PROJECT_REVIEW_SUMMARY.md` |
| 📄 `README.md` |
| 📄 `REQUIREMENTS.md` |
| 📄 `API_REFERENCE.md` |
| 📁 `.specstory` |
| 📁 `config` |
| 📄 `.cursorindexingignore` |

</details>

---

<details>
            <summary>Listed directory src • **11** results</summary>
        
| Name |
|-------|
| 📄 `kernel_compat.cpp` |
| 📄 `file_monitor.cpp` |
| 📄 `main.cpp` |
| 📄 `main_monitor.cpp` |
| 📄 `process_monitor.cpp` |
| 📄 `psfsmon_core.cpp` |
| 📄 `api_server.cpp` |
| 📄 `network_monitor.cpp` |
| 📄 `path_protection.cpp` |
| 📄 `process_exclusion.cpp` |
| 📄 `utils.cpp` |

</details>

---

<details>
            <summary>Listed directory include • **9** results</summary>
        
| Name |
|-------|
| 📄 `kernel_compat.h` |
| 📄 `main_monitor.h` |
| 📄 `file_monitor.h` |
| 📄 `psfsmon.h` |
| 📄 `process_monitor.h` |
| 📄 `api_server.h` |
| 📄 `network_monitor.h` |
| 📄 `process_exclusion.h` |
| 📄 `path_protection.h` |

</details>

---

现在让我查看refdocs目录中的Fanotify文档：

---

Read file: refdocs/Fanotify框架说明(2025_6_6 10：26：51).html

---

让我更有针对性地查看当前的主要源代码文件，以了解现有的架构：

---

Read file: include/psfsmon.h

---

现在让我查看文件监控的相关代码：

---

Read file: include/file_monitor.h

---

现在查看进程监控相关代码：

---

Read file: include/process_monitor.h

---

现在查看当前的需求文档：

---

Read file: REQUIREMENTS.md

---

现在让我查看Fanotify相关的文档：

---

<details>
            <summary>Searched codebase "Fanotify 重点进程 文件熵值 写操作 读操作" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `ref_code/fanotify_monitor.c` | L291-348 |
| `ref_code/fanotify_monitor.c` | L124-149 |
| `ref_code/fanotify_monitor.c` | L348-409 |
| `ref_code/fanotify_monitor.c` | L235-291 |
| `test/final_capture_test.cpp` | L222-254 |
| `ref_code/fanotify_monitor.c` | L199-235 |
| `src/file_monitor.cpp` | L336-385 |
| `src/file_monitor.cpp` | L482-534 |
| `src/file_monitor.cpp` | L133-181 |
| `src/file_monitor.cpp` | L181-212 |
| `src/file_monitor.cpp` | L1388-1416 |
| `test/final_capture_test.cpp` | L373-390 |
| `ref_code/fanotify_monitor.c` | L409-473 |
| `include/file_monitor.h` | L129-184 |
| `refdocs/Fanotify框架说明(2025_6_6 10：26：51).html` | L364-386 |
| `src/file_monitor.cpp` | L799-837 |
| `test/final_capture_test.cpp` | L133-173 |
| `include/file_monitor.h` | L1-48 |
| `refdocs/Fanotify框架说明(2025_6_6 10：26：51).html` | L911-957 |
| `refdocs/Fanotify框架说明(2025_6_6 10：26：51).html` | L386-395 |
| `src/file_monitor.cpp` | L385-419 |
| `test/test_low_kernel_monitoring.cpp` | L170-199 |
| `src/file_monitor.cpp` | L212-261 |
| `src/file_monitor.cpp` | L581-622 |
| `ref_code/fanotify_monitor.c` | L1-73 |

</details>

---

基于我对当前项目代码的分析和您提出的重大需求变更，我将系统性地梳理并修改代码。主要变化集中在以下几个方面：

1. **去除Inotify支持，专注Fanotify**（需求10）
2. **不再管理线程级文件操作**（需求8.2, 14）
3. **增加重点进程监控和文件熵值计算**（需求17）
4. **支持特殊路径设置和进程中止**（需求18）

让我开始修改核心头文件：

---

```diff
      const char FILE_OP_RENAME_DELETE = 'P';  // 重命名时目标已存在
      const char FILE_OP_CLOSE = 'C';
+     
+     // 重点进程监控相关常量
+     const size_t DEFAULT_READ_ENTROPY_COUNT = 0;     // 默认读操作熵值计数
+     const size_t DEFAULT_WRITE_ENTROPY_COUNT = 0;    // 默认写操作熵值计数
+     const size_t MAX_PATH_HISTORY = 1000;            // 最大路径历史记录数
+     const size_t MAX_EXTENSION_LIST = 100;           // 最大扩展名列表长度
  }
  
- // 文件操作信息
+ // 文件操作信息 - 简化版（不再需要线程级管理）
  struct FileOperation {
      uint64_t file_id;           // 文件唯一标识符
```

```diff
  };
  
- // 线程信息
+ // 线程信息 - 不再管理文件操作
  struct ThreadInfo {
      pid_t tid;                  // 线程ID
```

```diff
      uint64_t created_time;      // 创建时间
      
-     // 文件操作信息 - 使用循环缓冲区
-     std::vector<FileOperation> file_operations;
-     size_t file_op_index;       // 当前操作索引
-     mutable std::mutex file_ops_mutex;
-     
-     ThreadInfo() : tid(0), cpu_usage(0.0), memory_usage(0), 
-                    created_time(0), file_op_index(0) {
-         file_operations.resize(Constants::MAX_FILES_PER_THREAD);
-     }
-     
-     // 添加文件操作
-     void addFileOperation(const FileOperation& op);
+     ThreadInfo() : tid(0), cpu_usage(0.0), memory_usage(0), created_time(0) {}
+ };
+ 
+ // 重点进程文件监控信息
+ struct FocusProcessFileInfo {
+     // 文件熵值信息
+     double total_read_entropy;     // 读操作累计文件熵值
+     double total_write_entropy;    // 写操作累计文件熵值
+     size_t read_entropy_count;     // 读操作熵值计数
+     size_t write_entropy_count;    // 写操作熵值计数
+     
+     // 文件操作统计
+     size_t delete_count;           // 删除文件数量
+     size_t rename_count;           // 重命名文件数量
+     std::vector<std::string> rename_extensions;  // 重命名扩展名列表
+     std::vector<std::string> path_history;       // 操作文件路径历史记录
+     
+     mutable std::mutex mutex;      // 线程安全锁
+     
+     FocusProcessFileInfo() : total_read_entropy(0.0), total_write_entropy(0.0),
+                            read_entropy_count(0), write_entropy_count(0),
+                            delete_count(0), rename_count(0) {}
+     
+     // 添加读操作熵值
+     void addReadEntropy(double entropy);
+     
+     // 添加写操作熵值
+     void addWriteEntropy(double entropy);
+     
+     // 记录删除操作
+     void recordDelete(const std::string& path);
+     
+     // 记录重命名操作
+     void recordRename(const std::string& old_path, const std::string& new_path);
      
-     // 获取文件操作历史
-     std::vector<FileOperation> getFileOperations() const;
+     // 记录文件路径操作
+     void recordPathOperation(const std::string& path);
+     
+     // 获取平均读熵值
+     double getAverageReadEntropy() const;
+     
+     // 获取平均写熵值
+     double getAverageWriteEntropy() const;
      
-     // 清理过期的文件操作记录
-     void cleanupOldFileOperations(uint64_t max_age_seconds = 3600); // 默认1小时
+     // 清理过期记录
+     void cleanup();
  };
  
```

```diff
      std::vector<NetworkConnection> network_connections;
      mutable std::mutex network_mutex;
+     
+     // 重点进程标记和文件监控信息
+     bool is_focus_process;      // 是否为重点进程
+     std::unique_ptr<FocusProcessFileInfo> focus_file_info; // 重点进程文件信息
      
      ProcessInfo() : pid(0), ppid(0), uid(0), gid(0), 
-                     cpu_usage(0.0), memory_usage(0), virtual_memory(0), start_time(0) {}
+                     cpu_usage(0.0), memory_usage(0), virtual_memory(0), start_time(0),
+                     is_focus_process(false) {}
      
      // 添加线程
```

```diff
      void updateNetworkConnections(const std::vector<NetworkConnection>& connections);
      
-     // 获取进程的文件操作汇总
-     std::map<uint64_t, std::string> getFileOperationsSummary() const;
+     // 设置为重点进程
+     void setFocusProcess(bool focus);
+     
+     // 获取重点进程文件信息（如果是重点进程）
+     FocusProcessFileInfo* getFocusFileInfo() const;
+     
+     // 基础信息获取（不再包含文件操作汇总）
+     std::map<std::string, std::string> getBasicInfoSummary() const;
  };
  
```

```diff
      std::string pid_file;              // PID文件路径
      
-     // 监控配置
-     size_t max_files_per_thread;       // 每线程最大文件数
+     // 文件监控配置（仅使用Fanotify）
      std::string watch_paths;           // 监控路径
      std::string exclude_paths;         // 排除路径
      bool recursive_monitor;            // 递归监控
      uint32_t dedup_window_ms;          // 去重时间窗口
-     // 注意：监控策略现在基于内核版本自动选择，无需手动配置
+     bool use_fanotify_only;            // 仅使用Fanotify（默认true）
+     
+     // 重点进程监控配置
+     bool enable_focus_process_monitoring; // 启用重点进程监控
+     bool calculate_file_entropy;      // 计算文件熵值
+     std::string focus_process_list;    // 重点进程列表（逗号分隔）
+     
+     // 特殊路径保护配置
+     bool enable_special_path_protection; // 启用特殊路径保护
+     std::string special_paths;         // 特殊路径列表（逗号分隔）
+     bool terminate_violating_processes; // 中止违规进程
      
      // 网络监控配置
```

```diff
          check_kernel_compatibility(true),
          min_kernel_version("5.4.18"),
-         enable_experimental_features(false) {}
+         enable_experimental_features(false),
+         use_fanotify_only(true),
+         enable_focus_process_monitoring(true),
+         calculate_file_entropy(true),
+         focus_process_list(""),
+         enable_special_path_protection(false),
+         special_paths(""),
+         terminate_violating_processes(false) {}
  };
  
```

---

现在修改文件监控头文件以移除Inotify支持并增加熵值计算功能：

---

```diff
  #include <queue>
  #include <condition_variable>
+ #include <functional>
  
  // Fanotify事件类型
```

```diff
  };
  
- // 文件监控器类
+ // 文件熵值计算器
+ class FileEntropyCalculator {
+ public:
+     // 计算文件的Shannon熵值
+     static double calculateFileEntropy(const std::string& file_path);
+     
+     // 计算缓冲区的熵值
+     static double calculateBufferEntropy(const char* buffer, size_t size);
+     
+ private:
+     // 内部熵值计算算法
+     static double computeEntropy(const std::vector<size_t>& frequencies, size_t total_bytes);
+ };
+ 
+ // 重点进程管理器
+ class FocusProcessManager {
+ public:
+     static FocusProcessManager& getInstance();
+     
+     // 添加重点进程
+     void addFocusProcess(pid_t pid);
+     void addFocusProcess(const std::string& process_name);
+     
+     // 移除重点进程
+     void removeFocusProcess(pid_t pid);
+     void removeFocusProcess(const std::string& process_name);
+     
+     // 检查是否为重点进程
+     bool isFocusProcess(pid_t pid) const;
+     bool isFocusProcess(const std::string& process_name) const;
+     
+     // 获取所有重点进程
+     std::vector<pid_t> getFocusProcesses() const;
+     
+     // 清空重点进程列表
+     void clear();
+     
+ private:
+     FocusProcessManager() = default;
+     ~FocusProcessManager() = default;
+     
+     std::unordered_set<pid_t> focus_pids_;
+     std::unordered_set<std::string> focus_names_;
+     mutable std::mutex mutex_;
+ };
+ 
+ // 特殊路径保护管理器
+ class SpecialPathManager {
+ public:
+     static SpecialPathManager& getInstance();
+     
+     // 添加特殊路径
+     void addSpecialPath(const std::string& path);
+     
+     // 移除特殊路径
+     void removeSpecialPath(const std::string& path);
+     
+     // 检查是否为特殊路径
+     bool isSpecialPath(const std::string& path) const;
+     
+     // 处理特殊路径违规
+     void handleViolation(const std::string& path, pid_t pid, const std::string& process_name);
+     
+     // 获取所有特殊路径
+     std::vector<std::string> getSpecialPaths() const;
+     
+     // 清空特殊路径
+     void clear();
+     
+ private:
+     SpecialPathManager() = default;
+     ~SpecialPathManager() = default;
+     
+     std::unordered_set<std::string> special_paths_;
+     mutable std::mutex mutex_;
+     bool terminate_violating_processes_ = false;
+     
+     friend class FileMonitor;
+ };
+ 
+ // 文件监控器类（仅使用Fanotify）
  class FileMonitor {
  public:
```

```diff
      const KernelCompat& getKernelCompat() const { return kernel_compat_; }
      bool supportsKernelTidReporting() const { return kernel_compat_.supportsReportTid(); }
-     bool needsInotifySupport() const { return kernel_compat_.needsInotifySupport(); }
      KernelCompat::MonitorStrategy getRecommendedStrategy() const { return kernel_compat_.getRecommendedStrategy(); }
      
      // 获取统计信息
      uint64_t getTotalEvents() const { return total_events_; }
      uint64_t getFanotifyEvents() const { return fanotify_events_; }
-     uint64_t getInotifyEvents() const { return inotify_events_; }
      
      // 检查内核支持
```

```diff
      bool initializeFanotify();
      void closeFanotify();
-     
-     // Inotify相关
-     int inotify_fd_;
-     bool inotify_initialized_;
-     std::thread inotify_thread_;
-     std::unordered_map<int, std::string> watch_descriptors_; // wd -> path映射
-     void inotifyEventLoop();
-     bool initializeInotify();
-     void closeInotify();
-     bool addInotifyWatch(const std::string& path, bool recursive);
-     void removeInotifyWatch(const std::string& path);
      
      // 事件处理
```

```diff
      pid_t getTidFromSyscallTrace(pid_t pid, const std::string& file_path);
      bool getProcessInfo(pid_t pid, uint32_t& uid, uint32_t& gid);
+     
+     // 重点进程文件监控处理
+     void handleFocusProcessFileEvent(const FileEvent& event);
+     
+     // 特殊路径保护处理
+     void handleSpecialPathAccess(const FileEvent& event);
      
      // 线程识别配置
```

```diff
      std::atomic<uint64_t> total_events_;
      std::atomic<uint64_t> fanotify_events_;
-     std::atomic<uint64_t> inotify_events_;
      
      // 日志
      void logError(const std::string& message);
-     void logInfo(const std::string& message);
      void logWarning(const std::string& message);
      void logDebug(const std::string& message);
+     void logInfo(const std::string& message);
      
-     // 辅助函数
+     // 事件类型转换
      FileEvent::EventType fanotifyMaskToEventType(uint64_t mask);
-     FileEvent::EventType inotifyMaskToEventType(uint32_t mask);
      bool isMoveFromEvent(uint64_t mask);
      bool isMoveToEvent(uint64_t mask);
      
-     // 递归目录遍历
+     // 目录监控辅助
      void addDirectoryRecursive(const std::string& path);
      bool isDirectory(const std::string& path);
-     std::vector<std::string> listDirectory(const std::string& path);
      
      // 内核版本检查
```

```diff
  };
  
- // 文件监控管理器 - 单例模式
+ // 文件监控管理器
  class FileMonitorManager {
  public:
      static FileMonitorManager& getInstance();
      
-     bool initialize(const MonitorConfig* config = nullptr);
+     bool initialize();
      bool start();
      void stop();
      
-     void setProcessMonitor(ProcessMonitor* monitor) { process_monitor_ = monitor; }
+     void setProcessMonitor(class ProcessMonitor* monitor) { process_monitor_ = monitor; }
      
-     // 获取文件监控器
-     FileMonitor* getFileMonitor() const;
-     
-     // 文件事件处理
+     // 事件处理
      void handleFileEvent(const FileEvent& event);
      
- private:
-     // 辅助函数
+     // 工具函数
      uint64_t generateFileIdFromStat(const std::string& path, const struct stat& st);
-     FileMonitorManager() : file_monitor_(nullptr), process_monitor_(nullptr) {}
+     
+ private:
+     FileMonitorManager() = default;
      ~FileMonitorManager() = default;
-     
-     // 禁用拷贝
-     FileMonitorManager(const FileMonitorManager&) = delete;
-     FileMonitorManager& operator=(const FileMonitorManager&) = delete;
      
      std::unique_ptr<FileMonitor> file_monitor_;
-     ProcessMonitor* process_monitor_;
+     class ProcessMonitor* process_monitor_;
      
-     // 事件映射表 - 将重命名操作配对
-     std::unordered_map<std::string, FileEvent> pending_moves_;
-     std::mutex pending_moves_mutex_;
+     // 移动事件处理（如果内核支持）
      void handleMoveEvent(const FileEvent& event);
      void processPendingMoves();
+     
+     // 排除复制和赋值
+     FileMonitorManager(const FileMonitorManager&) = delete;
+     FileMonitorManager& operator=(const FileMonitorManager&) = delete;
  };
  
```

---

现在修改进程监控头文件以支持重点进程管理：

---

```diff
      std::vector<std::shared_ptr<ProcessInfo>> findProcessesByName(const std::string& name) const;
      
-     // 添加文件操作到指定进程的线程
-     void addFileOperationToThread(pid_t pid, pid_t tid, const FileOperation& operation);
+     // 重点进程管理
+     void setFocusProcess(pid_t pid, bool focus = true);
+     void setFocusProcess(const std::string& process_name, bool focus = true);
+     bool isFocusProcess(pid_t pid) const;
+     std::vector<pid_t> getFocusProcesses() const;
      
-     // 添加文件操作（根据需求文档的接口）
-     void addFileOperation(pid_t pid, pid_t tid, const FileIdentifier& file_id, char op_char);
+     // 重点进程文件操作记录（仅针对重点进程）
+     void addFileOperationToFocusProcess(pid_t pid, const FileIdentifier& file_id, char op_char, 
+                                       const std::string& file_path = "");
+     
+     // 重点进程熵值记录
+     void addEntropyToFocusProcess(pid_t pid, double entropy, bool is_read_operation);
+     
+     // 重点进程统计记录
+     void recordFocusProcessDelete(pid_t pid, const std::string& file_path);
+     void recordFocusProcessRename(pid_t pid, const std::string& old_path, const std::string& new_path);
      
      // 获取统计信息
```

---

现在实现psfsmon.h中新增的方法。让我先创建实现文件：

---

```diff
  #include <algorithm>
  #include <cstring>
+ #include <chrono>
+ #include <cmath>
  
  // ThreadInfo 实现
```

```diff
      network_connections = connections;
  }
+ 
+ void ProcessInfo::setFocusProcess(bool focus) {
+     is_focus_process = focus;
+     
+     if (focus && !focus_file_info) {
+         focus_file_info = std::make_unique<FocusProcessFileInfo>();
+     } else if (!focus) {
+         focus_file_info.reset();
+     }
+ }
+ 
+ FocusProcessFileInfo* ProcessInfo::getFocusFileInfo() const {
+     return is_focus_process ? focus_file_info.get() : nullptr;
+ }
+ 
+ std::map<std::string, std::string> ProcessInfo::getBasicInfoSummary() const {
+     std::map<std::string, std::string> summary;
+     
+     summary["pid"] = std::to_string(pid);
+     summary["ppid"] = std::to_string(ppid);
+     summary["name"] = process_name;
+     summary["executable_path"] = executable_path;
+     summary["command_line"] = command_line;
+     summary["cwd"] = cwd;
+     summary["uid"] = std::to_string(uid);
+     summary["gid"] = std::to_string(gid);
+     summary["state"] = state;
+     summary["cpu_usage"] = std::to_string(cpu_usage);
+     summary["memory_usage"] = std::to_string(memory_usage);
+     summary["virtual_memory"] = std::to_string(virtual_memory);
+     summary["start_time"] = std::to_string(start_time);
+     summary["is_focus_process"] = is_focus_process ? "true" : "false";
+     
+     // 线程统计
+     {
+         std::lock_guard<std::mutex> lock(threads_mutex);
+         summary["thread_count"] = std::to_string(threads.size());
+     }
+     
+     // 网络连接统计
+     {
+         std::lock_guard<std::mutex> lock(network_mutex);
+         summary["network_connections"] = std::to_string(network_connections.size());
+         
+         uint64_t total_tx = 0, total_rx = 0;
+         for (const auto& conn : network_connections) {
+             total_tx += conn.tx_bytes;
+             total_rx += conn.rx_bytes;
+         }
+         summary["total_tx_bytes"] = std::to_string(total_tx);
+         summary["total_rx_bytes"] = std::to_string(total_rx);
+     }
+     
+     // 重点进程信息
+     if (is_focus_process && focus_file_info) {
+         summary["read_entropy_avg"] = std::to_string(focus_file_info->getAverageReadEntropy());
+         summary["write_entropy_avg"] = std::to_string(focus_file_info->getAverageWriteEntropy());
+         summary["delete_count"] = std::to_string(focus_file_info->delete_count);
+         summary["rename_count"] = std::to_string(focus_file_info->rename_count);
+         summary["path_history_size"] = std::to_string(focus_file_info->path_history.size());
+         summary["rename_extensions_count"] = std::to_string(focus_file_info->rename_extensions.size());
+     }
+     
+     return summary;
+ }
  
  std::map<uint64_t, std::string> ProcessInfo::getFileOperationsSummary() const {
```

```diff
      
      return summary;
+ }
+ 
+ // FocusProcessFileInfo 方法实现
+ void FocusProcessFileInfo::addReadEntropy(double entropy) {
+     std::lock_guard<std::mutex> lock(mutex);
+     total_read_entropy += entropy;
+     read_entropy_count++;
+ }
+ 
+ void FocusProcessFileInfo::addWriteEntropy(double entropy) {
+     std::lock_guard<std::mutex> lock(mutex);
+     total_write_entropy += entropy;
+     write_entropy_count++;
+ }
+ 
+ void FocusProcessFileInfo::recordDelete(const std::string& path) {
+     std::lock_guard<std::mutex> lock(mutex);
+     delete_count++;
+     recordPathOperation(path);
+ }
+ 
+ void FocusProcessFileInfo::recordRename(const std::string& old_path, const std::string& new_path) {
+     std::lock_guard<std::mutex> lock(mutex);
+     rename_count++;
+     
+     // 提取并记录扩展名
+     size_t dot_pos = new_path.find_last_of('.');
+     if (dot_pos != std::string::npos && dot_pos < new_path.length() - 1) {
+         std::string extension = new_path.substr(dot_pos + 1);
+         
+         // 检查扩展名是否已存在
+         auto it = std::find(rename_extensions.begin(), rename_extensions.end(), extension);
+         if (it == rename_extensions.end()) {
+             rename_extensions.push_back(extension);
+             
+             // 限制扩展名列表大小
+             if (rename_extensions.size() > Constants::MAX_EXTENSION_LIST) {
+                 rename_extensions.erase(rename_extensions.begin());
+             }
+         }
+     }
+     
+     recordPathOperation(old_path);
+     recordPathOperation(new_path);
+ }
+ 
+ void FocusProcessFileInfo::recordPathOperation(const std::string& path) {
+     // 注意：此方法假设已经持有mutex锁
+     path_history.push_back(path);
+     
+     // 限制路径历史大小
+     if (path_history.size() > Constants::MAX_PATH_HISTORY) {
+         path_history.erase(path_history.begin());
+     }
+ }
+ 
+ double FocusProcessFileInfo::getAverageReadEntropy() const {
+     std::lock_guard<std::mutex> lock(mutex);
+     if (read_entropy_count == 0) {
+         return 0.0;
+     }
+     return total_read_entropy / read_entropy_count;
+ }
+ 
+ double FocusProcessFileInfo::getAverageWriteEntropy() const {
+     std::lock_guard<std::mutex> lock(mutex);
+     if (write_entropy_count == 0) {
+         return 0.0;
+     }
+     return total_write_entropy / write_entropy_count;
+ }
+ 
+ void FocusProcessFileInfo::cleanup() {
+     std::lock_guard<std::mutex> lock(mutex);
+     
+     // 清理过长的路径历史（保留最近的一半）
+     if (path_history.size() > Constants::MAX_PATH_HISTORY / 2) {
+         size_t keep_count = Constants::MAX_PATH_HISTORY / 2;
+         path_history.erase(path_history.begin(), 
+                           path_history.end() - keep_count);
+     }
+     
+     // 清理过长的扩展名列表（保留最近的一半）
+     if (rename_extensions.size() > Constants::MAX_EXTENSION_LIST / 2) {
+         size_t keep_count = Constants::MAX_EXTENSION_LIST / 2;
+         rename_extensions.erase(rename_extensions.begin(),
+                                rename_extensions.end() - keep_count);
+     }
  } 
```

---

现在修改文件监控的实现，移除Inotify支持并添加熵值计算和重点进程监控功能：

---

```diff
  #include "../include/process_exclusion.h"
  #include "../include/path_protection.h"
+ #include "../include/utils.h"
+ #include "../include/kernel_compat.h"
  #include <sys/stat.h>
  #include <sys/types.h>
```

```diff
  #include <linux/fanotify.h>
  #include <sys/utsname.h>
+ #include <cmath>
  
  // 如果系统头文件没有定义FAN_REPORT_TID，则手动定义
```

```diff
  #endif
  
- // FileMonitor 实现
- FileMonitor::FileMonitor() 
-     : kernel_compat_(KernelCompat::getInstance())
-     , fanotify_fd_(-1), fanotify_initialized_(false)
-     , inotify_fd_(-1), inotify_initialized_(false)
-     , running_(false), stop_requested_(false), config_(nullptr)
-     , total_events_(0), fanotify_events_(0), inotify_events_(0) {
-     
-     // 检测内核版本和功能支持
-     if (!kernel_compat_.detectKernelVersion()) {
-         logError("内核版本检测失败");
-         return;
+ // 日志管理器单例声明
+ extern class LogManager& getLogManager();
+ 
+ // FileEntropyCalculator 实现
+ double FileEntropyCalculator::calculateFileEntropy(const std::string& file_path) {
+     std::ifstream file(file_path, std::ios::binary);
+     if (!file.is_open()) {
+         return 0.0;
+     }
+     
+     // 读取文件内容到缓冲区
+     std::vector<char> buffer((std::istreambuf_iterator<char>(file)), 
+                             std::istreambuf_iterator<char>());
+     file.close();
+     
+     if (buffer.empty()) {
+         return 0.0;
+     }
+     
+     return calculateBufferEntropy(buffer.data(), buffer.size());
+ }
+ 
+ double FileEntropyCalculator::calculateBufferEntropy(const char* buffer, size_t size) {
+     if (size == 0) {
+         return 0.0;
      }
      
-     // 根据内核版本设置默认线程检测模式
-     if (kernel_compat_.supportsReportTid()) {
-         thread_detection_mode_ = ThreadDetectionMode::KERNEL_NATIVE;
-         logInfo("使用内核原生TID支持 (FAN_REPORT_TID)");
-     } else {
-         thread_detection_mode_ = ThreadDetectionMode::SYSCALL_TRACE_BASED;
-         logInfo("使用启发式线程识别");
-     }
- }
- 
- FileMonitor::~FileMonitor() {
-     stop();
+     // 计算每个字节的频率
+     std::vector<size_t> frequencies(256, 0);
+     for (size_t i = 0; i < size; ++i) {
+         frequencies[static_cast<unsigned char>(buffer[i])]++;
+     }
+     
+     return computeEntropy(frequencies, size);
  }
  
- bool FileMonitor::initialize(const MonitorConfig* config) {
-     config_ = config;
-     
-     // 检查内核支持
-     if (!checkKernelSupport()) {
-         logError("内核不支持所需的文件监控特性");
-         return false;
-     }
-     
-     // 根据内核版本选择推荐的监控策略
-     auto strategy = kernel_compat_.getRecommendedStrategy();
-     bool enable_inotify = (strategy != KernelCompat::MonitorStrategy::FANOTIFY_ONLY);
-     
-     // 输出策略选择信息到控制台
-     std::string strategy_name;
-     switch (strategy) {
-         case KernelCompat::MonitorStrategy::FANOTIFY_ONLY:
-             strategy_name = "Fanotify高性能模式";
-             break;
-         case KernelCompat::MonitorStrategy::FANOTIFY_WITH_INOTIFY:
-             strategy_name = "Fanotify+Inotify混合模式";
-             break;
-         case KernelCompat::MonitorStrategy::INOTIFY_FALLBACK:
-             strategy_name = "Inotify兼容模式";
-             break;
-         default:
-             strategy_name = "未知策略";
-             break;
-     }
-     
-     LogManager::getInstance().strategyInfo("选择文件监控策略: " + strategy_name);
-     LogManager::getInstance().monitorDebug("策略代码: " + std::to_string(static_cast<int>(strategy)));
- 
-     // 初始化 Fanotify
-     if (!initializeFanotify()) {
-         LogManager::getInstance().systemError("Fanotify 初始化失败");
-         
-         // 如果策略不是INOTIFY_FALLBACK，这是致命错误
-         if (strategy != KernelCompat::MonitorStrategy::INOTIFY_FALLBACK) {
-             return false;
-         } else {
-             LogManager::getInstance().strategyWarning("Fanotify失败，回退到纯Inotify模式");
-             enable_inotify = true;
+ double FileEntropyCalculator::computeEntropy(const std::vector<size_t>& frequencies, size_t total_bytes) {
+     double entropy = 0.0;
+     
+     for (size_t freq : frequencies) {
+         if (freq > 0) {
+             double probability = static_cast<double>(freq) / total_bytes;
+             entropy -= probability * std::log2(probability);
+         }
+     }
+     
+     return entropy;
+ }
+ 
+ // FocusProcessManager 实现
+ FocusProcessManager& FocusProcessManager::getInstance() {
+     static FocusProcessManager instance;
+     return instance;
+ }
+ 
+ void FocusProcessManager::addFocusProcess(pid_t pid) {
+     std::lock_guard<std::mutex> lock(mutex_);
+     focus_pids_.insert(pid);
+ }
+ 
+ void FocusProcessManager::addFocusProcess(const std::string& process_name) {
+     std::lock_guard<std::mutex> lock(mutex_);
+     focus_names_.insert(process_name);
+ }
+ 
+ void FocusProcessManager::removeFocusProcess(pid_t pid) {
+     std::lock_guard<std::mutex> lock(mutex_);
+     focus_pids_.erase(pid);
+ }
+ 
+ void FocusProcessManager::removeFocusProcess(const std::string& process_name) {
+     std::lock_guard<std::mutex> lock(mutex_);
+     focus_names_.erase(process_name);
+ }
+ 
+ bool FocusProcessManager::isFocusProcess(pid_t pid) const {
+     std::lock_guard<std::mutex> lock(mutex_);
+     return focus_pids_.find(pid) != focus_pids_.end();
+ }
+ 
+ bool FocusProcessManager::isFocusProcess(const std::string& process_name) const {
+     std::lock_guard<std::mutex> lock(mutex_);
+     return focus_names_.find(process_name) != focus_names_.end();
+ }
+ 
+ std::vector<pid_t> FocusProcessManager::getFocusProcesses() const {
+     std::lock_guard<std::mutex> lock(mutex_);
+     return std::vector<pid_t>(focus_pids_.begin(), focus_pids_.end());
+ }
+ 
+ void FocusProcessManager::clear() {
+     std::lock_guard<std::mutex> lock(mutex_);
+     focus_pids_.clear();
+     focus_names_.clear();
+ }
+ 
+ // SpecialPathManager 实现
+ SpecialPathManager& SpecialPathManager::getInstance() {
+     static SpecialPathManager instance;
+     return instance;
+ }
+ 
+ void SpecialPathManager::addSpecialPath(const std::string& path) {
+     std::lock_guard<std::mutex> lock(mutex_);
+     special_paths_.insert(path);
+ }
+ 
+ void SpecialPathManager::removeSpecialPath(const std::string& path) {
+     std::lock_guard<std::mutex> lock(mutex_);
+     special_paths_.erase(path);
+ }
+ 
+ bool SpecialPathManager::isSpecialPath(const std::string& path) const {
+     std::lock_guard<std::mutex> lock(mutex_);
+     
+     // 检查精确匹配
+     if (special_paths_.find(path) != special_paths_.end()) {
+         return true;
+     }
+     
+     // 检查路径前缀匹配
+     for (const auto& special_path : special_paths_) {
+         if (path.find(special_path) == 0) {
+             return true;
+         }
+     }
+     
+     return false;
+ }
+ 
+ void SpecialPathManager::handleViolation(const std::string& path, pid_t pid, const std::string& process_name) {
+     logError("特殊路径访问违规: 进程 " + std::to_string(pid) + " (" + process_name + 
+              ") 尝试访问受保护路径: " + path);
+     
+     if (terminate_violating_processes_) {
+         logWarning("中止违规进程 " + std::to_string(pid) + " (" + process_name + ")");
+         if (kill(pid, SIGTERM) != 0) {
+             logError("无法中止进程 " + std::to_string(pid) + ": " + std::string(strerror(errno)));
          }
-     } else {
-         LogManager::getInstance().monitorInfo("Fanotify初始化成功");
-     }
-     
-     // 初始化 Inotify（如果需要）
-     if (enable_inotify) {
-         if (!initializeInotify()) {
-             LogManager::getInstance().systemError("Inotify 初始化失败");
-             return false;
-         } else {
-             LogManager::getInstance().monitorInfo("Inotify初始化成功");
-         }
+     }
+ }
+ 
+ std::vector<std::string> SpecialPathManager::getSpecialPaths() const {
+     std::lock_guard<std::mutex> lock(mutex_);
+     return std::vector<std::string>(special_paths_.begin(), special_paths_.end());
+ }
+ 
+ void SpecialPathManager::clear() {
+     std::lock_guard<std::mutex> lock(mutex_);
+     special_paths_.clear();
+ }
+ 
+ // FileMonitor 实现
+ FileMonitor::FileMonitor() 
+     : kernel_compat_(KernelCompat::getInstance()),
+       fanotify_fd_(-1),
+       fanotify_initialized_(false),
+       thread_detection_mode_(ThreadDetectionMode::KERNEL_NATIVE),
+       running_(false),
+       stop_requested_(false),
+       config_(nullptr),
+       total_events_(0),
+       fanotify_events_(0) {
+ }
+ 
+ FileMonitor::~FileMonitor() {
+     stop();
+ }
+ 
+ bool FileMonitor::initialize(const MonitorConfig* config) {
+     config_ = config;
+     
+     // 检查内核兼容性
+     if (!kernel_compat_.initialize()) {
+         logError("内核兼容性检查失败");
+         return false;
+     }
+     
+     logInfo("内核版本: " + kernel_compat_.getKernelVersionString());
+     logInfo("推荐监控策略: " + 
+             (kernel_compat_.getRecommendedStrategy() == KernelCompat::MonitorStrategy::FANOTIFY_ONLY 
+              ? "纯Fanotify模式" : "混合模式"));
+     
+     // 初始化Fanotify
+     if (!initializeFanotify()) {
+         logError("Fanotify 初始化失败");
+         return false;
      } else {
-         LogManager::getInstance().monitorInfo("跳过Inotify初始化（使用纯Fanotify模式）");
+         logInfo("Fanotify初始化成功");
      }
      
-     logInfo("文件监控器初始化成功");
+     logInfo("文件监控器初始化成功（仅使用Fanotify模式）");
      return true;
  }
```

```diff
      return true;
  }
- 
- bool FileMonitor::initializeInotify() {
-     inotify_fd_ = inotify_init1(IN_CLOEXEC | IN_NONBLOCK);
-     if (inotify_fd_ == -1) {
-         logError("inotify_init1 失败: " + std::string(strerror(errno)));
-         return false;
-     }
-     
-     inotify_initialized_ = true;
-     logInfo("Inotify 初始化成功");
-     return true;
- }
  
  bool FileMonitor::start() {
```

```diff
          fanotify_thread_ = std::thread(&FileMonitor::fanotifyEventLoop, this);
      }
-     
-     // 启动 Inotify 监控线程
-     if (inotify_initialized_) {
-         inotify_thread_ = std::thread(&FileMonitor::inotifyEventLoop, this);
-     }
      
      logInfo("文件监控器已启动");
```

```diff
      // 关闭文件描述符以中断阻塞的读取
      closeFanotify();
-     closeInotify();
      
      // 等待线程结束
      if (fanotify_thread_.joinable()) {
          fanotify_thread_.join();
      }
-     
-     if (inotify_thread_.joinable()) {
-         inotify_thread_.join();
-     }
      
      if (event_processor_thread_.joinable()) {
```

```diff
      }
  }
- 
- void FileMonitor::closeInotify() {
-     if (inotify_fd_ != -1) {
-         close(inotify_fd_);
-         inotify_fd_ = -1;
-         inotify_initialized_ = false;
-     }
- }
  
  void FileMonitor::fanotifyEventLoop() {
```

```diff
      logInfo("Fanotify 事件循环已结束");
  }
- 
- void FileMonitor::inotifyEventLoop() {
-     const size_t buffer_size = 4096;
-     char buffer[buffer_size];
-     
-     logInfo("Inotify 事件循环已启动");
-     
-     while (running_ && !stop_requested_) {
-         struct pollfd pfd;
-         pfd.fd = inotify_fd_;
-         pfd.events = POLLIN;
-         
-         int poll_result = poll(&pfd, 1, 1000); // 1秒超时
-         
-         if (poll_result == -1) {
-             if (errno == EINTR) {
-                 continue;
-             }
-             // 检查是否是因为停止导致的文件描述符关闭
-             if (stop_requested_ || errno == EBADF) {
-                 logInfo("Inotify 监控停止");
-                 break;
-             }
-             logError("Inotify poll 失败: " + std::string(strerror(errno)));
-             break;
-         }
-         
-         if (poll_result == 0) {
-             continue;
-         }
-         
-         ssize_t len = read(inotify_fd_, buffer, buffer_size);
-         if (len == -1) {
-             if (errno == EAGAIN || errno == EWOULDBLOCK) {
-                 continue;
-             }
-             // 检查是否是因为停止导致的文件描述符关闭
-             if (stop_requested_ || errno == EBADF) {
-                 logInfo("Inotify 监控停止");
-                 break;
-             }
-             logError("Inotify read 失败: " + std::string(strerror(errno)));
-             break;
-         }
-         
-         // 处理 Inotify 事件
-         const struct inotify_event* inotify_event;
-         for (char* ptr = buffer; ptr < buffer + len; ) {
-             inotify_event = (const struct inotify_event*)ptr;
-             
-             // 创建文件事件
-             FileEvent event;
-             event.type = inotifyMaskToEventType(inotify_event->mask);
-             event.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
-                 std::chrono::system_clock::now().time_since_epoch()).count();
-             
-             // 获取文件路径
-             auto wd_it = watch_descriptors_.find(inotify_event->wd);
-             if (wd_it != watch_descriptors_.end()) {
-                 event.file_path = wd_it->second;
-                 if (inotify_event->len > 0) {
-                     event.file_path += "/" + std::string(inotify_event->name);
-                 }
-                 
-                 // 检查路径是否被排除（避免监控自身日志等文件）
-                 auto& exclusion_manager = ProcessExclusionManagerSingleton::getInstance();
-                 if (exclusion_manager.isExcludedByPath(event.file_path)) {
-                     // 跳过被排除的路径
-                     ptr += sizeof(struct inotify_event) + inotify_event->len;
-                     continue;
-                 }
-                 
-                 // 生成文件ID
-                 struct stat file_stat;
-                 if (stat(event.file_path.c_str(), &file_stat) == 0) {
-                     event.file_id = generateFileId(event.file_path, 
-                                                   file_stat.st_dev, 
-                                                   file_stat.st_ino);
-                 }
-             }
-             
-             // Inotify 无法直接提供进程信息，设为0
-             event.pid = 0;
-             event.tid = 0;
-             event.uid = 0;
-             event.gid = 0;
-             
-             // 检查是否为重复事件
-             if (!isDuplicateEvent(event)) {
-                 {
-                     std::lock_guard<std::mutex> lock(event_queue_mutex_);
-                     event_queue_.push(event);
-                 }
-                 event_condition_.notify_one();
-                 
-                 inotify_events_++;
-                 total_events_++;
-                 
-                 addRecentEvent(event);
-             }
-             
-             ptr += sizeof(struct inotify_event) + inotify_event->len;
-         }
-     }
-     
-     logInfo("Inotify 事件循环已结束");
- }
  
  void FileMonitor::processEvents() {
```

```diff
          return true; // 已经在监控
      }
-     
-     // Fanotify 已经监控整个文件系统，这里主要是为 Inotify 添加监控
-     if (inotify_initialized_ && recursive) {
-         if (!addInotifyWatch(path, recursive)) {
-             logError("添加 Inotify 监控失败: " + path);
-             return false;
-         }
-     }
      
      monitored_paths_.insert(path);
      logInfo("添加监控路径: " + path);
      return true;
  }
- 
- bool FileMonitor::addInotifyWatch(const std::string& path, bool recursive) {
-     int wd = inotify_add_watch(inotify_fd_, path.c_str(), InotifyEvents::EVENTS);
-     if (wd == -1) {
-         logError("inotify_add_watch 失败: " + path + " - " + std::string(strerror(errno)));
-         return false;
-     }
-     
-     watch_descriptors_[wd] = path;
-     
-     // 递归添加子目录
-     if (recursive && isDirectory(path)) {
-         addDirectoryRecursive(path);
-     }
-     
-     return true;
- }
- 
- void FileMonitor::addDirectoryRecursive(const std::string& path) {
-     auto subdirs = listDirectory(path);
-     for (const auto& subdir : subdirs) {
-         std::string full_path = path + "/" + subdir;
-         if (isDirectory(full_path)) {
-             addInotifyWatch(full_path, true);
-         }
-     }
- }
  
  bool FileMonitor::removeWatchPath(const std::string& path) {
```

```diff
          return false;
      }
-     
-     // 移除 Inotify 监控
-     removeInotifyWatch(path);
      
      monitored_paths_.erase(it);
      logInfo("移除监控路径: " + path);
      return true;
  }
- 
- void FileMonitor::removeInotifyWatch(const std::string& path) {
-     for (auto it = watch_descriptors_.begin(); it != watch_descriptors_.end(); ) {
-         if (it->second == path || it->second.find(path + "/") == 0) {
-             inotify_rm_watch(inotify_fd_, it->first);
-             it = watch_descriptors_.erase(it);
-         } else {
-             ++it;
-         }
-     }
- }
  
  void FileMonitor::setEventCallback(std::function<void(const FileEvent&)> callback) {
```

---

让我继续修改文件监控实现，添加重点进程文件监控和特殊路径处理：

---



---



---

我理解您的项目需求，这是一个Linux平台安全监控程序的重大重构项目。从您的总结来看，主要变更包括：

1. **移除Inotify机制，仅使用Fanotify**
2. **停止线程级文件操作管理** 
3. **新增重点进程监控和文件熵值计算**
4. **添加特殊路径保护功能**

让我先查看当前项目的状态，然后继续完成剩余的实现工作。

---

<details>
            <summary>Listed current directory • **23** results</summary>
        
| Name |
|-------|
| 📁 `refdocs` |
| 📁 `obj` |
| 📁 `.git` |
| 📁 `.idea` |
| 📁 `include` |
| 📁 `src` |
| 📁 `cmake-build-debug` |
| 📄 `CMakeLists.txt` |
| 📄 `Makefile` |
| 📄 `DEBUG_QUICK_START.md` |
| 📁 `docs` |
| 📄 `.gdbinit` |
| 📁 `scripts` |
| 📄 `.gitignore` |
| 📁 `test` |
| 📁 `ref_code` |
| 📄 `PROJECT_REVIEW_SUMMARY.md` |
| 📄 `README.md` |
| 📄 `REQUIREMENTS.md` |
| 📄 `API_REFERENCE.md` |
| 📁 `.specstory` |
| 📁 `config` |
| 📄 `.cursorindexingignore` |

</details>

---

<details>
            <summary>Listed directory src • **11** results</summary>
        
| Name |
|-------|
| 📄 `file_monitor.cpp` |
| 📄 `psfsmon_core.cpp` |
| 📄 `kernel_compat.cpp` |
| 📄 `main.cpp` |
| 📄 `main_monitor.cpp` |
| 📄 `process_monitor.cpp` |
| 📄 `api_server.cpp` |
| 📄 `network_monitor.cpp` |
| 📄 `path_protection.cpp` |
| 📄 `process_exclusion.cpp` |
| 📄 `utils.cpp` |

</details>

---

<details>
            <summary>Listed directory include • **9** results</summary>
        
| Name |
|-------|
| 📄 `process_monitor.h` |
| 📄 `file_monitor.h` |
| 📄 `psfsmon.h` |
| 📄 `kernel_compat.h` |
| 📄 `main_monitor.h` |
| 📄 `api_server.h` |
| 📄 `network_monitor.h` |
| 📄 `process_exclusion.h` |
| 📄 `path_protection.h` |

</details>

---

让我查看关键的头文件，了解已完成的重构进展：

---

Read file: include/psfsmon.h

---

Read file: include/file_monitor.h

---

Read file: include/file_monitor.h

---

现在让我查看当前的file_monitor.cpp实现进展：

---

Read file: src/file_monitor.cpp

---

我看到当前的file_monitor.cpp文件很大，让我检查一下它的结构，看看是否还包含了Inotify的代码，这需要被移除：

---

<details>
            <summary>Grep search for "inotify" • **14** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///home/<USER>/coder/psfsmonL/src/main_monitor.cpp` | L589 | `file << "# - 内核 5.4.x：  使用Fanotify+Inotify混合模式" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/src/main_monitor.cpp` | L590 | `file << "# - 内核 4.19.x： 使用Fanotify+Inotify基础模式" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/src/kernel_compat.cpp` | L97 | `bool KernelCompat::needsInotifySupport() const {` |
| `file:///home/<USER>/coder/psfsmonL/src/kernel_compat.cpp` | L98 | `// 如果Fanotify不支持CREATE/DELETE/MOVE事件，需要Inotify补充` |
| `file:///home/<USER>/coder/psfsmonL/src/kernel_compat.cpp` | L110 | `// 5.4 和 4.19 需要Fanotify+Inotify混合模式` |
| `file:///home/<USER>/coder/psfsmonL/src/kernel_compat.cpp` | L111 | `return MonitorStrategy::FANOTIFY_WITH_INOTIFY;` |
| `file:///home/<USER>/coder/psfsmonL/src/kernel_compat.cpp` | L114 | `// 极老内核回退到Inotify` |
| `file:///home/<USER>/coder/psfsmonL/src/kernel_compat.cpp` | L115 | `return MonitorStrategy::INOTIFY_FALLBACK;` |
| `file:///home/<USER>/coder/psfsmonL/src/main.cpp` | L72 | `std::cout << "  - 内核 5.4.x：  使用Fanotify+Inotify混合模式" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/src/main.cpp` | L73 | `std::cout << "  - 内核 4.19.x： 使用Fanotify+Inotify基础模式" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/src/api_server.cpp` | L716 | `.addNumber("inotify_events", static_cast<uint64_t>(file_monitor->getInotifyEvents()))` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L11 | `*    - Fanotify + Inotify 混合模式` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L13 | `*    - Inotify补充处理CREATE/DELETE/MOVE事件` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L17 | `*    - Fanotify + Inotify 混合模式` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L1183 | `// 这里返回false，移动检测将依赖Inotify` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L1191 | `// 这里返回false，移动检测将依赖Inotify` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L1196 | `FileEvent::EventType FileMonitor::inotifyMaskToEventType(uint32_t mask) {` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L1275 | `// 检查 Inotify 支持` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L1276 | `fd = inotify_init();` |
| `file:///home/<USER>/coder/psfsmonL/test/test_kernel_compat.cpp` | L57 | `case KernelCompat::MonitorStrategy::FANOTIFY_WITH_INOTIFY:` |
| `file:///home/<USER>/coder/psfsmonL/test/test_kernel_compat.cpp` | L58 | `std::cout << "Fanotify + Inotify 混合模式" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_kernel_compat.cpp` | L60 | `case KernelCompat::MonitorStrategy::INOTIFY_FALLBACK:` |
| `file:///home/<USER>/coder/psfsmonL/test/test_kernel_compat.cpp` | L61 | `std::cout << "主要使用Inotify (兼容模式)" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_kernel_compat.cpp` | L65 | `std::cout << "\n🔍 是否需要Inotify支持: " << (kernel_compat.needsInotifySupport() ? "是" : "否") << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_kernel_compat.cpp` | L87 | `std::cout << "✅ 需要Inotify: " << (monitor.needsInotifySupport() ? "是" : "否") << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_thread_detection.cpp` | L49 | `case KernelCompat::MonitorStrategy::FANOTIFY_WITH_INOTIFY:` |
| `file:///home/<USER>/coder/psfsmonL/test/test_thread_detection.cpp` | L50 | `std::cout << "Fanotify+Inotify混合模式" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_thread_detection.cpp` | L51 | `std::cout << "   • Fanotify处理主要事件，Inotify补充不支持的功能" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_thread_detection.cpp` | L53 | `case KernelCompat::MonitorStrategy::INOTIFY_FALLBACK:` |
| `file:///home/<USER>/coder/psfsmonL/test/test_thread_detection.cpp` | L54 | `std::cout << "Inotify兼容模式" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_thread_detection.cpp` | L55 | `std::cout << "   • 使用Inotify作为主要监控方式" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_low_kernel_monitoring.cpp` | L10 | `* 测试重点：验证在Fanotify功能受限、Inotify的进程线程获取能力有限的情况下，` |
| `file:///home/<USER>/coder/psfsmonL/test/test_low_kernel_monitoring.cpp` | L71 | `int inotify_events = 0;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_low_kernel_monitoring.cpp` | L158 | `case KernelCompat::MonitorStrategy::FANOTIFY_WITH_INOTIFY:` |
| `file:///home/<USER>/coder/psfsmonL/test/test_low_kernel_monitoring.cpp` | L159 | `std::cout << "Fanotify+Inotify混合模式" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_low_kernel_monitoring.cpp` | L161 | `case KernelCompat::MonitorStrategy::INOTIFY_FALLBACK:` |
| `file:///home/<USER>/coder/psfsmonL/test/test_low_kernel_monitoring.cpp` | L162 | `std::cout << "Inotify兼容模式" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_low_kernel_monitoring.cpp` | L178 | `std::cout << "⚠️  警告: 当前内核Fanotify功能受限，需要Inotify补充" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_low_kernel_monitoring.cpp` | L572 | `case KernelCompat::MonitorStrategy::FANOTIFY_WITH_INOTIFY:` |
| `file:///home/<USER>/coder/psfsmonL/test/test_low_kernel_monitoring.cpp` | L573 | `std::cout << "Fanotify+Inotify混合模式" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_low_kernel_monitoring.cpp` | L575 | `case KernelCompat::MonitorStrategy::INOTIFY_FALLBACK:` |
| `file:///home/<USER>/coder/psfsmonL/test/test_low_kernel_monitoring.cpp` | L576 | `std::cout << "Inotify兼容模式" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_low_kernel_monitoring.cpp` | L642 | `std::cout << "\n⚠️  注意: 当前内核Fanotify功能受限，部分操作依赖Inotify" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_log_system.cpp` | L115 | `case KernelCompat::MonitorStrategy::FANOTIFY_WITH_INOTIFY:` |
| `file:///home/<USER>/coder/psfsmonL/test/test_log_system.cpp` | L116 | `strategy_name = "Fanotify+Inotify混合模式";` |
| `file:///home/<USER>/coder/psfsmonL/test/test_log_system.cpp` | L118 | `case KernelCompat::MonitorStrategy::INOTIFY_FALLBACK:` |
| `file:///home/<USER>/coder/psfsmonL/test/test_log_system.cpp` | L119 | `strategy_name = "Inotify兼容模式";` |
| `file:///home/<USER>/coder/psfsmonL/test/test_monitoring_exclusion_demo.cpp` | L177 | `case KernelCompat::MonitorStrategy::FANOTIFY_WITH_INOTIFY:` |
| `file:///home/<USER>/coder/psfsmonL/test/test_monitoring_exclusion_demo.cpp` | L178 | `strategy_name = "Fanotify+Inotify混合模式";` |
| `file:///home/<USER>/coder/psfsmonL/test/test_monitoring_exclusion_demo.cpp` | L180 | `case KernelCompat::MonitorStrategy::INOTIFY_FALLBACK:` |
| `file:///home/<USER>/coder/psfsmonL/test/test_monitoring_exclusion_demo.cpp` | L181 | `strategy_name = "Inotify兼容模式";` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L46 | `bool needs_inotify_fallback = false;     // 是否需要inotify回退` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L64 | `uint64_t inotify_events = 0;            // Inotify事件数` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L137 | `std::cout << "   Inotify回退: " << (config_.needs_inotify_fallback ? "是" : "否") << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L163 | `uint64_t initial_inotify_events_ = 0;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L202 | `config_.needs_inotify_fallback = true;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L203 | `std::cout << "⚠️  较老内核版本，可能需要inotify回退支持" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L262 | `return getFileStats(initial_file_events_, initial_fanotify_events_, initial_inotify_events_);` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L265 | `bool getFileStats(uint64_t& total_events, uint64_t& fanotify_events, uint64_t& inotify_events) {` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L287 | `inotify_events = stats_json.value("inotify_events", 0ULL);` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L302 | `std::cout << "   初始Inotify事件: " << initial_inotify_events_ << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L393 | `uint64_t final_total_events, final_fanotify_events, final_inotify_events;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L394 | `if (!getFileStats(final_total_events, final_fanotify_events, final_inotify_events)) {` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L402 | `stats_.inotify_events = final_inotify_events - initial_inotify_events_;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L429 | `std::cout << "   Inotify回退需求: " << (config_.needs_inotify_fallback ? "是" : "否") << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L446 | `std::cout << "   Inotify捕获事件: " << stats_.inotify_events << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L476 | `if (stats_.fanotify_events > stats_.inotify_events) {` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L479 | `std::cout << "   • Inotify使用较多，可能需要优化Fanotify配置" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L498 | `<< "fanotify_events,inotify_events,execution_rate,capture_efficiency,test_duration_ms" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/file_monitor_efficiency_test.cpp` | L505 | `<< stats_.inotify_events << ","` |
| `file:///home/<USER>/coder/psfsmonL/test/final_capture_test.cpp` | L544 | `std::cout << "   - Inotify只能监控文件系统事件，无法提供进程信息" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/final_capture_test.cpp` | L621 | `std::cout << "   💡 技术: 使用fanotify（能提供进程PID），而非inotify" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/test/test_self_exclusion.cpp` | L118 | `case KernelCompat::MonitorStrategy::FANOTIFY_WITH_INOTIFY:` |
| `file:///home/<USER>/coder/psfsmonL/test/test_self_exclusion.cpp` | L119 | `std::cout << "Fanotify+Inotify混合模式";` |
| `file:///home/<USER>/coder/psfsmonL/test/test_self_exclusion.cpp` | L121 | `case KernelCompat::MonitorStrategy::INOTIFY_FALLBACK:` |
| `file:///home/<USER>/coder/psfsmonL/test/test_self_exclusion.cpp` | L122 | `std::cout << "Inotify兼容模式";` |
| `file:///home/<USER>/coder/psfsmonL/test/test_self_exclusion.cpp` | L159 | `std::cout << "  3. 在inotify事件中也可以通过路径过滤实现自身排除" << std::endl;` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L11 | `*    - Fanotify + Inotify 混合模式` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L13 | `*    - Inotify补充处理CREATE/DELETE/MOVE事件` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L17 | `*    - Fanotify + Inotify 混合模式` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L1183 | `// 这里返回false，移动检测将依赖Inotify` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L1191 | `// 这里返回false，移动检测将依赖Inotify` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L1196 | `FileEvent::EventType FileMonitor::inotifyMaskToEventType(uint32_t mask) {` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L1275 | `// 检查 Inotify 支持` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L1276 | `fd = inotify_init();` |

</details>

---

现在我需要检查file_monitor.cpp文件，看看需要移除哪些Inotify相关的代码，并且需要补充重点进程和特殊路径处理的方法：

---

Read file: src/file_monitor.cpp

---

现在让我检查file_monitor.cpp的完整结构，看看还需要添加哪些方法：

---

<details>
            <summary>Grep search for "handleFocusProcessFileEvent|handleSpecialPathAccess" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L611 | `void FileMonitor::handleFocusProcessFileEvent(const FileEvent& event) {` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L664 | `void FileMonitor::handleSpecialPathAccess(const FileEvent& event) {` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L611 | `void FileMonitor::handleFocusProcessFileEvent(const FileEvent& event) {` |
| `file:///home/<USER>/coder/psfsmonL/src/file_monitor.cpp` | L664 | `void FileMonitor::handleSpecialPathAccess(const FileEvent& event) {` |

</details>

---

让我查看这两个方法的实现：

---

Read file: src/file_monitor.cpp

---





---

