<!-- Generated by SpecStory -->

# 打印程序调用的url和json结果 (2025-06-09 12:48:49)

_**User**_

 测试程序默认使用--no-start方式启动。它将调用的ｕｒｌ和返回的ｊｓｏｎ结果，要打印输出出来，方便我们细节的验证。

---

_**Assistant**_



---

我需要先查看当前工作空间的结构，了解要修改的程序。

---

<details>
            <summary>Listed current directory • **22** results</summary>
        
| Name |
|-------|
| 📁 `test` |
| 📁 `obj` |
| 📁 `bin` |
| 📁 `.git` |
| 📁 `build` |
| 📄 `CMakeLists.txt` |
| 📄 `README.md` |
| 📁 `scripts` |
| 📄 `DESIGN.md` |
| 📄 `Makefile` |
| 📁 `src` |
| 📁 `config` |
| 📁 `ref_docs` |
| 📄 `REQUIREMENTS.md` |
| 📁 `.idea` |
| 📁 `include` |
| 📁 `cmake-build-debug` |
| 📄 `.gdbinit` |
| 📄 `.gitignore` |
| 📁 `ref_code` |
| 📁 `.specstory` |
| 📄 `.cursorindexingignore` |

</details>

---

现在让我查看源代码目录和测试目录，了解程序的结构：

---

<details>
            <summary>Listed directory src • **12** results</summary>
        
| Name |
|-------|
| 📄 `main_monitor.cpp` |
| 📄 `main.cpp` |
| 📄 `file_monitor.cpp` |
| 📄 `api_server.cpp` |
| 📄 `http_server.cpp` |
| 📄 `psfsmon_core.cpp` |
| 📄 `process_monitor.cpp` |
| 📄 `kernel_compat.cpp` |
| 📄 `network_monitor.cpp` |
| 📄 `path_protection.cpp` |
| 📄 `process_exclusion.cpp` |
| 📄 `utils.cpp` |

</details>

---

<details>
            <summary>Listed directory test • **4** results</summary>
        
| Name |
|-------|
| 📄 `test_api_comprehensive` |
| 📄 `test_api_comprehensive.cpp` |
| 📄 `Makefile` |
| 📄 `run_api_tests.sh` |

</details>

---

让我查看测试程序的源代码，了解当前的启动方式：

---

Read file: test/test_api_comprehensive.cpp

---

现在让我查看main函数部分，了解当前的启动参数处理：

---

Read file: test/test_api_comprehensive.cpp

---

现在让我查看`makeHttpRequest`方法和测试方法，看看是否已经有URL和JSON结果的输出：

---

Read file: test/test_api_comprehensive.cpp

---

现在我了解了程序结构。我需要修改代码来满足用户的要求：

1. 默认使用 `--no-start` 方式启动
2. 打印输出调用的URL和当前的JSON结果，方便细节验证

---

```diff
          
          std::cout << "测试: " << test_name << " ... ";
+         std::cout << std::endl;
          
          try {
+             std::string full_url = base_url_ + endpoint;
+             std::cout << "  调用URL: " << method << " " << full_url << std::endl;
+             if (!data.empty()) {
+                 std::cout << "  请求数据: " << data << std::endl;
+             }
+             
              auto response = makeHttpRequest(method, endpoint, data);
+             
+             std::cout << "  响应状态: HTTP " << response.response_code << " (" << response.total_time << "s)" << std::endl;
+             std::cout << "  响应JSON: " << response.body << std::endl;
              
              // 检查HTTP状态码
              if (response.response_code < 200 || response.response_code >= 300) {
                  if (response.response_code == 404 || response.response_code == 501) {
                      // API未实现或不存在，跳过
-                     std::cout << "跳过 (HTTP " << response.response_code << ")" << std::endl;
+                     std::cout << "  结果: 跳过 (HTTP " << response.response_code << ")" << std::endl;
                      stats_.skipped_tests++;
                      return;
```

```diff
              // 执行自定义验证
              if (validator(root)) {
-                 std::cout << "通过 (" << response.total_time << "s)" << std::endl;
+                 std::cout << "  结果: 通过" << std::endl;
                  stats_.passed_tests++;
              } else {
```

```diff
              
          } catch (const std::exception& e) {
-             std::cout << "失败 (" << e.what() << ")" << std::endl;
+             std::cout << "  结果: 失败 (" << e.what() << ")" << std::endl;
              stats_.failed_tests++;
              stats_.failed_test_names.push_back(test_name);
              stats_.error_messages.push_back(e.what());
          }
+         
+         std::cout << std::endl;
      }
      
```

---

现在我需要修改`runApiTestAllowError`方法以保持一致的输出格式：

---

Read file: test/test_api_comprehensive.cpp

---

现在我需要修改`runApiTestAllowError`方法：

---

```diff
          
          std::cout << "测试: " << test_name << " ... ";
+         std::cout << std::endl;
          
          try {
+             std::string full_url = base_url_ + endpoint;
+             std::cout << "  调用URL: " << method << " " << full_url << std::endl;
+             if (!data.empty()) {
+                 std::cout << "  请求数据: " << data << std::endl;
+             }
+             
              auto response = makeHttpRequest(method, endpoint, data);
+             
+             std::cout << "  响应状态: HTTP " << response.response_code << " (" << response.total_time << "s)" << std::endl;
+             std::cout << "  响应JSON: " << response.body << std::endl;
              
              // 检查HTTP状态码 - 允许4xx错误
              if (response.response_code < 200 || response.response_code >= 500) {
                  if (response.response_code == 404 || response.response_code == 501) {
                      // API未实现或不存在，跳过
-                     std::cout << "跳过 (HTTP " << response.response_code << ")" << std::endl;
+                     std::cout << "  结果: 跳过 (HTTP " << response.response_code << ")" << std::endl;
                      stats_.skipped_tests++;
                      return;
```

```diff
              // 执行自定义验证
              if (validator(root)) {
-                 std::cout << "通过 (" << response.total_time << "s)" << std::endl;
+                 std::cout << "  结果: 通过" << std::endl;
                  stats_.passed_tests++;
              } else {
```

```diff
              
          } catch (const std::exception& e) {
-             std::cout << "失败 (" << e.what() << ")" << std::endl;
+             std::cout << "  结果: 失败 (" << e.what() << ")" << std::endl;
              stats_.failed_tests++;
              stats_.failed_test_names.push_back(test_name);
              stats_.error_messages.push_back(e.what());
          }
+         
+         std::cout << std::endl;
      }
      
```

---

现在我需要修改main函数，将默认启动方式设置为`--no-start`：

---

Read file: test/test_api_comprehensive.cpp

---

现在我需要修改main函数，将默认启动方式改为`--no-start`：

---

```diff
      std::string psfsmon_binary = "../bin/psfsmon";
      std::string api_base_url = "http://127.0.0.1:8080";
-     bool start_service = true;
+     bool start_service = false;  // 默认不启动服务，使用--no-start方式
      
      // 解析命令行参数
```

```diff
          } else if (arg == "--url" && i + 1 < argc) {
              api_base_url = argv[++i];
+         } else if (arg == "--start") {
+             start_service = true;
          } else if (arg == "--no-start") {
              start_service = false;
```

```diff
              std::cout << "  --binary <path>   指定psfsmon二进制文件路径 (默认: ../bin/psfsmon)" << std::endl;
              std::cout << "  --url <url>       指定API基础URL (默认: http://127.0.0.1:8080)" << std::endl;
-             std::cout << "  --no-start        不自动启动服务，假设服务已运行" << std::endl;
+             std::cout << "  --start           自动启动服务" << std::endl;
+             std::cout << "  --no-start        不自动启动服务，假设服务已运行 (默认)" << std::endl;
              std::cout << "  --help            显示此帮助信息" << std::endl;
              return 0;
```

```diff
          
          std::cout << "psfsmon服务已启动 (PID: " << service_pid << ")" << std::endl;
+     } else {
+         std::cout << "使用--no-start模式，假设服务已运行于: " << api_base_url << std::endl;
      }
      
```

---

现在我还需要修改`preflightCheck`方法，让它也显示URL和JSON响应的详细信息：

---

Read file: test/test_api_comprehensive.cpp

---

现在修改`preflightCheck`方法，让它也显示详细的URL和JSON响应信息：

---

```diff
          std::cout << "--- 预检查服务可用性 ---" << std::endl;
          
-         auto response = makeHttpRequest("GET", "/api/health", "");
+         std::string endpoint = "/api/health";
+         std::string full_url = base_url_ + endpoint;
+         std::cout << "调用URL: GET " << full_url << std::endl;
+         
+         auto response = makeHttpRequest("GET", endpoint, "");
+         
+         std::cout << "响应状态: HTTP " << response.response_code << " (" << response.total_time << "s)" << std::endl;
+         std::cout << "响应JSON: " << response.body << std::endl;
          
          if (response.response_code != 200) {
```

---

完成了所有修改。现在让我编译测试程序以验证修改是否正确：

---

```bash
cd /home/<USER>/coder/psfsmonL/test && make test_api_comprehensive
```

```
编译API综合测试程序: test_api_comprehensive
g++ -std=c++11 -Wall -Wextra -pthread -g -I../include `pkg-config --cflags jsonc
pp` test_api_comprehensi
ve.cpp -o test_api_comprehensive -lcurl `pkg-config --libs jsoncpp` -lrt -ldl
                        
```

---

编译成功！现在让我们查看修改后的帮助信息，确认默认启动方式已经改为`--no-start`：

---

```bash
./test_api_comprehensive --help
```

```
helloub22@helloub22-virtual-machine:~/coder/psfsmonL/test$ ./test_api_comprehensive --help
用法: ./test_api_comprehensive [选项]
选项:
  --binary <path>   指定psfsmon二进制文件路径 (默认: ../bin/psfsmon)
  --url <url>       指定API基础URL (默认: http://127.0.0.1:8080)
  --start           自动启动服务
  --no-start        不自动启动服务，假设服务已运行 (默认)
  --help            显示此帮助信息
helloub22@helloub22-virtual-machine:~/coder/psfsmonL/test$
```

---

完美！现在我已经成功完成了用户的要求：

## 修改总结

我已经成功修改了`test_api_comprehensive.cpp`测试程序，实现了以下功能：

### 1. 默认使用`--no-start`方式启动
- 将`start_service`的默认值改为`false`
- 添加了`--start`参数来显式启动服务
- 修改了帮助信息，明确显示"不自动启动服务，假设服务已运行 (默认)"

### 2. 打印调用的URL和返回的JSON结果

对所有API测试方法进行了详细输出增强：

#### `runApiTest`方法：
- 显示调用的完整URL (`调用URL: [METHOD] [FULL_URL]`)
- 显示请求数据 (如果有的话)
- 显示响应状态码和响应时间 (`响应状态: HTTP [CODE] ([TIME]s)`)
- 显示完整的JSON响应内容 (`响应JSON: [JSON_CONTENT]`)
- 显示最终测试结果 (`结果: 通过/失败/跳过`)

#### `runApiTestAllowError`方法：
- 同样的详细输出格式，保持一致性

#### `preflightCheck`方法：
- 也添加了URL和JSON响应的详细输出

### 3. 输出格式示例

现在程序运行时会显示类似这样的详细信息：
```
测试: 健康检查API ... 

  调用URL: GET http://127.0.0.1:8080/api/health
  响应状态: HTTP 200 (0.005s)
  响应JSON: {"status":"ok","service":"psfsmon","timestamp":**********}
  结果: 通过

```

这样的详细输出使得开发者能够：
- 清楚地看到每个API调用的具体URL
- 验证返回的JSON数据格式和内容
- 快速定位问题所在
- 方便进行调试和验证

程序现在默认以`--no-start`模式运行，假设服务已经在运行，这更适合在已有服务环境中进行测试。

---

_**User**_

　测试程序在输出ｊｓｏｎ数据时，要格式化输出，方便我们阅读。

---

_**Assistant**_



---

我需要修改测试程序，让JSON输出格式化显示，方便阅读。我将使用jsoncpp库的格式化功能来美化JSON输出。

---

```diff
      }
  
+     // JSON格式化输出辅助函数
+     std::string formatJsonOutput(const std::string& json_str) {
+         Json::Value root;
+         Json::Reader reader;
+         if (reader.parse(json_str, root)) {
+             Json::StreamWriterBuilder builder;
+             builder["indentation"] = "  ";  // 使用2个空格缩进
+             builder["commentStyle"] = "None";
+             builder["enableYAMLCompatibility"] = false;
+             builder["dropNullPlaceholders"] = false;
+             builder["useSpecialFloats"] = false;
+             builder["precision"] = 6;
+             
+             std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());
+             std::ostringstream formatted;
+             writer->write(root, &formatted);
+             return formatted.str();
+         } else {
+             // 如果JSON解析失败，返回原始字符串
+             return json_str;
+         }
+     }
+ 
  public:
      ApiTestFramework(const std::string& base_url) 
```

---

现在修改`preflightCheck`方法使用格式化的JSON输出：

---

```diff
          
          std::cout << "响应状态: HTTP " << response.response_code << " (" << response.total_time << "s)" << std::endl;
-         std::cout << "响应JSON: " << response.body << std::endl;
+         std::cout << "响应JSON:" << std::endl;
+         std::cout << formatJsonOutput(response.body) << std::endl;
          
          if (response.response_code != 200) {
```

---

现在修改`runApiTest`方法使用格式化的JSON输出：

---



---

让我查看当前的runApiTest方法并进行修改：

---

Read file: test/test_api_comprehensive.cpp

---

我看到了有两个runApiTest方法。让我使用search_replace来修改正确的runApiTest方法：

---



---

我需要分别修改这两个方法。先修改第一个runApiTestAllowError方法：

---

Read file: test/test_api_comprehensive.cpp

---

先修改`runApiTestAllowError`方法：

---



---

我需要使用更具体的上下文来区分两个方法。让我先修改`runApiTestAllowError`方法：

---



---

现在修改`runApiTest`方法：

---



---

现在需要添加必要的头文件来支持`std::ostringstream`：

---

Read file: test/test_api_comprehensive.cpp

---

好的，头文件已经包含了`<sstream>`，所以应该可以编译。现在让我们编译测试一下：

---

```bash
make test_api_comprehensive
```

```
编译API综合测试程序: test_api_comprehensive
g++ -std=c++11 -Wall -Wextra -pthread -g -I../include `pkg-config --cflags jsoncpp` test_api_comprehensive.cpp -o test_api_co
mprehensive -lcurl `pkg-config --libs jsoncpp` -lrt -ldl
helloub22@helloub22-virtual-machine:~/coder/psfsmonL/test$
```

---

编译成功！现在让我们测试一下JSON格式化功能是否正常工作。为了测试，我将创建一个简单的测试程序来验证JSON格式化：

---

```diff
- 
+ #include <iostream>
+ #include <string>
+ #include <sstream>
+ #include <jsoncpp/json/json.h>
+ 
+ std::string formatJsonOutput(const std::string& json_str) {
+     Json::Value root;
+     Json::Reader reader;
+     if (reader.parse(json_str, root)) {
+         Json::StreamWriterBuilder builder;
+         builder["indentation"] = "  ";  // 使用2个空格缩进
+         builder["commentStyle"] = "None";
+         builder["enableYAMLCompatibility"] = false;
+         builder["dropNullPlaceholders"] = false;
+         builder["useSpecialFloats"] = false;
+         builder["precision"] = 6;
+         
+         std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());
+         std::ostringstream formatted;
+         writer->write(root, &formatted);
+         return formatted.str();
+     } else {
+         // 如果JSON解析失败，返回原始字符串
+         return json_str;
+     }
+ }
+ 
+ int main() {
+     std::string test_json = R"({"status":"ok","service":"psfsmon","timestamp":**********,"processes":[{"pid":1,"name":"init"},{"pid":2,"name":"kthreadd"}]})";
+     
+     std::cout << "原始JSON:" << std::endl;
+     std::cout << test_json << std::endl;
+     
+     std::cout << std::endl << "格式化后JSON:" << std::endl;
+     std::cout << formatJsonOutput(test_json) << std::endl;
+     
+     return 0;
+ } 
```

---

```bash
g++ -std=c++11 `pkg-config --cflags jsoncpp` test_json_format.cpp -o test_json_format `pkg-config --libs jsoncpp` && ./test_json_format
```

```
helloub22@helloub22-virtual-machine:~/coder/psfsmonL/test$ g++ -std=c++11 `pkg-config --cflags jsoncpp` test_json_format.cpp 
-o test_json_format `pkg-config --libs jsoncpp` && ./test_jg++ -std=c++11 `pkg-config --cflags jsoncpp` test_json_format.cpp 
-o test_json_format `pkg-config --libs jsoncpp` && ./test_json_format
原始JSON:
{"status":"ok","service":"psfsmon","timestamp":**********,"processes":[{"pid":1,"name":"init"},{"pid":2,"name":"kthreadd"}]}

格式化后JSON:
{
  "processes" : 
  [
    {
      "name" : "init",
      "pid" : 1
    },
    {
      "name" : "kthreadd",
      "pid" : 2
    }
  ],
  "service" : "psfsmon",
  "status" : "ok",
  "timestamp" : **********
}
helloub22@helloub22-virtual-machine:~/coder/psfsmonL/test$
```

---

完美！JSON格式化功能正常工作。现在让我清理测试文件：

---

删除临时测试文件

---

```bash
rm -f test_json_format
```

```
helloub22@helloub22-virtual-machine:~/coder/psfsmonL/test$ 

```

---

## 修改完成总结

我已经成功为测试程序添加了JSON格式化输出功能！现在测试程序会以格式化的方式显示JSON数据，方便阅读。

### 实现的功能：

1. **添加了JSON格式化函数**：
   - 使用jsoncpp库的`StreamWriterBuilder`来格式化JSON
   - 设置了2个空格的缩进，便于阅读
   - 如果JSON解析失败，会返回原始字符串

2. **修改了所有JSON输出位置**：
   - `preflightCheck`方法：格式化显示健康检查的JSON响应
   - `runApiTest`方法：格式化显示请求数据和响应JSON
   - `runApiTestAllowError`方法：格式化显示请求数据和响应JSON

3. **输出格式优化**：
   - 请求数据和响应JSON都使用单独的行显示标题
   - JSON内容按照结构化格式显示，具有清晰的缩进

### 输出效果示例：

**之前的输出（单行）：**
```
响应JSON: {"status":"ok","service":"psfsmon","timestamp":**********}
```

**现在的输出（格式化）：**
```
响应JSON:
{
  "service" : "psfsmon",
  "status" : "ok",
  "timestamp" : **********
}
```

这样的格式化输出使得：
- JSON数据结构更加清晰
- 嵌套的对象和数组更容易识别
- 便于开发者快速定位问题
- 适合复杂的JSON响应数据阅读

程序现在保持了之前的所有功能（默认`--no-start`模式、详细的URL输出等），并增加了JSON格式化显示，大大提升了用户体验和调试便利性。

---

_**User**_

　新建一个测试脚本，完全以ｃｕｒｌ的方式，实现测试程序的所有测试功能，方便我们手动进行测试时参考。

---

_**Assistant**_



---

我需要先查看现有测试程序中的所有API测试，然后创建一个完整的curl测试脚本。

---

Read file: test/test_api_comprehensive.cpp

---

现在我创建一个完整的curl测试脚本，包含所有的API测试：

---

```diff
- 
+ #!/bin/bash
+ 
+ # PSFSMON-L API综合测试脚本 (使用curl)
+ # 本脚本用于手动测试psfsmon项目对外提供的所有API接口
+ 
+ # 默认配置
+ BASE_URL="http://127.0.0.1:8080"
+ TIMEOUT=30
+ VERBOSE=false
+ RUN_ALL=true
+ 
+ # 颜色定义
+ RED='\033[0;31m'
+ GREEN='\033[0;32m'
+ YELLOW='\033[1;33m'
+ BLUE='\033[0;34m'
+ NC='\033[0m' # No Color
+ 
+ # 帮助信息
+ show_help() {
+     echo "用法: $0 [选项] [测试类别]"
+     echo ""
+     echo "选项:"
+     echo "  --url <url>       指定API基础URL (默认: http://127.0.0.1:8080)"
+     echo "  --timeout <sec>   设置请求超时时间 (默认: 30秒)"
+     echo "  --verbose         显示详细的curl输出"
+     echo "  --help            显示此帮助信息"
+     echo ""
+     echo "测试类别:"
+     echo "  health            健康检查和状态API测试"
+     echo "  process           进程相关API测试"
+     echo "  network           网络相关API测试"
+     echo "  system            系统统计API测试"
+     echo "  config            配置管理API测试"
+     echo "  management        进程管理API测试"
+     echo "  path              路径管理API测试"
+     echo "  file              文件监控API测试"
+     echo "  realtime          实时数据API测试"
+     echo "  all               运行所有测试 (默认)"
+     echo ""
+     echo "示例:"
+     echo "  $0                                    # 运行所有测试"
+     echo "  $0 health                             # 只运行健康检查测试"
+     echo "  $0 --url http://localhost:9090 all   # 使用不同的URL运行所有测试"
+ }
+ 
+ # JSON格式化函数
+ format_json() {
+     if command -v jq >/dev/null 2>&1; then
+         echo "$1" | jq '.'
+     elif command -v python3 >/dev/null 2>&1; then
+         echo "$1" | python3 -m json.tool 2>/dev/null || echo "$1"
+     else
+         echo "$1"
+     fi
+ }
+ 
+ # 执行API测试
+ run_api_test() {
+     local test_name="$1"
+     local method="$2"
+     local endpoint="$3"
+     local data="$4"
+     local expect_success="${5:-true}"
+     
+     echo -e "${BLUE}测试: $test_name${NC}"
+     echo "  调用URL: $method $BASE_URL$endpoint"
+     
+     if [ -n "$data" ]; then
+         echo "  请求数据:"
+         format_json "$data"
+     fi
+     
+     # 构建curl命令
+     local curl_cmd="curl -s"
+     
+     if [ "$VERBOSE" = true ]; then
+         curl_cmd="$curl_cmd -v"
+     fi
+     
+     curl_cmd="$curl_cmd -w '\n响应状态: HTTP %{http_code} (%{time_total}s)\n'"
+     curl_cmd="$curl_cmd --connect-timeout 10 --max-time $TIMEOUT"
+     
+     case "$method" in
+         "GET")
+             curl_cmd="$curl_cmd -X GET"
+             ;;
+         "POST")
+             curl_cmd="$curl_cmd -X POST"
+             if [ -n "$data" ]; then
+                 curl_cmd="$curl_cmd -H 'Content-Type: application/json' -d '$data'"
+             fi
+             ;;
+         "PUT")
+             curl_cmd="$curl_cmd -X PUT"
+             if [ -n "$data" ]; then
+                 curl_cmd="$curl_cmd -H 'Content-Type: application/json' -d '$data'"
+             fi
+             ;;
+         "DELETE")
+             curl_cmd="$curl_cmd -X DELETE"
+             if [ -n "$data" ]; then
+                 curl_cmd="$curl_cmd -H 'Content-Type: application/json' -d '$data'"
+             fi
+             ;;
+     esac
+     
+     curl_cmd="$curl_cmd '$BASE_URL$endpoint'"
+     
+     # 执行curl命令
+     echo "  响应JSON:"
+     local response
+     response=$(eval "$curl_cmd" 2>&1)
+     
+     # 解析响应状态码
+     local status_line=$(echo "$response" | grep "响应状态: HTTP")
+     local json_response=$(echo "$response" | grep -v "响应状态: HTTP")
+     
+     # 格式化并显示JSON响应
+     format_json "$json_response"
+     
+     # 显示状态信息
+     echo "$status_line"
+     
+     # 检查是否成功
+     local http_code=$(echo "$status_line" | grep -o 'HTTP [0-9]*' | grep -o '[0-9]*')
+     
+     if [ "$expect_success" = true ]; then
+         if [ "$http_code" -ge 200 ] && [ "$http_code" -lt 300 ]; then
+             echo -e "  结果: ${GREEN}通过${NC}"
+         elif [ "$http_code" = 404 ] || [ "$http_code" = 501 ]; then
+             echo -e "  结果: ${YELLOW}跳过 (HTTP $http_code)${NC}"
+         else
+             echo -e "  结果: ${RED}失败 (HTTP $http_code)${NC}"
+         fi
+     else
+         if [ "$http_code" -ge 200 ] && [ "$http_code" -lt 500 ]; then
+             echo -e "  结果: ${GREEN}通过${NC}"
+         elif [ "$http_code" = 404 ] || [ "$http_code" = 501 ]; then
+             echo -e "  结果: ${YELLOW}跳过 (HTTP $http_code)${NC}"
+         else
+             echo -e "  结果: ${RED}失败 (HTTP $http_code)${NC}"
+         fi
+     fi
+     
+     echo ""
+ }
+ 
+ # 预检查服务可用性
+ preflight_check() {
+     echo -e "${BLUE}--- 预检查服务可用性 ---${NC}"
+     
+     echo "调用URL: GET $BASE_URL/api/health"
+     
+     local response
+     response=$(curl -s -w '\n响应状态: HTTP %{http_code} (%{time_total}s)\n' \
+                --connect-timeout 10 --max-time $TIMEOUT \
+                "$BASE_URL/api/health" 2>&1)
+     
+     local status_line=$(echo "$response" | grep "响应状态: HTTP")
+     local json_response=$(echo "$response" | grep -v "响应状态: HTTP")
+     
+     echo "响应JSON:"
+     format_json "$json_response"
+     echo "$status_line"
+     
+     local http_code=$(echo "$status_line" | grep -o 'HTTP [0-9]*' | grep -o '[0-9]*')
+     
+     if [ "$http_code" = 200 ]; then
+         echo -e "${GREEN}✓ 服务正常运行${NC}"
+         echo ""
+         return 0
+     else
+         echo -e "${RED}✗ 服务不可用 (HTTP $http_code)${NC}"
+         echo ""
+         return 1
+     fi
+ }
+ 
+ # 健康检查和状态API测试
+ test_health_and_status() {
+     echo -e "${BLUE}--- 健康检查和状态API测试 ---${NC}"
+     
+     run_api_test "健康检查API" "GET" "/api/health" ""
+     run_api_test "系统统计API" "GET" "/api/stats" ""
+     run_api_test "监控状态API" "GET" "/api/monitor/status" ""
+ }
+ 
+ # 进程相关API测试
+ test_process_apis() {
+     echo -e "${BLUE}--- 进程相关API测试 ---${NC}"
+     
+     run_api_test "获取所有进程API" "GET" "/api/processes" ""
+     run_api_test "获取进程列表API" "GET" "/api/processes-list" ""
+     run_api_test "获取进程树API" "GET" "/api/process-tree" ""
+     run_api_test "获取单个进程信息API" "GET" "/api/process?pid=1" ""
+     run_api_test "获取进程文件操作API" "GET" "/api/process/files?pid=1" ""
+ }
+ 
+ # 网络相关API测试
+ test_network_apis() {
+     echo -e "${BLUE}--- 网络相关API测试 ---${NC}"
+     
+     run_api_test "获取网络连接API" "GET" "/api/network" ""
+     run_api_test "获取网络统计API" "GET" "/api/network/stats" ""
+     run_api_test "获取监听端口API" "GET" "/api/network/listening" ""
+ }
+ 
+ # 系统统计API测试
+ test_system_stats_apis() {
+     echo -e "${BLUE}--- 系统统计API测试 ---${NC}"
+     
+     run_api_test "系统统计详细API" "GET" "/api/stats" ""
+ }
+ 
+ # 配置管理API测试
+ test_config_apis() {
+     echo -e "${BLUE}--- 配置管理API测试 ---${NC}"
+     
+     run_api_test "获取配置API" "GET" "/api/config" ""
+     
+     local test_config='{
+         "log_level": "INFO",
+         "enable_file_monitoring": true,
+         "monitor_all_interfaces": true
+     }'
+     
+     run_api_test "更新配置API" "PUT" "/api/config" "$test_config"
+ }
+ 
+ # 进程管理API测试
+ test_process_management_apis() {
+     echo -e "${BLUE}--- 进程管理API测试 ---${NC}"
+     
+     run_api_test "获取排除进程列表API" "GET" "/api/exclusions" ""
+     
+     local add_exclusion='{"type": "name", "value": "test_process"}'
+     run_api_test "添加排除进程API" "POST" "/api/exclusions" "$add_exclusion"
+     
+     run_api_test "删除排除进程API" "DELETE" "/api/exclusions?type=name&value=test_process" ""
+     
+     run_api_test "获取重点进程列表API" "GET" "/api/focus-processes" ""
+     
+     local add_focus='{"type": "name", "value": "important_process"}'
+     run_api_test "添加重点进程API" "POST" "/api/focus-processes" "$add_focus"
+     
+     run_api_test "获取重点进程文件信息API" "GET" "/api/focus-process/file-info?pid=1" "" false
+     
+     run_api_test "删除重点进程API" "DELETE" "/api/focus-processes?type=name&value=important_process" ""
+ }
+ 
+ # 路径管理API测试
+ test_path_management_apis() {
+     echo -e "${BLUE}--- 路径管理API测试 ---${NC}"
+     
+     run_api_test "获取受保护目录列表API" "GET" "/api/protected-paths" ""
+     
+     local add_protected='{"path": "/tmp/test_protected"}'
+     run_api_test "添加受保护目录API" "POST" "/api/protected-paths" "$add_protected"
+     
+     run_api_test "删除受保护目录API" "DELETE" "/api/protected-paths?path=/tmp/test_protected" ""
+ }
+ 
+ # 文件监控API测试
+ test_file_monitoring_apis() {
+     echo -e "${BLUE}--- 文件监控API测试 ---${NC}"
+     
+     run_api_test "获取文件事件API" "GET" "/api/file/events" ""
+     run_api_test "获取文件统计API" "GET" "/api/file/stats" ""
+ }
+ 
+ # 实时数据API测试
+ test_realtime_apis() {
+     echo -e "${BLUE}--- 实时数据API测试 ---${NC}"
+     
+     run_api_test "获取实时数据API" "GET" "/api/realtime" ""
+ }
+ 
+ # 解析命令行参数
+ while [[ $# -gt 0 ]]; do
+     case $1 in
+         --url)
+             BASE_URL="$2"
+             shift 2
+             ;;
+         --timeout)
+             TIMEOUT="$2"
+             shift 2
+             ;;
+         --verbose)
+             VERBOSE=true
+             shift
+             ;;
+         --help)
+             show_help
+             exit 0
+             ;;
+         health|process|network|system|config|management|path|file|realtime)
+             RUN_ALL=false
+             TEST_CATEGORY="$1"
+             shift
+             ;;
+         all)
+             RUN_ALL=true
+             shift
+             ;;
+         *)
+             echo "未知选项: $1"
+             show_help
+             exit 1
+             ;;
+     esac
+ done
+ 
+ # 主执行逻辑
+ echo -e "${GREEN}=== PSFSMON-L API综合测试脚本 (使用curl) ===${NC}"
+ echo "测试基础URL: $BASE_URL"
+ echo "请求超时: ${TIMEOUT}秒"
+ echo ""
+ 
+ # 预检查
+ if ! preflight_check; then
+     echo -e "${RED}预检查失败，测试终止${NC}"
+     exit 1
+ fi
+ 
+ # 根据参数执行测试
+ if [ "$RUN_ALL" = true ]; then
+     test_health_and_status
+     test_process_apis
+     test_network_apis
+     test_system_stats_apis
+     test_config_apis
+     test_process_management_apis
+     test_path_management_apis
+     test_file_monitoring_apis
+     test_realtime_apis
+ else
+     case "$TEST_CATEGORY" in
+         health)
+             test_health_and_status
+             ;;
+         process)
+             test_process_apis
+             ;;
+         network)
+             test_network_apis
+             ;;
+         system)
+             test_system_stats_apis
+             ;;
+         config)
+             test_config_apis
+             ;;
+         management)
+             test_process_management_apis
+             ;;
+         path)
+             test_path_management_apis
+             ;;
+         file)
+             test_file_monitoring_apis
+             ;;
+         realtime)
+             test_realtime_apis
+             ;;
+         *)
+             echo -e "${RED}无效的测试类别: $TEST_CATEGORY${NC}"
+             show_help
+             exit 1
+             ;;
+     esac
+ fi
+ 
+ echo -e "${GREEN}=== 测试完成 ===${NC}" 
```

---

现在给脚本添加执行权限并测试一下帮助功能：

---

```bash
chmod +x test_api_with_curl.sh
```

```
helloub22@helloub22-virtual-machine:~/coder/psfsmonL/test$ 

```

---

```bash
./test_api_with_curl.sh --help
```

```
helloub22@helloub22-virtual-machine:~/coder/psfsmonL/test$ ./test_api_with_curl.sh --help
用法: ./test_api_with_curl.sh [选项] [测试类别]

选项:
  --url <url>       指定API基础URL (默认: http://127.0.0.1:8080)
  --timeout <sec>   设置请求超时时间 (默认: 30秒)
  --verbose         显示详细的curl输出
  --help            显示此帮助信息

测试类别:
  health            健康检查和状态API测试
  process           进程相关API测试
  network           网络相关API测试
  system            系统统计API测试
  config            配置管理API测试
  management        进程管理API测试
  path              路径管理API测试
  file              文件监控API测试
  realtime          实时数据API测试
  all               运行所有测试 (默认)

示例:
  ./test_api_with_curl.sh                                    # 运行所有测试
  ./test_api_with_curl.sh health                             # 只运行健康检查测试
  ./test_api_with_curl.sh --url http://localhost:9090 all   # 使用不同的URL运行所有测试
helloub22@helloub22-virtual-machine:~/coder/psfsmonL/test$
```

---

现在让我创建一个简单的使用示例和文档：

---

```diff
- 
+ # PSFSMON-L API测试脚本使用指南
+ 
+ 本文档介绍如何使用 `test_api_with_curl.sh` 脚本进行API测试。
+ 
+ ## 概述
+ 
+ `test_api_with_curl.sh` 是一个完全基于curl的API测试脚本，实现了与C++测试程序相同的所有测试功能。该脚本方便手动测试和调试API接口。
+ 
+ ## 功能特性
+ 
+ - **完整的API覆盖**: 包含所有API接口测试
+ - **JSON格式化输出**: 自动格式化JSON响应，便于阅读
+ - **颜色输出**: 使用颜色区分测试结果
+ - **灵活的测试选项**: 可以运行全部测试或特定类别的测试
+ - **详细的调试信息**: 显示完整的URL、请求数据和响应信息
+ 
+ ## 基本用法
+ 
+ ### 1. 运行所有测试
+ ```bash
+ ./test_api_with_curl.sh
+ ```
+ 
+ ### 2. 运行特定类别的测试
+ ```bash
+ # 只测试健康检查相关API
+ ./test_api_with_curl.sh health
+ 
+ # 只测试进程相关API
+ ./test_api_with_curl.sh process
+ 
+ # 只测试网络相关API
+ ./test_api_with_curl.sh network
+ ```
+ 
+ ### 3. 使用不同的API服务地址
+ ```bash
+ # 测试运行在不同端口的服务
+ ./test_api_with_curl.sh --url http://localhost:9090
+ 
+ # 测试远程服务
+ ./test_api_with_curl.sh --url http://*************:8080
+ ```
+ 
+ ### 4. 调试模式
+ ```bash
+ # 显示详细的curl调试信息
+ ./test_api_with_curl.sh --verbose health
+ ```
+ 
+ ### 5. 自定义超时时间
+ ```bash
+ # 设置60秒超时
+ ./test_api_with_curl.sh --timeout 60
+ ```
+ 
+ ## 测试类别详解
+ 
+ ### health - 健康检查和状态API
+ - `/api/health` - 服务健康检查
+ - `/api/stats` - 系统统计信息
+ - `/api/monitor/status` - 监控状态
+ 
+ ### process - 进程相关API
+ - `/api/processes` - 获取所有进程
+ - `/api/processes-list` - 获取进程列表
+ - `/api/process-tree` - 获取进程树
+ - `/api/process?pid=1` - 获取单个进程信息
+ - `/api/process/files?pid=1` - 获取进程文件操作
+ 
+ ### network - 网络相关API
+ - `/api/network` - 获取网络连接
+ - `/api/network/stats` - 获取网络统计
+ - `/api/network/listening` - 获取监听端口
+ 
+ ### system - 系统统计API
+ - `/api/stats` - 系统统计详细信息
+ 
+ ### config - 配置管理API
+ - `/api/config` - 获取配置 (GET)
+ - `/api/config` - 更新配置 (PUT)
+ 
+ ### management - 进程管理API
+ - `/api/exclusions` - 排除进程管理
+ - `/api/focus-processes` - 重点进程管理
+ - `/api/focus-process/file-info` - 重点进程文件信息
+ 
+ ### path - 路径管理API
+ - `/api/protected-paths` - 受保护目录管理
+ 
+ ### file - 文件监控API
+ - `/api/file/events` - 文件事件
+ - `/api/file/stats` - 文件统计
+ 
+ ### realtime - 实时数据API
+ - `/api/realtime` - 实时监控数据
+ 
+ ## 输出格式
+ 
+ 脚本的输出格式与C++测试程序保持一致：
+ 
+ ```
+ 测试: 健康检查API
+   调用URL: GET http://127.0.0.1:8080/api/health
+   响应JSON:
+   {
+     "service": "psfsmon",
+     "status": "ok",
+     "timestamp": **********
+   }
+   响应状态: HTTP 200 (0.005s)
+   结果: 通过
+ ```
+ 
+ ## 依赖要求
+ 
+ 脚本会自动检测并使用以下工具进行JSON格式化：
+ 1. `jq` (推荐) - 提供最佳的JSON格式化效果
+ 2. `python3` - 备选方案，使用json.tool模块
+ 3. 原始输出 - 如果以上工具都不可用
+ 
+ ## 手动测试示例
+ 
+ 你也可以使用脚本中的curl命令进行手动测试：
+ 
+ ### 健康检查
+ ```bash
+ curl -s -w '\n响应状态: HTTP %{http_code} (%{time_total}s)\n' \
+      --connect-timeout 10 --max-time 30 \
+      'http://127.0.0.1:8080/api/health' | jq '.'
+ ```
+ 
+ ### 获取进程列表
+ ```bash
+ curl -s -w '\n响应状态: HTTP %{http_code} (%{time_total}s)\n' \
+      --connect-timeout 10 --max-time 30 \
+      'http://127.0.0.1:8080/api/processes' | jq '.'
+ ```
+ 
+ ### 更新配置
+ ```bash
+ curl -s -X PUT \
+      -H 'Content-Type: application/json' \
+      -d '{"log_level": "DEBUG", "enable_file_monitoring": true}' \
+      -w '\n响应状态: HTTP %{http_code} (%{time_total}s)\n' \
+      --connect-timeout 10 --max-time 30 \
+      'http://127.0.0.1:8080/api/config' | jq '.'
+ ```
+ 
+ ## 故障排除
+ 
+ ### 1. 连接失败
+ - 确保psfsmon服务正在运行
+ - 检查端口是否正确 (默认8080)
+ - 使用 `--verbose` 选项查看详细的curl输出
+ 
+ ### 2. 超时问题
+ - 使用 `--timeout` 选项增加超时时间
+ - 检查网络连接
+ 
+ ### 3. JSON格式化问题
+ - 安装 `jq` 工具: `sudo apt-get install jq`
+ - 或确保系统有 `python3`
+ 
+ ### 4. 权限问题
+ - 确保脚本有执行权限: `chmod +x test_api_with_curl.sh`
+ 
+ ## 与C++测试程序的对比
+ 
+ | 特性 | C++测试程序 | curl脚本 |
+ |------|-------------|----------|
+ | 完整性 | ✓ 所有API | ✓ 所有API |
+ | JSON格式化 | ✓ jsoncpp | ✓ jq/python |
+ | 详细输出 | ✓ | ✓ |
+ | 启动服务 | ✓ 可选 | ✗ 需手动 |
+ | 依赖 | C++, jsoncpp, curl | bash, curl |
+ | 便携性 | 需编译 | 即用 |
+ | 调试便利性 | 一般 | ✓ 很好 |
+ 
+ ## 小结
+ 
+ `test_api_with_curl.sh` 脚本提供了一个轻量级、易于使用的API测试解决方案，特别适合：
+ - 快速验证API功能
+ - 调试API问题
+ - 学习API使用方法
+ - 在没有编译环境的情况下进行测试 
```

---

让我测试一个简单的curl命令来验证脚本是否能正常工作：

---

```bash
./test_api_with_curl.sh health
```

```
  ./test_api_with_curl.sh                                    # 运行所有测试
  ./test_api_with_curl.sh health                             # 只运行健康检查测试
  ./test_api_with_curl.sh --url http://localhost:9090 all   # 使用不同的URL运行所有测试
helloub22@helloub22-virtual-machine:~/coder/psfsmonL/test$ ./test_api_with_curl.sh health
=== PSFSMON-L API综合测试脚本 (使用curl) ===
测试基础URL: http://127.0.0.1:8080
请求超时: 30秒

--- 预检查服务可用性 ---
调用URL: GET http://127.0.0.1:8080/api/health
响应JSON:
{
  "status": "ok",
  "service": "psfsmon",
  "timestamp": **********
}
响应状态: HTTP 200 (0.001545s)
✓ 服务正常运行

--- 健康检查和状态API测试 ---
测试: 健康检查API
  调用URL: GET http://127.0.0.1:8080/api/health
  响应JSON:
{
  "status": "ok",
  "service": "psfsmon",
  "timestamp": **********
}
响应状态: HTTP 200 (0.000585s)
  结果: 通过

测试: 系统统计API
  调用URL: GET http://127.0.0.1:8080/api/stats
  响应JSON:
{
  "total_processes": 326,
  "total_file_events": 124,
  "total_network_events": 10780,
  "uptime_seconds": 1601,
  "start_time": **********
}
响应状态: HTTP 200 (0.000438s)
  结果: 通过

测试: 监控状态API
  调用URL: GET http://127.0.0.1:8080/api/monitor/status
  响应JSON:
{
  "status": "running",
  "uptime_seconds": 1601,
  "file_monitor_enabled": true,
  "network_monitor_enabled": true,
  "process_monitor_enabled": true
}
响应状态: HTTP 200 (0.000205s)
  结果: 通过

=== 测试完成 ===
helloub22@helloub22-virtual-machine:~/coder/psfsmonL/test$
```

---

太好了！看起来psfsmon服务正在运行，我们的curl脚本也工作得很好。让我再测试一个更复杂的测试类别：

---

```bash
./test_api_with_curl.sh process
```

```
helloub22@helloub22-virtual-machine:~/coder/psfsmonL/test$ 
      "parent_name": "kthreadd",
      "name": "kworker/R-scsi_",
      "state": "UNKNOWN",
      "cpu_usage": 0,
      "memory_usage": 0,
      "virtual_memory": 0,
      "executable_path": "",
      "command_line": "",
      "cwd": "/",
      "uid": 0,
      "gid": 0,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 770,
      "ppid": 1,
      "parent_name": "systemd",
      "name": "systemd-oomd",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 6784,
      "virtual_memory": 14836,
      "executable_path": "/usr/lib/systemd/systemd-oomd",
      "command_line": "/lib/systemd/systemd-oomd",
      "cwd": "/",
      "uid": 108,
      "gid": 116,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 1910,
      "ppid": 1901,
      "parent_name": "systemd",
      "name": "pulseaudio",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 28136,
      "virtual_memory": 1500804,
      "executable_path": "/usr/bin/pulseaudio",
      "command_line": "/usr/bin/pulseaudio --daemonize=no --log-target=journal",
      "cwd": "/",
      "uid": 1000,
      "gid": 1000,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 828,
      "ppid": 1,
      "parent_name": "systemd",
      "name": "acpid",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 2048,
      "virtual_memory": 2816,
      "executable_path": "/usr/sbin/acpid",
      "command_line": "/usr/sbin/acpid",
      "cwd": "/",
      "uid": 0,
      "gid": 0,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 1031,
      "ppid": 1,
      "parent_name": "systemd",
      "name": "unattended-upgr",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 23296,
      "virtual_memory": 121276,
      "executable_path": "/usr/bin/python3.10",
      "command_line": "/usr/bin/python3 /usr/share/unattended-upgrades/unattended-upgrade-shutdo
wn --wait-for-signal",
      "cwd": "/",
      "uid": 0,
      "gid": 0,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 2113,
      "ppid": 1901,
      "parent_name": "systemd",
      "name": "goa-daemon",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 41088,
      "virtual_memory": 600532,
      "executable_path": "/usr/libexec/goa-daemon",
      "command_line": "/usr/libexec/goa-daemon",
      "cwd": "/home/<USER>",
      "uid": 1000,
      "gid": 1000,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 880,
      "ppid": 835,
      "parent_name": "avahi-daemon",
      "name": "avahi-daemon",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 1300,
      "virtual_memory": 7444,
      "executable_path": "/usr/sbin/avahi-daemon",
      "command_line": "avahi-daemon: chroot helper",
      "cwd": "/",
      "uid": 114,
      "gid": 121,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 1908,
      "ppid": 1901,
      "parent_name": "systemd",
      "name": "pipewire",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 6016,
      "virtual_memory": 42524,
      "executable_path": "/usr/bin/pipewire",
      "command_line": "/usr/bin/pipewire",
      "cwd": "/home/<USER>",
      "uid": 1000,
      "gid": 1000,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 2651,
      "ppid": 1901,
      "parent_name": "systemd",
      "name": "gnome-terminal-",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 80660,
      "virtual_memory": 671472,
      "executable_path": "/usr/libexec/gnome-terminal-server",
      "command_line": "/usr/libexec/gnome-terminal-server",
      "cwd": "/home/<USER>",
      "uid": 1000,
      "gid": 1000,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 1028,
      "ppid": 1,
      "parent_name": "systemd",
      "name": "cupsd",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 13184,
      "virtual_memory": 75848,
      "executable_path": "/usr/sbin/cupsd",
      "command_line": "/usr/sbin/cupsd -l",
      "cwd": "/",
      "uid": 0,
      "gid": 0,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 1411,
      "ppid": 1,
      "parent_name": "systemd",
      "name": "upowerd",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 8320,
      "virtual_memory": 245316,
      "executable_path": "/usr/libexec/upowerd",
      "command_line": "/usr/libexec/upowerd",
      "cwd": "/",
      "uid": 0,
      "gid": 0,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 1417,
      "ppid": 1,
      "parent_name": "systemd",
      "name": "packagekitd",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 20556,
      "virtual_memory": 301652,
      "executable_path": "/usr/libexec/packagekitd",
      "command_line": "/usr/libexec/packagekitd",
      "cwd": "/",
      "uid": 0,
      "gid": 0,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 1573,
      "ppid": 1,
      "parent_name": "systemd",
      "name": "colord",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 12860,
      "virtual_memory": 248556,
      "executable_path": "/usr/libexec/colord",
      "command_line": "/usr/libexec/colord",
      "cwd": "/",
      "uid": 122,
      "gid": 129,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 1924,
      "ppid": 1901,
      "parent_name": "systemd",
      "name": "dbus-daemon",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 5496,
      "virtual_memory": 9668,
      "executable_path": "/usr/bin/dbus-daemon",
      "command_line": "/usr/bin/dbus-daemon --session --address=systemd: --nofork --nopidfile --
systemd-activation --syslog-only",
      "cwd": "/home/<USER>",
      "uid": 1000,
      "gid": 1000,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 1927,
      "ppid": 1,
      "parent_name": "systemd",
      "name": "gnome-keyring-d",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 7960,
      "virtual_memory": 465376,
      "executable_path": "/usr/bin/gnome-keyring-daemon",
      "command_line": "/usr/bin/gnome-keyring-daemon --daemonize --login",
      "cwd": "/",
      "uid": 1000,
      "gid": 1000,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 1960,
      "ppid": 1957,
      "parent_name": "gdm-wayland-ses",
      "name": "gnome-session-b",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 15616,
      "virtual_memory": 226300,
      "executable_path": "/usr/libexec/gnome-session-binary",
      "command_line": "/usr/libexec/gnome-session-binary --session=ubuntu",
      "cwd": "/home/<USER>",
      "uid": 1000,
      "gid": 1000,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 2067,
      "ppid": 1901,
      "parent_name": "systemd",
      "name": "gvfsd",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 8064,
      "virtual_memory": 244188,
      "executable_path": "/usr/libexec/gvfsd",
      "command_line": "/usr/libexec/gvfsd",
      "cwd": "/home/<USER>",
      "uid": 1000,
      "gid": 1000,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 2104,
      "ppid": 1901,
      "parent_name": "systemd",
      "name": "gvfs-udisks2-vo",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 10752,
      "virtual_memory": 319308,
      "executable_path": "/usr/libexec/gvfs-udisks2-volume-monitor",
      "command_line": "/usr/libexec/gvfs-udisks2-volume-monitor",
      "cwd": "/home/<USER>",
      "uid": 1000,
      "gid": 1000,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 5,
      "ppid": 2,
      "parent_name": "kthreadd",
      "name": "kworker/R-rcu_p",
      "state": "UNKNOWN",
      "cpu_usage": 0,
      "memory_usage": 0,
      "virtual_memory": 0,
      "executable_path": "",
      "command_line": "",
      "cwd": "/",
      "uid": 0,
      "gid": 0,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 2169,
      "ppid": 2067,
      "parent_name": "gvfsd",
      "name": "gvfsd-trash",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 9344,
      "virtual_memory": 392156,
      "executable_path": "/usr/libexec/gvfsd-trash",
      "command_line": "/usr/libexec/gvfsd-trash --spawner :1.25 /org/gtk/gvfs/exec_spaw/0",
      "cwd": "/home/<USER>",
      "uid": 1000,
      "gid": 1000,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 13,
      "ppid": 2,
      "parent_name": "kthreadd",
      "name": "rcu_tasks_kthread",
      "state": "UNKNOWN",
      "cpu_usage": 0,
      "memory_usage": 0,
      "virtual_memory": 0,
      "executable_path": "",
      "command_line": "",
      "cwd": "/",
      "uid": 0,
      "gid": 0,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 2177,
      "ppid": 1901,
      "parent_name": "systemd",
      "name": "at-spi2-registr",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 7552,
      "virtual_memory": 162688,
      "executable_path": "/usr/libexec/at-spi2-registryd",
      "command_line": "/usr/libexec/at-spi2-registryd --use-gnome-session",
      "cwd": "/home/<USER>",
      "uid": 1000,
      "gid": 1000,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 14,
      "ppid": 2,
      "parent_name": "kthreadd",
      "name": "rcu_tasks_rude_kthread",
      "state": "UNKNOWN",
      "cpu_usage": 0,
      "memory_usage": 0,
      "virtual_memory": 0,
      "executable_path": "",
      "command_line": "",
      "cwd": "/",
      "uid": 0,
      "gid": 0,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 2178,
      "ppid": 1901,
      "parent_name": "systemd",
      "name": "gjs",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 27688,
      "virtual_memory": 2598788,
      "executable_path": "/usr/bin/gjs-console",
      "command_line": "/usr/bin/gjs /usr/share/gnome-shell/org.gnome.Shell.Notifications",
      "cwd": "/home/<USER>",
      "uid": 1000,
      "gid": 1000,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 2205,
      "ppid": 1901,
      "parent_name": "systemd",
      "name": "sh",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 1664,
      "virtual_memory": 2892,
      "executable_path": "/usr/bin/dash",
      "command_line": "sh -c /usr/bin/ibus-daemon --panel disable $([ \"$XDG_SESSION_TYPE\" = \"
x11\" ] && echo \"--xim\")",
      "cwd": "/home/<USER>",
      "uid": 1000,
      "gid": 1000,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 2207,
      "ppid": 2205,
      "parent_name": "sh",
      "name": "ibus-daemon",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 13888,
      "virtual_memory": 392680,
      "executable_path": "/usr/bin/ibus-daemon",
      "command_line": "/usr/bin/ibus-daemon --panel disable",
      "cwd": "/home/<USER>",
      "uid": 1000,
      "gid": 1000,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 56,
      "ppid": 2,
      "parent_name": "kthreadd",
      "name": "irq/9-acpi",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 0,
      "virtual_memory": 0,
      "executable_path": "",
      "command_line": "",
      "cwd": "/",
      "uid": 0,
      "gid": 0,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 2220,
      "ppid": 1901,
      "parent_name": "systemd",
      "name": "gsd-datetime",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 16640,
      "virtual_memory": 378876,
      "executable_path": "/usr/libexec/gsd-datetime",
      "command_line": "/usr/libexec/gsd-datetime",
      "cwd": "/home/<USER>",
      "uid": 1000,
      "gid": 1000,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 62,
      "ppid": 2,
      "parent_name": "kthreadd",
      "name": "kworker/R-md_bi",
      "state": "UNKNOWN",
      "cpu_usage": 0,
      "memory_usage": 0,
      "virtual_memory": 0,
      "executable_path": "",
      "command_line": "",
      "cwd": "/",
      "uid": 0,
      "gid": 0,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 2226,
      "ppid": 1901,
      "parent_name": "systemd",
      "name": "gsd-housekeepin",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 8320,
      "virtual_memory": 315336,
      "executable_path": "/usr/libexec/gsd-housekeeping",
      "command_line": "/usr/libexec/gsd-housekeeping",
      "cwd": "/home/<USER>",
      "uid": 1000,
      "gid": 1000,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 64,
      "ppid": 2,
      "parent_name": "kthreadd",
      "name": "kworker/R-devfr",
      "state": "UNKNOWN",
      "cpu_usage": 0,
      "memory_usage": 0,
      "virtual_memory": 0,
      "executable_path": "",
      "command_line": "",
      "cwd": "/",
      "uid": 0,
      "gid": 0,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 2228,
      "ppid": 2020,
      "parent_name": "gnome-session-b",
      "name": "gsd-disk-utilit",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 7680,
      "virtual_memory": 306012,
      "executable_path": "/usr/libexec/gsd-disk-utility-notify",
      "command_line": "/usr/libexec/gsd-disk-utility-notify",
      "cwd": "/home/<USER>",
      "uid": 1000,
      "gid": 1000,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 65,
      "ppid": 2,
      "parent_name": "kthreadd",
      "name": "watchdogd",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 0,
      "virtual_memory": 0,
      "executable_path": "",
      "command_line": "",
      "cwd": "/",
      "uid": 0,
      "gid": 0,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 2229,
      "ppid": 1901,
      "parent_name": "systemd",
      "name": "vmtoolsd",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 38344,
      "virtual_memory": 144056,
      "executable_path": "/usr/bin/vmtoolsd",
      "command_line": "/usr/bin/vmtoolsd -n vmusr --blockFd 3 --uinputFd 4",
      "cwd": "/home/<USER>",
      "uid": 1000,
      "gid": 1000,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 76,
      "ppid": 2,
      "parent_name": "kthreadd",
      "name": "irq/30-pciehp",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 0,
      "virtual_memory": 0,
      "executable_path": "",
      "command_line": "",
      "cwd": "/",
      "uid": 0,
      "gid": 0,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 2240,
      "ppid": 1901,
      "parent_name": "systemd",
      "name": "gsd-keyboard",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 22660,
      "virtual_memory": 418032,
      "executable_path": "/usr/libexec/gsd-keyboard",
      "command_line": "/usr/libexec/gsd-keyboard",
      "cwd": "/home/<USER>",
      "uid": 1000,
      "gid": 1000,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 83,
      "ppid": 2,
      "parent_name": "kthreadd",
      "name": "irq/37-pciehp",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 0,
      "virtual_memory": 0,
      "executable_path": "",
      "command_line": "",
      "cwd": "/",
      "uid": 0,
      "gid": 0,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 2247,
      "ppid": 1901,
      "parent_name": "systemd",
      "name": "gsd-media-keys",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 27520,
      "virtual_memory": 720580,
      "executable_path": "/usr/libexec/gsd-media-keys",
      "command_line": "/usr/libexec/gsd-media-keys",
      "cwd": "/home/<USER>",
      "uid": 1000,
      "gid": 1000,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 29299,
      "ppid": 2,
      "parent_name": "kthreadd",
      "name": "kworker/2:1-cgroup_destroy",
      "state": "UNKNOWN",
      "cpu_usage": 0,
      "memory_usage": 0,
      "virtual_memory": 0,
      "executable_path": "",
      "command_line": "",
      "cwd": "/",
      "uid": 0,
      "gid": 0,
      "start_time": 1749445120,
      "num_children": 0
    },
    {
      "pid": 85,
      "ppid": 2,
      "parent_name": "kthreadd",
      "name": "irq/39-pciehp",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 0,
      "virtual_memory": 0,
      "executable_path": "",
      "command_line": "",
      "cwd": "/",
      "uid": 0,
      "gid": 0,
      "start_time": **********,
      "num_children": 0
    },
    {
      "pid": 2249,
      "ppid": 2207,
      "parent_name": "ibus-daemon",
      "name": "ibus-memconf",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 7424,
      "virtual_memory": 166872,
      "executable_path": "/usr/libexec/ibus-memconf",
      "command_line": "/usr/libexec/ibus-memconf",
      "cwd": "/home/<USER>",
      "uid": 1000,
      "gid": 1000,
      "start_time": **********,
      "num_children": 0
    }
  ]
}
响应状态: HTTP 200 (0.001414s)
  结果: 通过

测试: 获取进程树API
  调用URL: GET http://127.0.0.1:8080/api/process-tree
  响应JSON:
{
  "pid": 1,
  "name": "systemd",
  "state": "SLEEPING",
  "cpu_usage": 0,
  "memory_usage": 11484,
  "children": [
    {
      "pid": 1901,
      "name": "systemd",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 10368,
      "child_count": 54
    },
    {
      "pid": 869,
      "name": "wpa_supplicant",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 6272
    },
    {
      "pid": 865,
      "name": "udisksd",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 13124
    },
    {
      "pid": 863,
      "name": "systemd-logind",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 7852
    },
    {
      "pid": 345,
      "name": "systemd-journal",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 39296
    },
    {
      "pid": 857,
      "name": "switcheroo-cont",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 6656
    },
    {
      "pid": 856,
      "name": "snapd",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 38192
    },
    {
      "pid": 853,
      "name": "rsyslogd",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 6400
    },
    {
      "pid": 852,
      "name": "power-profiles-",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 6784
    },
    {
      "pid": 851,
      "name": "polkitd",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 11052
    },
    {
      "pid": 850,
      "name": "networkd-dispat",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 21120
    },
    {
      "pid": 835,
      "name": "avahi-daemon",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 3712,
      "child_count": 1
    },
    {
      "pid": 1077,
      "name": "gdm3",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 8832,
      "child_count": 1
    },
    {
      "pid": 849,
      "name": "irqbalance",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 3840
    },
    {
      "pid": 827,
      "name": "accounts-daemon",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 7744
    },
    {
      "pid": 1033,
      "name": "ModemManager",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 12104
    },
    {
      "pid": 838,
      "name": "cron",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 2816
    },
    {
      "pid": 839,
      "name": "dbus-daemon",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 6400
    },
    {
      "pid": 840,
      "name": "NetworkManager",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 18148
    },
    {
      "pid": 1251,
      "name": "cups-browsed",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 11008
    },
    {
      "pid": 772,
      "name": "systemd-resolve",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 14008
    },
    {
      "pid": 1259,
      "name": "rtkit-daemon",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 3328
    },
    {
      "pid": 774,
      "name": "systemd-timesyn",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 7296
    },
    {
      "pid": 1266,
      "name": "kerneloops",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 2208
    },
    {
      "pid": 1269,
      "name": "kerneloops",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 2208
    },
    {
      "pid": 785,
      "name": "VGAuthService",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 11520
    },
    {
      "pid": 787,
      "name": "vmtoolsd",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 8576
    },
    {
      "pid": 372,
      "name": "vmware-vmblock-",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 1420
    },
    {
      "pid": 398,
      "name": "systemd-udevd",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 7424
    },
    {
      "pid": 770,
      "name": "systemd-oomd",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 6784
    },
    {
      "pid": 828,
      "name": "acpid",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 2048
    },
    {
      "pid": 1031,
      "name": "unattended-upgr",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 23296
    },
    {
      "pid": 1028,
      "name": "cupsd",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 13184,
      "child_count": 1
    },
    {
      "pid": 1411,
      "name": "upowerd",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 8320
    },
    {
      "pid": 1417,
      "name": "packagekitd",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 20556
    },
    {
      "pid": 1573,
      "name": "colord",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 12860
    },
    {
      "pid": 1927,
      "name": "gnome-keyring-d",
      "state": "SLEEPING",
      "cpu_usage": 0,
      "memory_usage": 7960,
      "child_count": 1
    }
  ]
}
响应状态: HTTP 200 (0.001296s)
  结果: 通过

测试: 获取单个进程信息API
  调用URL: GET http://127.0.0.1:8080/api/process?pid=1
  响应JSON:
{
  "pid": 1,
  "ppid": 0,
  "name": "systemd",
  "state": "SLEEPING",
  "cpu_usage": 0,
  "memory_usage": 11484,
  "virtual_memory": 166764,
  "executable_path": "/usr/lib/systemd/systemd",
  "command_line": "/sbin/init auto noprompt splash",
  "cwd": "/",
  "uid": 0,
  "gid": 0,
  "start_time": **********,
  "num_children": 0
}
响应状态: HTTP 200 (0.000218s)
  结果: 通过

测试: 获取进程文件操作API
  调用URL: GET http://127.0.0.1:8080/api/process/files?pid=1
  响应JSON:
{
  "pid": 1,
  "is_focus_process": false,
  "message": "非重点进程，不监控文件操作详情"
}
响应状态: HTTP 200 (0.000242s)
  结果: 通过

=== 测试完成 ===

```

---

太好了！curl脚本完美运行，显示了格式化的JSON输出，并且与C++测试程序的功能完全一致。

## 完成总结

我已经成功创建了一个完整的curl测试脚本 `test_api_with_curl.sh`，实现了以下功能：

### 🎯 **核心功能**

1. **完整的API覆盖**: 包含所有与C++测试程序相同的API测试
2. **JSON格式化输出**: 自动检测并使用 `jq` 或 `python3` 进行JSON格式化
3. **颜色化输出**: 使用颜色区分测试结果（通过/失败/跳过）
4. **灵活的测试选项**: 支持运行全部测试或特定类别的测试

### 📋 **测试类别**

- **health**: 健康检查和状态API
- **process**: 进程相关API
- **network**: 网络相关API  
- **system**: 系统统计API
- **config**: 配置管理API
- **management**: 进程管理API
- **path**: 路径管理API
- **file**: 文件监控API
- **realtime**: 实时数据API

### 🛠️ **使用方式**

```bash
# 运行所有测试
./test_api_with_curl.sh

# 运行特定类别测试
./test_api_with_curl.sh health

# 使用不同URL
./test_api_with_curl.sh --url http://localhost:9090

# 调试模式
./test_api_with_curl.sh --verbose

# 自定义超时
./test_api_with_curl.sh --timeout 60
```

### 🎨 **输出特性**

- 详细显示调用的URL和HTTP方法
- 格式化显示请求数据（JSON）
- 美化显示响应JSON内容
- 显示响应状态码和响应时间
- 彩色显示测试结果

### 📁 **创建的文件**

1. `test_api_with_curl.sh` - 主要的curl测试脚本
2. `curl_test_examples.md` - 详细的使用指南和说明文档

### 🔧 **技术特点**

- **自适应JSON格式化**: 优先使用 `jq`，备用 `python3`，确保兼容性
- **错误处理**: 完善的错误处理和超时机制
- **参数解析**: 灵活的命令行参数支持
- **预检查机制**: 在测试前先检查服务可用性

这个curl脚本为手动测试提供了极大的便利，开发者可以：
- 快速验证API功能
- 调试API问题
- 学习API使用方法
- 在没有编译环境时进行测试
- 参考具体的curl命令语法

脚本与C++测试程序功能完全一致，但更加轻量级和易于使用！

---

_**User**_

　要将curl_cmd输出一下，方便我们拷贝后手动使用。

---

_**Assistant**_



---



---



---



---



---



---

