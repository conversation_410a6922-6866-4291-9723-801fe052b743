# Makefile for PSFSMON-L
# Linux安全监控程序

# 编译器设置
CXX = g++
CC = gcc

# 基础编译标志
CXXFLAGS = -std=c++11 -Wall -Wextra -Wpedantic -O2 -pthread
CFLAGS = -Wall -Wextra -Wpedantic -O2 -pthread

# 调试版本标志 - 增强版
DEBUG_CXXFLAGS = -std=c++11 -Wall -Wextra -Wpedantic -g3 -O0 -DDEBUG -pthread \
                 -fno-omit-frame-pointer -DDEBUG_MODE=1 -DVERBOSE_DEBUG=1 \
                 -DENABLE_TRACE_LOGGING=1 -DENABLE_MEMORY_DEBUG=1
DEBUG_CFLAGS = -Wall -Wextra -Wpedantic -g3 -O0 -DDEBUG -pthread \
               -fno-omit-frame-pointer -DDEBUG_MODE=1

# Sanitizer标志
SANITIZER_FLAGS = -fsanitize=address -fsanitize=undefined -fsanitize=leak
SANITIZER_CXXFLAGS = $(DEBUG_CXXFLAGS) $(SANITIZER_FLAGS)
SANITIZER_CFLAGS = $(DEBUG_CFLAGS) $(SANITIZER_FLAGS)
SANITIZER_LDFLAGS = $(SANITIZER_FLAGS)

# Profiling标志
PROFILE_CXXFLAGS = $(CXXFLAGS) -pg
PROFILE_CFLAGS = $(CFLAGS) -pg
PROFILE_LDFLAGS = -pg

# Valgrind友好的调试标志
VALGRIND_CXXFLAGS = $(DEBUG_CXXFLAGS) -DVALGRIND_SUPPORT=1
VALGRIND_CFLAGS = $(DEBUG_CFLAGS) -DVALGRIND_SUPPORT=1

# 包含目录
INCLUDES = -Iinclude

# 库链接
LIBS = -pthread -lrt -ldl

# 源文件目录
SRCDIR = src
INCDIR = include
OBJDIR = obj
BINDIR = bin
DEBUGDIR = $(BINDIR)/debug

# 可执行文件名
TARGET = psfsmon
DEBUG_TARGET = psfsmon-debug
SANITIZER_TARGET = psfsmon-sanitizer
PROFILE_TARGET = psfsmon-profile
VALGRIND_TARGET = psfsmon-valgrind

# 源文件
SOURCES = $(wildcard $(SRCDIR)/*.cpp)
OBJECTS = $(SOURCES:$(SRCDIR)/%.cpp=$(OBJDIR)/%.o)
DEBUG_OBJECTS = $(SOURCES:$(SRCDIR)/%.cpp=$(OBJDIR)/debug/%.o)
SANITIZER_OBJECTS = $(SOURCES:$(SRCDIR)/%.cpp=$(OBJDIR)/sanitizer/%.o)
PROFILE_OBJECTS = $(SOURCES:$(SRCDIR)/%.cpp=$(OBJDIR)/profile/%.o)
VALGRIND_OBJECTS = $(SOURCES:$(SRCDIR)/%.cpp=$(OBJDIR)/valgrind/%.o)

# 默认目标
.PHONY: all clean install uninstall debug release help sanitizer profile valgrind debug-info

all: release

# 发布版本
release: CXXFLAGS += -DNDEBUG
release: $(BINDIR)/$(TARGET)

# 调试版本
debug: $(DEBUGDIR)/$(DEBUG_TARGET)

# Sanitizer版本
sanitizer: $(DEBUGDIR)/$(SANITIZER_TARGET)

# Profile版本
profile: $(DEBUGDIR)/$(PROFILE_TARGET)

# Valgrind友好版本
valgrind: $(DEBUGDIR)/$(VALGRIND_TARGET)

# 创建可执行文件
$(BINDIR)/$(TARGET): $(OBJECTS) | $(BINDIR)
	@echo "Linking release version $@..."
	$(CXX) $(OBJECTS) -o $@ $(LIBS)
	@echo "Release build completed successfully!"

# 调试版本可执行文件
$(DEBUGDIR)/$(DEBUG_TARGET): $(DEBUG_OBJECTS) | $(DEBUGDIR)
	@echo "Linking debug version $@..."
	$(CXX) $(DEBUG_OBJECTS) -o $@ $(LIBS)
	@echo "Debug build completed successfully!"

# Sanitizer版本可执行文件
$(DEBUGDIR)/$(SANITIZER_TARGET): $(SANITIZER_OBJECTS) | $(DEBUGDIR)
	@echo "Linking sanitizer version $@..."
	$(CXX) $(SANITIZER_OBJECTS) -o $@ $(LIBS) $(SANITIZER_LDFLAGS)
	@echo "Sanitizer build completed successfully!"

# Profile版本可执行文件
$(DEBUGDIR)/$(PROFILE_TARGET): $(PROFILE_OBJECTS) | $(DEBUGDIR)
	@echo "Linking profile version $@..."
	$(CXX) $(PROFILE_OBJECTS) -o $@ $(LIBS) $(PROFILE_LDFLAGS)
	@echo "Profile build completed successfully!"

# Valgrind版本可执行文件
$(DEBUGDIR)/$(VALGRIND_TARGET): $(VALGRIND_OBJECTS) | $(DEBUGDIR)
	@echo "Linking valgrind version $@..."
	$(CXX) $(VALGRIND_OBJECTS) -o $@ $(LIBS)
	@echo "Valgrind build completed successfully!"

# 编译目标文件 - 发布版本
$(OBJDIR)/%.o: $(SRCDIR)/%.cpp | $(OBJDIR)
	@echo "Compiling (release) $<..."
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# 编译目标文件 - 调试版本
$(OBJDIR)/debug/%.o: $(SRCDIR)/%.cpp | $(OBJDIR)/debug
	@echo "Compiling (debug) $<..."
	$(CXX) $(DEBUG_CXXFLAGS) $(INCLUDES) -c $< -o $@

# 编译目标文件 - Sanitizer版本
$(OBJDIR)/sanitizer/%.o: $(SRCDIR)/%.cpp | $(OBJDIR)/sanitizer
	@echo "Compiling (sanitizer) $<..."
	$(CXX) $(SANITIZER_CXXFLAGS) $(INCLUDES) -c $< -o $@

# 编译目标文件 - Profile版本
$(OBJDIR)/profile/%.o: $(SRCDIR)/%.cpp | $(OBJDIR)/profile
	@echo "Compiling (profile) $<..."
	$(CXX) $(PROFILE_CXXFLAGS) $(INCLUDES) -c $< -o $@

# 编译目标文件 - Valgrind版本
$(OBJDIR)/valgrind/%.o: $(SRCDIR)/%.cpp | $(OBJDIR)/valgrind
	@echo "Compiling (valgrind) $<..."
	$(CXX) $(VALGRIND_CXXFLAGS) $(INCLUDES) -c $< -o $@

# 创建目录
$(OBJDIR):
	mkdir -p $(OBJDIR)

$(OBJDIR)/debug:
	mkdir -p $(OBJDIR)/debug

$(OBJDIR)/sanitizer:
	mkdir -p $(OBJDIR)/sanitizer

$(OBJDIR)/profile:
	mkdir -p $(OBJDIR)/profile

$(OBJDIR)/valgrind:
	mkdir -p $(OBJDIR)/valgrind

$(BINDIR):
	mkdir -p $(BINDIR)

$(DEBUGDIR):
	mkdir -p $(DEBUGDIR)

# 清理
clean:
	@echo "Cleaning up..."
	rm -rf $(OBJDIR) $(BINDIR)
	@echo "Clean completed!"

# 调试信息显示
debug-info:
	@echo "=== Debug Build Information ==="
	@echo "Compiler: $(CXX)"
	@echo "Debug CXXFLAGS: $(DEBUG_CXXFLAGS)"
	@echo "Sanitizer CXXFLAGS: $(SANITIZER_CXXFLAGS)"
	@echo "Profile CXXFLAGS: $(PROFILE_CXXFLAGS)"
	@echo "Valgrind CXXFLAGS: $(VALGRIND_CXXFLAGS)"
	@echo "Libraries: $(LIBS)"
	@echo "Available debug targets:"
	@echo "  - $(DEBUGDIR)/$(DEBUG_TARGET)"
	@echo "  - $(DEBUGDIR)/$(SANITIZER_TARGET)"
	@echo "  - $(DEBUGDIR)/$(PROFILE_TARGET)"
	@echo "  - $(DEBUGDIR)/$(VALGRIND_TARGET)"
	@echo "==============================="

# GDB调试助手
gdb: debug
	@echo "Starting GDB with debug version..."
	gdb --args $(DEBUGDIR)/$(DEBUG_TARGET)

# GDB调试助手（带参数）
gdb-args: debug
	@echo "Starting GDB with debug version (you can add arguments)..."
	@echo "Usage: make gdb-args ARGS='your_arguments_here'"
	gdb --args $(DEBUGDIR)/$(DEBUG_TARGET) $(ARGS)

# Valgrind内存检查
memcheck: valgrind
	@echo "Running Valgrind memcheck..."
	valgrind --tool=memcheck --leak-check=full --show-leak-kinds=all \
		--track-origins=yes --verbose $(DEBUGDIR)/$(VALGRIND_TARGET) $(ARGS)

# Valgrind堆检查
heapcheck: valgrind
	@echo "Running Valgrind heap profiler..."
	valgrind --tool=massif $(DEBUGDIR)/$(VALGRIND_TARGET) $(ARGS)

# Address Sanitizer检查
asan: sanitizer
	@echo "Running with Address Sanitizer..."
	ASAN_OPTIONS=verbosity=1:halt_on_error=1 $(DEBUGDIR)/$(SANITIZER_TARGET) $(ARGS)

# 性能分析
profile-run: profile
	@echo "Running profile version..."
	$(DEBUGDIR)/$(PROFILE_TARGET) $(ARGS)
	@echo "Generating profile report..."
	gprof $(DEBUGDIR)/$(PROFILE_TARGET) gmon.out > profile_report.txt
	@echo "Profile report saved to profile_report.txt"

# 安装
install: $(BINDIR)/$(TARGET)
	@echo "Installing $(TARGET)..."
	sudo mkdir -p /usr/local/bin
	sudo cp $(BINDIR)/$(TARGET) /usr/local/bin/
	sudo chmod +x /usr/local/bin/$(TARGET)
	sudo mkdir -p /etc/psfsmon
	sudo mkdir -p /var/log/psfsmon
	@if [ -f config/psfsmon.conf ]; then \
		sudo cp config/psfsmon.conf /etc/psfsmon/; \
	fi
	@if [ -f scripts/psfsmon.service ] && [ -d /lib/systemd/system ]; then \
		sudo cp scripts/psfsmon.service /lib/systemd/system/; \
		sudo systemctl daemon-reload; \
		echo "Systemd service installed. Use 'sudo systemctl enable psfsmon' to enable autostart."; \
	fi
	@echo "Installation completed!"

# 安装调试版本
install-debug: debug
	@echo "Installing debug version..."
	sudo mkdir -p /usr/local/bin
	sudo cp $(DEBUGDIR)/$(DEBUG_TARGET) /usr/local/bin/
	sudo chmod +x /usr/local/bin/$(DEBUG_TARGET)
	@echo "Debug version installed to /usr/local/bin/$(DEBUG_TARGET)"

# 卸载
uninstall:
	@echo "Uninstalling $(TARGET)..."
	sudo rm -f /usr/local/bin/$(TARGET)
	sudo rm -f /usr/local/bin/$(DEBUG_TARGET)
	@if [ -f /lib/systemd/system/psfsmon.service ]; then \
		sudo systemctl stop psfsmon; \
		sudo systemctl disable psfsmon; \
		sudo rm -f /lib/systemd/system/psfsmon.service; \
		sudo systemctl daemon-reload; \
	fi
	@echo "Uninstallation completed!"
	@echo "Configuration files in /etc/psfsmon and logs in /var/log/psfsmon were preserved."

# 测试编译
test: $(BINDIR)/$(TARGET)
	@echo "Running basic tests..."
	@if $(BINDIR)/$(TARGET) --version > /dev/null 2>&1; then \
		echo "✓ Version check passed"; \
	else \
		echo "✗ Version check failed"; \
	fi
	@if $(BINDIR)/$(TARGET) --help > /dev/null 2>&1; then \
		echo "✓ Help check passed"; \
	else \
		echo "✗ Help check failed"; \
	fi

# 运行基本测试脚本
test-basic: $(BINDIR)/$(TARGET)
	@echo "Running basic tests..."
	@if [ -x "./test/psfsmon_comprehensive_test.sh" ]; then \
		./test/psfsmon_comprehensive_test.sh basic; \
	else \
		echo "Warning: test/psfsmon_comprehensive_test.sh not found or not executable"; \
		echo "Running basic executable tests instead..."; \
		if $(BINDIR)/$(TARGET) --version > /dev/null 2>&1; then \
			echo "✓ Version check passed"; \
		else \
			echo "✗ Version check failed"; \
		fi; \
		if $(BINDIR)/$(TARGET) --help > /dev/null 2>&1; then \
			echo "✓ Help check passed"; \
		else \
			echo "✗ Help check failed"; \
		fi; \
	fi

# 运行新功能测试 (需要root权限)
test-features: $(BINDIR)/$(TARGET)
	@echo "Running new features test..."
	@if [ "$$EUID" -eq 0 ]; then \
		if [ -x "./test/psfsmon_comprehensive_test.sh" ]; then \
			./test/psfsmon_comprehensive_test.sh features; \
		else \
			echo "Warning: test/psfsmon_comprehensive_test.sh not found"; \
			echo "Please run comprehensive tests manually"; \
		fi; \
	else \
		echo "Warning: Feature tests require root privileges"; \
		echo "Run: sudo make test-features"; \
	fi

# 运行所有测试
test-all: test test-basic
	@echo "All tests completed"

# 调试测试
test-debug: debug
	@echo "Running debug tests..."
	@if $(DEBUGDIR)/$(DEBUG_TARGET) --version > /dev/null 2>&1; then \
		echo "✓ Debug version check passed"; \
	else \
		echo "✗ Debug version check failed"; \
	fi

# 检查依赖
check-deps:
	@echo "Checking system dependencies..."
	@echo -n "Checking kernel version: "
	@uname -r
	@echo -n "Checking for fanotify support: "
	@if [ -f /usr/include/sys/fanotify.h ]; then \
		echo "✓ Found"; \
	else \
		echo "✗ Not found"; \
	fi
	# inotify检查已移除，仅使用fanotify
	@echo -n "Checking for pthread support: "
	@if ldconfig -p | grep -q libpthread; then \
		echo "✓ Found"; \
	else \
		echo "✗ Not found"; \
	fi
	@echo -n "Checking for GDB: "
	@if command -v gdb > /dev/null 2>&1; then \
		echo "✓ Found"; \
	else \
		echo "✗ Not found (install gdb for debugging)"; \
	fi
	@echo -n "Checking for Valgrind: "
	@if command -v valgrind > /dev/null 2>&1; then \
		echo "✓ Found"; \
	else \
		echo "✗ Not found (install valgrind for memory debugging)"; \
	fi

# 打包
package: clean
	@echo "Creating package..."
	tar -czf psfsmon-$(shell date +%Y%m%d).tar.gz \
		--exclude='.git*' \
		--exclude='*.o' \
		--exclude='obj/' \
		--exclude='bin/' \
		--exclude='build/' \
		.
	@echo "Package created: psfsmon-$(shell date +%Y%m%d).tar.gz"

# 帮助
help:
	@echo "PSFSMON-L Makefile Help"
	@echo "======================="
	@echo ""
	@echo "Available targets:"
	@echo "  all           - Build release version (default)"
	@echo "  release       - Build optimized release version"
	@echo "  debug         - Build debug version with full symbols"
	@echo "  sanitizer     - Build with Address/UB/Leak sanitizers"
	@echo "  profile       - Build with profiling support"
	@echo "  valgrind      - Build valgrind-friendly version"
	@echo "  clean         - Remove all build files"
	@echo "  install       - Install release version to system"
	@echo "  install-debug - Install debug version to system"
	@echo "  uninstall     - Remove from system"
	@echo "  test          - Build and run basic tests"
	@echo "  test-basic    - Run basic test scripts"
	@echo "  test-features - Run feature tests (requires root)"
	@echo "  test-all      - Run all tests"
	@echo "  test-debug    - Build and run debug tests"
	@echo "  check-deps    - Check system dependencies"
	@echo "  package       - Create distribution package"
	@echo ""
	@echo "Debug and analysis targets:"
	@echo "  debug-info    - Show debug build information"
	@echo "  gdb           - Start GDB with debug version"
	@echo "  gdb-args      - Start GDB with arguments (use ARGS=...)"
	@echo "  memcheck      - Run Valgrind memory check"
	@echo "  heapcheck     - Run Valgrind heap profiler"
	@echo "  asan          - Run with Address Sanitizer"
	@echo "  profile-run   - Run and generate profile report"
	@echo ""
	@echo "Usage examples:"
	@echo "  make                    # Build release version"
	@echo "  make debug              # Build debug version"
	@echo "  make sanitizer          # Build with sanitizers"
	@echo "  make gdb                # Debug with GDB"
	@echo "  make memcheck           # Check memory with Valgrind"
	@echo "  make gdb-args ARGS='--config /path/to/config'"
	@echo "  make profile-run ARGS='--test-mode'"

# 依赖文件检查
-include $(OBJECTS:.o=.d)
-include $(DEBUG_OBJECTS:.o=.d)
-include $(SANITIZER_OBJECTS:.o=.d)
-include $(PROFILE_OBJECTS:.o=.d)
-include $(VALGRIND_OBJECTS:.o=.d)

# 自动生成依赖文件
$(OBJDIR)/%.d: $(SRCDIR)/%.cpp | $(OBJDIR)
	@$(CXX) $(CXXFLAGS) $(INCLUDES) -MM $< | sed 's|.*:|$(OBJDIR)/$*.o:|' > $@

$(OBJDIR)/debug/%.d: $(SRCDIR)/%.cpp | $(OBJDIR)/debug
	@$(CXX) $(DEBUG_CXXFLAGS) $(INCLUDES) -MM $< | sed 's|.*:|$(OBJDIR)/debug/$*.o:|' > $@

$(OBJDIR)/sanitizer/%.d: $(SRCDIR)/%.cpp | $(OBJDIR)/sanitizer
	@$(CXX) $(SANITIZER_CXXFLAGS) $(INCLUDES) -MM $< | sed 's|.*:|$(OBJDIR)/sanitizer/$*.o:|' > $@

$(OBJDIR)/profile/%.d: $(SRCDIR)/%.cpp | $(OBJDIR)/profile
	@$(CXX) $(PROFILE_CXXFLAGS) $(INCLUDES) -MM $< | sed 's|.*:|$(OBJDIR)/profile/$*.o:|' > $@

$(OBJDIR)/valgrind/%.d: $(SRCDIR)/%.cpp | $(OBJDIR)/valgrind
	@$(CXX) $(VALGRIND_CXXFLAGS) $(INCLUDES) -MM $< | sed 's|.*:|$(OBJDIR)/valgrind/$*.o:|' > $@
