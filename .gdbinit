# GDB初始化文件 - PSFSMON-L 调试配置

# 设置历史记录
set history save on
set history size 1000
set history filename ~/.gdb_history

# 漂亮的打印
set print pretty on
set print array on
set print array-indexes on
set print elements 0
set print null-stop on

# 调试信息
set print symbol-filename on
set print frame-arguments all

# 自动反汇编
set disassembly-flavor intel

# 彩色输出（如果支持）
set style enabled on

# 分页设置
set pagination off

# 线程信息
set print thread-events on

# 断点时显示更多信息
set print inferior-events on

# 自动加载本地.gdbinit文件
set auto-load safe-path /

# 定义有用的宏和函数

# 显示当前所有线程的栈帧
define threads
    info threads
    thread apply all bt
end
document threads
显示所有线程信息和栈跟踪
end

# 显示进程内存映射
define memmap
    info proc mappings
end
document memmap
显示进程内存映射信息
end

# 显示文件描述符
define fds
    info proc files
end
document fds
显示进程打开的文件描述符
end

# 显示系统调用
define syscalls
    catch syscall
    continue
end
document syscalls
捕获并显示系统调用
end

# 监控文件操作
define watch_files
    catch syscall open
    catch syscall openat
    catch syscall close
    catch syscall read
    catch syscall write
    continue
end
document watch_files
监控文件操作相关的系统调用
end

# 监控网络操作
define watch_network
    catch syscall socket
    catch syscall bind
    catch syscall listen
    catch syscall accept
    catch syscall connect
    catch syscall send
    catch syscall recv
    continue
end
document watch_network
监控网络操作相关的系统调用
end

# 监控进程操作
define watch_process
    catch syscall fork
    catch syscall vfork
    catch syscall clone
    catch syscall execve
    catch syscall exit
    catch syscall exit_group
    continue
end
document watch_process
监控进程操作相关的系统调用
end

# 显示结构体的所有成员
define pstruct
    if $argc != 1
        help pstruct
    else
        set $ptr = (void*)$arg0
        printf "结构体地址: %p\n", $ptr
        print *($arg0)
    end
end
document pstruct
打印结构体的详细信息
用法: pstruct <结构体指针>
end

# 显示字符串内容（支持长字符串）
define pstr
    if $argc != 1
        help pstr
    else
        set $str = (char*)$arg0
        printf "字符串地址: %p\n", $str
        printf "字符串内容: \"%s\"\n", $str
        printf "字符串长度: %d\n", strlen($str)
    end
end
document pstr
打印字符串的详细信息
用法: pstr <字符串指针>
end

# 显示内存内容（十六进制和ASCII）
define hexdump
    if $argc != 2
        help hexdump
    else
        set $addr = (unsigned char*)$arg0
        set $len = $arg1
        set $i = 0
        while $i < $len
            if $i % 16 == 0
                printf "\n0x%016lx: ", $addr + $i
            end
            printf "%02x ", *($addr + $i)
            if $i % 16 == 15
                printf " "
                set $j = $i - 15
                while $j <= $i
                    set $c = *($addr + $j)
                    if $c >= 32 && $c <= 126
                        printf "%c", $c
                    else
                        printf "."
                    end
                    set $j = $j + 1
                end
            end
            set $i = $i + 1
        end
        printf "\n"
    end
end
document hexdump
以十六进制和ASCII格式显示内存内容
用法: hexdump <地址> <长度>
end

# 显示调用栈中的所有局部变量
define locals_all
    set $frame = 0
    while $frame < 10
        printf "\n=== 栈帧 %d ===\n", $frame
        frame $frame
        info locals
        set $frame = $frame + 1
    end
    frame 0
end
document locals_all
显示调用栈中所有帧的局部变量
end

# 监控内存分配
define watch_malloc
    break malloc
    break calloc
    break realloc
    break free
    commands
        printf "内存操作: %s\n", $pc
        bt 3
        continue
    end
end
document watch_malloc
监控内存分配和释放操作
end

# 跟踪函数调用
define trace_calls
    if $argc != 1
        help trace_calls
    else
        break $arg0
        commands
            printf "调用函数: %s\n", $arg0
            info args
            bt 2
            continue
        end
    end
end
document trace_calls
跟踪指定函数的调用
用法: trace_calls <函数名>
end

# 快速设置调试环境
define debug_setup
    printf "设置PSFSMON-L调试环境...\n"
    
    # 设置常用断点
    break main
    
    # 监控关键函数（根据项目实际情况调整）
    trace_calls process_monitor_start
    trace_calls file_monitor_start
    trace_calls network_monitor_start
    
    printf "调试环境设置完成！\n"
    printf "使用 'c' 开始运行程序\n"
    printf "使用 'help user-defined' 查看自定义命令\n"
end
document debug_setup
自动设置PSFSMON-L项目的调试环境
end

# 显示当前程序状态
define status
    printf "\n=== 程序状态 ===\n"
    info program
    printf "\n=== 当前线程 ===\n"
    info thread
    printf "\n=== 栈跟踪 ===\n"
    bt 5
    printf "\n=== 寄存器 ===\n"
    info registers
    printf "\n=== 当前位置 ===\n"
    list
end
document status
显示程序的完整状态信息
end

# 智能断点 - 在错误时停止
define smart_break
    # 在常见错误函数处设置断点
    break abort
    break exit
    break _exit
    break __assert_fail
    
    # 在信号处理时停止
    handle SIGSEGV stop print
    handle SIGBUS stop print
    handle SIGFPE stop print
    handle SIGILL stop print
    
    printf "智能断点已设置，程序将在错误时自动停止\n"
end
document smart_break
设置智能断点，在程序出错时自动停止
end

# 启动时显示欢迎信息
printf "\n"
printf "=================================================\n"
printf "    PSFSMON-L GDB 调试环境已加载\n"
printf "=================================================\n"
printf "可用的自定义命令:\n"
printf "  debug_setup  - 设置调试环境\n"
printf "  status       - 显示程序状态\n"
printf "  smart_break  - 设置智能断点\n"
printf "  threads      - 显示所有线程信息\n"
printf "  memmap       - 显示内存映射\n"
printf "  hexdump      - 十六进制内存转储\n"
printf "  watch_files  - 监控文件操作\n"
printf "  watch_network- 监控网络操作\n"
printf "  watch_process- 监控进程操作\n"
printf "\n使用 'help user-defined' 查看所有自定义命令\n"
printf "=================================================\n"
printf "\n"

# 自动设置智能断点
smart_break 