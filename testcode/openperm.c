#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/fanotify.h>
#include <poll.h>
#include <errno.h>
#include <string.h>
#include <sys/types.h>
#include <sys/stat.h>

int main(int argc, char *argv[]) {
    int fan_fd, poll_num;
    struct pollfd fds[1];
    char buf[4096];
    struct fanotify_event_metadata *md;
    //struct fanotify_response response;

    if (argc < 2) {
        fprintf(stderr, "Usage: %s <path>\n", argv[0]);
        return EXIT_FAILURE;
    }

    // 检查是否有足够权限
    if (geteuid() != 0) {
        fprintf(stderr, "警告: 此程序通常需要 root 权限才能正常工作\n");
        fprintf(stderr, "如果遇到权限错误，请使用 sudo 运行\n");
    }

    // Linux 2.6.36 引入 fanotify_init，2.6.37 开启
    fan_fd = fanotify_init(FAN_NONBLOCK,
                           O_RDONLY | O_LARGEFILE | O_CLOEXEC);
    if (fan_fd < 0) {
        perror("fanotify_init");
        return EXIT_FAILURE;
    }

    if (fanotify_mark(fan_fd,
                      FAN_MARK_ADD | FAN_MARK_ONLYDIR,
                      FAN_MODIFY | FAN_CLOSE_WRITE | FAN_EVENT_ON_CHILD,
                      AT_FDCWD, argv[1]) < 0) {
        perror("fanotify_mark");
        return EXIT_FAILURE;
    }

    fds[0].fd = fan_fd;
    fds[0].events = POLLIN;

    printf("开始监控路径: %s\n", argv[1]);
    printf("等待文件系统事件... (按 Ctrl+C 停止)\n");

    int event_count = 0;
    for (;;) {
        poll_num = poll(fds, 1, -1);
        if (poll_num < 0) {
            if (errno == EINTR) continue;
            perror("poll");
            break;
        }

        ssize_t len = read(fan_fd, buf, sizeof(buf));
        if (len < 0) {
            if (errno == EINTR) continue;
            perror("read");
            break;
        }

        md = (struct fanotify_event_metadata *)buf;
        while (FAN_EVENT_OK(md, len)) {
            event_count++;
            
            if (md->mask & FAN_MODIFY) {
                printf("[%d] FAN_MODIFY: 文件打开写事件检测到 (PID: %d, FD: %d)\n", event_count, md->pid, md->fd);
            }

            if (md->mask & FAN_CLOSE_WRITE) {
                printf("[%d] CLOSE_WRITE: 文件写入关闭事件检测到  (PID: %d, FD: %d)\n", event_count, md->pid, md->fd);
            }
            
            if (md->mask & FAN_EVENT_ON_CHILD) {
                printf("[%d] FAN_EVENT_ON_CHILD: 目录下操作文件  (PID: %d, FD: %d)\n", event_count, md->pid, md->fd);
            }

            if (md->fd >= 0)
                close(md->fd);

            md = FAN_EVENT_NEXT(md, len);
        }
    }

    close(fan_fd);
    return 0;
}
