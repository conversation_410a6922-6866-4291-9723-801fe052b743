# SpecStory explanation file
.specstory/.what-is-this.md

# VSCode
.vscode/

# Build artifacts
obj/
bin/psfsmon
build/
cmake-build-debug/

# Test executables (详细规则见 test/.gitignore)
test/protected_path_violator
test/entropy_test_program
testcode/openperm
testcode/testdir/
test/test_*
!test/test_*.cpp
!test/test_*.c
!test/test_*.h
!test/test_*.sh
!test/test_*.md

# Test results
test/test_results/
test/*.log

# Temporary files
*.tmp
*.temp
/tmp/psfsmon_test_*
