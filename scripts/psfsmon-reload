#!/bin/bash

# PSFSMON-L 配置重载工具
# 用于向运行中的psfsmon进程发送SIGHUP信号来重新加载配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -p, --pid-file FILE     指定PID文件路径 (默认: /var/run/psfsmon.pid)"
    echo "  -s, --signal SIGNAL     指定信号类型 (默认: SIGHUP)"
    echo "  -v, --verbose           详细输出"
    echo ""
    echo "示例:"
    echo "  $0                      # 使用默认PID文件发送SIGHUP"
    echo "  $0 -p /tmp/psfsmon.pid  # 使用指定PID文件"
    echo "  $0 -s SIGUSR1           # 发送SIGUSR1信号"
    echo ""
    echo "信号类型:"
    echo "  SIGHUP   重新加载配置文件 (默认)"
    echo "  SIGUSR1  重新加载配置文件 (备用)"
    echo "  SIGTERM  优雅停止程序"
    echo "  SIGINT   中断程序"
}

# 默认值
PID_FILE="/var/run/psfsmon.pid"
SIGNAL="SIGHUP"
VERBOSE=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -p|--pid-file)
            PID_FILE="$2"
            shift 2
            ;;
        -s|--signal)
            SIGNAL="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        *)
            echo -e "${RED}错误: 未知选项 $1${NC}" >&2
            show_help
            exit 1
            ;;
    esac
done

# 详细输出函数
log() {
    if [ "$VERBOSE" = true ]; then
        echo -e "${BLUE}[INFO]${NC} $1"
    fi
}

# 错误输出函数
error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

# 成功输出函数
success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 警告输出函数
warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 检查PID文件是否存在
if [ ! -f "$PID_FILE" ]; then
    error "PID文件不存在: $PID_FILE"
    echo "请确保psfsmon正在运行，或者使用 -p 选项指定正确的PID文件路径"
    exit 1
fi

# 读取PID
PID=$(cat "$PID_FILE" 2>/dev/null)
if [ -z "$PID" ]; then
    error "无法读取PID文件: $PID_FILE"
    exit 1
fi

log "从PID文件读取进程ID: $PID"

# 检查进程是否存在
if ! kill -0 "$PID" 2>/dev/null; then
    error "进程 $PID 不存在或无法访问"
    echo "PID文件可能已过期，请检查psfsmon是否正在运行"
    exit 1
fi

log "进程 $PID 存在，正在发送信号 $SIGNAL..."

# 发送信号
if kill -s "$SIGNAL" "$PID" 2>/dev/null; then
    case "$SIGNAL" in
        SIGHUP|SIGUSR1)
            success "已向进程 $PID 发送 $SIGNAL 信号"
            echo "配置重新加载请求已发送"
            echo "请检查日志文件以确认配置是否成功重新加载"
            ;;
        SIGTERM)
            success "已向进程 $PID 发送 $SIGNAL 信号"
            echo "程序停止请求已发送"
            ;;
        SIGINT)
            success "已向进程 $PID 发送 $SIGNAL 信号"
            echo "程序中断请求已发送"
            ;;
        *)
            success "已向进程 $PID 发送 $SIGNAL 信号"
            ;;
    esac
else
    error "无法向进程 $PID 发送 $SIGNAL 信号"
    echo "请检查进程权限或信号是否有效"
    exit 1
fi

# 如果是重新加载信号，等待一下然后检查进程状态
if [ "$SIGNAL" = "SIGHUP" ] || [ "$SIGNAL" = "SIGUSR1" ]; then
    log "等待配置重新加载完成..."
    sleep 1
    
    if kill -0 "$PID" 2>/dev/null; then
        log "进程仍在运行，配置重新加载可能已成功"
    else
        warning "进程已停止，可能配置重新加载失败导致程序退出"
    fi
fi 