[Unit]
Description=PSFSMON-L Linux Security Monitor
Documentation=man:psfsmon(8)
After=network.target local-fs.target
Wants=network.target

[Service]
Type=forking
ExecStart=/usr/local/bin/psfsmon --daemon --config /etc/psfsmon/psfsmon.conf
ExecReload=/bin/kill -HUP $MAINPID
ExecStop=/bin/kill -TERM $MAINPID
PIDFile=/var/run/psfsmon.pid
User=root
Group=root
Restart=on-failure
RestartSec=5
TimeoutStartSec=30
TimeoutStopSec=30

# 安全设置
NoNewPrivileges=false
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/psfsmon /var/run /etc/psfsmon
ProtectKernelTunables=false
ProtectKernelModules=false
ProtectControlGroups=false
RestrictRealtime=true
RestrictSUIDSGID=true
MemoryDenyWriteExecute=false
LockPersonality=true
RestrictNamespaces=false

# 能力设置 - 需要这些权限进行系统监控
CapabilityBoundingSet=CAP_SYS_ADMIN CAP_DAC_READ_SEARCH CAP_SYS_PTRACE CAP_NET_ADMIN
AmbientCapabilities=CAP_SYS_ADMIN CAP_DAC_READ_SEARCH CAP_SYS_PTRACE CAP_NET_ADMIN

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target 