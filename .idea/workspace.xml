<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="BackendCodeEditorMiscSettings">
    <option name="/Default/RiderDebugger/RiderRestoreDecompile/RestoreDecompileSetting/@EntryValue" value="false" type="bool" />
    <option name="/Default/Housekeeping/GlobalSettingsUpgraded/IsUpgraded/@EntryValue" value="true" type="bool" />
    <option name="/Default/Housekeeping/FeatureSuggestion/FeatureSuggestionManager/DisabledSuggesters/=SwitchToGoToActionSuggester/@EntryIndexedValue" value="true" type="bool" />
  </component>
  <component name="CMakePresetLoader">{
  &quot;useNewFormat&quot;: true
}</component>
  <component name="CMakeProjectFlavorService">
    <option name="flavorId" value="CMakePlainProjectFlavor" />
  </component>
  <component name="CMakeReloadState">
    <option name="reloaded" value="true" />
  </component>
  <component name="CMakeRunConfigurationManager">
    <generated>
      <config projectName="psfsmon" targetName="psfsmon" />
      <config projectName="psfsmon" targetName="psfsmon-debug" />
      <config projectName="psfsmon" targetName="debug-info" />
      <config projectName="psfsmon" targetName="gdb" />
      <config projectName="psfsmon" targetName="valgrind" />
    </generated>
  </component>
  <component name="CMakeSettings">
    <configurations>
      <configuration PROFILE_NAME="Debug" ENABLED="true" CONFIG_NAME="Debug" />
    </configurations>
  </component>
  <component name="ChangeListManager">
    <list default="true" id="dfaa5609-0b14-48e3-9160-94882b01c40b" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ref_code/rules_about.cpp" beforeDir="false" afterPath="$PROJECT_DIR$/ref_code/rules_about.cpp" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="CMakeBuildProfile:Debug" />
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/src/file_monitor.cpp" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="ProjectApplicationVersion">
    <option name="ide" value="CLion" />
    <option name="majorVersion" value="2024" />
    <option name="minorVersion" value="3.5" />
    <option name="productBranch" value="Classic" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2xw7Qe1F9M4M0irDv3kwDOpUucb" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "CMake 应用程序.psfsmon.executor": "Debug",
    "RunOnceActivity.RadMigrateCodeStyle": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.git.unshallow": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "RunOnceActivity.west.config.association.type.startup.service": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "git-widget-placeholder": "master",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "onboarding.tips.debug.path": "/home/<USER>/coder/psfsmonL/main.cpp",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true",
    "原生应用程序.psfsmon_mf.executor": "Profiler"
  }
}]]></component>
  <component name="RunManager" selected="CMake 应用程序.psfsmon">
    <configuration default="true" type="CLionExternalRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true">
      <method v="2">
        <option name="CLION.EXTERNAL.BUILD" enabled="true" />
      </method>
    </configuration>
    <configuration name="debug-info" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="psfsmon" TARGET_NAME="debug-info" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="gdb" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="psfsmon" TARGET_NAME="gdb" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="psfsmon-debug" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="psfsmon" TARGET_NAME="psfsmon-debug" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="psfsmon" RUN_TARGET_NAME="psfsmon-debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="psfsmon" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="true" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="psfsmon" TARGET_NAME="psfsmon" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="psfsmon" RUN_TARGET_NAME="psfsmon">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="valgrind" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="psfsmon" TARGET_NAME="valgrind" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="CMake 应用程序.debug-info" />
      <item itemvalue="CMake 应用程序.gdb" />
      <item itemvalue="CMake 应用程序.psfsmon-debug" />
      <item itemvalue="CMake 应用程序.psfsmon" />
      <item itemvalue="CMake 应用程序.valgrind" />
    </list>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="dfaa5609-0b14-48e3-9160-94882b01c40b" name="更改" comment="" />
      <created>1748837267889</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748837267889</updated>
      <workItem from="1748837271033" duration="8164000" />
      <workItem from="1749251386262" duration="14000" />
      <workItem from="1753063894874" duration="5256000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VCPKGProject">
    <isAutomaticCheckingOnLaunch value="false" />
    <isAutomaticFoundErrors value="true" />
    <isAutomaticReloadCMake value="true" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/src/file_monitor.cpp</url>
          <line>150</line>
          <option name="timeStamp" value="3" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>