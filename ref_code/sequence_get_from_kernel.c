#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/stat.h>
#include <errno.h>
#include <stdbool.h>

void parse_sequence_from_kernel() {
    printf("\n🔍 === 解析和验证SEQUENCE输出 ===\n");
    
    FILE *fp = fopen("/proc/fs_monitor/monitor", "r");
    if (!fp) {
        printf("❌ 无法读取监控记录: %s\n", strerror(errno));
        return;
    }
    
    char line[1024];
    int our_pid = getpid();
    bool found_sequence = false;
    char actual_sequence[256] = {0};
    int sequence_pid = -1, sequence_tid = -1;
    
    printf("📋 查找PID=%d的SEQUENCE记录...\n", our_pid);
    
    while (fgets(line, sizeof(line), fp)) {
        // 查找SEQUENCE行
        if (strstr(line, "SEQUENCE")) {
            printf("📄 发现SEQUENCE行: %s", line);
            
            // 解析SEQUENCE行的PID、TID和序列字符串
            if (sscanf(line, "# SEQUENCE PID=%d TID=%d: %255s", &sequence_pid, &sequence_tid, actual_sequence) >= 3) {
                if (sequence_pid == our_pid) {
                    found_sequence = true;
                    printf("🎯 找到我们的SEQUENCE记录:\n");
                    printf("  PID: %d (匹配)\n", sequence_pid);
                    printf("  TID: %d\n", sequence_tid);
                    printf("  序列: %s\n", actual_sequence);
                    break;
                } else {
                    printf("  其他进程的SEQUENCE (PID=%d)\n", sequence_pid);
                }
            }
        } else if (line[0] == '#') {
            // 其他注释行
            printf("  系统信息: %s", line);
        } else {
            // 在正常模式下，不应该有操作记录行
            printf("  ⚠️  意外的操作记录行: %s", line);
        }
    }
    
    fclose(fp);
}