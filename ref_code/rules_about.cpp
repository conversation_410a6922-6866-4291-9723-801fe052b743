#include <deque>
#include <map>
#include <memory>
#include <mutex>
#include <shared_mutex>
#include <stdint.h>
#include <string>
#include <locale>
#include <codecvt>
#include <cctype>
#include <cwctype>
#include <tuple>
#include <vector>
#include <set>
#include <iostream>
#include <thread>
#include <fstream>
#include "../include/fast_simple_lcsk.h"

typedef unsigned long ULONG;

constexpr auto COMPRESS_RULE = 0x00000001;
constexpr auto NOTCOMPRESS_RULE = 0x00000002;
constexpr auto RENAME_RULE = 0x00000004;
constexpr auto DELETE_RULE = 0x00000008;
constexpr auto MAP_READOPT_RULE = 0x00000010;
constexpr auto MAP_WRITEOPT_RULE = 0x00000020;
constexpr auto MAP_DELETEOPT_RULE = 0x00000040;

//定义常规规则类回调函数
typedef int (*CallBack_WatcherFunc)(ULONG, PCHANGE_PIDINFO_CTX, int, const char*);
extern CallBack_WatcherFunc cbWatcherFunc;

//定义常规Tid2Pid转换回调函数
typedef ULONG(*CallBack_Tid2PidFunc)(ULONG);
extern CallBack_Tid2PidFunc cbTid2PidFunc;

//0-9是给回调函数调试打印用的
//回调函数调试打印标识
constexpr auto CB_DEBUGINFO = 9;
constexpr auto CB_RULES_MATCH_ALGO = 8;

//通常的watch_level值
//1级规则，基础算法
constexpr auto CB_MIN_WATCH_LEVEL = 10;
constexpr auto CB_MIN_STRICT_RENAME_WATCH_LEVEL = CB_MIN_WATCH_LEVEL + 1;
constexpr auto CB_MIN_STRICT_DELETE_WATCH_LEVEL = CB_MIN_WATCH_LEVEL + 2;
constexpr auto CB_MIN_SENSITIVE_RENAME_WATCH_LEVEL = CB_MIN_WATCH_LEVEL + 11;
constexpr auto CB_MIN_SENSITIVE_DELETE_WATCH_LEVEL = CB_MIN_WATCH_LEVEL + 12;
constexpr auto CB_MIN_NORMAL_RENAME_WATCH_LEVEL = CB_MIN_WATCH_LEVEL + 21;
constexpr auto CB_MIN_NORMAL_DELETE_WATCH_LEVEL = CB_MIN_WATCH_LEVEL + 22;
constexpr auto CB_MIN_LAX_RENAME_WATCH_LEVEL = CB_MIN_WATCH_LEVEL + 31;
constexpr auto CB_MIN_LAX_DELETE_WATCH_LEVEL = CB_MIN_WATCH_LEVEL + 32;

//2级规则，非基础类算法，简称变体算法
constexpr auto CB_NORMAL_WATCH_LEVEL = 50;
//删除类2级规则
constexpr auto CB_NORMAL_WATCH_LEVEL_DELETE = CB_NORMAL_WATCH_LEVEL + 1;
//重命名类2级规则
constexpr auto CB_NORMAL_WATCH_LEVEL_RENAME = CB_NORMAL_WATCH_LEVEL + 2;

//3级规则，配合类算法，该类算法自己不工作，需与变体算法配合工作
constexpr auto CB_PATCH_WATCH_LEVEL = 70;
constexpr auto CB_PATCH_WATCH_LEVEL_DELETE = CB_PATCH_WATCH_LEVEL + 1;
constexpr auto CB_PATCH_WATCH_LEVEL_RENAME = CB_PATCH_WATCH_LEVEL + 2;

//未明确定位类规则
constexpr auto CB_NOTYPE_WATCH_LEVEL = 90;
//回调函数重命名、删除事件专门处理标识
constexpr auto CB_CATCH_RENAMEDELETE_ENVENT_LEVEL = CB_NOTYPE_WATCH_LEVEL + 1;
//用作诱饵的目录触发
constexpr auto CB_CATCH_BAITCATCH_ENVENT_LEVEL = CB_NOTYPE_WATCH_LEVEL + 2;

#define WITH_OPT_MATCH_STR_EVENT_RECORD

//单个规则
struct OneCmpRule {
	std::string RuleName;
	std::string Rule;

	int len_Value = 0;
	int k_Value = 0;
	int d_Value = 0;
	int w_Level = 0;

	ULONG RuleType = COMPRESS_RULE;

	OneCmpRule(std::string RuleName,//规则名
		std::string Rule,//规则串
		int len_Value,//规则初筛匹配时，单个信息串（即默认关键信息串的最小连续串长度为1时）的最小串长度；
		int k_Value, //规则精确匹配时，关键信息串的最小连续串长度；
		int d_Value, //规则精确匹配时，关键信息串出现的最少次数，和k_Value相乘，是关键信息串的最小串长度；
		int w_Level, //规则所设置的等级；
		ULONG RuleType//规则类型；
	);
};

struct CmpRuleManager {

	std::mutex m_CmpRuleManager_Mutex;

	std::vector<std::shared_ptr<OneCmpRule>> mRulesList;

	void AddOneCmpRule(std::shared_ptr<OneCmpRule> prule);

	bool CatchCmpRule2TID(std::string &kernel_tag_str);
};

OneCmpRule::OneCmpRule(std::string RuleName,
	std::string Rule,
	int len_Value, int k_Value,
	int d_Value, int w_Level, ULONG RuleType)
	: RuleName(RuleName)//规则名
	, Rule(Rule)//规则串
	, len_Value(len_Value)//规则初筛匹配时，单个信息串（即默认关键信息串的最小连续串长度为1时）的最小串长度；
	, k_Value(k_Value)//规则精确匹配时，关键信息串的最小连续串长度；
	, d_Value(d_Value)//规则精确匹配时，关键信息串出现的最少次数，和k_Value相乘，是关键信息串的最小串长度；
	, w_Level(w_Level)//规则所设置的等级；
	, RuleType(RuleType)//配置规则的类型；
{
}

void CmpRuleManager::AddOneCmpRule(std::shared_ptr<OneCmpRule> prule)
{
	std::lock_guard<std::mutex> lock(m_CmpRuleManager_Mutex);

	bool findit = false;
	for (auto CmpRuleIter = mRulesList.begin(); CmpRuleIter != mRulesList.end(); CmpRuleIter++)
	{
		auto tprul = CmpRuleIter->get();
		if ((tprul->RuleName == prule->RuleName) &&
			(tprul->Rule == prule->Rule) &&
			(tprul->k_Value == prule->k_Value) &&
			(tprul->len_Value == prule->len_Value) &&
			(tprul->d_Value == prule->d_Value) &&
			(tprul->w_Level == prule->w_Level)
			)
		{
			findit = true;
			break;
		}
	}

	if (!findit) mRulesList.push_back(prule);
}

//重点进程可能需要补充的信息是：
struct CHANGE_PIDINFO_CTX {
	ULONG BaseDeleteRuleCounts;//基本删除规则触发数
	ULONG BaseReNameRuleCounts;//基本重命名规则触发数
	ULONG ChangeDelteRuleCounts;//删除类变体规则触发数
	ULONG ChangeReNameRuleCounts;//重命名类变体规则触发数
	ULONG PatchDelteRuleCounts;//删除类配合规则触发数
	ULONG PatchReNameRuleCounts;//重命名配合体规则触发数
	ULONGLONG LastRuleCatchTicket;//最后的触发时间;
	//以上共8个ULONG;

	ULONG AllCatchCount;//总触发数；
	ULONG DeleteReNameMoreTimesRuleCount;//定期删除重命名次数规则触发数;
	ULONG AllDeleteTimes;//总的删除文件次数;
	ULONG AllReNameTimes;//总的重命名文件次数;
	ULONG AllWatcherCatchCount;//总触发数后被监控的次数，需服务来回调填充；
	ULONG SourceExtNameCount;//源扩展名数；
	ULONG TargetExtNameCount;//目标扩展名数；
	ULONG ReNameExtNameCount;//重命名扩展名数；
	//以上共8个ULONG;

	//进程是否是白名单等信息标识，目前最低1位为1，列入白名单，最低2位为1，hash值检验成功；
	ULONG PIDInfo;
	ULONG PIDValue;//目前未用，占位，32*2=64字节

	//目前未用的其它标识
	CHAR  MostTargetExtName[32];//最多的目标扩展名；
	CHAR  MostReNameExtName[32];//最多的重命名扩展名；
};

bool CmpRuleManager::CatchCmpRule2TID(std::string &kernel_tag_str)
{
	std::string cmpstr = kernel_tag_str;
	if (cmpstr.empty()) return false;

	bool ret = false;

	for (auto CmpRuleIter = mRulesList.begin(); CmpRuleIter != mRulesList.end(); CmpRuleIter++)
	{
		ULONG ruleType = CmpRuleIter->get()->RuleType;
		//if ((ruleType & COMPRESS_RULE) && !compress) continue;

		std::vector<std::pair<int, int>> recon;

		std::string l_rule = CmpRuleIter->get()->Rule;
		std::string l_rulename = CmpRuleIter->get()->RuleName;
		LcsKppSparseFast(l_rule, cmpstr, 1, &recon);
		size_t length = recon.size();

#ifdef PRINT_BASE_MATCH_RULESTR
		//"BsseMatch,ProcessID,123,lcskpp=1,rule,"testrule",cmpstr,"testcmpstr",searched_Length,12,matched_MinLength,8"，共12项
		std::string cbPrint = "BsseMatch,ProcessID," + std::to_string(ProcessID) + ",lcskpp=1,rule,\"" + l_rulename + "/" + l_rule + "\",cmpstr,\"" + cmpstr + \
			"\",searched_Length," + std::to_string(length) + ",matched_MinLength," + std::to_string(CmpRuleIter->get()->len_Value);
#ifndef _NO_DEBUG_STDOUT_PRINT_EVENTMATCH
		debug_printf(cbPrint.c_str());
		debug_printf("\n");
#endif
		if (cbWatcherFunc) cbWatcherFunc(ProcessID, 0, CB_RULES_MATCH_ALGO, cbPrint.c_str());
#endif

		if (length >= CmpRuleIter->get()->len_Value)
		{
			std::string tmpcmpstr = "";
			for (auto& p : recon) {
				tmpcmpstr.push_back(l_rule[p.first]);
			}

			LcsKppSparseFast(l_rule, tmpcmpstr, CmpRuleIter->get()->k_Value, &recon);
			size_t kdvalue_length = recon.size();
			size_t min_dkvalue_len = static_cast<size_t>(CmpRuleIter->get()->k_Value) * static_cast<size_t>(CmpRuleIter->get()->d_Value);

#ifdef PRINT_ACCURAT_MATCH_RULESTR
			cbPrint = "AccuratMatch,ProcessID," + std::to_string(ProcessID) + ",lcskpp=" + std::to_string(CmpRuleIter->get()->k_Value) + ",rule,\"" + \
				l_rulename + "/" + l_rule + "\",tmpcmpstr,\"" + tmpcmpstr + \
				"\",searched_K*D_Value_Length," + std::to_string(kdvalue_length) + ",matched_Min_K*D_Length," + std::to_string(min_dkvalue_len);
#ifndef _NO_DEBUG_STDOUT_PRINT_EVENTMATCH
			debug_printf(cbPrint.c_str());
			debug_printf("\n");
#endif
			if (cbWatcherFunc) cbWatcherFunc(ProcessID, 0, CB_RULES_MATCH_ALGO, cbPrint.c_str());
#endif

			if (kdvalue_length >= min_dkvalue_len)
			{
				std::string kd_matchstr = "";
				for (auto& p : recon)
					kd_matchstr.push_back(l_rule[p.first]);
#ifdef PRINT_ACCURAT_MATCH_AND_CATHCED_RULESTR
				std::string cbmatchPrint = "AccuratMatchAndCatchedProcess,ProcessID," + std::to_string(ProcessID) + ",,rule,\"" + \
					l_rulename + "/" + l_rule + "\",kd_matchstr,\"" + kd_matchstr + "\",tmpcmpstr,\"" + tmpcmpstr + "\",cmpstr,\"" + cmpstr + "\"";
#ifndef _NO_DEBUG_STDOUT_PRINT_EVENTMATCH
				debug_printf(cbmatchPrint.c_str());
				debug_printf("\n");
#endif
				//调试打印不计算CatchCount
				if (cbWatcherFunc) cbWatcherFunc(ProcessID, 0, CB_RULES_MATCH_ALGO, cbmatchPrint.c_str());
#endif
                //do_something();
                /*
                tid_finfo->pchpid_ctx->LastRuleCatchTicket = GetTickCount64();
				tid_finfo->pchpid_ctx->AllCatchCount++;

				int w_Level = CmpRuleIter->get()->w_Level;

				switch (w_Level)
				{
				case CB_MIN_STRICT_RENAME_WATCH_LEVEL:
				case CB_MIN_SENSITIVE_RENAME_WATCH_LEVEL:
				case CB_MIN_NORMAL_RENAME_WATCH_LEVEL:
				case CB_MIN_LAX_RENAME_WATCH_LEVEL:
					tid_finfo->pchpid_ctx->BaseReNameRuleCounts++;
					break;
				case CB_MIN_STRICT_DELETE_WATCH_LEVEL:
				case CB_MIN_SENSITIVE_DELETE_WATCH_LEVEL:
				case CB_MIN_NORMAL_DELETE_WATCH_LEVEL:
				case CB_MIN_LAX_DELETE_WATCH_LEVEL:
					tid_finfo->pchpid_ctx->BaseDeleteRuleCounts++;
					break;
				case CB_NORMAL_WATCH_LEVEL_DELETE:
					tid_finfo->pchpid_ctx->ChangeDelteRuleCounts++;
					break;
				case CB_NORMAL_WATCH_LEVEL_RENAME:
					tid_finfo->pchpid_ctx->ChangeReNameRuleCounts++;
					break;
				case CB_PATCH_WATCH_LEVEL_DELETE:
					tid_finfo->pchpid_ctx->PatchDelteRuleCounts++;
					break;
				case CB_PATCH_WATCH_LEVEL_RENAME:
					tid_finfo->pchpid_ctx->PatchReNameRuleCounts++;
					break;
				}

				if (cbWatcherFunc) cbWatcherFunc(ProcessID, tid_finfo->pchpid_ctx, w_Level,
					kd_matchstr.c_str());

				break;
			}*/
				ret = true;
				
		}
	}

	return ret;
}

//将规则管理引擎单独列出来，以进行统一化，类似所有进程包装器一样，硬性定义为单例
CmpRuleManager _g_p_mCmpRManager;

//规则的初始化伪示例代码

void rules_init()
{
#ifdef WITH_OPT_MATCH_STR_EVENT_RECORD

#ifndef _DEBUG
	//VIRTUALIZER_FISH_WHITE_START
#endif
		/*静态规则以进行测试*/

		//1-5类基础算法

		auto rule = std::make_shared<OneCmpRule>(
#ifdef _DEBUG
			"1_ReadS_WriteS_ReNameS",
#else
			"1",
#endif
			"!aA&!bB'!cC(!dD)!eE*!fF+!gG,!hH-!iI.!jJ/!aA&!bB'!cC(!dD)!eE*!fF+!gG,!hH-!iI.!jJ/!",
			9, 3, 2, CB_MIN_STRICT_RENAME_WATCH_LEVEL, COMPRESS_RULE | RENAME_RULE
			);
	_g_p_mCmpRManager.AddOneCmpRule(rule);

	rule = std::make_shared<OneCmpRule>(
#ifdef _DEBUG
		"2_ReNameS_ReadS_WriteS",
#else
		"2",
#endif
		"!&aA!'bB!(cC!)dD!*eE!+fF!,gG!-hH!.iI!/jJ!&aA!'bB!(cC!)dD!*eE!+fF!,gG!-hH!.iI!/jJ!",
		9, 3, 2, CB_MIN_STRICT_RENAME_WATCH_LEVEL, COMPRESS_RULE | RENAME_RULE
		);
	_g_p_mCmpRManager.AddOneCmpRule(rule);

	rule = std::make_shared<OneCmpRule>(
#ifdef _DEBUG
		"3_ReadS_ReNameS_WriteS",
#else
		"3",
#endif
		"!a&A!b'B!c(C!d)D!e*E!f+F!g,G!h-H!i.I!j/J!a&A!b'B!c(C!d)D!e*E!f+F!g,G!h-H!i.I!j/J!",
		9, 3, 2, CB_MIN_STRICT_RENAME_WATCH_LEVEL, COMPRESS_RULE | RENAME_RULE
		);
	_g_p_mCmpRManager.AddOneCmpRule(rule);

	rule = std::make_shared<OneCmpRule>(
#ifdef _DEBUG
		"4_ReadS_WriteT_WriteS_DeleteS",
#else
		"4",
#endif
		"!aBA0!cDC2!eFE4!gHG6!iJI8!aBA0!cDC2!eFE4!gHG6!iJI8!",
		12, 4, 2, CB_MIN_STRICT_DELETE_WATCH_LEVEL, COMPRESS_RULE | DELETE_RULE
		);
	_g_p_mCmpRManager.AddOneCmpRule(rule);

	rule = std::make_shared<OneCmpRule>(
#ifdef _DEBUG
		"5_ReadS_WriteT_WriteS_DeleteS",
#else
		"5",
#endif
		"!bCB1!dED3!fGF5!hIH7!jAJ9!bCB1!dED3!fGF5!hIH7!jAJ9!",
		12, 4, 2, CB_MIN_STRICT_DELETE_WATCH_LEVEL, COMPRESS_RULE | DELETE_RULE
		);
	_g_p_mCmpRManager.AddOneCmpRule(rule);

	rule = std::make_shared<OneCmpRule>(
#ifdef _DEBUG
		"6_ReadS_WriteT_WriteS_DeleteS",
#else
		"6",
#endif
		"!aJA0!cBC2!eDE4!gFG6!iHI8!aJA0!cBC2!eDE4!gFG6!iHI8!",
		12, 4, 2, CB_MIN_STRICT_DELETE_WATCH_LEVEL, COMPRESS_RULE | DELETE_RULE
		);
	_g_p_mCmpRManager.AddOneCmpRule(rule);

	rule = std::make_shared<OneCmpRule>(
#ifdef _DEBUG
		"7_ReadS_WriteT_WriteS_DeleteS",
#else
		"7",
#endif
		"!bAB1!dCD3!fEF5!hGH7!jIJ9!bAB1!dCD3!fEF5!hGH7!jIJ9!",
		12, 4, 2, CB_MIN_STRICT_DELETE_WATCH_LEVEL, COMPRESS_RULE | DELETE_RULE
		);
	_g_p_mCmpRManager.AddOneCmpRule(rule);

#ifndef _DEBUG
	//VIRTUALIZER_FISH_WHITE_END
#endif

#endif
}