#define _GNU_SOURCE
#include <errno.h>
#include <fcntl.h>
#include <limits.h>
#include <linux/fanotify.h>
#include <linux/limits.h>
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/fanotify.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <time.h>
#include <unistd.h>
#include <sys/signalfd.h>
#include <poll.h>
#include <libgen.h>
#include <sys/time.h>
#include <dirent.h>
#include <sys/utsname.h>
#include <sys/select.h>

// 配置结构体
typedef struct {
    char **protected_dirs;    // 受保护的目录列表
    int protected_dirs_count; // 受保护目录的数量
    int kill_write_processes; // 是否杀死写入受保护目录的进程
    int verbose;              // 详细输出模式
    char *log_file;           // 日志文件路径
} Config;

// 全局变量
static int fanotify_fd = -1;
static FILE *log_fp = NULL;
static Config config = {0};
static fd_set rfds;

// 事件类型枚举
typedef enum {
    EVENT_READ,
    EVENT_WRITE,
    EVENT_DELETE,
    EVENT_RENAME,
    EVENT_UNKNOWN
} EventType;

// 事件信息结构体
typedef struct {
    pid_t pid;
    pid_t ppid;
    char proc_name[256];
    char file_path[PATH_MAX];
    EventType event_type;
    struct timeval timestamp;
} EventInfo;

// 函数声明
static void handle_events(void);
static void process_event(struct fanotify_event_metadata *metadata);
static void log_event(const EventInfo *event_info);
static void cleanup(void);
static int init_fanotify(void);
static int add_watch(int fanotify_fd, const char *path);
static void signal_handler(int sig);
static EventType determine_event_type(uint64_t mask);
static void get_process_name(pid_t pid, char *name, size_t size);
static void setup_signal_handlers(void);
static void print_help(void);
static int parse_config(int argc, char *argv[]);
static int handle_perm(int fan_fd, struct fanotify_event_metadata *metadata, int allow);

// 主函数
int main(int argc, char *argv[]) {
    // 解析配置
    if (parse_config(argc, argv) != 0) {
        print_help();
        return EXIT_FAILURE;
    }

    // 检查是否有root权限
    if (geteuid() != 0) {
        fprintf(stderr, "错误: 此程序需要root权限运行\n");
        return EXIT_FAILURE;
    }

    // 检查内核版本
    struct utsname uts;
    if (uname(&uts) == 0) {
        fprintf(stderr, "内核版本: %s\n", uts.release);
    }

    // 设置信号处理
    setup_signal_handlers();

    // 初始化fanotify
    if (init_fanotify() != 0) {
        fprintf(stderr, "初始化fanotify失败\n");
        return EXIT_FAILURE;
    }

    // 打开日志文件
    if (config.log_file) {
        log_fp = fopen(config.log_file, "a");
        if (!log_fp) {
            fprintf(stderr, "无法打开日志文件 %s: %s\n", config.log_file, strerror(errno));
            cleanup();
            return EXIT_FAILURE;
        }
    } else {
        log_fp = stdout;
    }

    fprintf(stderr, "文件系统监控已启动...\n");
    
    // 主事件循环
    handle_events();

    // 清理资源
    cleanup();
    return EXIT_SUCCESS;
}

// 初始化fanotify
static int init_fanotify(void) {
    uint64_t fan_mask = FAN_OPEN | FAN_CLOSE | FAN_ACCESS | FAN_MODIFY;
    unsigned int init_flags = 0;
    
    // 如果需要权限控制，使用FAN_CLASS_CONTENT，否则使用FAN_CLASS_NOTIF
    if (config.kill_write_processes) {
        fan_mask |= FAN_OPEN_PERM | FAN_ACCESS_PERM;
        init_flags |= FAN_CLASS_CONTENT;
    } else {
        init_flags |= FAN_CLASS_NOTIF;
    }
    
    // 初始化fanotify
    fanotify_fd = fanotify_init(init_flags | FAN_CLOEXEC | FAN_NONBLOCK,
                               O_RDONLY | O_LARGEFILE);
    if (fanotify_fd == -1) {
        fprintf(stderr, "fanotify_init失败: %s (错误码: %d)\n", strerror(errno), errno);
        return -1;
    }

    // 初始化fd_set
    FD_ZERO(&rfds);
    FD_SET(fanotify_fd, &rfds);

    // 如果没有指定受保护目录，则监控根目录
    if (config.protected_dirs_count == 0) {
        // 监控根目录
        fprintf(stderr, "未指定受保护目录，将尝试监控根目录\n");
        fprintf(stderr, "警告: 监控根目录可能会产生大量事件，建议指定特定目录\n");
        
        if (add_watch(fanotify_fd, "/") != 0) {
            fprintf(stderr, "无法监控根目录，尝试监控常用目录\n");
            
            // 尝试监控一些常用目录
            int success = 0;
            const char *common_dirs[] = {
                "/home", "/var", "/tmp", "/opt", "/usr/local"
            };
            
            for (size_t i = 0; i < sizeof(common_dirs) / sizeof(common_dirs[0]); i++) {
                if (add_watch(fanotify_fd, common_dirs[i]) == 0) {
                    fprintf(stderr, "成功添加监控: %s\n", common_dirs[i]);
                    success = 1;
                }
            }
            
            if (!success) {
                fprintf(stderr, "无法监控任何目录\n");
                close(fanotify_fd);
                return -1;
            }
        }
    } else {
        // 监控指定的受保护目录
        int success = 0;
        for (int i = 0; i < config.protected_dirs_count; i++) {
            if (add_watch(fanotify_fd, config.protected_dirs[i]) == 0) {
                fprintf(stderr, "成功添加监控: %s\n", config.protected_dirs[i]);
                success = 1;
            } else {
                fprintf(stderr, "无法监控目录 %s\n", config.protected_dirs[i]);
            }
        }
        
        if (!success) {
            fprintf(stderr, "无法监控任何指定的目录\n");
            close(fanotify_fd);
            return -1;
        }
    }

    return 0;
}

// 添加监控
static int add_watch(int fanotify_fd, const char *path) {
    uint64_t fan_mask = FAN_OPEN | FAN_CLOSE | FAN_ACCESS | FAN_MODIFY;
    unsigned int mark_flags = FAN_MARK_ADD;
    
    // 添加目录和子目录标志
    fan_mask |= FAN_ONDIR;
    fan_mask |= FAN_EVENT_ON_CHILD;
    
    // 添加挂载点标志，以监控子目录
    mark_flags |= FAN_MARK_MOUNT;
    
    // 如果需要权限控制，添加权限事件
    if (config.kill_write_processes) {
        fan_mask |= FAN_OPEN_PERM | FAN_ACCESS_PERM;
    }
    
    // 添加监控标记
    if (fanotify_mark(fanotify_fd, mark_flags, fan_mask, AT_FDCWD, path) != 0) {
        if (config.verbose) {
            fprintf(stderr, "无法添加监控标记到 %s: %s (错误码: %d)\n", 
                    path, strerror(errno), errno);
        }
        
        // 尝试使用更基本的标志
        fan_mask = FAN_OPEN | FAN_CLOSE | FAN_ACCESS | FAN_MODIFY;
        if (fanotify_mark(fanotify_fd, mark_flags, fan_mask, AT_FDCWD, path) != 0) {
            fprintf(stderr, "无法添加基本监控标记到 %s: %s (错误码: %d)\n", 
                    path, strerror(errno), errno);
            return -1;
        }
    }
    
    return 0;
}

// 处理事件
static void handle_events(void) {
    char buf[4096];
    ssize_t len;
    
    while (1) {
        // 等待事件
        while (select(fanotify_fd + 1, &rfds, NULL, NULL, NULL) < 0) {
            if (errno != EINTR) {
                fprintf(stderr, "select失败: %s\n", strerror(errno));
                return;
            }
        }
        
        // 读取事件
        while ((len = read(fanotify_fd, buf, sizeof(buf))) > 0) {
            struct fanotify_event_metadata *metadata = (struct fanotify_event_metadata *)buf;
            
            // 处理所有事件
            while (FAN_EVENT_OK(metadata, len)) {
                // 检查版本
                if (metadata->vers < 2) {
                    fprintf(stderr, "内核fanotify版本太旧\n");
                    return;
                }
                
                // 处理事件
                process_event(metadata);
                
                // 移动到下一个事件
                metadata = FAN_EVENT_NEXT(metadata, len);
            }
        }
        
        if (len < 0 && errno != EAGAIN) {
            fprintf(stderr, "从fanotify读取失败: %s\n", strerror(errno));
            break;
        }
    }
}

// 处理权限事件
static int handle_perm(int fan_fd, struct fanotify_event_metadata *metadata, int allow) {
    struct fanotify_response response_struct = {
        .fd = metadata->fd,
        .response = allow ? FAN_ALLOW : FAN_DENY
    };
    
    int ret = write(fan_fd, &response_struct, sizeof(response_struct));
    if (ret < 0) {
        fprintf(stderr, "发送响应失败: %s\n", strerror(errno));
    }
    
    return (ret < 0) ? -1 : 0;
}

// 处理单个事件
static void process_event(struct fanotify_event_metadata *metadata) {
    char path[PATH_MAX];
    char fd_path[PATH_MAX];
    int fd = metadata->fd;
    EventInfo event_info = {0};
    int should_deny = 0;
    
    // 获取事件时间戳
    gettimeofday(&event_info.timestamp, NULL);
    
    // 获取进程ID
    event_info.pid = metadata->pid;
    
    // 获取进程名称
    get_process_name(metadata->pid, event_info.proc_name, sizeof(event_info.proc_name));
    
    // 获取文件路径
    if (fd >= 0) {
        snprintf(fd_path, sizeof(fd_path), "/proc/self/fd/%d", fd);
        ssize_t path_len = readlink(fd_path, path, sizeof(path) - 1);
        
        if (path_len != -1) {
            path[path_len] = '\0';
            strncpy(event_info.file_path, path, sizeof(event_info.file_path) - 1);
            event_info.file_path[sizeof(event_info.file_path) - 1] = '\0';
        } else {
            strcpy(event_info.file_path, "未知路径");
        }
    } else {
        strcpy(event_info.file_path, ".");
    }
    
    // 确定事件类型
    event_info.event_type = determine_event_type(metadata->mask);
    
    // 记录事件
    log_event(&event_info);
    
    // 检查是否需要杀死写入受保护目录的进程
    if (config.kill_write_processes && event_info.event_type == EVENT_WRITE) {
        for (int i = 0; i < config.protected_dirs_count; i++) {
            if (strncmp(event_info.file_path, config.protected_dirs[i], strlen(config.protected_dirs[i])) == 0) {
                fprintf(stderr, "检测到对受保护目录的写入操作，正在终止进程 %d (%s)\n", 
                        event_info.pid, event_info.proc_name);
                kill(event_info.pid, SIGTERM);
                should_deny = 1;
                break;
            }
        }
    }
    
    // 处理权限事件
    if (metadata->mask & (FAN_OPEN_PERM | FAN_ACCESS_PERM)) {
        handle_perm(fanotify_fd, metadata, !should_deny);
    }
    
    // 关闭文件描述符
    if (fd >= 0) {
        close(fd);
    }
}

// 确定事件类型
static EventType determine_event_type(uint64_t mask) {
    // 添加调试信息
    if (config.verbose) {
        fprintf(stderr, "事件掩码: 0x%lx\n", mask);
    }
    
    if (mask & (FAN_ACCESS | FAN_ACCESS_PERM)) {
        return EVENT_READ;
    } else if (mask & FAN_MODIFY) {
        return EVENT_WRITE;
    } else if (mask & FAN_CLOSE_WRITE) {
        return EVENT_WRITE;
    } else if (mask & FAN_OPEN_PERM) {
        return EVENT_READ;
    } else if (mask & FAN_OPEN) {
        return EVENT_READ;
    } else if (mask & FAN_CLOSE) {
        if (mask & FAN_CLOSE_WRITE) {
            return EVENT_WRITE;
        } else {
            return EVENT_READ;
        }
    } else {
        return EVENT_UNKNOWN;
    }
}

// 获取进程名称
static void get_process_name(pid_t pid, char *name, size_t size) {
    char proc_path[PATH_MAX];
    char buffer[1024];
    FILE *fp;
    
    snprintf(proc_path, sizeof(proc_path), "/proc/%d/comm", pid);
    fp = fopen(proc_path, "r");
    
    if (fp) {
        if (fgets(buffer, sizeof(buffer), fp) != NULL) {
            // 移除换行符
            buffer[strcspn(buffer, "\n")] = 0;
            // 使用安全的字符串复制
            strncpy(name, buffer, size - 1);
            name[size - 1] = '\0';
        } else {
            strncpy(name, "未知进程", size - 1);
            name[size - 1] = '\0';
        }
        fclose(fp);
    } else {
        strncpy(name, "未知进程", size - 1);
        name[size - 1] = '\0';
    }
}

// 记录事件
static void log_event(const EventInfo *event_info) {
    char time_str[64];
    struct tm *tm_info;
    char event_type_str[20];
    
    // 格式化时间
    tm_info = localtime(&event_info->timestamp.tv_sec);
    strftime(time_str, sizeof(time_str), "%Y-%m-%d %H:%M:%S", tm_info);
    
    // 确定事件类型字符串
    switch (event_info->event_type) {
        case EVENT_READ:
            strcpy(event_type_str, "读取");
            break;
        case EVENT_WRITE:
            strcpy(event_type_str, "写入");
            break;
        case EVENT_DELETE:
            strcpy(event_type_str, "删除");
            break;
        case EVENT_RENAME:
            strcpy(event_type_str, "重命名");
            break;
        default:
            strcpy(event_type_str, "未知");
            break;
    }
    
    // 输出日志
    fprintf(log_fp, "[%s.%03ld] 进程ID: %d, 进程名: %s, 操作: %s, 文件: %s\n",
            time_str, event_info->timestamp.tv_usec / 1000,
            event_info->pid, event_info->proc_name,
            event_type_str, event_info->file_path);
    fflush(log_fp);
    
    // 如果启用了详细模式，也输出到标准错误
    if (config.verbose && log_fp != stdout) {
        fprintf(stderr, "[%s.%03ld] 进程ID: %d, 进程名: %s, 操作: %s, 文件: %s\n",
                time_str, event_info->timestamp.tv_usec / 1000,
                event_info->pid, event_info->proc_name,
                event_type_str, event_info->file_path);
    }
}

// 设置信号处理
static void setup_signal_handlers(void) {
    struct sigaction sa;
    
    sa.sa_handler = signal_handler;
    sigemptyset(&sa.sa_mask);
    sa.sa_flags = SA_RESTART;
    
    sigaction(SIGINT, &sa, NULL);
    sigaction(SIGTERM, &sa, NULL);
}

// 信号处理函数
static void signal_handler(int sig) {
    fprintf(stderr, "接收到信号 %d，正在退出...\n", sig);
    cleanup();
    exit(EXIT_SUCCESS);
}

// 清理资源
static void cleanup(void) {
    if (fanotify_fd != -1) {
        close(fanotify_fd);
        fanotify_fd = -1;
    }
    
    if (log_fp && log_fp != stdout) {
        fclose(log_fp);
        log_fp = NULL;
    }
    
    // 释放受保护目录列表
    if (config.protected_dirs) {
        for (int i = 0; i < config.protected_dirs_count; i++) {
            free(config.protected_dirs[i]);
        }
        free(config.protected_dirs);
        config.protected_dirs = NULL;
    }
    
    if (config.log_file) {
        free(config.log_file);
        config.log_file = NULL;
    }
}

// 解析配置
static int parse_config(int argc, char *argv[]) {
    int opt;
    
    // 初始化配置
    config.protected_dirs = NULL;
    config.protected_dirs_count = 0;
    config.kill_write_processes = 0;
    config.verbose = 0;
    config.log_file = NULL;
    
    while ((opt = getopt(argc, argv, "p:kl:vh")) != -1) {
        switch (opt) {
            case 'p': {
                // 添加受保护目录
                config.protected_dirs_count++;
                config.protected_dirs = realloc(config.protected_dirs, 
                                              config.protected_dirs_count * sizeof(char *));
                if (!config.protected_dirs) {
                    fprintf(stderr, "内存分配失败\n");
                    return -1;
                }
                config.protected_dirs[config.protected_dirs_count - 1] = strdup(optarg);
                break;
            }
            case 'k':
                // 启用杀死写入受保护目录的进程
                config.kill_write_processes = 1;
                break;
            case 'l':
                // 设置日志文件
                config.log_file = strdup(optarg);
                break;
            case 'v':
                // 启用详细输出
                config.verbose = 1;
                break;
            case 'h':
                // 显示帮助
                return -1;
            default:
                return -1;
        }
    }
    
    return 0;
}

// 显示帮助
static void print_help(void) {
    printf("用法: fanotify_monitor [选项]\n");
    printf("选项:\n");
    printf("  -p <path>  添加受保护目录 (可多次使用)\n");
    printf("  -k         启用杀死写入受保护目录的进程\n");
    printf("  -l <file>  指定日志文件 (默认为标准输出)\n");
    printf("  -v         启用详细输出\n");
    printf("  -h         显示此帮助信息\n");
} 