# PSFSMON-L 熵值API增强功能文档

## 概述

根据用户需求，本次更新大幅扩展了PSFSMON-L的统计信息和API接口，特别是重点进程的读熵、写熵信息计算和输出。改进后的系统提供了更详细、更细化的查询接口和全面的测试覆盖。

## 主要改进

### 1. 扩展了文件事件统计结构 (FileEventStats)

**新增统计字段：**

#### 详细事件类型统计
- `read_events` - 读事件数量
- `write_events` - 写事件数量
- `open_events` - 打开事件数量
- `close_events` - 关闭事件数量
- `access_events` - 访问事件数量
- `modify_events` - 修改事件数量
- `create_events` - 创建事件数量
- `delete_events` - 删除事件数量
- `move_events` - 移动/重命名事件数量
- `permission_events` - 权限检查事件数量

#### 按事件类型的熵值计算统计
- `read_entropy_calculations` - 读操作熵值计算次数
- `write_entropy_calculations` - 写操作熵值计算次数
- `open_entropy_calculations` - 打开操作熵值计算次数
- `close_entropy_calculations` - 关闭操作熵值计算次数

#### 重点进程相关统计
- `focus_process_files` - 重点进程操作的文件数量
- `focus_process_unique_files` - 重点进程操作的唯一文件数量

### 2. 增强了API接口

#### 2.1 扩展现有API `/api/file/stats`

**新增输出字段：**
```json
{
  "total_events": 1250,
  "fanotify_events": 890,
  "focus_process_events": 156,
  "entropy_calculations": 89,
  
  // 新增：详细事件类型统计
  "read_events": 234,
  "write_events": 178,
  "open_events": 456,
  "close_events": 445,
  "access_events": 123,
  "modify_events": 67,
  "create_events": 45,
  "delete_events": 23,
  "move_events": 12,
  "permission_events": 89,
  
  // 新增：按事件类型的熵值计算统计
  "read_entropy_calculations": 45,
  "write_entropy_calculations": 34,
  "open_entropy_calculations": 67,
  "close_entropy_calculations": 56,
  
  // 新增：重点进程相关统计
  "focus_process_files": 234,
  "focus_process_unique_files": 156
}
```

#### 2.2 新增API `/api/focus-processes/entropy-stats`

**专门用于重点进程详细熵值统计：**
```json
{
  "global_file_stats": {
    "total_events": 1250,
    "focus_process_events": 156,
    "entropy_calculations": 89,
    "read_entropy_calculations": 45,
    "write_entropy_calculations": 34
  },
  "focus_processes_summary": {
    "total_focus_processes": 3,
    "active_processes": 2,
    "total_files_monitored": 234
  },
  "detailed_process_stats": [
    {
      "pid": 1234,
      "process_name": "important_app",
      "read_entropy_count": 23,
      "write_entropy_count": 18,
      "total_read_entropy": 45.67,
      "total_write_entropy": 38.92,
      "average_read_entropy": 1.986,
      "average_write_entropy": 2.162,
      "files_count": 45,
      "last_activity": 1672531200
    }
  ]
}
```

#### 2.3 支持按PID查询特定重点进程

**查询特定进程的详细统计：**
```bash
GET /api/focus-processes/entropy-stats?pid=1234
```

### 3. 统一的测试体系

#### 3.1 完善的Makefile集成

**新增测试目标：**
```makefile
# 编译目标
all                   # 编译所有测试程序
test_api_comprehensive # 主要综合测试程序
test_comprehensive_entropy_api # 熵值API测试程序
entropy_test_program  # 熵值测试程序
protected_path_violator # 受保护路径违规测试程序

# 测试目标
test                  # 运行完整综合测试
test-entropy          # 运行熵值专项测试
test-comprehensive    # 运行全面综合测试
test-basic            # 运行基础功能测试
test-focus-process    # 运行重点进程专项测试
test-protected-path   # 运行受保护路径测试
test-shell            # 运行Shell脚本测试
test-quick            # 快速测试(无交互)
```

#### 3.2 优化的API测试程序

**新的test_api_comprehensive.cpp特性：**

1. **预定义API端点测试**
   - 17个预定义API端点
   - 自动HTTP请求和JSON验证
   - 详细的性能统计

2. **分类测试支持**
   - health: 健康检查和状态API
   - process: 进程相关API
   - network: 网络相关API
   - stats: 系统统计API
   - config: 配置管理API
   - process-mgmt: 进程管理API
   - focus-process: 重点进程API
   - file-monitor: 文件监控API
   - path-mgmt: 路径管理API
   - realtime: 实时数据API

3. **增强的测试报告**
   - 分类统计
   - 性能分析
   - 详细的错误信息
   - API端点成功率

#### 3.3 专门的熵值测试程序

**entropy_test_program.cpp功能：**
- 多种文件大小测试（50MB、100MB、200MB、1GB）
- 分块读取测试（避免内存问题）
- 100MB文件大小限制验证
- 详细的操作日志
- 灵活的命令行参数

### 4. 测试使用方法

#### 4.1 编译所有测试程序
```bash
cd test
make all
```

#### 4.2 运行基础API测试
```bash
# 基础功能测试
make test-basic

# 或者直接运行
./test_api_comprehensive --test health,process,network,stats
```

#### 4.3 运行重点进程熵值专项测试
```bash
# 完整的重点进程测试
make test-focus-process

# 或者分别测试
./test_api_comprehensive --test focus-process
./test_api_comprehensive --test entropy-test
```

#### 4.4 运行受保护路径测试
```bash
# 受保护路径违规测试
make test-protected-path

# 或者直接运行
./test_api_comprehensive --test protected-path
```

#### 4.5 运行全面综合测试
```bash
# 所有API和功能测试
make test-comprehensive

# 或者
./test_api_comprehensive --all
```

## 技术实现细节

### 1. 统计数据收集

- **原子操作**：所有统计计数器使用`std::atomic`确保线程安全
- **事件分类**：根据fanotify事件掩码自动分类计数
- **熵值关联**：每次熵值计算时同步更新对应类型的计数器

### 2. API接口设计

- **RESTful设计**：遵循REST接口设计原则
- **JSON格式**：所有响应使用JSON格式，便于解析和处理
- **错误处理**：统一的错误响应格式
- **兼容性**：保持向后兼容，扩展而不破坏现有接口

### 3. 测试框架特性

- **模块化设计**：每个测试功能独立，便于维护
- **自动化验证**：自动验证API响应格式和内容
- **性能监控**：记录响应时间和性能统计
- **详细报告**：提供全面的测试结果分析

## 故障排除

### 1. 熵值计算未增加

**可能原因：**
- 重点进程未正确添加
- 文件监控路径未覆盖测试目录
- fanotify权限不足

**解决方法：**
1. 检查重点进程列表：`GET /api/focus-processes`
2. 确认文件监控配置：`GET /api/config`
3. 验证服务权限：确保以root权限运行

### 2. API测试失败

**可能原因：**
- 服务未启动或端口不对
- 权限不足
- 依赖库缺失

**解决方法：**
1. 确认服务状态：`curl http://localhost:8080/api/health`
2. 检查依赖：`make check-deps`
3. 安装依赖：`make install-deps`

### 3. 测试程序编译失败

**解决方法：**
```bash
# 清理并重新编译
make clean
make check-deps
make install-deps  # 如果需要
make all
```

## 总结

本次更新显著增强了PSFSMON-L的统计能力和API接口，特别是：

1. **细粒度统计**：从单一的熵值计算计数扩展到按事件类型的详细统计
2. **专项API**：新增专门的重点进程熵值统计API
3. **完整测试**：建立了全面的测试体系，确保功能正确性
4. **易用性**：通过Makefile统一管理，简化了测试流程

这些改进为重点进程的文件监控和熵值分析提供了强大而细致的支持，满足了深度监控和分析的需求。 