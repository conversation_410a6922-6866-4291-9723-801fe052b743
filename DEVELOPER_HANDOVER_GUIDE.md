# PSFSMON-L 开发人员接手指南

## 🎯 快速开始

### 第一天：环境搭建和项目理解
1. **克隆项目并编译**
   ```bash
   git clone <repository-url>
   cd psfsmonL
   make clean && make -j$(nproc)
   ```

2. **运行基础测试**
   ```bash
   # 检查编译产物
   ls -la bin/psfsmon
   
   # 运行健康检查
   sudo ./bin/psfsmon --help
   
   # 运行API测试
   cd test && ./test_api_with_curl.sh
   ```

3. **阅读核心文档**
   - `PROJECT_ARCHITECTURE_ANALYSIS.md` - 完整架构分析
   - `REQUIREMENTS.md` - 19项功能需求
   - `DESIGN.md` - 双Fanotify Group设计

### 第一周：核心功能理解
1. **理解双Fanotify Group架构**（最重要）
2. **掌握重点进程监控和熵值计算逻辑**
3. **熟悉受保护路径防护机制**
4. **了解API接口设计和实现**

---

## 🏗️ 核心架构速览

### 项目特色（必须理解）
```
双Fanotify Group策略 = 受保护路径监控Group + 重点进程监控Group
├── 受保护路径Group: 权限检查，KILL/LOG模式处理违规
└── 重点进程Group: 全局监控，文件熵值计算和变化检测
```

### 关键类层次结构
```
MainMonitor (主控制器)
├── ProcessMonitor (进程监控)
├── FileMonitor (文件监控 - 核心创新)
│   ├── protected_fanotify_fd_ (受保护路径Group)
│   └── focus_fanotify_fd_ (重点进程Group)
├── NetworkMonitor (网络监控)
└── ApiServer (API服务)
```

### 数据流向
```
Linux内核事件 → Fanotify → FileMonitor → 事件分类处理
├── 受保护路径事件 → ProtectedPathManager → 违规处理
└── 重点进程事件 → FocusProcessManager → 熵值计算
```

---

## 🔧 开发环境配置

### 必需工具
```bash
# 编译工具
sudo apt-get install build-essential g++ make

# 开发库
sudo apt-get install libc6-dev linux-headers-$(uname -r)

# 调试工具
sudo apt-get install gdb valgrind strace

# 可选：性能分析
sudo apt-get install perf linux-tools-generic
```

### IDE配置建议
- **VSCode**: 推荐使用C/C++扩展
- **CLion**: 支持CMake项目导入
- **Vim/Neovim**: 配置coc.nvim或LSP

### 调试配置
```bash
# 编译调试版本
make debug

# 使用GDB调试
sudo gdb ./bin/psfsmon
(gdb) set args --debug --verbose
(gdb) run

# 内存检查
make sanitizer
sudo ./bin/psfsmon --debug
```

---

## 📚 核心概念深入理解

### 1. 双Fanotify Group架构（项目核心）

**为什么需要双Group？**
- 单一Group无法同时处理权限检查和信息收集
- 避免事件冲突和性能瓶颈
- 实现功能分离和模块化设计

**Group 1: 受保护路径监控**
```cpp
// 初始化受保护路径Group
protected_fanotify_fd_ = fanotify_init(
    FAN_CLASS_NOTIF | FAN_CLOEXEC | FAN_NONBLOCK,
    O_RDONLY | O_LARGEFILE | O_NONBLOCK | O_CLOEXEC
);

// 监控事件类型
FAN_MODIFY | FAN_CLOSE_WRITE | FAN_EVENT_ON_CHILD
```

**Group 2: 重点进程监控**
```cpp
// 初始化重点进程Group
focus_fanotify_fd_ = fanotify_init(
    FAN_CLASS_NOTIF | FAN_CLOEXEC | FAN_NONBLOCK,
    O_RDONLY | O_LARGEFILE | O_NONBLOCK
);

// 监控事件类型
FAN_OPEN | FAN_CLOSE_WRITE  // 用于熵值计算
```

### 2. 文件熵值计算逻辑（重点中的重点）

**Shannon熵值公式**:
```
H(X) = -Σ p(xi) * log2(p(xi))
```

**关键时机控制**:
```cpp
// FAN_OPEN: 读取时获取一次原始熵值
if (!file_info.has_original_entropy) {
    file_info.original_entropy = calculateEntropy(file_path);
    file_info.has_original_entropy = true;  // 确保只计算一次
}

// FAN_CLOSE_WRITE: 写入完成后可多次更新
file_info.final_entropy = calculateEntropy(file_path);  // 每次都更新
file_info.entropy_change = final_entropy - original_entropy;
```

**为什么这样设计？**
- 原始熵值反映文件的初始状态，只需计算一次
- 最终熵值反映文件的当前状态，每次写入都可能改变
- 熵值变化量用于检测文件内容的随机性变化（恶意软件检测）

### 3. 受保护路径防护机制

**KILL模式处理流程**:
```cpp
// 优雅终止策略
kill(pid, SIGTERM);                    // 先发送SIGTERM
std::this_thread::sleep_for(100ms);    // 等待100ms
if (kill(pid, 0) == 0) {               // 检查进程是否还存在
    kill(pid, SIGKILL);                // 强制终止
}
```

**LOG模式**:
- 仅记录违规行为，不终止进程
- 用于审计和分析，不影响系统运行

---

## 🔍 关键代码位置

### 核心文件优先级
1. **src/file_monitor.cpp** - 双Fanotify Group实现（最重要）
2. **src/main_monitor.cpp** - 主控制器逻辑
3. **src/process_monitor.cpp** - 进程管理
4. **src/psfsmon_impl.cpp** - 数据结构实现
5. **include/psfsmon.h** - 所有数据结构定义

### 关键函数位置
```cpp
// 双Group初始化
FileMonitor::initializeProtectedPathGroup()  // line ~320
FileMonitor::initializeFocusProcessGroup()   // line ~345

// 事件处理循环
FileMonitor::processProtectedPathEvents()    // line ~450
FileMonitor::processFocusProcessEvents()     // line ~550

// 熵值计算
FocusProcessFileInfo::recordFileOpen()       // line ~108
FocusProcessFileInfo::recordFileCloseWrite() // line ~148

// 违规处理
ProtectedPathManager::handleViolation()     // line ~61
```

### API端点实现
```cpp
// 基础API
ApiServer::handleGetStats()           // src/api_handlers_basic.cpp:24
ApiServer::handleGetProcesses()       // src/api_server.cpp:200
ApiServer::handleGetProcessTree()     // src/api_server.cpp:260

// 重点进程API
ApiServer::handleGetFocusProcesses()  // src/api_server.cpp:400
ApiServer::handleGetFocusProcessFileInfo() // src/api_server.cpp:450
```

---

## 🐛 常见开发问题和解决方案

### 1. 编译问题
```bash
# 问题：找不到头文件
# 解决：检查include路径
make clean && make CPPFLAGS="-I/usr/include/linux"

# 问题：链接错误
# 解决：检查库依赖
ldd bin/psfsmon
```

### 2. 运行时问题
```bash
# 问题：fanotify_init失败
# 原因：权限不足或内核不支持
# 解决：
sudo ./bin/psfsmon  # 使用root权限
uname -r            # 检查内核版本 >= 4.19

# 问题：API服务器启动失败
# 原因：端口被占用
# 解决：
netstat -tlnp | grep 8080  # 检查端口占用
./bin/psfsmon --api-port 8081  # 使用其他端口
```

### 3. 调试技巧
```bash
# 启用详细日志
sudo ./bin/psfsmon --debug --verbose

# 使用strace跟踪系统调用
sudo strace -e trace=fanotify_init,fanotify_mark ./bin/psfsmon

# 检查fanotify事件
sudo cat /proc/$(pgrep psfsmon)/fd/  # 查看文件描述符
```

---

## 📝 开发规范

### 代码风格
```cpp
// 类名：PascalCase
class FileMonitor {
private:
    int protected_fanotify_fd_;  // 私有成员以下划线结尾
    std::mutex mutex_;           // 互斥锁命名
    
public:
    bool initialize();           // 函数名：camelCase
    void processEvents();        // 动词开头
};

// 常量：UPPER_CASE
const int MAX_EVENTS = 1024;
const std::string DEFAULT_CONFIG_PATH = "/etc/psfsmon/psfsmon.conf";
```

### 错误处理
```cpp
// 使用RAII和异常安全
try {
    std::lock_guard<std::mutex> lock(mutex_);
    // 关键操作
} catch (const std::exception& e) {
    LogManager::getInstance().error("操作失败: " + std::string(e.what()));
    return false;
}
```

### 日志规范
```cpp
// 日志级别使用
LogManager::getInstance().debug("调试信息");    // 开发调试
LogManager::getInstance().info("正常信息");     // 重要状态
LogManager::getInstance().warning("警告信息");  // 潜在问题
LogManager::getInstance().error("错误信息");    // 严重错误
```

---

## 🚀 扩展开发指南

### 添加新的监控功能
1. **创建新的Monitor类**
   ```cpp
   class CustomMonitor {
   public:
       bool initialize();
       bool start();
       void stop();
   private:
       std::thread monitor_thread_;
       void monitorLoop();
   };
   ```

2. **集成到MainMonitor**
   ```cpp
   // 在MainMonitor中添加
   std::unique_ptr<CustomMonitor> custom_monitor_;
   
   // 在initialize()中初始化
   custom_monitor_.reset(new CustomMonitor());
   custom_monitor_->initialize();
   ```

### 扩展API接口
1. **添加新的API处理函数**
   ```cpp
   void ApiServer::handleCustomApi(const HttpRequest& request, HttpResponse& response) {
       // 实现API逻辑
   }
   ```

2. **注册路由**
   ```cpp
   // 在registerRoutes()中添加
   routes_["/api/custom"] = [this](const HttpRequest& req, HttpResponse& res) {
       handleCustomApi(req, res);
   };
   ```

### 添加配置项
1. **在MonitorConfig中添加字段**
   ```cpp
   struct MonitorConfig {
       bool enable_custom_monitoring;
       std::string custom_config_path;
   };
   ```

2. **在配置解析中处理**
   ```cpp
   // 在ConfigManager中添加解析逻辑
   ```

---

## 📊 性能优化建议

### 1. 内存优化
- 使用`std::move`避免不必要的拷贝
- 合理使用智能指针，避免内存泄露
- 定期清理过期数据

### 2. CPU优化
- 避免在锁内进行耗时操作
- 使用原子操作替代锁（适当情况下）
- 合理设置扫描间隔

### 3. I/O优化
- 批量处理文件操作
- 使用非阻塞I/O
- 合理设置缓冲区大小

---

## 🧪 测试策略

### 单元测试
```cpp
// 测试熵值计算
TEST(FileEntropyCalculator, BasicEntropy) {
    double entropy = FileEntropyCalculator::calculateFileEntropy("/dev/urandom");
    EXPECT_GT(entropy, 7.0);  // 随机数据熵值应该很高
}
```

### 集成测试
```bash
# API测试
curl -s http://localhost:8080/api/health | jq .

# 功能测试
echo "test" > /tmp/test_file
# 检查是否正确监控到文件操作
```

### 压力测试
```bash
# 大量文件操作
for i in {1..1000}; do
    echo "test $i" > /tmp/test_$i.txt
done

# 检查系统资源使用
top -p $(pgrep psfsmon)
```

---

## 📞 支持和联系

### 问题排查流程
1. 查看日志文件：`/var/log/psfsmon/psfsmon.log`
2. 检查系统兼容性：内核版本、权限等
3. 运行调试模式：`--debug --verbose`
4. 查阅相关文档和源代码注释

### 文档资源
- 项目架构分析：`PROJECT_ARCHITECTURE_ANALYSIS.md`
- 功能需求文档：`REQUIREMENTS.md`
- 设计文档：`DESIGN.md`
- 源代码注释：详细的内联注释

---

---

## 🎓 学习路径建议

### 新手开发者（0-2年经验）
**第1周：基础理解**
- 熟悉Linux系统编程基础
- 理解fanotify机制原理
- 掌握C++11基本特性

**第2周：项目架构**
- 深入理解双Fanotify Group设计
- 掌握线程安全编程
- 学习项目的设计模式应用

**第3-4周：实践开发**
- 修复简单bug
- 添加日志和调试信息
- 编写单元测试

### 有经验开发者（2+年经验）
**第1-2天：快速上手**
- 阅读架构文档和核心代码
- 理解业务逻辑和技术选型
- 搭建开发环境

**第3-5天：深入理解**
- 掌握核心算法（熵值计算）
- 理解性能优化策略
- 熟悉API设计

**第2周：贡献代码**
- 优化现有功能
- 添加新特性
- 改进文档

---

## 🔧 实用开发工具

### Makefile目标说明
```bash
make clean          # 清理编译产物
make release        # 编译发布版本（默认）
make debug          # 编译调试版本（-g -O0）
make profile        # 编译性能分析版本（-pg）
make sanitizer      # 编译内存检查版本（-fsanitize=address）
make valgrind       # 编译Valgrind兼容版本
make install        # 安装到系统目录
make uninstall      # 从系统目录卸载
```

### 有用的调试命令
```bash
# 查看fanotify文件描述符
sudo ls -la /proc/$(pgrep psfsmon)/fd/

# 监控系统调用
sudo strace -p $(pgrep psfsmon) -e trace=fanotify_init,fanotify_mark,read

# 查看内存使用
sudo pmap $(pgrep psfsmon)

# 监控文件操作
sudo lsof -p $(pgrep psfsmon)

# 查看线程状态
ps -T -p $(pgrep psfsmon)
```

### 性能分析工具
```bash
# CPU性能分析
sudo perf record -p $(pgrep psfsmon)
sudo perf report

# 内存泄露检查
valgrind --tool=memcheck --leak-check=full ./bin/psfsmon

# 线程竞争检查
valgrind --tool=helgrind ./bin/psfsmon
```

---

## 📋 开发检查清单

### 代码提交前检查
- [ ] 代码编译无警告
- [ ] 通过所有单元测试
- [ ] 内存检查无泄露
- [ ] 添加必要的日志
- [ ] 更新相关文档
- [ ] 代码风格符合规范

### 功能开发检查
- [ ] 理解需求和设计
- [ ] 考虑线程安全
- [ ] 处理异常情况
- [ ] 添加配置选项
- [ ] 提供API接口
- [ ] 编写测试用例

### 性能优化检查
- [ ] 避免不必要的内存分配
- [ ] 减少锁的持有时间
- [ ] 优化热点代码路径
- [ ] 合理设置缓冲区大小
- [ ] 使用合适的数据结构

---

## 🚨 紧急问题处理

### 系统崩溃处理
1. **收集崩溃信息**
   ```bash
   # 查看系统日志
   dmesg | tail -50
   journalctl -u psfsmon -n 50

   # 查看核心转储
   ls -la /var/crash/
   gdb bin/psfsmon core
   ```

2. **快速恢复**
   ```bash
   # 停止服务
   sudo systemctl stop psfsmon

   # 检查系统状态
   sudo systemctl status psfsmon

   # 重启服务
   sudo systemctl start psfsmon
   ```

### 性能问题处理
1. **识别瓶颈**
   ```bash
   # CPU使用率
   top -p $(pgrep psfsmon)

   # 内存使用
   cat /proc/$(pgrep psfsmon)/status | grep -E "VmRSS|VmSize"

   # I/O统计
   iotop -p $(pgrep psfsmon)
   ```

2. **临时优化**
   ```bash
   # 调整扫描间隔
   # 修改配置文件中的update_interval_ms

   # 减少监控范围
   # 临时移除部分受保护路径或重点进程
   ```

### API服务异常处理
1. **检查服务状态**
   ```bash
   curl -s http://localhost:8080/api/health
   netstat -tlnp | grep 8080
   ```

2. **重启API服务**
   ```bash
   # 发送SIGUSR1信号重新加载配置
   sudo kill -USR1 $(pgrep psfsmon)
   ```

---

## 📚 深入学习资源

### Linux系统编程
- 《Unix环境高级编程》- 系统编程基础
- 《Linux系统编程》- Linux特定功能
- fanotify(7) man页面 - 官方文档

### C++现代编程
- 《Effective Modern C++》- C++11/14最佳实践
- 《C++并发编程实战》- 多线程编程
- 《C++性能优化指南》- 性能调优

### 网络编程
- 《Unix网络编程》- 网络编程经典
- 《TCP/IP详解》- 网络协议深入理解

### 安全监控
- 《恶意软件分析实战》- 恶意软件检测
- 《Linux安全技术》- Linux安全机制
- Shannon熵值理论 - 信息论基础

---

## 🎯 项目发展方向

### 短期目标（1-3个月）
- 优化性能，减少CPU和内存占用
- 增强API功能，提供更丰富的数据
- 改进错误处理和日志记录
- 完善测试覆盖率

### 中期目标（3-6个月）
- 支持更多Linux发行版
- 添加Web管理界面
- 实现配置热重载
- 增加更多安全检测算法

### 长期目标（6个月+）
- 支持分布式部署
- 机器学习异常检测
- 与SIEM系统集成
- 云原生支持

---

**祝你在PSFSMON-L项目开发中取得成功！**

记住：理解双Fanotify Group架构是掌握这个项目的关键。如果有任何疑问，请仔细阅读相关源代码和注释。

**重要提醒**：在修改核心监控逻辑时，请务必在测试环境中充分验证，避免影响生产系统的稳定性。
