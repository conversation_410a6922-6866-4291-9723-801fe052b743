# PSFSMON-L 项目架构分析报告

## 📋 文档概述

本文档是为新接手开发人员准备的完整项目架构分析报告，详细解析了PSFSMON-L（Linux进程安全文件系统监控程序）的设计架构、核心类功能、调用关系和开发指南。

**文档版本**: 1.0  
**创建日期**: 2025-07-05  
**适用版本**: PSFSMON-L 双Fanotify Group架构版本  

---

## 🎯 项目总体概述

### 项目定位
PSFSMON-L是一个Linux系统安全监控程序，基于**双Fanotify Group + 双线程监控架构**，实现实时的进程、文件系统和网络监控功能。

### 核心特色
- **双Fanotify Group策略**：分离受保护路径权限检查和重点进程文件监控
- **重点进程文件操作行为分析**：基于Shannon熵值计算的文件变化检测
- **受保护路径防护**：支持KILL/LOG模式的路径保护机制
- **跨内核版本兼容**：支持Linux 4.19+内核，4.20+优化支持

### 技术规格
- **开发语言**: C++11标准
- **平台支持**: Linux内核 >= 4.19，支持x64/amd64、aarch64/arm64
- **编译方式**: 支持Makefile和CMake双编译方式
- **运行模式**: 支持直接执行和守护进程服务模式

---

## 🏗️ 总体架构设计

### 架构模式
项目采用**分层模块化架构**，主要包含以下层次：

1. **应用层**: main.cpp程序入口，命令行参数处理
2. **控制层**: MainMonitor主监控器，统一管理各子模块
3. **监控层**: ProcessMonitor、FileMonitor、NetworkMonitor三大监控器
4. **管理层**: 各种Manager单例类，负责特定功能管理
5. **数据层**: 各种Info和Event数据结构
6. **工具层**: Utils、LogManager等工具类

### 设计模式应用
- **单例模式**: 所有Manager类（FocusProcessManager、ProtectedPathManager等）
- **观察者模式**: FileMonitor的事件回调机制
- **工厂模式**: ProcessInfo的创建和管理
- **RAII模式**: 资源管理和异常安全保证

---

## 🔧 核心类功能详解

### 1. MainMonitor - 主监控器
**职责**: 系统的核心控制器，负责统一管理所有子监控器

**核心功能**:
- 初始化和启动各个子监控器
- 统一的配置管理和状态监控
- 性能统计数据汇总
- 信号处理和优雅关闭
- PID文件管理

**关键方法**:
```cpp
bool initialize(const MonitorConfig& config);  // 初始化配置
bool start();                                  // 启动所有监控器
void stop();                                   // 停止所有监控器
MonitorStats getStats() const;                 // 获取统计信息
```

### 2. FileMonitor - 文件监控器（核心模块）
**职责**: 实现双Fanotify Group监控架构，这是项目的核心创新

**双Group架构**:
- **受保护路径监控Group**: 使用`protected_fanotify_fd_`监控特定路径的违规访问
- **重点进程监控Group**: 使用`focus_fanotify_fd_`全局监控重点进程的文件操作

**核心功能**:
- 双线程事件处理（`protected_monitor_thread_`、`focus_monitor_thread_`）
- 文件熵值计算和变化检测
- 受保护路径违规检测和处理
- 内核版本自适应（4.19基础版本 vs 4.20+优化版本）

**关键方法**:
```cpp
bool initializeProtectedPathGroup();          // 初始化受保护路径Group
bool initializeFocusProcessGroup();           // 初始化重点进程Group
void processProtectedPathEvents();            // 处理受保护路径事件
void processFocusProcessEvents();             // 处理重点进程事件
```

### 3. ProcessMonitor - 进程监控器
**职责**: 管理系统中所有进程的信息收集和状态跟踪

**核心功能**:
- 进程生命周期管理（新建、更新、退出）
- 进程树构建和维护
- CPU、内存使用率计算
- 重点进程标记和管理
- 智能扫描策略（全量扫描 + 增量更新）

**关键方法**:
```cpp
void scanProcesses();                          // 扫描进程列表
void handleNewProcess(pid_t pid);              // 处理新进程
void handleProcessExit(pid_t pid);             // 处理进程退出
std::shared_ptr<ProcessTreeNode> buildProcessTree(); // 构建进程树
```

### 4. NetworkMonitor - 网络监控器
**职责**: 监控系统网络连接状态，建立网络连接与进程的关联

**核心功能**:
- 双接口网络监控（Netlink诊断 + /proc文件系统）
- 网络连接与进程的inode映射
- TCP/UDP连接统计
- 监听端口检测
- 性能优化的分层扫描策略

**关键方法**:
```cpp
bool initializeNetlinkDiag();                 // 初始化Netlink诊断接口
void updateConnections();                     // 更新网络连接
void updateProcessSocketMapping();            // 更新进程套接字映射
```

### 5. ApiServer - API服务器
**职责**: 提供RESTful API接口，对外输出监控数据

**核心功能**:
- HTTP服务器管理
- 25个API端点的路由处理
- JSON数据序列化
- 错误处理和状态码管理
- 线程安全的数据访问

**API分类**:
- 基础API: `/api/health`, `/api/stats`, `/api/monitor/status`
- 进程API: `/api/processes`, `/api/process-tree`, `/api/process/{pid}`
- 网络API: `/api/network`, `/api/network/stats`
- 重点进程API: `/api/focus-processes/*`
- 管理API: `/api/exclusions`, `/api/protected-paths`

---

## 📊 数据结构设计

### ProcessInfo - 进程信息结构
```cpp
struct ProcessInfo {
    pid_t pid, ppid;                           // 进程ID和父进程ID
    std::string process_name, executable_path; // 进程名和可执行文件路径
    double cpu_usage;                          // CPU使用率
    uint64_t memory_usage, virtual_memory;     // 内存使用量
    bool is_focus_process;                     // 重点进程标记
    std::unique_ptr<FocusProcessFileInfo> focus_file_info; // 重点进程文件信息
    std::vector<NetworkConnection> network_connections;    // 网络连接
};
```

### FileEntropyInfo - 文件熵值信息
```cpp
struct FileEntropyInfo {
    std::string file_path;                     // 文件路径
    uint64_t file_hash;                        // 文件哈希
    double original_entropy, final_entropy;    // 原始和最终熵值
    double entropy_change;                     // 熵值变化量
    bool has_original_entropy, has_final_entropy; // 熵值状态标记
    time_t open_time, close_time;              // 打开和关闭时间
    size_t file_size;                          // 文件大小
};
```

### NetworkConnection - 网络连接信息
```cpp
struct NetworkConnection {
    std::string protocol;                      // 协议类型
    std::string local_address, remote_address; // 本地和远程地址
    uint16_t local_port, remote_port;          // 本地和远程端口
    std::string state;                         // 连接状态
    pid_t pid;                                 // 关联进程ID
    unsigned long inode;                       // 套接字inode
};
```

---

## 🔄 关键业务流程

### 文件熵值计算流程（重点进程监控核心）
1. **FAN_OPEN事件**: 重点进程打开文件时
   - 检查`has_original_entropy`标记
   - 如果未计算过，计算Shannon熵值并设置`original_entropy`
   - 设置`has_original_entropy = true`，确保只计算一次

2. **FAN_CLOSE_WRITE事件**: 重点进程写入并关闭文件时
   - 每次都重新计算文件熵值，更新`final_entropy`
   - 计算`entropy_change = final_entropy - original_entropy`
   - 更新`close_time`时间戳
   - 支持多次写入更新

### 受保护路径违规处理流程
1. **路径检查**: 检查访问路径是否在受保护路径列表中
2. **进程排除检查**: 检查进程是否在排除列表中
3. **违规处理**:
   - **KILL模式**: 先发送SIGTERM，100ms后检查，必要时发送SIGKILL
   - **LOG模式**: 记录违规行为到日志
4. **统计更新**: 更新违规次数和时间统计

---

## 🧵 线程模型

### 主要线程
1. **主线程**: 程序入口，初始化和信号处理
2. **进程监控线程**: `ProcessMonitor::monitorLoop()`
3. **受保护路径监控线程**: `FileMonitor::processProtectedPathEvents()`
4. **重点进程监控线程**: `FileMonitor::processFocusProcessEvents()`
5. **网络监控线程**: `NetworkMonitor::monitorLoop()`
6. **API服务器线程池**: HTTP请求处理
7. **状态监控线程**: `MainMonitor::statusMonitorLoop()`

### 线程安全策略
- 所有共享数据结构使用`std::mutex`保护
- 事件统计使用`std::atomic`原子操作
- RAII模式确保锁的正确释放
- 避免嵌套锁定，防止死锁

---

## 📁 文件组织结构

### 源代码文件（src/）
- `main.cpp`: 程序入口和命令行处理
- `main_monitor.cpp`: 主监控器实现
- `file_monitor.cpp`: 文件监控器实现（双Fanotify Group）
- `process_monitor.cpp`: 进程监控器实现
- `network_monitor.cpp`: 网络监控器实现
- `api_server.cpp`: API服务器实现
- `api_handlers_basic.cpp`: 基础API处理器
- `api_json_builder.cpp`: JSON构建工具
- `http_server.cpp`: HTTP服务器实现
- `focus_process_manager.cpp`: 重点进程管理器
- `protected_path_manager.cpp`: 受保护路径管理器
- `process_exclusion.cpp`: 进程排除管理器
- `psfsmon_impl.cpp`: 核心数据结构实现

### 头文件（include/）
- `psfsmon.h`: 主头文件，包含所有数据结构定义
- `main_monitor.h`: 主监控器头文件
- `file_monitor.h`: 文件监控器头文件
- `process_monitor.h`: 进程监控器头文件
- `network_monitor.h`: 网络监控器头文件
- `api_server.h`: API服务器头文件
- `http_server.h`: HTTP服务器头文件
- `utils.h`: 工具类头文件

---

## 🔧 编译和构建

### Makefile构建
```bash
# 清理和编译
make clean && make -j$(nproc)

# 不同构建模式
make release          # 发布版本
make debug           # 调试版本
make profile         # 性能分析版本
make sanitizer       # 内存检查版本
```

### 构建产物
- `bin/psfsmon`: 主程序可执行文件
- `obj/`: 编译中间文件目录
- 支持多种构建配置（release、debug、profile、sanitizer）

---

## 🚀 运行和部署

### 直接运行模式
```bash
# 基本运行
sudo ./bin/psfsmon

# 指定配置文件
sudo ./bin/psfsmon --config /etc/psfsmon/psfsmon.conf

# 调试模式
sudo ./bin/psfsmon --debug --verbose
```

### 守护进程模式
```bash
# 安装服务
sudo cp scripts/psfsmon.service /etc/systemd/system/
sudo systemctl enable psfsmon
sudo systemctl start psfsmon

# 查看状态
sudo systemctl status psfsmon
```

### API访问
```bash
# 健康检查
curl http://localhost:8080/api/health

# 获取系统统计
curl http://localhost:8080/api/stats

# 获取进程列表
curl http://localhost:8080/api/processes
```

---

## 📝 配置管理

### 主要配置项
```ini
# 监控功能开关
enable_file_monitoring=true
enable_network_monitoring=true
enable_process_monitoring=true

# 重点进程监控
enable_focus_process_monitoring=true
calculate_file_entropy=true
focus_process_list="1234,5678,nginx,apache"

# 受保护路径
enable_protected_path_monitoring=true
protected_paths="/etc,/boot,/usr/bin"
terminate_violating_processes=false

# API服务器
api_port=8080
bind_address=127.0.0.1

# 性能调优
update_interval_ms=1000
process_scan_interval_ms=2000
full_scan_interval=60
```

---

## 🐛 调试和故障排除

### 常见问题
1. **权限不足**: 需要root权限或CAP_SYS_ADMIN能力
2. **内核版本不兼容**: 需要Linux 4.19+内核
3. **fanotify初始化失败**: 检查内核配置和权限
4. **API服务器启动失败**: 检查端口占用和防火墙设置

### 调试工具
```bash
# 启用调试模式
sudo ./bin/psfsmon --debug --verbose

# 查看日志
tail -f /var/log/psfsmon/psfsmon.log

# 检查进程状态
ps aux | grep psfsmon
```

### 性能监控
- 使用`/api/stats`端点监控系统性能
- 检查线程CPU使用率和内存占用
- 监控fanotify事件处理速度

---

## 📚 开发指南

### 代码风格
- 遵循C++11标准
- 使用4空格缩进
- 类名使用PascalCase，函数名使用camelCase
- 私有成员变量以下划线结尾
- 详细的注释和文档

### 扩展开发
1. **添加新的监控功能**: 继承相应的Monitor基类
2. **扩展API接口**: 在ApiServer中添加新的路由处理
3. **增加数据结构**: 在psfsmon.h中定义新的结构体
4. **添加配置项**: 在MonitorConfig中添加新字段

### 测试
```bash
# 运行API测试
cd test
./test_api_with_curl.sh

# 编译并运行综合测试
make
./test_api_comprehensive
```

---

## 📖 参考资料

### 相关文档
- `README.md`: 项目基本介绍和使用说明
- `REQUIREMENTS.md`: 详细的19项功能需求
- `DESIGN.md`: 双Fanotify Group架构设计文档

### 技术参考
- Linux fanotify(7) 手册页
- Linux proc(5) 文件系统文档
- Shannon熵值计算算法
- HTTP/1.1 协议规范

---

**文档结束**

此文档为新接手开发人员提供了全面的项目理解基础。如有疑问，请参考相关源代码注释或联系原开发团队。
