#ifndef PROCESS_EXCLUSION_H
#define PROCESS_EXCLUSION_H

#include "psfsmon.h"
#include "main_monitor.h"
#include <set>
#include <regex>
#include <unordered_set>
#include <unistd.h>

// 进程排除管理器
class ProcessExclusionManager {
public:
    ProcessExclusionManager();
    ~ProcessExclusionManager();
    
    // 初始化
    bool initialize();
    
    // 检查进程是否被排除
    bool isExcluded(pid_t pid) const;
    bool isExcluded(const std::string& process_name) const;
    bool isExcludedByPath(const std::string& executable_path) const;
    
    // 添加排除规则
    void addExclusionByPid(pid_t pid);
    void addExclusionByName(const std::string& name);
    void addExclusionByPattern(const std::string& pattern);
    void addExclusionByPath(const std::string& path);
    
    // 移除排除规则
    void removeExclusionByPid(pid_t pid);
    void removeExclusionByName(const std::string& name);
    void removeExclusionByPath(const std::string& path);
    
    // 批量操作
    void addExclusionsFromConfig(const std::vector<std::string>& config_lines);
    void loadExclusionsFromFile(const std::string& file_path);
    void saveExclusionsToFile(const std::string& file_path) const;
    
    // 获取排除列表
    std::set<pid_t> getExcludedPids() const;
    std::set<std::string> getExcludedNames() const;
    std::set<std::string> getExcludedPaths() const;
    std::vector<std::string> getExcludedPatterns() const;
    
    // 统计信息
    size_t getTotalExclusions() const;
    size_t getExclusionsByType(const std::string& type) const;
    
    // 清理
    void clearAllExclusions();
    void clearExclusionsByType(const std::string& type);
    
    // 默认排除规则
    void addDefaultExclusions();
    
private:
    // 排除规则存储
    std::unordered_set<pid_t> excluded_pids_;
    std::set<std::string> excluded_names_;
    std::set<std::string> excluded_paths_;
    std::vector<std::regex> excluded_patterns_;
    std::vector<std::string> excluded_pattern_strings_; // 保存原始字符串用于序列化
    
    // 线程安全
    mutable std::mutex ProcessExclusionManager_exclusions_mutex_;
    
    // 预定义的系统进程排除列表
    static const std::vector<std::string> SYSTEM_PROCESSES;
    static const std::vector<std::string> SYSTEM_PATHS;
    static const std::vector<std::string> SYSTEM_PATTERNS;
    
    // 辅助函数
    bool matchesPattern(const std::string& text) const;
    void compilePatterns();
    
    // 日志
    void logInfo(const std::string& message) const;
    void logError(const std::string& message) const;
    void logDebug(const std::string& message) const;
};

// 单例管理器
class ProcessExclusionManagerSingleton {
public:
    static ProcessExclusionManager& getInstance();
    
private:
    ProcessExclusionManagerSingleton() = default;
    ~ProcessExclusionManagerSingleton() = default;
    ProcessExclusionManagerSingleton(const ProcessExclusionManagerSingleton&) = delete;
    ProcessExclusionManagerSingleton& operator=(const ProcessExclusionManagerSingleton&) = delete;
    
    static std::unique_ptr<ProcessExclusionManager> instance_;
    static std::mutex instance_mutex_;
};

#endif // PROCESS_EXCLUSION_H 