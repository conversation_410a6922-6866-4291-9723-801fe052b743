#ifndef PROCESS_MONITOR_H
#define PROCESS_MONITOR_H

#include "psfsmon.h"
#include <dirent.h>
#include <set>

// 进程状态枚举
enum class ProcessState {
    RUNNING,    // R
    SLEEPING,   // S
    DISK_SLEEP, // D
    ZOMBIE,     // Z
    STOPPED,    // T
    PAGING,     // W
    DEAD,       // X
    UNKNOWN
};

// 进程监控器类
class ProcessMonitor {
public:
    ProcessMonitor();
    ~ProcessMonitor();
    
    // 初始化
    bool initialize();
    
    // 启动监控
    bool start();
    
    // 停止监控
    void stop();
    
    // 手动更新所有进程信息
    void updateAllProcesses();
    
    // 获取进程信息
    std::shared_ptr<ProcessInfo> getProcess(pid_t pid) const;
    
    // 获取所有进程
    std::vector<std::shared_ptr<ProcessInfo>> getAllProcesses() const;
    
    // 获取进程树
    std::shared_ptr<ProcessTreeNode> getProcessTree() const;
    
    // 根据进程名查找进程
    std::vector<std::shared_ptr<ProcessInfo>> findProcessesByName(const std::string& name) const;
    
    // 重点进程管理
    void setFocusProcess(pid_t pid, bool focus = true);
    void setFocusProcess(const std::string& process_name, bool focus = true);
    bool isFocusProcess(pid_t pid) const;
    std::vector<pid_t> getFocusProcesses() const;
    
    // 注意：不再记录详细的文件操作序列和熵值
    
    // 重点进程统计记录
    void recordFocusProcessDelete(pid_t pid, const std::string& file_path);
    void recordFocusProcessRename(pid_t pid, const std::string& old_path, const std::string& new_path);

    // 重点进程熵值记录
    void addReadEntropyToFocusProcess(pid_t pid, double entropy);
    void addWriteEntropyToFocusProcess(pid_t pid, double entropy);
    
    // 新的文件熵值记录方法
    void recordFileReadOnceEntropy(pid_t pid, const std::string& file_path, double entropy, size_t file_size);
    void recordFileCloseWriteEntropy(pid_t pid, const std::string& file_path, double entropy, size_t file_size);

    // 【性能优化】：检查是否需要计算熵值（避免重复计算）
    bool needsEntropyCalculation(pid_t pid, const std::string& file_path) const;
    
    // 获取文件熵值信息
    std::vector<FileEntropyInfo> getFocusProcessFileEntropyInfo(pid_t pid) const;
    double getFocusProcessTotalOriginalEntropy(pid_t pid) const;
    double getFocusProcessTotalFinalEntropy(pid_t pid) const;
    double getFocusProcessTotalEntropyChange(pid_t pid) const;

    // 重点进程文件路径历史记录
    void addFilePathToHistory(pid_t pid, const std::string& file_path);
    
    // 获取统计信息
    size_t getTotalProcesses() const;
    // 删除线程统计方法

private:
    // CPU使用率计算相关
    struct CpuStats {
        uint64_t utime;      // 用户态时间
        uint64_t stime;      // 内核态时间
        uint64_t total_time; // 总时间
        uint64_t timestamp;  // 时间戳
        
        CpuStats() : utime(0), stime(0), total_time(0), timestamp(0) {}
    };
    
    std::unordered_map<pid_t, CpuStats> process_cpu_stats_;
    std::mutex ProcessMonitor_cpu_stats_mutex_;
    
    double calculateCpuUsage(pid_t id, const CpuStats& current_stats);
    
    // 进程信息管理
    std::unordered_map<pid_t, std::shared_ptr<ProcessInfo>> processes_;
    mutable std::mutex ProcessMonitor_processes_mutex_;
    
    // 监控线程
    std::thread monitor_thread_;
    std::thread focus_monitor_thread_;  // 重点进程监控线程
    std::atomic<bool> running_;
    std::atomic<bool> focus_monitor_running_;  // 重点进程监控线程运行标志
    std::atomic<bool> stop_requested_;
    uint32_t update_interval_ms_;
    
    // 进程扫描
    void monitorLoop();
    void scanProcesses();
    
    // 进程信息读取
    bool readProcessInfo(pid_t pid, std::shared_ptr<ProcessInfo> process);
    bool readProcessStatus(pid_t pid, std::shared_ptr<ProcessInfo> process);
    bool readProcessStat(pid_t pid, std::shared_ptr<ProcessInfo> process);
    bool readProcessCmdline(pid_t pid, std::shared_ptr<ProcessInfo> process);
    bool readProcessCwd(pid_t pid, std::shared_ptr<ProcessInfo> process);
    bool readProcessExe(pid_t pid, std::shared_ptr<ProcessInfo> process);
    
    // 内存信息
    struct MemoryInfo {
        uint64_t rss_kb;        // 物理内存
        uint64_t vsize_kb;      // 虚拟内存
        uint64_t shared_kb;     // 共享内存
        uint64_t text_kb;       // 代码段
        uint64_t data_kb;       // 数据段
        
        MemoryInfo() : rss_kb(0), vsize_kb(0), shared_kb(0), text_kb(0), data_kb(0) {}
    };
    
    bool readMemoryInfo(pid_t pid, MemoryInfo& mem_info);
    
    // 进程树构建
    std::shared_ptr<ProcessTreeNode> buildProcessTree() const;
    void addChildrenToNode(std::shared_ptr<ProcessTreeNode> node, 
                          const std::unordered_map<pid_t, std::vector<pid_t>>& children_map) const;
    
    // 进程状态转换
    ProcessState charToProcessState(char state_char);
    std::string processStateToString(ProcessState state);
    
    // 文件系统辅助函数
    bool fileExists(const std::string& path);
    std::string readFileContent(const std::string& path);
    std::vector<std::string> readFileLines(const std::string& path);
    std::vector<std::string> splitString(const std::string& str, char delimiter);
    
    // /proc文件系统解析
    std::vector<pid_t> getPidList();
    std::string getProcPath(pid_t pid, const std::string& file = "");
    
    // 进程生命周期管理
    void handleNewProcess(pid_t pid);
    void handleProcessExit(pid_t pid);
    
    // 清理过期进程
    void cleanupDeadProcesses();
    std::set<pid_t> previous_pids_;
    
    // 系统信息
    uint32_t num_cpu_cores_;
    uint64_t page_size_;
    uint64_t clock_ticks_per_sec_;
    
    void initializeSystemInfo();
    
    // 日志和调试
    void logError(const std::string& message);
    void logInfo(const std::string& message);
    void logDebug(const std::string& message);
    
    // 性能优化
    time_t last_full_scan_;
    bool needsFullScan() const;
    void performIncrementalUpdate();
    void performFullScan();
    
    // 重点进程监控
    void focusMonitorLoop();  // 重点进程监控循环
    void monitorFocusProcesses();  // 监控所有重点进程
};

// 进程监控管理器
class ProcessMonitorManager {
public:
    static ProcessMonitorManager& getInstance();
    
    bool initialize();
    bool start();
    void stop();
    bool reinitialize();
    
    ProcessMonitor* getProcessMonitor() { return process_monitor_.get(); }
    
private:
    ProcessMonitorManager() = default;
    ~ProcessMonitorManager() = default;
    
    // 禁用拷贝
    ProcessMonitorManager(const ProcessMonitorManager&) = delete;
    ProcessMonitorManager& operator=(const ProcessMonitorManager&) = delete;
    
    std::unique_ptr<ProcessMonitor> process_monitor_;
};

#endif // PROCESS_MONITOR_H
