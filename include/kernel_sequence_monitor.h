/*
 * 内核文件操作序列监控器
 * 用于从/proc接口获取进程的文件操作序列并进行规则匹配
 */

#ifndef KERNEL_SEQUENCE_MONITOR_H
#define KERNEL_SEQUENCE_MONITOR_H

#include <string>
#include <vector>
#include <memory>
#include <mutex>
#include <atomic>
#include <chrono>
#include <unordered_map>
#include <thread>

// 常量定义
constexpr uint32_t KERNEL_SEQUENCE_MONITOR_INTERVAL_MS = 1000;  // 1秒
constexpr uint32_t FOCUS_PROCESS_STATUS_CHECK_INTERVAL_MS = 5000; // 5秒
constexpr const char* KERNEL_MONITOR_PROC_PATH = "/proc/fs_monitor/monitor";

// 规则类型定义（与ref_code/rules_about.cpp保持一致）
constexpr auto COMPRESS_RULE = 0x00000001;
constexpr auto NOTCOMPRESS_RULE = 0x00000002;
constexpr auto RENAME_RULE = 0x00000004;
constexpr auto DELETE_RULE = 0x00000008;

// 监控等级定义
constexpr auto CB_MIN_WATCH_LEVEL = 10;
constexpr auto CB_MIN_STRICT_RENAME_WATCH_LEVEL = CB_MIN_WATCH_LEVEL + 1;
constexpr auto CB_MIN_STRICT_DELETE_WATCH_LEVEL = CB_MIN_WATCH_LEVEL + 2;

// 前向声明
struct ProcessSequenceInfo;

// 单个比较规则
class OneCmpRule {
public:
    std::string RuleName;
    std::string Rule;
    int len_Value;
    int k_Value;
    int d_Value;
    int w_Level;
    unsigned long RuleType;

    OneCmpRule(const std::string& ruleName, const std::string& rule,
               int lenValue, int kValue, int dValue, int wLevel, unsigned long ruleType)
        : RuleName(ruleName), Rule(rule), len_Value(lenValue),
          k_Value(kValue), d_Value(dValue), w_Level(wLevel), RuleType(ruleType) {}
};

// 规则管理器（单例模式）
class CmpRuleManager {
private:
    CmpRuleManager() = default;
    ~CmpRuleManager() = default;
    
    std::mutex m_CmpRuleManager_Mutex;
    std::vector<std::shared_ptr<OneCmpRule>> mRulesList;

public:
    static CmpRuleManager& getInstance() {
        static CmpRuleManager instance;
        return instance;
    }

    CmpRuleManager(const CmpRuleManager&) = delete;
    CmpRuleManager& operator=(const CmpRuleManager&) = delete;

    void AddOneCmpRule(std::shared_ptr<OneCmpRule> prule);
    bool CatchCmpRule2TID(const std::string& kernel_tag_str);
    std::vector<std::pair<std::shared_ptr<OneCmpRule>, int>> getMatchingRules(const std::string& kernel_tag_str);
    void initializeDefaultRules();
    const std::vector<std::shared_ptr<OneCmpRule>>& getRules() const;
};

// 进程序列信息
struct ProcessSequenceInfo {
    pid_t pid;
    pid_t tid;
    std::string sequence_string;
    uint64_t last_update_time;
    std::vector<std::string> matched_rules;
    bool has_match;

    ProcessSequenceInfo() : pid(0), tid(0), last_update_time(0), has_match(false) {}
};

// 内核序列监控器
class KernelSequenceMonitor {
private:
    std::atomic<bool> running_{false};
    std::thread monitor_thread_;
    mutable std::mutex data_mutex_;
    
    std::unordered_map<pid_t, ProcessSequenceInfo> process_sequences_;

    bool readKernelSequence(pid_t pid, std::string& sequence);
    void monitorLoop();
    void parseSequenceLine(const std::string& line, pid_t& pid, pid_t& tid, std::string& sequence);

public:
    KernelSequenceMonitor() = default;
    ~KernelSequenceMonitor();

    bool start();
    void stop();
    
    ProcessSequenceInfo getProcessSequence(pid_t pid) const;
    std::vector<ProcessSequenceInfo> getAllProcessSequences() const;
    bool checkProcessForFocus(pid_t pid);
};

#endif // KERNEL_SEQUENCE_MONITOR_H
