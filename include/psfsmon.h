/*
 * PSFSMON-L 主头文件 - 双Fanotify Group监控系统
 * 
 * Linux进程安全文件系统监控程序 (Process Security File System Monitor for Linux)
 * 
 * 【项目总览】
 * 本项目实现了一个基于双Fanotify Group + 双线程监控架构的Linux系统安全监控程序，
 * 严格按照19项功能需求实现，特别是需求13（双Fanotify Group策略）和需求12（重点进程监控）。
 * 
 * 【核心架构特点】
 * - 双Fanotify Group策略：受保护路径权限检查 + 重点进程文件监控
 * - 双线程处理：独立线程处理不同监控任务，避免事件冲突
 * - 基础Fanotify机制：仅使用4.19+内核支持的基础特性，确保兼容性
 * - 完全移除Inotify：不再依赖Inotify机制，简化架构复杂度
 * - 进程级信息汇总：所有监控信息归属并管理在进程层次
 * 
 * 【19项核心需求覆盖】
 * 1-9: 基础系统要求（Linux平台、C++11、多CPU、进程监控、网络监控、禁用eBPF等）
 * 10: 受保护路径概念（KILL/LOG模式）
 * 11: 忽略进程概念
 * 12: 重点进程概念（文件操作监控、熵值计算、操作历史记录）
 * 13: 双Fanotify Group基础监控策略（核心架构需求）
 * 14-19: 系统架构要求（避免大规模依赖、双执行模式、API接口、双编译方式、代码可读性、文档整理）
 * 
 * 【技术特性】
 * - 内核兼容性：支持4.19+内核，4.20+使用FAN_MARK_FILESYSTEM优化
 * - 线程安全：所有共享数据结构使用互斥锁保护
 * - 性能优化：重点进程快速过滤，非重点进程直接跳过
 * - API完整性：提供完整的RESTful API接口覆盖所有功能
 */

#ifndef PSFSMON_H
#define PSFSMON_H

#include <cstdint>
#include <string>
#include <vector>
#include <map>
#include <unordered_map>
#include <memory>
#include <atomic>
#include <mutex>
#include <thread>
#include <chrono>
#include <sys/types.h>
#include <functional>
#include <set>

// 包含规则匹配信息头文件
#include "rule_match_info.h"

// 文件标识符结构
struct FileIdentifier {
    dev_t device_id;              // 设备ID
    ino_t inode_number;           // Inode号
    uint64_t generation;          // 生成号（如果可用）
    
    FileIdentifier() : device_id(0), inode_number(0), generation(0) {}
    
    // 生成唯一标识字符串
    std::string to_string() const {
        return std::to_string(device_id) + ":" + std::to_string(inode_number);
    }
    
    // 生成数值ID
    uint64_t to_id() const {
        return (static_cast<uint64_t>(device_id) << 32) | static_cast<uint64_t>(inode_number);
    }
};

// 常量定义
namespace Constants {
    const size_t MAX_FILES_PER_THREAD = 50;      // 每个线程最多记录的文件数
    const size_t MAX_FILES_UPPER_LIMIT = 100;    // 文件数量上限
    const size_t MAX_PATH_LENGTH = 4096;         // 最大路径长度
    const size_t BUFFER_SIZE = 8192;             // 缓冲区大小
    
    // 文件操作字符编码
    const char FILE_OP_OPEN = 'O';
    const char FILE_OP_READ = 'R';
    const char FILE_OP_WRITE = 'W';
    const char FILE_OP_RENAME = 'N';
    const char FILE_OP_DELETE = 'D';
    const char FILE_OP_RENAME_DELETE = 'P';  // 重命名时目标已存在
    const char FILE_OP_CLOSE = 'C';
    
    // 重点进程监控相关常量
    const size_t DEFAULT_READ_ENTROPY_COUNT = 0;     // 默认读操作熵值计数
    const size_t DEFAULT_WRITE_ENTROPY_COUNT = 0;    // 默认写操作熵值计数
    const size_t MAX_PATH_HISTORY = 1000;            // 最大路径历史记录数
    const size_t MAX_EXTENSION_LIST = 100;           // 最大扩展名列表长度
}

// 注意：根据需求10，不再需要文件操作序列，已移除FileOperation结构体

// 网络连接信息
struct NetworkConnection {
    std::string local_addr;     // 本地地址
    uint16_t local_port;        // 本地端口
    std::string remote_addr;    // 远程地址
    uint16_t remote_port;       // 远程端口
    std::string protocol;       // 协议类型 (TCP/UDP)
    std::string state;          // 连接状态
    uint64_t tx_bytes;          // 发送字节数
    uint64_t rx_bytes;          // 接收字节数
    uint64_t created_time;      // 创建时间
    
    NetworkConnection() : local_port(0), remote_port(0), 
                         tx_bytes(0), rx_bytes(0), created_time(0) {}
};

// 文件熵值信息结构体
struct FileEntropyInfo {
    std::string file_path;           // 文件路径
    uint64_t file_hash;              // 文件路径哈希值（作为唯一标识符）
    double original_entropy;         // 原始熵值（FAN_ACCESS时计算）
    double final_entropy;            // 最终熵值（FAN_CLOSE_WRITE时计算）
    double entropy_change;           // 熵值变化量
    bool has_original_entropy;       // 是否已计算原始熵值
    bool has_final_entropy;          // 是否已计算最终熵值
    time_t open_time;                // 文件打开时间
    time_t close_time;               // 文件关闭时间
    size_t file_readsize;            // 文件读大小
    size_t file_writesize;           // 文件写大小
    
    FileEntropyInfo() : file_hash(0), original_entropy(0.0), final_entropy(0.0), 
                       entropy_change(0.0), has_original_entropy(false), 
                       has_final_entropy(false), open_time(0), close_time(0), file_readsize(0), file_writesize(0) {}
    
    FileEntropyInfo(const std::string& path, uint64_t hash) :
        file_path(path), file_hash(hash), original_entropy(0.0), final_entropy(0.0),
        entropy_change(0.0), has_original_entropy(false), has_final_entropy(false),
        open_time(0), close_time(0), file_readsize(0), file_writesize(0){}
};

// 熵值统计摘要结构体
struct EntropyStatsSummary {
    size_t total_files;                    // 总文件数
    size_t files_with_original_entropy;    // 有原始熵值的文件数
    size_t files_with_final_entropy;       // 有最终熵值的文件数
    size_t files_with_both_entropy;        // 有完整熵值对的文件数
    double total_original_entropy;         // 总原始熵值
    double total_final_entropy;            // 总最终熵值
    double total_entropy_change;           // 总熵值变化量
    double max_entropy_change;             // 最大熵值变化
    double min_entropy_change;             // 最小熵值变化
    double entropy_change_percentage;      // 熵值变化百分比

    EntropyStatsSummary() : total_files(0), files_with_original_entropy(0),
                           files_with_final_entropy(0), files_with_both_entropy(0),
                           total_original_entropy(0.0), total_final_entropy(0.0),
                           total_entropy_change(0.0), max_entropy_change(0.0),
                           min_entropy_change(0.0), entropy_change_percentage(0.0) {}
};

// 重点进程文件监控信息
struct FocusProcessFileInfo {
    // 按文件存储的熵值信息
    std::unordered_map<uint64_t, FileEntropyInfo> file_entropy_map;  // 文件哈希 -> 熵值信息
    mutable std::mutex FocusProcessFileInfo_file_entropy_mutex_;     // 文件熵值映射表锁
    
    // 总体统计信息（兼容现有API）
    double total_read_entropy;     // 读操作累计文件熵值
    double total_write_entropy;    // 写操作累计文件熵值
    size_t read_entropy_count;     // 读操作熵值计数
    size_t write_entropy_count;    // 写操作熵值计数
    
    // 文件操作统计
    size_t delete_count;           // 删除文件数量
    size_t rename_count;           // 重命名文件数量
    std::vector<std::string> rename_extensions;  // 重命名扩展名列表
    std::vector<std::string> path_history;       // 操作文件路径历史记录
    
    // 规则匹配统计信息
    std::unique_ptr<RuleMatchStats> rule_match_stats;  // 规则匹配统计
    
    mutable std::mutex FocusProcessFileInfo_data_mutex_;  // 线程安全锁
    
    // 熵值计算配置
    static constexpr size_t MAX_FILE_SIZE_FOR_ENTROPY = 100 * 1024 * 1024; // 100MB
    
    FocusProcessFileInfo() : total_read_entropy(0.0), total_write_entropy(0.0),
                           read_entropy_count(0), write_entropy_count(0),
                           delete_count(0), rename_count(0) {
        rule_match_stats.reset(new RuleMatchStats());
    }
    
    // 文件熵值管理方法
    void recordFileReadOnce(const std::string& file_path, double entropy, size_t file_size);
    void recordFileCloseWrite(const std::string& file_path, double entropy, size_t file_size);
    FileEntropyInfo* getFileEntropyInfo(const std::string& file_path);
    std::vector<FileEntropyInfo> getAllFileEntropyInfo() const;

    // 【性能优化】：检查是否需要计算原始熵值（避免重复计算）
    bool needsOriginalEntropyCalculation(const std::string& file_path) const;
    
    // 计算文件路径哈希值
    static uint64_t calculateFileHash(const std::string& file_path);
    
    // 兼容现有API的方法
    void addReadEntropy(double entropy);
    void addWriteEntropy(double entropy);
    
    // 记录删除操作
    void recordDelete(const std::string& path);
    
    // 记录重命名操作
    void recordRename(const std::string& old_path, const std::string& new_path);
    
    // 记录文件路径操作
    void recordPathOperation(const std::string& path);

    // 添加文件路径到历史记录
    void addFilePathToHistory(const std::string& path);

    // 获取平均读熵值
    double getAverageReadEntropy() const;
    
    // 获取平均写熵值
    double getAverageWriteEntropy() const;
    
    // 获取总体熵值统计
    double getTotalOriginalEntropy() const;
    double getTotalFinalEntropy() const;
    double getTotalEntropyChange() const;

    // 获取熵值统计摘要
    EntropyStatsSummary getEntropyStatsSummary() const;

    // 清理过期记录
    void cleanup();
};

// 进程信息
struct ProcessInfo {
    pid_t pid;                  // 进程ID
    pid_t ppid;                 // 父进程ID
    std::string process_name;   // 进程名称
    std::string executable_path; // 可执行文件路径
    std::string command_line;   // 命令行参数
    std::string cwd;            // 当前工作目录
    uid_t uid;                  // 用户ID
    gid_t gid;                  // 组ID
    
    // 性能信息
    double cpu_usage;           // CPU使用率
    uint64_t memory_usage;      // 内存使用量 (KB)
    uint64_t virtual_memory;    // 虚拟内存使用量 (KB)
    std::string state;          // 进程状态
    uint64_t start_time;        // 启动时间
    
    // 子进程（移除线程管理）
    std::vector<pid_t> children; // 子进程ID列表
    
    // 网络连接
    std::vector<NetworkConnection> network_connections;
    mutable std::mutex ProcessInfo_network_connections_mutex_;
    
    // 重点进程标记和文件监控信息
    bool is_focus_process;      // 是否为重点进程
    std::unique_ptr<FocusProcessFileInfo> focus_file_info; // 重点进程文件信息
    
    ProcessInfo() : pid(0), ppid(0), uid(0), gid(0), 
                    cpu_usage(0.0), memory_usage(0), virtual_memory(0), start_time(0),
                    is_focus_process(false) {}
    
    // 更新网络连接
    void updateNetworkConnections(const std::vector<NetworkConnection>& connections);
    
    // 设置为重点进程
    void setFocusProcess(bool focus);
    
    // 获取重点进程文件信息（如果是重点进程）
    FocusProcessFileInfo* getFocusFileInfo() const;
    
    // 基础信息获取（不再包含文件操作汇总）
    std::map<std::string, std::string> getBasicInfoSummary() const;
};

// 进程树节点
struct ProcessTreeNode {
    std::shared_ptr<ProcessInfo> process;
    std::vector<std::shared_ptr<ProcessTreeNode>> children;
    
    ProcessTreeNode(std::shared_ptr<ProcessInfo> proc) : process(proc) {}
};

// 监控配置
struct MonitorConfig {
    // 基础配置
    bool enable_file_monitoring;       // 启用文件监控
    bool enable_network_monitoring;    // 启用网络监控
    bool enable_process_monitoring;    // 启用进程监控
    bool daemon_mode;                  // 守护进程模式
    bool debug_mode;                   // 调试模式
    bool verbose_mode;                 // 详细输出模式
    
    // 更新配置
    uint32_t update_interval_ms;       // 更新间隔(毫秒)
    uint32_t process_scan_interval_ms; // 进程扫描间隔
    uint32_t full_scan_interval;       // 完整扫描间隔(秒)
    
    // API配置
    uint32_t api_port;                 // API端口
    std::string bind_address;          // 绑定地址
    bool api_auth_required;            // API认证要求
    std::string allowed_api_clients;   // 允许的API客户端
    
    // 文件路径配置
    std::string log_file;              // 日志文件路径
    std::string config_file;           // 配置文件路径
    std::string pid_file;              // PID文件路径
    
    // 文件监控配置
    bool use_fanotify_only;            // 仅使用Fanotify（默认true）
    size_t max_files_per_thread;       // 每个线程最大文件数
    
    // 重点进程监控配置
    bool enable_focus_process_monitoring; // 启用重点进程监控
    bool calculate_file_entropy;      // 计算文件熵值
    std::string focus_process_list;    // 重点进程列表（逗号分隔）
    
    // 受保护路径配置
    bool enable_protected_path_monitoring; // 启用受保护路径监控
    std::string protected_paths;         // 受保护路径列表（逗号分隔）
    bool terminate_violating_processes; // 中止违规进程
    
    // 网络监控配置
    bool monitor_all_interfaces;       // 监控所有网络接口
    uint32_t connection_update_interval; // 连接更新间隔
    bool use_netlink_diag;             // 使用Netlink诊断
    
    // 进程监控配置
    bool monitor_all_processes;        // 监控所有进程
    uint32_t cpu_calc_window;          // CPU计算窗口
    
    // 安全配置
    bool require_root;                 // 要求root权限
    
    // 性能配置
    size_t worker_threads;             // 工作线程数
    size_t max_event_queue_size;       // 最大事件队列大小
    size_t memory_limit_mb;            // 内存限制(MB)
    bool enable_performance_stats;     // 启用性能统计
    
    // 日志配置
    std::string log_level;             // 日志级别
    size_t max_log_size;               // 最大日志大小(MB)
    size_t max_log_files;              // 最大日志文件数
    
    // 高级配置
    bool check_kernel_compatibility;   // 检查内核兼容性
    std::string min_kernel_version;    // 最小内核版本
    bool enable_experimental_features; // 启用实验性功能
    
    MonitorConfig() : 
        enable_file_monitoring(true),
        enable_network_monitoring(true),
        enable_process_monitoring(true),
        daemon_mode(false),
        debug_mode(false),
        verbose_mode(false),
        update_interval_ms(1000),
        process_scan_interval_ms(2000),
        full_scan_interval(60),
        api_port(8080),
        bind_address("127.0.0.1"),
        api_auth_required(false),
        allowed_api_clients("127.0.0.1,::1"),
        log_file("/var/log/psfsmon/psfsmon.log"),
        config_file("/etc/psfsmon/psfsmon.conf"),
        pid_file("/var/run/psfsmon.pid"),
        use_fanotify_only(true),
        max_files_per_thread(50),
        enable_focus_process_monitoring(true),
        calculate_file_entropy(true),
        focus_process_list(""),
        enable_protected_path_monitoring(true),  // 从配置文件正确读取
        protected_paths(""),
        terminate_violating_processes(false),
        monitor_all_interfaces(true),
        connection_update_interval(5),
        use_netlink_diag(false),
        monitor_all_processes(true),
        cpu_calc_window(5),
        require_root(true),
        worker_threads(0),
        max_event_queue_size(10000),
        memory_limit_mb(0),
        enable_performance_stats(true),
        log_level("INFO"),
        max_log_size(100),
        max_log_files(5),
        check_kernel_compatibility(true),
        min_kernel_version("4.19"),
        enable_experimental_features(false) {}
};

// 统计信息
struct MonitorStats {
    std::atomic<uint64_t> total_processes{0};
    std::atomic<uint64_t> total_file_events{0};
    std::atomic<uint64_t> total_network_events{0};
    std::atomic<uint64_t> uptime_seconds{0};
    uint64_t start_time;
    
    MonitorStats() : start_time(static_cast<uint64_t>(std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()).count())) {}
    
    // 拷贝构造函数
    MonitorStats(const MonitorStats& other) : 
        total_processes(other.total_processes.load()),
        total_file_events(other.total_file_events.load()),
        total_network_events(other.total_network_events.load()),
        uptime_seconds(other.uptime_seconds.load()),
        start_time(other.start_time) {}
    
    // 赋值运算符
    MonitorStats& operator=(const MonitorStats& other) {
        if (this != &other) {
            total_processes = other.total_processes.load();
            total_file_events = other.total_file_events.load();
            total_network_events = other.total_network_events.load();
            uptime_seconds = other.uptime_seconds.load();
            start_time = other.start_time;
        }
        return *this;
    }
};

// 前向声明
class ProcessMonitor;
class FileMonitor;
class NetworkMonitor;
class ApiServer;

// 规则匹配统计结构体前向声明
struct RuleMatchStats;

#endif // PSFSMON_H
