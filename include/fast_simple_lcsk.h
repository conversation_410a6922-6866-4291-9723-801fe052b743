/**
 * Fast Simple LCSK 静态库 - 完整单头文件实现
 * 
 * 这是一个完整的单头文件实现，包含所有必要的类和函数。
 * 只需要包含这个头文件即可使用LCSK算法。
 * 
 * 主要功能：
 * - LcsKSparseFast: 快速LCSk算法
 * - LcsKppSparseFast: 快速LCSk++算法
 * - LcskSparseSlow: 慢速LCSk算法（用于验证）
 * - LcskppSparseSlow: 慢速LCSk++算法（用于验证）
 * - 完整的测试和验证功能
 */

#ifndef FAST_SIMPLE_LCSK_H
#define FAST_SIMPLE_LCSK_H

#include <string>
#include <vector>
#include <utility>
#include <memory>
#include <queue>
#include <tuple>
#include <unordered_map>
#include <algorithm>
#include <cassert>
#include <climits>
#include <cmath>
#include <stdexcept>
#include <iostream>

// 条件编译选项：如果需要在release版本中保持assert检查，可以定义LCSK_ENABLE_RELEASE_CHECKS
#ifdef LCSK_ENABLE_RELEASE_CHECKS
  #ifdef NDEBUG
    #undef NDEBUG
    #include <cassert>
    #define NDEBUG
  #endif
  #define LCSK_ASSERT(condition) assert(condition)
#else
  // 默认行为：保持与Google原版完全一致
  #define LCSK_ASSERT(condition) assert(condition)
#endif

// ==================== 对象计数器 ====================
template <typename T>
struct ObjectCounter {
  ObjectCounter() {
    objects_created++;
    objects_alive++;
    max_objects_alive = std::max(max_objects_alive, objects_alive);
  }

  virtual ~ObjectCounter() {
    --objects_alive;
  }

  static uint64_t objects_created;
  static uint64_t objects_alive;
  static uint64_t max_objects_alive;
};

template <typename T> uint64_t ObjectCounter<T>::objects_created(0);
template <typename T> uint64_t ObjectCounter<T>::objects_alive(0);
template <typename T> uint64_t ObjectCounter<T>::max_objects_alive(0);

// ==================== 匹配对结构 ====================
struct MatchPair : ObjectCounter<MatchPair> {
  int end_row;
  int end_col;
  int dp;
  std::shared_ptr<MatchPair> prev;

  MatchPair() {}

  MatchPair(int end_row, int end_col, int dp, std::shared_ptr<MatchPair> prev)
      : end_row(end_row), end_col(end_col), dp(dp), prev(prev) {}
};

// ==================== 匹配事件队列 ====================
struct MatchEventsQueue {
  std::queue<std::tuple<int, int, std::shared_ptr<MatchPair>>> begin;
  std::queue<std::tuple<int, int, std::shared_ptr<MatchPair>>> end;

  void AddBegin(const std::tuple<int, int, std::shared_ptr<MatchPair>>& event) {
    if (std::get<0>(event) >= 0 && std::get<1>(event) >= 0) {
      begin.push(event);
    }
  }

  void AddEnd(const std::tuple<int, int, std::shared_ptr<MatchPair>>& event) {
    if (std::get<0>(event) >= 0 && std::get<1>(event) >= 0) {
      end.push(event);
    }
  }

  bool PopBegin(int row, std::tuple<int, int, std::shared_ptr<MatchPair>>* event) {
    if (!event || row < 0) return false;
    
    if (!begin.empty() && std::get<0>(begin.front()) == row) {
      *event = begin.front();
      begin.pop();
      return true;
    }
    return false;
  }

  bool PopEnd(int row, std::tuple<int, int, std::shared_ptr<MatchPair>>* event) {
    if (!event || row < 0) return false;
    
    if (!end.empty() && std::get<0>(end.front()) == row) {
      *event = end.front();
      end.pop();
      return true;
    }
    return false;
  }
};

// ==================== 滚动哈希器 ====================
class RollingHasher {
public:
  RollingHasher(const std::string& s, int k,
                const std::vector<char>& char_to_id, int alphabet_size)
      : s_(s), k_(k), char_to_id_(char_to_id), alphabet_size_(alphabet_size),
        hash_mod_(1), hash_(0), col_(0) {
    
    // 参数验证
    if (k <= 0) {
      throw std::invalid_argument("k must be positive");
    }
    if (alphabet_size <= 0) {
      throw std::invalid_argument("alphabet_size must be positive");
    }
    if (char_to_id.empty()) {
      throw std::invalid_argument("char_to_id cannot be empty");
    }
    
    // 安全地计算hash_mod_，避免溢出
    hash_mod_ = 1;
    for (int i = 0; i < k; ++i) {
      if (hash_mod_ > ULLONG_MAX / alphabet_size) {
        hash_mod_ = 1000000007ULL;
        break;
      }
      hash_mod_ *= alphabet_size;
    }
    
    if (hash_mod_ == 0) {
      hash_mod_ = 1000000007ULL;
    }
  }

  bool Next(unsigned long long* hash) {
    if (!hash) return false;
    
    if (col_ + k_ > static_cast<int>(s_.size())) {
      return false;
    }
    
    if (col_ < 0 || k_ <= 0) {
      return false;
    }

    if (col_ == 0) {
      hash_ = 0;
      for (int i = 0; i < k_ - 1; ++i) {
        unsigned char ch = static_cast<unsigned char>(s_[i]);
        if (ch >= char_to_id_.size()) {
          return false;
        }
        
        char char_id = char_to_id_[ch];
        if (char_id < 0 || char_id >= alphabet_size_) {
          return false;
        }
        
        if (hash_ > ULLONG_MAX / alphabet_size_) {
          hash_ = (hash_ % hash_mod_) * alphabet_size_ + char_id;
        } else {
          hash_ = hash_ * alphabet_size_ + char_id;
        }
      }
    }

    int next_pos = col_ + k_ - 1;
    if (next_pos >= static_cast<int>(s_.size())) {
      return false;
    }
    
    unsigned char ch = static_cast<unsigned char>(s_[next_pos]);
    if (ch >= char_to_id_.size()) {
      return false;
    }
    
    char char_id = char_to_id_[ch];
    if (char_id < 0 || char_id >= alphabet_size_) {
      return false;
    }
    
    if (hash_mod_ == 0) {
      return false;
    }
    
    if (hash_ > ULLONG_MAX / alphabet_size_) {
      hash_ = (hash_ % hash_mod_) * alphabet_size_ + char_id;
    } else {
      hash_ = hash_ * alphabet_size_ + char_id;
    }
    
    hash_ %= hash_mod_;
    *hash = hash_;
    ++col_;
    
    return true;
  }

  bool IsValid() const {
    return k_ > 0 && alphabet_size_ > 0 && hash_mod_ > 0 && !char_to_id_.empty();
  }

private:
  const std::string& s_;
  int k_;
  const std::vector<char>& char_to_id_;
  int alphabet_size_;
  unsigned long long hash_mod_;
  unsigned long long hash_;
  int col_;
};

// ==================== 匹配制造器 ====================
enum MatchMakerType { NAIVE, PERFECT_HASH };

class MatchMaker {
public:
  MatchMaker() {}
  virtual ~MatchMaker() {}

  virtual bool GetNextMatches(std::vector<int>* matches) = 0;

  static std::unique_ptr<MatchMaker> Create(const std::string& a,
                                            const std::string& b, int k,
                                            MatchMakerType type);
};

class NaiveMatchMaker : public MatchMaker {
public:
  NaiveMatchMaker(const std::string& a, const std::string& b, int k)
      : a_(a), b_(b), k_(k), row_(0) {}

  bool GetNextMatches(std::vector<int>* matches) override {
    if (!matches) return false;
    
    matches->clear();
    
    if (row_ < 0 || row_ + k_ > static_cast<int>(a_.size())) {
      return false;
    }

    for (int b_index = 0; b_index + k_ <= static_cast<int>(b_.size()); ++b_index) {
      bool match = true;
      for (int i = 0; i < k_ && match; ++i) {
        if (a_[row_ + i] != b_[b_index + i]) {
          match = false;
        }
      }
      
      if (match) {
        matches->push_back(b_index);
      }
    }

    ++row_;
    return true;
  }

private:
  std::string a_;
  std::string b_;
  int k_;
  int row_;
};

class PerfectHashMatchMaker : public MatchMaker {
public:
  PerfectHashMatchMaker(const std::string& a, const std::string& b, int k) {
    if (k <= 0) {
      throw std::invalid_argument("k must be positive");
    }
    
    if (a.empty() || b.empty()) {
      throw std::invalid_argument("Input strings cannot be empty");
    }
    
    if (k > static_cast<int>(a.size()) || k > static_cast<int>(b.size())) {
      throw std::invalid_argument("k cannot be larger than string length");
    }
    
    a_ = a;
    b_ = b;
    k_ = k;
    row_ = 0;
    
    try {
      PrepareAlphabet(a, b, char_to_id_, alphabet_size_);
      
      if (alphabet_size_ <= 0 || alphabet_size_ > 256) {
        throw std::runtime_error("Invalid alphabet size");
      }
      
      ahasher_.reset(new RollingHasher(a_, k_, char_to_id_, alphabet_size_));
      
      if (!ahasher_ || !ahasher_->IsValid()) {
        throw std::runtime_error("Failed to create valid RollingHasher");
      }
      
      InitBMap(b);
    } catch (const std::exception& e) {
      ahasher_.reset();
      bmap_.clear();
      char_to_id_.clear();
      alphabet_size_ = 0;
      throw;
    }
  }

  bool GetNextMatches(std::vector<int>* matches) override {
    if (!matches) return false;
    
    matches->clear();
    
    if (row_ < 0 || row_ + k_ > static_cast<int>(a_.size())) {
      return false;
    }
    
    if (!ahasher_ || !ahasher_->IsValid()) {
      return false;
    }
    
    unsigned long long hash = 0;
    
    if (!ahasher_->Next(&hash)) {
      return false;
    }
    
    auto it = bmap_.find(hash);
    if (it != bmap_.end()) {
      const auto& match_indices = it->second;
      
      try {
        matches->reserve(match_indices.size());
        for (size_t i = 0; i < match_indices.size(); ++i) {
          int index = match_indices[i];
          if (index >= 0 && index + k_ <= static_cast<int>(b_.size())) {
            matches->push_back(index);
          }
        }
      } catch (const std::bad_alloc& e) {
        matches->clear();
        return false;
      }
    }
    
    ++row_;
    return true;
  }

private:
  static void PrepareAlphabet(const std::string& a, const std::string& b,
                              std::vector<char>& aid, int& alphabet_size) {
    aid = std::vector<char>(256, -1);
    alphabet_size = 0;
    
    for (size_t i = 0; i < a.size(); ++i) {
      unsigned char ch = static_cast<unsigned char>(a[i]);
      if (aid[ch] == -1) {
        if (alphabet_size >= 255) {
          throw std::overflow_error("Alphabet size exceeded maximum limit");
        }
        aid[ch] = static_cast<char>(alphabet_size++);
      }
    }
    
    for (size_t i = 0; i < b.size(); ++i) {
      unsigned char ch = static_cast<unsigned char>(b[i]);
      if (aid[ch] == -1) {
        if (alphabet_size >= 255) {
          throw std::overflow_error("Alphabet size exceeded maximum limit");
        }
        aid[ch] = static_cast<char>(alphabet_size++);
      }
    }
  }

  void InitBMap(const std::string& b) {
    bmap_.clear();
    
    if (b.empty() || k_ <= 0 || static_cast<size_t>(k_) > b.size()) {
      return;
    }
    
    try {
      RollingHasher bhasher(b_, k_, char_to_id_, alphabet_size_);
      
      if (!bhasher.IsValid()) {
        return;
      }
      
      unsigned long long hash = 0;
      
      for (size_t i = 0; i + static_cast<size_t>(k_) <= b.size(); ++i) {
        if (!bhasher.Next(&hash)) {
          break;
        }
        
        auto& vec = bmap_[hash];
        
        try {
          vec.push_back(static_cast<int>(i));
        } catch (const std::bad_alloc& e) {
          bmap_.clear();
          return;
        }
      }
    } catch (const std::exception& e) {
      bmap_.clear();
    }
  }

  std::string a_;
  std::string b_;
  int k_;
  int row_;
  std::vector<char> char_to_id_;
  int alphabet_size_;
  std::unique_ptr<RollingHasher> ahasher_;
  std::unordered_map<unsigned long long, std::vector<int>> bmap_;
};

// ==================== 匹配制造器工厂 ====================
std::unique_ptr<MatchMaker> MatchMaker::Create(const std::string& a, const std::string& b,
                                               int k, MatchMakerType type) {
  if (k <= 0 || a.empty() || b.empty()) {
    return nullptr;
  }
  
  if (k > static_cast<int>(a.size()) || k > static_cast<int>(b.size())) {
    return nullptr;
  }
  
  std::unique_ptr<MatchMaker> match_maker;
  try {
    switch (type) {
      case MatchMakerType::NAIVE:
        match_maker.reset(new NaiveMatchMaker(a, b, k));
        break;
      case MatchMakerType::PERFECT_HASH:
        match_maker.reset(new PerfectHashMatchMaker(a, b, k));
        break;
      default:
        return nullptr;
    }
  } catch (const std::exception& e) {
    return nullptr;
  }
  
  return match_maker;
}

// ==================== 核心算法实现 ====================
namespace {

inline void FillLcskReconstruction(const int k, std::shared_ptr<MatchPair> best,
                            std::vector<std::pair<int, int>>* lcsk_recon) {
  LCSK_ASSERT(lcsk_recon != nullptr);
  lcsk_recon->clear();

  for (auto ft = best; ft != nullptr; ft = ft->prev) {
    int r = ft->end_row;
    int c = ft->end_col;

    if (ft->prev == nullptr ||
        (ft->prev->end_row + k <= ft->end_row &&
         ft->prev->end_col + k <= ft->end_col)) {
      for (int j = 0; j < k; ++j, --r, --c) {
        lcsk_recon->push_back(std::make_pair(r, c));
      }
    } else {
      LCSK_ASSERT(ft->prev->end_row + 1 == ft->end_row &&
             ft->prev->end_col + 1 == ft->end_col);
      lcsk_recon->push_back(std::make_pair(r, c));
    }
  }
  std::reverse(lcsk_recon->begin(), lcsk_recon->end());
}

inline bool CompareByCol(const std::shared_ptr<MatchPair>& a,
                  const std::shared_ptr<MatchPair>& b) {
  return a->end_col < b->end_col;
}

inline void RowUpdate(const int k, const int row, MatchEventsQueue* events_ptr,
               std::vector<std::shared_ptr<MatchPair>>* compressed_table_ptr,
               std::vector<std::shared_ptr<MatchPair>>* prev_row_match_pairs,
               bool lcsk_plus) {
  if (!events_ptr || !compressed_table_ptr || !prev_row_match_pairs) return;
  
  auto& events = *events_ptr;
  auto& compressed_table = *compressed_table_ptr;
  auto& prev_row = *prev_row_match_pairs;

  std::tuple<int, int, std::shared_ptr<MatchPair>> event;
  std::vector<std::shared_ptr<MatchPair>> curr_row;
  int curr_continuation_index = 0;

  while (events.PopEnd(row, &event)) {
    int i = std::get<0>(event);
    int j = std::get<1>(event);
    auto match_pair_end = std::get<2>(event);

    if (lcsk_plus) { // LCSk++
      while (curr_continuation_index < static_cast<int>(prev_row.size()) &&
             prev_row[curr_continuation_index]->end_col + 1 < match_pair_end->end_col) {
        curr_continuation_index++;
      }

      if (curr_continuation_index < static_cast<int>(prev_row.size()) &&
          prev_row[curr_continuation_index]->end_col + 1 == match_pair_end->end_col) {
        int continuation_dp = prev_row[curr_continuation_index]->dp + 1;
        if (continuation_dp > match_pair_end->dp) {
          match_pair_end->dp = continuation_dp;
          match_pair_end->prev = prev_row[curr_continuation_index];
        }
      }

      curr_row.emplace_back(match_pair_end);

      int dp = match_pair_end->dp;
      while (static_cast<int>(compressed_table.size()) <= dp) {
        int idx = static_cast<int>(compressed_table.size());
        compressed_table.push_back(std::make_shared<MatchPair>(i+1, j+1, idx, nullptr));
      }

      for (int idx = dp; idx > dp - k && idx > 0 && idx < static_cast<int>(compressed_table.size()) && 
           compressed_table[idx] && j < compressed_table[idx]->end_col; --idx) {
        compressed_table[idx] = match_pair_end;
      }
    } else { // LCSk
      int idx = match_pair_end->dp / k;
      if (idx == static_cast<int>(compressed_table.size())) {
        compressed_table.emplace_back(match_pair_end);
      } else if (idx < static_cast<int>(compressed_table.size()) && 
                 compressed_table[idx] && j < compressed_table[idx]->end_col) {
        compressed_table[idx] = match_pair_end;
      }
    }
  }

  prev_row.swap(curr_row);
}

inline void AmortizedRowQuery(const int k, const int row, MatchEventsQueue* events_ptr,
                       std::vector<std::shared_ptr<MatchPair>>* compressed_table_ptr) {
  auto& events = *events_ptr;
  auto& compressed_table = *compressed_table_ptr;

  int curr_threshold_index = 0;
  std::tuple<int, int, std::shared_ptr<MatchPair>> event;

  while (events.PopBegin(row, &event)) {
    int i = std::get<0>(event);
    int j = std::get<1>(event);
    LCSK_ASSERT(i == row);
    while (curr_threshold_index < static_cast<int>(compressed_table.size()) &&
           compressed_table[curr_threshold_index]->end_col < j) {
        ++curr_threshold_index;
    }

    auto prev_best = compressed_table[curr_threshold_index - 1];
    auto match_pair = std::make_shared<MatchPair>(i + k - 1, j + k - 1, k, nullptr);
    if (prev_best->dp > 0) {
      match_pair->dp = prev_best->dp + k;
      match_pair->prev = prev_best;
    }
    events.AddEnd(std::make_tuple(i + k - 1, j + k - 1, match_pair));
  }
}

inline void ElementwiseRowQuery(const int k, const int row, MatchEventsQueue* events_ptr,
                         std::vector<std::shared_ptr<MatchPair>>* compressed_table_ptr) {
  auto& events = *events_ptr;
  auto& compressed_table = *compressed_table_ptr;

  std::tuple<int, int, std::shared_ptr<MatchPair>> event;

  while (events.PopBegin(row, &event)) {
    int i = std::get<0>(event);
    int j = std::get<1>(event);
    LCSK_ASSERT(i == row);

    auto dummy_match_pair = std::make_shared<MatchPair>(0, j, 0, nullptr);
    auto prev_best = std::lower_bound(compressed_table.begin(), compressed_table.end(),
                                     dummy_match_pair, CompareByCol) - 1;
    
    // We reuse dummy_match_pair in order to keep the object counters precise
    // (otherwise the objects_created counter would roughly double due to
    // instantiation of the dummy object).
    //
    // The following several lines can be read as:
    // auto match_pair =
    //    std::make_shared<MatchPair>(i + k - 1, j + k - 1, k, nullptr);
    auto match_pair = dummy_match_pair;
    match_pair->end_row = i + k - 1;
    match_pair->end_col = j + k - 1;
    match_pair->dp = k;

    if ((*prev_best)->dp > 0) {
      match_pair->dp = (*prev_best)->dp + k;
      match_pair->prev = *prev_best;
    }
    events.AddEnd(std::make_tuple(i + k - 1, j + k - 1, match_pair));
  }
}

void LcsKSparseFastImpl(const std::string& a, const std::string& b, int k,
                        std::vector<std::pair<int, int>>* lcsk_reconstruction,
                        const bool lcsk_plus) {
  if (lcsk_reconstruction == nullptr) return;
  
  if (k <= 0 || a.empty() || b.empty()) {
    lcsk_reconstruction->clear();
    return;
  }
  
  if (k > static_cast<int>(a.size()) || k > static_cast<int>(b.size())) {
    lcsk_reconstruction->clear();
    return;
  }
  
  lcsk_reconstruction->clear();

  MatchEventsQueue events;
  auto match_maker = MatchMaker::Create(a, b, k, PERFECT_HASH);
  
  if (!match_maker) {
    return;
  }

  std::vector<std::shared_ptr<MatchPair>> compressed_table;
  
  auto initial_pair = std::make_shared<MatchPair>(-1, -1, 0, nullptr);
  if (!initial_pair) {
    return;
  }
  compressed_table.emplace_back(initial_pair);
  
  std::vector<std::shared_ptr<MatchPair>> prev_row_match_pairs;

  for (int row = 0; row <= static_cast<int>(a.size()); ++row) {
    std::vector<int> row_matches;
    match_maker->GetNextMatches(&row_matches);
    for (int col : row_matches) {
      events.AddBegin(std::make_tuple(row, col, nullptr));
    }

    int table_row_size = compressed_table.size();
    int num_begin_events = row_matches.size();
    bool use_amortized_row_update = (table_row_size + num_begin_events <
                                     6 * num_begin_events * std::log(table_row_size) / std::log(2));

    if (use_amortized_row_update) {
      AmortizedRowQuery(k, row, &events, &compressed_table);
    } else {
      ElementwiseRowQuery(k, row, &events, &compressed_table);
    }

    RowUpdate(k, row, &events, &compressed_table, &prev_row_match_pairs, lcsk_plus);
  }

  std::shared_ptr<MatchPair> best = nullptr;
  if (!compressed_table.empty() && compressed_table.back() != nullptr && 
      compressed_table.back()->end_row != -1) {
    best = compressed_table.back();
  }
  
  FillLcskReconstruction(k, best, lcsk_reconstruction);
}

} // namespace

// ==================== 公共接口 ====================

// 快速LCSk算法
inline void LcsKSparseFast(const std::string& a, const std::string& b, int k,
                    std::vector<std::pair<int, int>>* lcsk_reconstruction) {
  if (!lcsk_reconstruction) return;
  
  if (a.empty() || b.empty() || k <= 0) {
    lcsk_reconstruction->clear();
    return;
  }
  
  try {
    LcsKSparseFastImpl(a, b, k, lcsk_reconstruction, false);
  } catch (const std::exception& e) {
    lcsk_reconstruction->clear();
  }
}

// 快速LCSk++算法
inline void LcsKppSparseFast(const std::string& a, const std::string& b, int k,
                      std::vector<std::pair<int, int>>* lcsk_reconstruction) {
  if (!lcsk_reconstruction) return;
  
  if (a.empty() || b.empty() || k <= 0) {
    lcsk_reconstruction->clear();
    return;
  }
  
  try {
    LcsKSparseFastImpl(a, b, k, lcsk_reconstruction, true);
  } catch (const std::exception& e) {
    lcsk_reconstruction->clear();
  }
}

// ==================== 慢速算法实现（用于验证） ====================

// 准备字母表映射
static void PrepareAlphabetSlow(const std::string& a, const std::string& b,
                               std::vector<char>& aid, int& alphabet_size) {
  aid = std::vector<char>(256, -1);
  alphabet_size = 0;

  for (size_t i = 0; i < a.size(); ++i) {
    if (aid[a[i]] == -1) {
      aid[a[i]] = alphabet_size++;
    }
  }
  for (size_t i = 0; i < b.size(); ++i) {
    if (aid[b[i]] == -1) {
      aid[b[i]] = alphabet_size++;
    }
  }
}

// 获取匹配对
static void GetMatches(const std::string& a, const std::string& b, const int k,
                       std::vector<std::pair<int, int>>* matches) {
  matches->clear();
  
  std::vector<char> aid;
  int alphabet_size;
  PrepareAlphabetSlow(a, b, aid, alphabet_size);
  
  // 计算 alphabet_size^k
  unsigned long long max_hash = 1;
  for (int i = 0; i < k; ++i) {
    max_hash *= alphabet_size;
  }
  
  std::unordered_map<unsigned long long, std::vector<int>> b_positions;
  
  // 计算字符串b的所有k-mer哈希值
  if (static_cast<int>(b.size()) >= k) {
    unsigned long long hash = 0;
    unsigned long long power = 1;
    
    // 计算第一个k-mer的哈希值
    for (int i = 0; i < k; ++i) {
      hash = hash * alphabet_size + aid[b[i]];
      if (i < k - 1) power *= alphabet_size;
    }
    b_positions[hash].push_back(0);
    
    // 滚动哈希计算剩余的k-mer
    for (int i = k; i < static_cast<int>(b.size()); ++i) {
      hash = (hash - aid[b[i - k]] * power) * alphabet_size + aid[b[i]];
      b_positions[hash].push_back(i - k + 1);
    }
  }
  
  // 计算字符串a的所有k-mer哈希值并查找匹配
  if (static_cast<int>(a.size()) >= k) {
    unsigned long long hash = 0;
    unsigned long long power = 1;
    
    // 计算第一个k-mer的哈希值
    for (int i = 0; i < k; ++i) {
      hash = hash * alphabet_size + aid[a[i]];
      if (i < k - 1) power *= alphabet_size;
    }
    
    // 查找匹配
    auto it = b_positions.find(hash);
    if (it != b_positions.end()) {
      for (int pos : it->second) {
        matches->push_back(std::make_pair(0, pos));
      }
    }
    
    // 滚动哈希计算剩余的k-mer
    for (int i = k; i < static_cast<int>(a.size()); ++i) {
      hash = (hash - aid[a[i - k]] * power) * alphabet_size + aid[a[i]];
      
      auto it = b_positions.find(hash);
      if (it != b_positions.end()) {
        for (int pos : it->second) {
          matches->push_back(std::make_pair(i - k + 1, pos));
        }
      }
    }
  }
  
  std::sort(matches->begin(), matches->end());
}

// 填充LCSk重构结果
static void FillLcskReconstructionSlow(const std::vector<std::pair<int, int>>& matches,
                                       const int k, const std::vector<int>& prev_idx,
                                       const int last_idx,
                                       std::vector<std::pair<int, int>>* lcsk_recon,
                                       const bool /* lcskpp */) {
  if (lcsk_recon == nullptr) return;
  lcsk_recon->clear();

  for (int i = last_idx; i != -1; i = prev_idx[i]) {
    int r = matches[i].first + k - 1;
    int c = matches[i].second + k - 1;

    if (prev_idx[i] == -1 || 
        (matches[prev_idx[i]].first + k <= matches[i].first &&
         matches[prev_idx[i]].second + k <= matches[i].second)) {
      // 取整个匹配间隔
      for (int j = 0; j < k; ++j, --r, --c) {
        lcsk_recon->push_back(std::make_pair(r, c));
      }
    } else {
      // 否则是延续（仅LCSk++）。仅取最后一个字符
      lcsk_recon->push_back(std::make_pair(r, c));
    }
  }

  std::reverse(lcsk_recon->begin(), lcsk_recon->end());
}

// 慢速LCSk算法内部实现
static void LcskSparseSlowImpl(const std::vector<std::pair<int, int>>& matches, const int k,
                               std::vector<std::pair<int, int>>* lcsk_reconstruction,
                               const bool lcskpp) {
  if (!std::is_sorted(matches.begin(), matches.end())) {
    return;
  }

  if (matches.empty()) {
    lcsk_reconstruction->clear();
  } else {
    int n = matches.size();
    std::vector<int> dp(n);
    std::vector<int> recon(n);
    int best_idx = 0;

    for (int i = 0; i < n; ++i) {
      dp[i] = k;
      recon[i] = -1;

      for (int j = i - 1; j >= 0; --j) {
        if (matches[j].first + k <= matches[i].first &&
            matches[j].second + k <= matches[i].second) {
          // 1) 取整个匹配间隔并继续另一个"结束"的匹配
          if (dp[j] + k > dp[i]) {
            dp[i] = dp[j] + k;
            recon[i] = j;
          }
        }

        if (lcskpp && matches[j].first + 1 == matches[i].first &&
            matches[j].second + 1 == matches[i].second) {
          // 2) 延续
          if (dp[j] + 1 > dp[i]) {
            dp[i] = dp[j] + 1;
            recon[i] = j;
          }
        }
      }

      if (dp[i] > dp[best_idx]) {
        best_idx = i;
      }
    }

    FillLcskReconstructionSlow(matches, k, recon, best_idx, lcsk_reconstruction, lcskpp);
  }
}

// 慢速LCSk算法对外接口
inline void LcskSparseSlow(const std::string& a, const std::string& b, const int k,
                           std::vector<std::pair<int, int>>* lcsk_reconstruction) {
  if (lcsk_reconstruction == nullptr) return;
  
  std::vector<std::pair<int, int>> matches;
  GetMatches(a, b, k, &matches);
  LcskSparseSlowImpl(matches, k, lcsk_reconstruction, false);
}

// 慢速LCSk++算法对外接口
inline void LcskppSparseSlow(const std::string& a, const std::string& b, const int k,
                             std::vector<std::pair<int, int>>* lcsk_reconstruction) {
  if (lcsk_reconstruction == nullptr) return;
  
  std::vector<std::pair<int, int>> matches;
  GetMatches(a, b, k, &matches);
  LcskSparseSlowImpl(matches, k, lcsk_reconstruction, true);
}

// 验证LCSk结果
inline bool ValidLcsk(const std::string& a, const std::string& b, const int k,
                      const std::vector<std::pair<int, int>>& lcsk_recon) {
  // 1) 验证每个匹配是否有效
  for (auto match : lcsk_recon) {
    int i = match.first;
    int j = match.second;

    if (i < 0 || i >= static_cast<int>(a.size())) {
      return false;
    }
    if (j < 0 || j >= static_cast<int>(b.size())) {
      return false;
    }
    if (a[i] != b[j]) {
      return false;
    }
  }

  // 2) 确保索引序列具有正确的长度
  int run_a = 1;
  int run_b = 1;
  for (size_t i = 1; i < lcsk_recon.size(); ++i) {
    if (lcsk_recon[i - 1].first >= lcsk_recon[i].first) {
      return false;
    }
    if (lcsk_recon[i - 1].second >= lcsk_recon[i].second) {
      return false;
    }

    if (lcsk_recon[i - 1].first + 1 == lcsk_recon[i].first) {
      ++run_a;
    }
    if (lcsk_recon[i - 1].second + 1 == lcsk_recon[i].second) {
      ++run_b;
    }

    if (i + 1 == lcsk_recon.size() ||
        lcsk_recon[i - 1].first + 1 != lcsk_recon[i].first) {
      if (run_a % k != 0) {
        return false;
      }
      run_a = 1;
    }

    if (i + 1 == lcsk_recon.size() ||
        lcsk_recon[i - 1].second + 1 != lcsk_recon[i].second) {
      if (run_b % k != 0) {
        return false;
      }
      run_b = 1;
    }
  }

  return true;
}

// 验证LCSk++结果
inline bool ValidLcskpp(const std::string& a, const std::string& b, const int k,
                        const std::vector<std::pair<int, int>>& lcsk_recon) {
  // 1) 验证每个匹配是否有效
  for (auto match : lcsk_recon) {
    int i = match.first;
    int j = match.second;

    if (i < 0 || i >= static_cast<int>(a.size())) {
      return false;
    }
    if (j < 0 || j >= static_cast<int>(b.size())) {
      return false;
    }
    if (a[i] != b[j]) {
      return false;
    }
  }

  // 2) 确保索引序列具有正确的长度（至少k）
  int run_a = 1;
  int run_b = 1;
  for (size_t i = 1; i < lcsk_recon.size(); ++i) {
    if (lcsk_recon[i - 1].first >= lcsk_recon[i].first) {
      return false;
    }
    if (lcsk_recon[i - 1].second >= lcsk_recon[i].second) {
      return false;
    }

    if (lcsk_recon[i - 1].first + 1 == lcsk_recon[i].first) {
      ++run_a;
    }
    if (lcsk_recon[i - 1].second + 1 == lcsk_recon[i].second) {
      ++run_b;
    }

    if (i + 1 == lcsk_recon.size() ||
        lcsk_recon[i - 1].first + 1 != lcsk_recon[i].first) {
      if (run_a < k) {
        return false;
      }
      run_a = 1;
    }

    if (i + 1 == lcsk_recon.size() ||
        lcsk_recon[i - 1].second + 1 != lcsk_recon[i].second) {
      if (run_b < k) {
        return false;
      }
      run_b = 1;
    }
  }

  return true;
}

// 辅助函数：min3
static int min3(int a, int b, int c) { 
  return std::min(std::min(a, b), c); 
}

// 慢速LCSk长度计算
inline void LcskSlow(const std::string& a, const std::string& b, const int K, int* lcsk_length) {
  if (lcsk_length == nullptr) return;
  
  std::vector<std::vector<int>> dp(a.size() + 1, std::vector<int>(b.size() + 1));

  for (int i = 0; i <= static_cast<int>(a.size()); ++i) dp[i][0] = 0;
  for (int j = 0; j <= static_cast<int>(b.size()); ++j) dp[0][j] = 0;

  for (int i = 1; i <= static_cast<int>(a.size()); ++i) {
    for (int j = 1; j <= static_cast<int>(b.size()); ++j) {
      dp[i][j] = std::max(dp[i - 1][j], dp[i][j - 1]);
      
      // 2*K足够，因为更大的值被某些先前的匹配间隔覆盖
      for (int k = 1; k <= min3(i, j, 2*K); ++k) {
        char aa = a[i - k];
        char bb = b[j - k];
        if (aa != bb) {
          break;
        }

        if (k == K) {
          dp[i][j] = std::max(dp[i][j], dp[i - k][j - k] + k);
        }
      }
    }
  }

  *lcsk_length = dp[a.size()][b.size()];
}

// 慢速LCSk++长度计算
inline void LcskppSlow(const std::string& a, const std::string& b, const int K, int* lcsk_length) {
  if (lcsk_length == nullptr) return;
  
  std::vector<std::vector<int>> dp(a.size() + 1, std::vector<int>(b.size() + 1));

  for (int i = 0; i <= static_cast<int>(a.size()); ++i) dp[i][0] = 0;
  for (int j = 0; j <= static_cast<int>(b.size()); ++j) dp[0][j] = 0;

  for (int i = 1; i <= static_cast<int>(a.size()); ++i) {
    for (int j = 1; j <= static_cast<int>(b.size()); ++j) {
      dp[i][j] = std::max(dp[i - 1][j], dp[i][j - 1]);
      
      // 2*K足够，因为更大的值被某些先前的匹配间隔覆盖
      for (int k = 1; k <= min3(i, j, 2*K); ++k) {
        char aa = a[i - k];
        char bb = b[j - k];
        if (aa != bb) {
          break;
        }

        if (k >= K) {
          dp[i][j] = std::max(dp[i][j], dp[i - k][j - k] + k);
        }
      }
    }
  }

  *lcsk_length = dp[a.size()][b.size()];
}

#endif  // FAST_SIMPLE_LCSK_H
