#ifndef MAIN_MONITOR_H
#define MAIN_MONITOR_H

#include "psfsmon.h"
#include "process_monitor.h"
#include "file_monitor.h"
#include "network_monitor.h"
#include "kernel_sequence_monitor.h"
#include <signal.h>
#include <fstream>

// 主监控器状态
enum class MonitorStatus {
    STOPPED,
    STARTING,
    RUNNING,
    STOPPING,
    ERROR
};

// 主监控器类
class MainMonitor {
public:
    MainMonitor();
    ~MainMonitor();
    
    // 初始化
    bool initialize(const MonitorConfig& config);
    
    // 启动监控
    bool start();
    
    // 停止监控
    void stop();
    
    // 重新加载配置
    bool reloadConfig(const MonitorConfig& config);
    
    // 获取状态
    MonitorStatus getStatus() const { return status_; }
    
    // 获取配置
    const MonitorConfig& getConfig() const { return config_; }
    
    // 获取统计信息
    MonitorStats getStats() const;
    
    // 获取进程监控器
    ProcessMonitor* getProcessMonitor() const;
    
    // 获取文件监控器
    FileMonitor* getFileMonitor() const;
    
    // 获取网络监控器
    NetworkMonitor* getNetworkMonitor() const;
    
    // 守护进程模式
    bool daemonize();
    
    // 信号处理
    static void signalHandler(int signal);
    static MainMonitor* instance_;

private:
    // 配置和状态
    MonitorConfig config_;
    std::atomic<MonitorStatus> status_;
    
    // 监控器实例
    // process_monitor_ 移除，现在使用ProcessMonitorManager::getInstance()
    std::unique_ptr<FileMonitor> file_monitor_;
    std::unique_ptr<NetworkMonitor> network_monitor_;
    std::unique_ptr<KernelSequenceMonitor> kernel_sequence_monitor_;
    
    // 事件处理
    void handleFileEvent(const FileEvent& event);
    
    // 统计信息
    MonitorStats stats_;
    mutable std::mutex MainMonitor_stats_mutex_;
    
    // 状态监控线程
    std::thread status_thread_;
    std::atomic<bool> status_thread_running_;
    void statusMonitorLoop();
    
    // 初始化各个模块
    bool initializeProcessMonitor();
    bool initializeFileMonitor();
    bool initializeNetworkMonitor();
    bool initializeKernelSequenceMonitor();
    
    // 启动各个模块
    bool startProcessMonitor();
    bool startFileMonitor();
    bool startNetworkMonitor();
    bool startKernelSequenceMonitor();
    
    // 停止各个模块
    void stopProcessMonitor();
    void stopFileMonitor();
    void stopNetworkMonitor();
    void stopKernelSequenceMonitor();
    
    // 清理资源
    void cleanup();
    
    // 日志
    void logError(const std::string& message);
    void logInfo(const std::string& message);
    void logDebug(const std::string& message);
    
    // 权限检查
    bool checkPermissions();
    bool hasRootPrivileges();
    
    // 系统兼容性检查
    bool checkSystemCompatibility();
    bool checkKernelVersion();
    bool checkRequiredFeatures();
    
    // 信号处理设置
    void setupSignalHandlers();
    static std::atomic<bool> shutdown_requested_;
    
    // PID文件管理
    std::string pid_file_path_;
    bool createPidFile();
    void removePidFile();
    
    // 性能监控
    void updatePerformanceStats();
    uint64_t start_time_;
    uint64_t last_stats_update_;
};

// 配置管理器
class ConfigManager {
public:
    static ConfigManager& getInstance();
    
    // 加载配置
    bool loadConfig(const std::string& config_file, MonitorConfig& config);
    
    // 保存配置
    bool saveConfig(const std::string& config_file, const MonitorConfig& config);
    
    // 解析命令行参数
    bool parseCommandLine(int argc, char* argv[], MonitorConfig& config);
    
    // 解析命令行参数（覆盖模式，不重置配置）
    bool parseCommandLineOverlay(int argc, char* argv[], MonitorConfig& config);
    
    // 创建默认配置文件
    bool createDefaultConfigFile(const std::string& config_file);
    
    // 打印帮助信息
    void printHelp(const char* program_name);
    
    // 打印版本信息
    void printVersion();

private:
    ConfigManager() = default;
    ~ConfigManager() = default;
    
    // 禁用拷贝
    ConfigManager(const ConfigManager&) = delete;
    ConfigManager& operator=(const ConfigManager&) = delete;
    
    // JSON配置解析
    bool parseJsonConfig(const std::string& json_content, MonitorConfig& config);
    
    // 默认配置
    MonitorConfig getDefaultConfig();
    
    // 配置验证
    bool validateConfig(const MonitorConfig& config);
    
    // 字符串解析辅助函数
    bool parseBool(const std::string& str);
    uint32_t parseUint32(const std::string& str);
    std::string trim(const std::string& str);
    std::vector<std::string> split(const std::string& str, char delimiter);
};

// 日志管理器
class LogManager {
public:
    enum LogLevel {
        LOG_DEBUG = 0,    // 改名避免与DEBUG宏冲突
        LOG_INFO,
        LOG_WARNING,
        LOG_ERROR,
        LOG_FATAL
    };
    
    // 日志类型 - 用于控制输出目标
    enum LogType {
        LOG_SYSTEM,       // 系统信息：启动、配置、退出等 - 输出到控制台+文件
        LOG_MONITOR,      // 监控信息：文件事件、进程事件等 - 仅输出到文件
        LOG_STRATEGY,     // 策略信息：监控策略选择等 - 输出到控制台+文件（非daemon）
        LOG_NETWORK,      // 网络信息：网络连接事件等 - 仅输出到文件
        LOG_API,          // API信息：API请求响应等 - 仅输出到文件
        LOG_GENERAL       // 一般信息：按级别决定输出目标
    };
    
    static LogManager& getInstance();
    
    // 初始化日志
    bool initialize(const std::string& log_file, LogLevel file_level = LOG_INFO, LogLevel console_level = LOG_INFO);
    
    // 设置daemon模式（影响策略信息是否输出到控制台）
    void setDaemonMode(bool daemon_mode) { daemon_mode_ = daemon_mode; }
    
    // 记录日志 - 新接口，支持日志类型
    void log(LogLevel level, LogType type, const std::string& message);
    void log(LogLevel level, LogType type, const char* format, ...);
    
    // 兼容旧接口
    void log(LogLevel level, const std::string& message) { log(level, LOG_GENERAL, message); }
    void log(LogLevel level, const char* format, ...);
    
    // 便捷函数 - 系统日志（重要信息，输出到控制台+文件）
    void systemInfo(const std::string& message) { log(LOG_INFO, LOG_SYSTEM, message); }
    void systemWarning(const std::string& message) { log(LOG_WARNING, LOG_SYSTEM, message); }
    void systemError(const std::string& message) { log(LOG_ERROR, LOG_SYSTEM, message); }
    void systemDebug(const std::string& message) { log(LOG_DEBUG, LOG_SYSTEM, message); }
    
    // 便捷函数 - 监控日志（详细信息，仅输出到文件）
    void monitorInfo(const std::string& message) { log(LOG_INFO, LOG_MONITOR, message); }
    void monitorDebug(const std::string& message) { log(LOG_DEBUG, LOG_MONITOR, message); }
    
    // 便捷函数 - 策略日志（策略信息，非daemon时输出到控制台+文件）
    void strategyInfo(const std::string& message) { log(LOG_INFO, LOG_STRATEGY, message); }
    void strategyWarning(const std::string& message) { log(LOG_WARNING, LOG_STRATEGY, message); }
    
    // 便捷函数 - 网络日志（仅输出到文件）
    void networkInfo(const std::string& message) { log(LOG_INFO, LOG_NETWORK, message); }
    void networkDebug(const std::string& message) { log(LOG_DEBUG, LOG_NETWORK, message); }
    
    // 便捷函数 - API日志（仅输出到文件）
    void apiInfo(const std::string& message) { log(LOG_INFO, LOG_API, message); }
    void apiDebug(const std::string& message) { log(LOG_DEBUG, LOG_API, message); }
    
    // 兼容旧接口的便捷函数
    void debug(const std::string& message) { log(LOG_DEBUG, LOG_GENERAL, message); }
    void info(const std::string& message) { log(LOG_INFO, LOG_GENERAL, message); }
    void warning(const std::string& message) { log(LOG_WARNING, LOG_GENERAL, message); }
    void error(const std::string& message) { log(LOG_ERROR, LOG_GENERAL, message); }
    void fatal(const std::string& message) { log(LOG_FATAL, LOG_GENERAL, message); }
    
    // 设置日志级别
    void setFileLogLevel(LogLevel level) { file_log_level_ = level; }
    void setConsoleLogLevel(LogLevel level) { console_log_level_ = level; }
    void setLogLevel(LogLevel level) { file_log_level_ = level; console_log_level_ = level; } // 兼容接口
    
    // 刷新日志
    void flush();

private:
    LogManager() : file_log_level_(LOG_INFO), console_log_level_(LOG_INFO), 
                   daemon_mode_(false), initialized_(false) {}
    ~LogManager();
    
    // 禁用拷贝
    LogManager(const LogManager&) = delete;
    LogManager& operator=(const LogManager&) = delete;
    
    LogLevel file_log_level_;      // 文件日志级别
    LogLevel console_log_level_;   // 控制台日志级别
    bool daemon_mode_;             // 是否为daemon模式
    std::string log_file_;
    std::ofstream log_stream_;
    std::mutex LogManager_log_mutex_;
    bool initialized_;
    
    // 判断是否应该输出到控制台
    bool shouldOutputToConsole(LogLevel level, LogType type);
    
    // 判断是否应该输出到文件
    bool shouldOutputToFile(LogLevel level, LogType type);
    
    // 获取时间戳
    std::string getCurrentTimestamp();
    
    // 获取日志级别字符串
    std::string logLevelToString(LogLevel level);
    
    // 获取日志类型字符串
    std::string logTypeToString(LogType type);
};

// 实用工具类
class Utils {
public:
    // 时间相关
    static uint64_t getCurrentTimestamp();
    static std::string formatTimestamp(uint64_t timestamp);
    static std::string formatDuration(uint64_t seconds);
    
    // 内存相关
    static std::string formatBytes(uint64_t bytes);
    static uint64_t parseMemorySize(const std::string& size_str);
    
    // 文件系统相关
    static bool fileExists(const std::string& path);
    static bool directoryExists(const std::string& path);
    static bool createDirectory(const std::string& path);
    static std::string readFileContent(const std::string& path);
    static bool writeFileContent(const std::string& path, const std::string& content);
    
    // 字符串相关
    static std::string trim(const std::string& str);
    static std::vector<std::string> split(const std::string& str, char delimiter);
    static std::string join(const std::vector<std::string>& vec, const std::string& delimiter);
    static bool startsWith(const std::string& str, const std::string& prefix);
    static bool endsWith(const std::string& str, const std::string& suffix);
    static std::string toLower(const std::string& str);
    static std::string toUpper(const std::string& str);
    
    // 进程相关
    static pid_t getCurrentPid();
    static std::string getCurrentUser();
    static bool isRootUser();
    static bool processExists(pid_t pid);

    // 统一的进程名获取函数 - 优先使用cmdline，备选comm
    static std::string getProcessName(pid_t pid);
    
    // 新增：更严格的进程存在检查，用于死进程清理
    static bool processExistsStrict(pid_t pid);
    
    // 系统信息
    static std::string getKernelVersion();
    static uint32_t getCpuCores();
    static uint64_t getTotalMemory();
    static uint64_t getPageSize();
    
    // 网络相关
    static bool isValidIpAddress(const std::string& ip);
    static bool isValidPort(uint16_t port);
    
    // 哈希函数
    static uint64_t hash64(const std::string& str);
    static uint32_t hash32(const std::string& str);
};

#endif // MAIN_MONITOR_H
