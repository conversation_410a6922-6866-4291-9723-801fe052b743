#ifndef API_SERVER_H
#define API_SERVER_H

#include "psfsmon.h"
#include "main_monitor.h"
#include "process_exclusion.h"
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <queue>
#include <condition_variable>

// HTTP状态码
enum class HttpStatus {
    OK = 200,
    CREATED = 201,
    NO_CONTENT = 204,
    BAD_REQUEST = 400,
    UNAUTHORIZED = 401,
    FORBIDDEN = 403,
    NOT_FOUND = 404,
    METHOD_NOT_ALLOWED = 405,
    INTERNAL_SERVER_ERROR = 500,
    NOT_IMPLEMENTED = 501,
    SERVICE_UNAVAILABLE = 503
};

// HTTP方法
enum class HttpMethod {
    GET,
    POST,
    PUT,
    DELETE,
    PATCH,
    HEAD,
    OPTIONS,
    UNKNOWN
};

// HTTP请求结构
struct HttpRequest {
    HttpMethod method;
    std::string path;
    std::string query_string;
    std::map<std::string, std::string> headers;
    std::map<std::string, std::string> query_params;
    std::string body;
    std::string client_ip;
    uint16_t client_port;
    
    HttpRequest() : method(HttpMethod::UNKNOWN), client_port(0) {}
};

// HTTP响应结构
struct HttpResponse {
    HttpStatus status;
    std::map<std::string, std::string> headers;
    std::string body;
    std::string content_type;
    
    HttpResponse() : status(HttpStatus::OK), content_type("application/json") {}
};

// API端点处理器
typedef std::function<void(const HttpRequest&, HttpResponse&)> ApiHandler;

// 简单的HTTP服务器类
class HttpServer {
public:
    HttpServer();
    ~HttpServer();
    
    // 初始化服务器
    bool initialize(uint16_t port, const std::string& bind_address = "0.0.0.0");
    
    // 启动服务器
    bool start();
    
    // 停止服务器
    void stop();
    
    // 注册路由
    void registerRoute(HttpMethod method, const std::string& path, ApiHandler handler);
    
    // 设置静态文件目录
    void setStaticDirectory(const std::string& directory);
    
    // 设置请求超时
    void setRequestTimeout(uint32_t timeout_seconds);

private:
    // 服务器配置
    uint16_t port_;
    std::string bind_address_;
    int server_socket_;
    std::atomic<bool> running_;
    std::atomic<bool> stop_requested_;
    
    // 路由表
    std::map<std::pair<HttpMethod, std::string>, ApiHandler> routes_;
    std::mutex HttpServer_routes_mutex_;

    // 线程池
    std::vector<std::thread> worker_threads_;
    std::queue<int> client_sockets_;
    std::mutex HttpServer_socket_queue_mutex_;
    std::condition_variable socket_condition_;
    size_t max_worker_threads_;
    
    // 静态文件服务
    std::string static_directory_;
    
    // 配置
    uint32_t request_timeout_;
    size_t max_request_size_;
    size_t max_header_size_;
    
    // 服务器主循环
    void serverLoop();
    
    // 工作线程
    void workerLoop();
    
    // 处理客户端连接
    void handleClient(int client_socket);
    
    // HTTP解析
    bool parseHttpRequest(const std::string& request_data, HttpRequest& request);
    std::string buildHttpResponse(const HttpResponse& response);
    
    // 路由匹配
    ApiHandler findHandler(HttpMethod method, const std::string& path);
    bool matchRoute(const std::string& pattern, const std::string& path);
    
    // 静态文件处理
    void handleStaticFile(const HttpRequest& request, HttpResponse& response);
    std::string getMimeType(const std::string& file_extension);
    
    // 工具函数
    HttpMethod stringToMethod(const std::string& method_str);
    std::string methodToString(HttpMethod method);
    std::string statusToString(HttpStatus status);
    std::map<std::string, std::string> parseQueryString(const std::string& query);
    std::string urlDecode(const std::string& str);
    std::string urlEncode(const std::string& str);
    
    // 日志
    void logError(const std::string& message);
    void logInfo(const std::string& message);
    void logDebug(const std::string& message);
};

// API服务器类
class ApiServer {
public:
    ApiServer();
    ~ApiServer();
    
    // 初始化
    bool initialize(uint16_t port, MainMonitor* monitor);
    
    // 启动服务器
    bool start();
    
    // 停止服务器
    void stop();
    
    // 获取服务器状态
    bool isRunning() const { return http_server_ && running_; }

private:
    // HTTP服务器实例
    std::unique_ptr<HttpServer> http_server_;
    MainMonitor* main_monitor_;
    std::atomic<bool> running_;
    
    // 注册所有API端点
    void registerApiEndpoints();
    
    // 进程相关API
    void handleGetProcessTree(const HttpRequest& request, HttpResponse& response);
    void handleGetProcess(const HttpRequest& request, HttpResponse& response);
    // 注意：根据需求10，已删除handleGetProcessFiles（文件操作序列）功能
    void handleGetAllProcesses(const HttpRequest& request, HttpResponse& response);
    void handleGetProcessesList(const HttpRequest& request, HttpResponse& response);
    
    // 排除进程管理API
    void handleGetExclusions(const HttpRequest& request, HttpResponse& response);
    void handleAddExclusion(const HttpRequest& request, HttpResponse& response);
    void handleRemoveExclusion(const HttpRequest& request, HttpResponse& response);
    
    // 受保护路径管理API
    void handleGetProtectedPaths(const HttpRequest& request, HttpResponse& response);
    void handleAddProtectedPath(const HttpRequest& request, HttpResponse& response);
    void handleRemoveProtectedPath(const HttpRequest& request, HttpResponse& response);
    
    // 重点进程管理API
    void handleGetFocusProcesses(const HttpRequest& request, HttpResponse& response);
    void handleAddFocusProcess(const HttpRequest& request, HttpResponse& response);
    void handleGetFocusProcessEntropy(const HttpRequest& request, HttpResponse& response);
    void handleRemoveFocusProcess(const HttpRequest& request, HttpResponse& response);
    void handleGetFocusProcessFileInfo(const HttpRequest& request, HttpResponse& response);
    
    // 新增：重点进程详细熵值统计API
    void handleGetFocusProcessEntropyStats(const HttpRequest& request, HttpResponse& response);
    
    // 网络相关API
    void handleGetNetworkConnections(const HttpRequest& request, HttpResponse& response);
    void handleGetNetworkStats(const HttpRequest& request, HttpResponse& response);
    void handleGetListeningPorts(const HttpRequest& request, HttpResponse& response);
    
    // 系统统计API
    void handleGetSystemStats(const HttpRequest& request, HttpResponse& response);
    void handleGetMonitorStatus(const HttpRequest& request, HttpResponse& response);
    
    // 配置相关API
    void handleGetConfig(const HttpRequest& request, HttpResponse& response);
    void handleUpdateConfig(const HttpRequest& request, HttpResponse& response);
    
    // 文件监控API
    void handleGetFileEvents(const HttpRequest& request, HttpResponse& response);
    void handleGetFileStats(const HttpRequest& request, HttpResponse& response);
    
    // 实时数据API (WebSocket模拟)
    void handleGetRealtimeData(const HttpRequest& request, HttpResponse& response);
    
    // 健康检查API
    void handleHealthCheck(const HttpRequest& request, HttpResponse& response);
    
    // 错误处理
    void handleNotFound(const HttpRequest& request, HttpResponse& response);
    void handleMethodNotAllowed(const HttpRequest& request, HttpResponse& response);
    void handleInternalError(const HttpRequest& request, HttpResponse& response, 
                            const std::string& error_message);
    
    // JSON序列化
    std::string serializeProcessInfo(const ProcessInfo& process);
    std::string serializeProcessTree(const ProcessTreeNode& node);
    // 删除线程序列化方法
    std::string serializeNetworkConnection(const NetworkConnection& connection);
    // 注意：根据需求10，已删除FileOperation相关功能
    std::string serializeMonitorStats(const MonitorStats& stats);
    std::string serializeMonitorConfig(const MonitorConfig& config);
    
    // JSON反序列化
    bool deserializeMonitorConfig(const std::string& json, MonitorConfig& config);
    
    // 参数解析
    pid_t extractPidFromPath(const std::string& path);
    std::map<std::string, std::string> parseQueryParameters(const HttpRequest& request);
    
    // 响应构建辅助函数
    void setJsonResponse(HttpResponse& response, const std::string& json);
    void setErrorResponse(HttpResponse& response, HttpStatus status, 
                         const std::string& error_message);
    void setCorsHeaders(HttpResponse& response);
    
    // 简单的JSON构建器
    class JsonBuilder {
    public:
        JsonBuilder();
        
        JsonBuilder& startObject();
        JsonBuilder& endObject();
        JsonBuilder& startArray(const std::string& key = "");
        JsonBuilder& endArray();
        JsonBuilder& addKey(const std::string& key);
        JsonBuilder& addObjectKey(const std::string& key); // 添加键并开始对象
        JsonBuilder& addString(const std::string& key, const std::string& value);
        JsonBuilder& addNumber(const std::string& key, int64_t value);
        JsonBuilder& addNumber(const std::string& key, uint64_t value);
        JsonBuilder& addNumber(const std::string& key, double value);
        JsonBuilder& addBool(const std::string& key, bool value);
        JsonBuilder& addNull(const std::string& key);
        JsonBuilder& addStringValue(const std::string& value);
        JsonBuilder& addNumberValue(int64_t value);
        JsonBuilder& addNumberValue(uint64_t value);
        JsonBuilder& addNumberValue(double value);
        JsonBuilder& addBoolValue(bool value);
        JsonBuilder& addNullValue();
        
        std::string toString() const;
        
    private:
        std::string json_;
        std::vector<bool> is_array_stack_; // 跟踪当前是否在数组中
        std::vector<bool> first_element_stack_; // 跟踪是否是第一个元素
        
        void addCommaIfNeeded();
        std::string escapeJsonString(const std::string& str);
    };
    
    // 日志
    void logError(const std::string& message);
    void logInfo(const std::string& message);
    void logDebug(const std::string& message);
};

#endif // API_SERVER_H 