/*
 * 规则匹配信息头文件
 * 用于记录重点进程在规则匹配时的统计信息
 */

#ifndef RULE_MATCH_INFO_H
#define RULE_MATCH_INFO_H

#include <cstdint>
#include <string>
#include <vector>
#include <unordered_map>
#include <memory>
#include <mutex>
#include <cstring>
#include <algorithm>
#include <set>

// 规则等级定义（与内核模块保持一致）
enum RuleLevel {
    // 基本规则等级 - 重命名
    RULE_LEVEL_STRICT_RENAME = 1,
    RULE_LEVEL_SENSITIVE_RENAME = 2,
    RULE_LEVEL_NORMAL_RENAME = 3,
    RULE_LEVEL_LAX_RENAME = 4,
    
    // 基本规则等级 - 删除
    RULE_LEVEL_STRICT_DELETE = 5,
    RULE_LEVEL_SENSITIVE_DELETE = 6,
    RULE_LEVEL_NORMAL_DELETE = 7,
    RULE_LEVEL_LAX_DELETE = 8,
    
    // 变体规则等级
    RULE_LEVEL_VARIANT_DELETE = 9,
    RULE_LEVEL_VARIANT_RENAME = 10,
    
    // 配合规则等级
    RULE_LEVEL_PATCH_DELETE = 11,
    RULE_LEVEL_PATCH_RENAME = 12,
    
    // 调试和特殊等级
    RULE_LEVEL_DEBUG = 13,
    RULE_LEVEL_ALGO = 14
};

// 重点进程规则匹配统计信息
struct RuleMatchStats {
    // 基本规则触发计数
    uint32_t base_delete_rule_counts;      // 基本删除规则触发数
    uint32_t base_rename_rule_counts;      // 基本重命名规则触发数
    
    // 变体规则触发计数
    uint32_t change_delete_rule_counts;    // 删除类变体规则触发数
    uint32_t change_rename_rule_counts;    // 重命名类变体规则触发数
    
    // 配合规则触发计数
    uint32_t patch_delete_rule_counts;     // 删除类配合规则触发数
    uint32_t patch_rename_rule_counts;     // 重命名类配合规则触发数
    
    // 时间相关
    uint64_t last_rule_catch_ticket;       // 最后的触发时间（毫秒时间戳）
    
    // 总体统计
    uint32_t all_catch_count;              // 总触发数
    uint32_t delete_rename_more_times_rule_count;  // 定期删除重命名次数规则触发数
    
    // 文件操作统计（从内核特征字符串计算）
    uint32_t all_delete_times;             // 总的删除文件次数
    uint32_t all_rename_times;             // 总的重命名文件次数
    uint32_t all_watcher_catch_count;      // 总触发数后被监控的次数
    
    // 扩展名统计
    uint32_t source_ext_name_count;        // 源扩展名数
    uint32_t target_ext_name_count;        // 目标扩展名数
    uint32_t rename_ext_name_count;        // 重命名扩展名数
    
    // 进程标识信息
    uint32_t pid_info;                     // 进程标识（白名单等）
    uint32_t pid_value;                    // 进程ID值
    
    // 扩展信息
    char most_target_ext_name[32];         // 最多的目标扩展名
    char most_rename_ext_name[32];         // 最多的重命名扩展名
    
    // 构造函数
    RuleMatchStats() {
        reset();
    }
    
    // 重置所有统计信息
    void reset() {
        base_delete_rule_counts = 0;
        base_rename_rule_counts = 0;
        change_delete_rule_counts = 0;
        change_rename_rule_counts = 0;
        patch_delete_rule_counts = 0;
        patch_rename_rule_counts = 0;
        last_rule_catch_ticket = 0;
        all_catch_count = 0;
        delete_rename_more_times_rule_count = 0;
        all_delete_times = 0;
        all_rename_times = 0;
        all_watcher_catch_count = 0;
        source_ext_name_count = 0;
        target_ext_name_count = 0;
        rename_ext_name_count = 0;
        pid_info = 0;
        pid_value = 0;
        
        memset(most_target_ext_name, 0, sizeof(most_target_ext_name));
        memset(most_rename_ext_name, 0, sizeof(most_rename_ext_name));
    }
    
    // 根据规则等级增加计数
    void incrementRuleCount(int rule_level) {
        all_catch_count++;
        
        // 使用数值范围判断，避免枚举冲突
        if (rule_level >= 1 && rule_level <= 4) {
            // 基本重命名规则 (1-4)
            base_rename_rule_counts++;
        } else if (rule_level >= 5 && rule_level <= 8) {
            // 基本删除规则 (5-8)
            base_delete_rule_counts++;
        } else if (rule_level == 9) {
            // 变体删除规则
            change_delete_rule_counts++;
        } else if (rule_level == 10) {
            // 变体重命名规则
            change_rename_rule_counts++;
        } else if (rule_level == 11) {
            // 配合删除规则
            patch_delete_rule_counts++;
        } else if (rule_level == 12) {
            // 配合重命名规则
            patch_rename_rule_counts++;
        }
        // 其他值不处理
        
        // 更新时间戳
        last_rule_catch_ticket = getCurrentTimestamp();
    }
    
    // 从内核特征字符串计算文件操作次数
    void calculateFileOperationsFromKernelString(const std::string& kernel_string) {
        // 操作符映射表 - 与内核模块中的定义保持一致
        static const char delete_ops[10] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9'};
        static const char rename_ops[10] = {'&', '\'', '(', ')', '*', '+', ',', '-', '.', '/'};
        
        all_delete_times = 0;
        all_rename_times = 0;
        
        // 统计删除操作次数
        for (char op : delete_ops) {
            all_delete_times += std::count(kernel_string.begin(), kernel_string.end(), op);
        }
        
        // 统计重命名操作次数
        for (char op : rename_ops) {
            all_rename_times += std::count(kernel_string.begin(), kernel_string.end(), op);
        }
    }
    
    // 获取当前时间戳（毫秒）
    static uint64_t getCurrentTimestamp() {
        using namespace std::chrono;
        return duration_cast<milliseconds>(system_clock::now().time_since_epoch()).count();
    }
    
    // 获取规则匹配摘要信息
    std::string getSummary() const;
    
    // 获取JSON格式的统计信息
    std::string toJson() const;
};

// 重点进程规则匹配管理器
class RuleMatchManager {
public:
    static RuleMatchManager& getInstance();
    
    // 初始化
    bool initialize();
    
    // 记录规则匹配
    void recordRuleMatch(pid_t pid, int rule_level, const std::string& kernel_string = "");
    
    // 获取进程规则匹配统计
    std::shared_ptr<RuleMatchStats> getRuleMatchStats(pid_t pid) const;
    
    // 获取所有重点进程的统计信息
    std::vector<std::pair<pid_t, RuleMatchStats>> getAllRuleMatchStats() const;
    
    // 清理过期进程的数据
    void cleanupDeadProcesses(const std::set<pid_t>& alive_pids);
    
    // 重置指定进程的统计
    void resetProcessStats(pid_t pid);
    
    // 重置所有统计
    void resetAllStats();
    
private:
    RuleMatchManager() = default;
    ~RuleMatchManager() = default;
    
    // 禁用拷贝
    RuleMatchManager(const RuleMatchManager&) = delete;
    RuleMatchManager& operator=(const RuleMatchManager&) = delete;
    
    // 进程规则匹配统计
    mutable std::mutex rule_match_mutex_;
    std::unordered_map<pid_t, std::shared_ptr<RuleMatchStats>> process_rule_stats_;
};

#endif // RULE_MATCH_INFO_H
