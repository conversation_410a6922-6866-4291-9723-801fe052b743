/*
 * PSFSMON-L 文件监控器 - 双Fanotify Group架构实现
 * 
 * 本模块是需求13的核心实现，采用双Fanotify Group + 双线程监控策略：
 * 
 * 【需求13核心架构】
 * - 受保护路径监控Group：使用FAN_CLASS_NOTIF + 事件记录实现路径监控
 * - 重点进程监控Group：使用FAN_CLASS_NOTIF + 全局监控实现文件操作收集
 * - 仅使用基础Fanotify特性，确保4.19+内核兼容性
 * - 完全移除Inotify机制，不再使用FAN_REPORT_TID/PIDFD/FID等复杂特性
 * 
 * 【双线程处理策略】
 * - protected_monitor_thread_：专门处理受保护路径事件记录
 * - focus_monitor_thread_：专门处理重点进程文件操作收集
 * - 独立事件循环，避免事件冲突和性能瓶颈
 * 
 * 【内核版本适配】
 * - 4.19+基础版本：使用FAN_MARK_MOUNT逐个标记挂载点
 * - 4.20+优化版本：可选使用FAN_MARK_FILESYSTEM避免枚举挂载点
 * - 自动检测并选择最适合的监控策略
 * 
 * 【线程安全保证】
 * - 所有共享数据结构使用互斥锁保护
 * - 事件统计使用原子操作
 * - 与其他模块（重点进程管理、受保护路径管理）安全协作
 */

#ifndef FILE_MONITOR_H
#define FILE_MONITOR_H

#include "psfsmon.h"
#include <sys/fanotify.h>
#include <functional>
#include <atomic>
#include <thread>
#include <vector>
#include <string>
#include <mutex>
#include <unordered_set>
#include <fstream>

// 文件事件结构体 - 简化版，不再包含复杂的文件操作序列
struct FileEvent {
    enum EventType {
		//OPEN =0,
		READ = 0,
        WRITE,
        DELETE,
        RENAME,
        //DELETERENAME,
        //CLOSE,
        //MODIFY,//区分于写关闭，给受保护路径用
        UNKNOWN
    };
    
    pid_t pid;                      // 进程ID
    EventType type;                 // 事件类型
    std::string file_path;          // 文件路径
    uint64_t timestamp;             // 时间戳
    uint64_t mask;                  // fanotify事件掩码（用于区分FAN_CLOSE_WRITE和FAN_CLOSE_NOWRITE）
    bool is_focus_process;          // 是否为重点进程
    bool is_protected_path;         // 是否为受保护路径
    
    FileEvent() : pid(0), type(UNKNOWN), timestamp(0), mask(0),
                  is_focus_process(false), is_protected_path(false) {}
};

// 文件熵值计算器 - 需求12核心算法
class FileEntropyCalculator {
public:
    // 计算文件的Shannon熵值
    static double calculateFileEntropy(const std::string& file_path);
    
    // 分块计算文件熵值（用于大文件）
    static double calculateFileEntropyInChunks(std::ifstream& file, size_t total_size);
    
    // 计算缓冲区的Shannon熵值
    static double calculateBufferEntropy(const char* buffer, size_t size);
    
private:
    // 计算熵值的核心算法
    static double computeEntropy(const std::vector<size_t>& frequencies, size_t total_bytes);
};

// 事件统计结构体
struct FileEventStats {
    std::atomic<uint64_t> total_events{0};
    std::atomic<uint64_t> protected_path_events{0};
    std::atomic<uint64_t> focus_process_events{0};
    std::atomic<uint64_t> permission_denied_events{0};
    std::atomic<uint64_t> fanotify_events{0};
    std::atomic<uint64_t> entropy_calculations{0};
    
    // 详细事件类型统计
    std::atomic<uint64_t> read_events{0};           // 读事件数量
    std::atomic<uint64_t> write_events{0};          // 写事件数量
    std::atomic<uint64_t> open_events{0};           // 打开事件数量
    std::atomic<uint64_t> close_events{0};          // 关闭事件数量
    //std::atomic<uint64_t> access_events{0};         // 访问事件数量
    //std::atomic<uint64_t> modify_events{0};         // 修改事件数量
    std::atomic<uint64_t> create_events{0};         // 创建事件数量
    std::atomic<uint64_t> delete_events{0};         // 删除事件数量
    std::atomic<uint64_t> move_events{0};           // 移动/重命名事件数量
    std::atomic<uint64_t> permission_events{0};     // 权限检查事件数量
    
    // 按事件类型的熵值计算统计
    std::atomic<uint64_t> read_entropy_calculations{0};    // 读操作熵值计算次数
    std::atomic<uint64_t> write_entropy_calculations{0};   // 写操作熵值计算次数
    //std::atomic<uint64_t> open_entropy_calculations{0};    // 打开操作熵值计算次数
    //std::atomic<uint64_t> close_entropy_calculations{0};   // 关闭操作熵值计算次数
    
    // 重点进程相关统计
    std::atomic<uint64_t> focus_process_files{0};          // 重点进程操作的文件数量
    std::atomic<uint64_t> focus_process_unique_files{0};   // 重点进程操作的唯一文件数量
    
    FileEventStats() = default;
    
    // 拷贝构造函数
    FileEventStats(const FileEventStats& other) :
        total_events(other.total_events.load()),
        protected_path_events(other.protected_path_events.load()),
        focus_process_events(other.focus_process_events.load()),
        permission_denied_events(other.permission_denied_events.load()),
        fanotify_events(other.fanotify_events.load()),
        entropy_calculations(other.entropy_calculations.load()),
        read_events(other.read_events.load()),
        write_events(other.write_events.load()),
        open_events(other.open_events.load()),
        close_events(other.close_events.load()),
        //access_events(other.access_events.load()),
        //modify_events(other.modify_events.load()),
        create_events(other.create_events.load()),
        delete_events(other.delete_events.load()),
        move_events(other.move_events.load()),
        permission_events(other.permission_events.load()),
        read_entropy_calculations(other.read_entropy_calculations.load()),
        write_entropy_calculations(other.write_entropy_calculations.load()),
        //open_entropy_calculations(other.open_entropy_calculations.load()),
        //close_entropy_calculations(other.close_entropy_calculations.load()),
        focus_process_files(other.focus_process_files.load()),
        focus_process_unique_files(other.focus_process_unique_files.load()) {}
};

// 文件监控器主类 - 需求13核心实现
class FileMonitor {
public:
    FileMonitor();
    ~FileMonitor();
    
    // 禁止拷贝和赋值
    FileMonitor(const FileMonitor&) = delete;
    FileMonitor& operator=(const FileMonitor&) = delete;
    
    // 初始化文件监控器
    bool initialize(const MonitorConfig* config);
    
    // 启动/停止监控
    bool start();
    void stop();
    
    // 重新加载配置
    bool reloadConfig(const MonitorConfig* config);
    
    // 设置文件事件回调函数
    void setEventCallback(std::function<void(const FileEvent&)> callback);
    
    // 获取统计信息
    uint64_t getTotalEvents() const;
    FileEventStats getEventStats() const;
    
    // 获取最近事件
    std::vector<FileEvent> getRecentEvents(size_t limit = 100) const;
    
    // 受保护路径动态管理 (public接口供API调用)
    bool addProtectedPathMark(const std::string& path);
    void removeProtectedPathMark(const std::string& path);

private:
    // 配置和状态
    const MonitorConfig* config_;
    std::atomic<bool> running_;
    bool kernel_supports_filesystem_mark_;
    
    // 双Fanotify文件描述符 - 需求13架构
    int protected_fanotify_fd_;  // 受保护路径监控Group  
    int focus_fanotify_fd_;      // 重点进程监控Group
    
    // 双线程监控
    std::thread protected_monitor_thread_;
    std::thread focus_monitor_thread_;
    
    // 事件统计和存储
    mutable std::mutex FileMonitor_stats_mutex_;
    mutable std::mutex FileMonitor_events_mutex_;
    FileEventStats event_stats_;
    std::vector<FileEvent> recent_events_;
    std::function<void(const FileEvent&)> event_callback_;
    static const size_t MAX_RECENT_EVENTS = 1000;

    // 挂载点缓存
    std::vector<std::string> mount_points_;
    mutable std::mutex FileMonitor_mount_points_mutex_;
    
    // 双Fanotify Group初始化
    bool initializeProtectedPathGroup();
    bool initializeFocusProcessGroup();
    
    // 双监控策略设置
    bool setupProtectedPathMonitoring();
    bool setupFocusProcessMonitoring();
    
    // 双线程事件处理循环
    void processProtectedPathEvents();
    void processFocusProcessEvents();
    
    // 事件处理器
    void processEventBuffer(const char* buffer, size_t buffer_len, bool is_protected_path);
    bool handleProtectedPathEvent(struct fanotify_event_metadata* metadata);
    void handleFocusProcessEvent(struct fanotify_event_metadata* metadata);
    
    // 权限检查已移除 - 在FAN_CLASS_NOTIF模式下不再需要权限检查
    
    // 重点进程文件操作处理 - 需求12核心功能
    void processFocusProcessFileOperation(const FileEvent& event);
    void calculateAndRecordEntropy(FileEvent& event);
    void recordFileOperationHistory(pid_t pid, const FileEvent& event);
    
    // 工具方法
    FileEvent::EventType fanotifyMaskToEventType(uint64_t mask);
    std::string getPathFromFd(int fd);
    std::string getProcessName(pid_t pid);
    void addRecentEvent(const FileEvent& event);
    
    // 内核版本检测
    void detectKernelCapabilities();
    bool checkKernelVersion(int major, int minor);
    
    // 挂载点管理
    bool enumerateMountPoints();
    bool shouldMonitorMountPoint(const std::string& mount_point);
    
    // 清理方法
    void cleanup();
    void closeAllFds();
};

// 重点进程管理器 - 需求12支持
class FocusProcessManager {
public:
    static FocusProcessManager& getInstance();
    
    // 重点进程管理
    void addFocusProcess(pid_t pid);
    void addFocusProcess(const std::string& process_name);
    void removeFocusProcess(pid_t pid);
    void removeFocusProcess(const std::string& process_name);
    
    // 检查是否为重点进程
    bool isFocusProcess(pid_t pid) const;
    bool isFocusProcess(const std::string& process_name) const;
    
    // 获取重点进程列表
    std::vector<pid_t> getFocusProcesses() const;
    std::vector<std::string> getFocusProcessNames() const;
    
    // 配置加载
    void loadFromConfig(const std::string& config_str);
    void clear();
    
    // 重新加载配置
    bool reloadConfig(const std::string& config_str);
    
private:
    mutable std::mutex FocusProcessManager_focus_data_mutex_;
    std::unordered_set<pid_t> focus_pids_;
    std::unordered_set<std::string> focus_names_;
    
    // 内部辅助方法
    std::string getProcessNameFromPid(pid_t pid) const;
    
    FocusProcessManager() = default;
    ~FocusProcessManager() = default;
    FocusProcessManager(const FocusProcessManager&) = delete;
    FocusProcessManager& operator=(const FocusProcessManager&) = delete;
};

// 受保护路径管理器 - 需求10支持
class ProtectedPathManager {
public:
    static ProtectedPathManager& getInstance();
    
    // 受保护路径管理
    void addProtectedPath(const std::string& path);
    void removeProtectedPath(const std::string& path);
    bool isProtectedPath(const std::string& path) const;
    
    // 违规处理
    void handleViolation(const std::string& path, pid_t pid, const std::string& process_name);
    
    // 获取受保护路径列表
    std::vector<std::string> getProtectedPaths() const;
    
    // 配置加载
    void loadFromConfig(const std::string& config_str);
    void clear();
    
    // 重新加载配置
    bool reloadConfig(const std::string& config_str, bool terminate_violating_processes);
    
    // 终止违规进程设置
    void setTerminateViolatingProcesses(bool terminate);
    bool shouldTerminateViolatingProcesses() const;

    // 获取违规统计
    size_t getViolationCount() const { return violation_count_; }
    time_t getLastViolationTime() const { return last_violation_time_; }

private:
    mutable std::mutex ProtectedPathManager_protected_paths_mutex_;
    std::unordered_set<std::string> protected_paths_;
    bool terminate_violating_processes_;

    // 违规统计
    std::atomic<size_t> violation_count_{0};
    std::atomic<time_t> last_violation_time_{0};

    // 内部辅助方法
    bool isPathUnderProtected(const std::string& path, const std::string& protected_path) const;
    
    ProtectedPathManager() : terminate_violating_processes_(false) {}
    ~ProtectedPathManager() = default;
    ProtectedPathManager(const ProtectedPathManager&) = delete;
    ProtectedPathManager& operator=(const ProtectedPathManager&) = delete;
};

#endif // FILE_MONITOR_H 