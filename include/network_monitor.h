#ifndef NETWORK_MONITOR_H
#define NETWORK_MONITOR_H

#include "psfsmon.h"
#include <netinet/in.h>
#include <linux/netlink.h>
#include <linux/sock_diag.h>
#include <linux/inet_diag.h>
#include <unistd.h>

// 网络连接状态
enum class ConnectionState {
    ESTABLISHED = 1,    // TCP_ESTABLISHED
    SYN_SENT,          // TCP_SYN_SENT
    SYN_RECV,          // TCP_SYN_RECV
    FIN_WAIT1,         // TCP_FIN_WAIT1
    FIN_WAIT2,         // TCP_FIN_WAIT2
    TIME_WAIT,         // TCP_TIME_WAIT
    CLOSE,             // TCP_CLOSE
    CLOSE_WAIT,        // TCP_CLOSE_WAIT
    LAST_ACK,          // TCP_LAST_ACK
    LISTEN,            // TCP_LISTEN
    CLOSING,           // TCP_CLOSING
    NEW_SYN_RECV,      // TCP_NEW_SYN_RECV
    UNKNOWN
};

// 网络协议类型
enum class ProtocolType {
    TCP,
    UDP,
    TCP6,
    UDP6,
    UNIX,
    UNKNOWN
};

// 网络接口信息
struct NetworkInterface {
    std::string name;           // 接口名称
    std::string ipv4_addr;      // IPv4地址
    std::string ipv6_addr;      // IPv6地址
    uint32_t flags;             // 接口标志
    uint64_t rx_bytes;          // 接收字节数
    uint64_t tx_bytes;          // 发送字节数
    uint64_t rx_packets;        // 接收包数
    uint64_t tx_packets;        // 发送包数
    
    NetworkInterface() : flags(0), rx_bytes(0), tx_bytes(0), 
                        rx_packets(0), tx_packets(0) {}
};

// 网络统计信息
struct NetworkStats {
    uint64_t total_connections;     // 总连接数
    uint64_t tcp_connections;       // TCP连接数
    uint64_t udp_connections;       // UDP连接数
    uint64_t unix_connections;      // Unix socket连接数
    uint64_t listening_ports;       // 监听端口数
    uint64_t established_connections; // 已建立连接数
    uint64_t total_tx_bytes;        // 总发送字节数
    uint64_t total_rx_bytes;        // 总接收字节数
    uint64_t total_bytes_sent;      // 总发送字节数（别名）
    uint64_t total_bytes_received;  // 总接收字节数（别名）
    uint64_t packets_sent;          // 发送包数
    uint64_t packets_received;      // 接收包数
    uint64_t last_update;           // 最后更新时间
    
    NetworkStats() : total_connections(0), tcp_connections(0), udp_connections(0),
                     unix_connections(0), listening_ports(0), established_connections(0),
                     total_tx_bytes(0), total_rx_bytes(0), total_bytes_sent(0), 
                     total_bytes_received(0), packets_sent(0), packets_received(0),
                     last_update(0) {}
};

// 套接字信息
struct SocketInfo {
    uint32_t inode;                 // inode号
    pid_t pid;                      // 进程ID
    ProtocolType protocol;          // 协议类型
    std::string local_addr;         // 本地地址
    uint16_t local_port;            // 本地端口
    std::string remote_addr;        // 远程地址
    uint16_t remote_port;           // 远程端口
    ConnectionState state;          // 连接状态
    uint64_t tx_queue;              // 发送队列大小
    uint64_t rx_queue;              // 接收队列大小
    uint32_t uid;                   // 用户ID
    std::string unix_path;          // Unix socket路径
    
    SocketInfo() : inode(0), pid(0), protocol(ProtocolType::UNKNOWN),
                   local_port(0), remote_port(0), state(ConnectionState::UNKNOWN),
                   tx_queue(0), rx_queue(0), uid(0) {}
};

// 网络监控器类
class NetworkMonitor {
public:
    NetworkMonitor();
    ~NetworkMonitor();
    
    // 初始化
    bool initialize();
    
    // 启动监控
    bool start();
    
    // 停止监控
    void stop();
    
    // 更新网络连接信息
    void updateNetworkConnections();
    
    // 获取进程的网络连接
    std::vector<NetworkConnection> getProcessConnections(pid_t pid) const;
    
    // 获取所有网络连接
    std::vector<SocketInfo> getAllConnections() const;
    
    // 获取网络统计信息
    NetworkStats getNetworkStats() const;
    
    // 获取监听端口
    std::vector<SocketInfo> getListeningPorts() const;
    
    // 获取网络接口信息
    std::vector<NetworkInterface> getNetworkInterfaces() const;
    
    // 获取事件总数
    uint64_t getTotalEvents() const;
    
    // 设置进程监控器引用
    void setProcessMonitor(ProcessMonitor* monitor) { process_monitor_ = monitor; }

private:
    // 监控线程
    std::thread monitor_thread_;
    std::atomic<bool> running_;
    std::atomic<bool> stop_requested_;
    uint32_t update_interval_ms_;
    
    // 进程监控器引用
    ProcessMonitor* process_monitor_;
    
    // 网络连接信息
    std::vector<SocketInfo> current_connections_;
    mutable std::mutex NetworkMonitor_connections_mutex_;

    // 进程到套接字的映射
    std::unordered_map<pid_t, std::vector<uint32_t>> process_to_sockets_;
    std::unordered_map<uint32_t, pid_t> socket_to_process_;
    mutable std::mutex NetworkMonitor_socket_mapping_mutex_;

    // 网络接口信息
    std::vector<NetworkInterface> network_interfaces_;
    mutable std::mutex NetworkMonitor_interfaces_mutex_;
    
    // 事件计数
    std::atomic<uint64_t> total_network_events_;
    
    // 监控循环
    void monitorLoop();
    
    // 连接信息收集
    bool scanTcpConnections();
    bool scanUdpConnections();
    bool scanTcp6Connections();
    bool scanUdp6Connections();
    bool scanUnixSockets();
    
    // /proc/net 文件解析
    bool parseProcNetFile(const std::string& filename, ProtocolType protocol);
    bool parseTcpLine(const std::string& line, ProtocolType protocol, SocketInfo& socket);
    bool parseUdpLine(const std::string& line, ProtocolType protocol, SocketInfo& socket);
    bool parseUnixLine(const std::string& line, SocketInfo& socket);
    
    // 地址解析
    std::pair<std::string, uint16_t> parseAddress(const std::string& addr_str, bool is_ipv6 = false);
    std::string hexToIpv4(const std::string& hex_addr);
    std::string hexToIpv6(const std::string& hex_addr);
    uint16_t hexToPort(const std::string& hex_port);
    
    // 进程-套接字映射
    void updateProcessSocketMapping();
    bool scanProcessFds(pid_t pid);
    uint32_t getSocketInode(const std::string& fd_link);
    
    // 套接字统计信息
    bool updateSocketStats();
    std::unordered_map<uint32_t, std::pair<uint64_t, uint64_t>> socket_stats_; // inode -> (tx, rx)
    
    // 连接状态转换
    ConnectionState intToConnectionState(int state);
    std::string connectionStateToString(ConnectionState state) const;
    std::string protocolTypeToString(ProtocolType protocol) const;
    
    // Netlink套接字诊断 (可选的高级功能)
    bool useNetlinkDiag_;
    int netlink_fd_;
    bool initializeNetlinkDiag();
    void closeNetlinkDiag();
    bool queryNetlinkConnections();
    
    // Netlink 扫描相关方法
    void scanConnectionsWithNetlink();
    void scanNetlinkConnections(int protocol);
    void processNetlinkResponse(const char* buffer, ssize_t len);
    void processInetDiagMessage(const struct inet_diag_msg* diag, const struct nlmsghdr* nlh);
    void parseNetlinkAttributes(const struct nlmsghdr* nlh, SocketInfo& socket);
    
    // Unix socket 扫描
    bool scanUnixConnections();
    
    // TCP状态转换
    ConnectionState tcpStateToConnectionState(int tcp_state);
    
    // 文件系统辅助函数
    std::vector<std::string> readFileLines(const std::string& path);
    std::string readFileContent(const std::string& path);
    bool fileExists(const std::string& path);
    std::vector<std::string> splitString(const std::string& str, char delimiter);
    std::string trim(const std::string& str);
    
    // 进程文件描述符扫描
    std::vector<std::string> getProcessFds(pid_t pid);
    std::string readSymbolicLink(const std::string& link_path);
    
    // 差异检测和事件生成
    std::vector<SocketInfo> previous_connections_;
    void detectConnectionChanges();
    void handleNewConnection(const SocketInfo& socket);
    void handleClosedConnection(const SocketInfo& socket);
    
    // 网络接口管理
    void updateNetworkInterfaces();
    
    // 网络接口统计
    struct InterfaceStats {
        std::string interface_name;
        uint64_t rx_bytes;
        uint64_t tx_bytes;
        uint64_t rx_packets;
        uint64_t tx_packets;
        uint64_t rx_errors;
        uint64_t tx_errors;
        
        InterfaceStats() : rx_bytes(0), tx_bytes(0), rx_packets(0), 
                          tx_packets(0), rx_errors(0), tx_errors(0) {}
    };
    
    std::vector<InterfaceStats> interface_stats_;
    bool updateInterfaceStats();
    bool parseInterfaceStatsLine(const std::string& line, InterfaceStats& stats);
    
    // 性能优化
    time_t last_full_scan_;
    bool needsFullScan() const;
    void performIncrementalUpdate();
    void performFullScan();
    
    // 日志
    void logError(const std::string& message);
    void logInfo(const std::string& message);
    void logDebug(const std::string& message);
    
    // 统计信息
    NetworkStats current_stats_;
    void updateStatistics();
};

// 网络监控管理器
class NetworkMonitorManager {
public:
    static NetworkMonitorManager& getInstance();
    
    bool initialize();
    bool start();
    void stop();
    
    NetworkMonitor* getNetworkMonitor() const { return network_monitor_.get(); }
    
    void setProcessMonitor(ProcessMonitor* monitor);
    
private:
    NetworkMonitorManager() = default;
    ~NetworkMonitorManager() = default;
    
    // 禁用拷贝
    NetworkMonitorManager(const NetworkMonitorManager&) = delete;
    NetworkMonitorManager& operator=(const NetworkMonitorManager&) = delete;
    
    std::unique_ptr<NetworkMonitor> network_monitor_;
};

#endif // NETWORK_MONITOR_H 