# PSFSMON-L 配置文件
# 此文件在首次运行时自动生成，您可以根据需要修改配置

[monitor]
# 启用各种监控功能
enable_file_monitoring = true
enable_network_monitoring = true
enable_process_monitoring = true
update_interval_ms = 1000
max_files_per_thread = 50

[daemon]
# 守护进程配置
daemon_mode = false
pid_file = /var/run/psfsmon.pid

[api]
# API服务器配置
api_port = 8080
bind_address = 127.0.0.1
api_auth_required = false
allowed_api_clients = 127.0.0.1,::1

[logging]
# 日志配置
log_file = /var/log/psfsmon/psfsmon.log
log_level = INFO
max_log_size = 100
max_log_files = 5

[file_monitor]
# Fanotify文件监控配置 - 直接监控受保护路径
# 根据内核版本自动选择监控事件类型
# 注意：监控策略现在基于内核版本自动选择
# - 内核 5.4.18+：使用Fanotify高性能模式
# - 内核 5.4.x：  使用Fanotify完整模式
# - 内核 4.19.x： 使用Fanotify基础模式
use_fanotify_only = true

[focus_process]
# 重点进程监控配置
enable_focus_process_monitoring = true
calculate_file_entropy = true
focus_process_list = 

[protected_path]
# 受保护路径监控配置
enable_protected_path_monitoring = true
protected_paths = 
terminate_violating_processes = true

[network_monitor]
# 网络监控配置
monitor_all_interfaces = true
connection_update_interval = 5
use_netlink_diag = false

[process_monitor]
# 进程监控配置
scan_interval_ms = 2000
full_scan_interval = 60
monitor_all_processes = true
cpu_calc_window = 5

[security]
# 安全配置
require_root = true

[performance]
# 性能配置
worker_threads = 0
max_event_queue_size = 10000
memory_limit_mb = 0
enable_performance_stats = true

[advanced]
# 高级配置
check_kernel_compatibility = true
min_kernel_version = 4.19
enable_experimental_features = false
debug_mode = false
verbose = false
