# 重点进程文件熵值计算功能

## 概述

本项目实现了针对重点进程的精确文件熵值计算系统，能够跟踪文件在打开和关闭写入时的熵值变化，为安全监控提供重要的数据指标。

## 核心设计

### 1. 文件唯一标识符
- 使用文件路径的哈希值作为唯一标识符
- 比文件描述符(fd)更稳定，避免fd重用问题
- 支持跨进程的文件操作跟踪

### 2. 熵值计算时机
- **FAN_OPEN**: 计算原始熵值（如果文件有内容且未计算过）
- **FAN_CLOSE_WRITE**: 计算最终熵值
- **FAN_CLOSE_NOWRITE**: 不计算熵值（不影响文件内容）
- **FAN_ACCESS/FAN_MODIFY**: 不计算熵值（避免过度计算）

### 3. 文件大小限制
- 最大计算文件大小：100MB
- 超过限制的文件只计算前100MB的熵值
- 使用分块读取避免内存不足问题

### 4. 按文件存储
每个重点进程维护一个文件熵值映射表：
```cpp
std::unordered_map<uint64_t, FileEntropyInfo> file_entropy_map;
```

## 数据结构

### FileEntropyInfo
```cpp
struct FileEntropyInfo {
    std::string file_path;           // 文件路径
    uint64_t file_hash;              // 文件路径哈希值
    double original_entropy;         // 原始熵值
    double final_entropy;            // 最终熵值
    double entropy_change;           // 熵值变化量
    bool has_original_entropy;       // 是否已计算原始熵值
    bool has_final_entropy;          // 是否已计算最终熵值
    time_t open_time;                // 文件打开时间
    time_t close_time;               // 文件关闭时间
    size_t file_size;                // 文件大小
};
```

## 核心方法

### FocusProcessFileInfo
- `recordFileOpen()`: 记录文件打开时的原始熵值
- `recordFileCloseWrite()`: 记录文件关闭写入时的最终熵值
- `getFileEntropyInfo()`: 获取特定文件的熵值信息
- `getAllFileEntropyInfo()`: 获取所有文件的熵值信息
- `getTotalOriginalEntropy()`: 获取总原始熵值
- `getTotalFinalEntropy()`: 获取总最终熵值
- `getTotalEntropyChange()`: 获取总熵值变化量

### ProcessMonitor
- `recordFileOpenEntropy()`: 记录重点进程文件打开熵值
- `recordFileCloseWriteEntropy()`: 记录重点进程文件关闭熵值
- `getFocusProcessFileEntropyInfo()`: 获取重点进程文件熵值信息

## API接口

### 获取重点进程熵值信息
```
GET /api/focus-processes/entropy
GET /api/focus-processes/entropy?pid=<进程ID>
```

#### 响应示例
```json
{
  "pid": 12345,
  "total_original_entropy": 15.6,
  "total_final_entropy": 18.2,
  "total_entropy_change": 2.6,
  "file_count": 3,
  "files": [
    {
      "file_path": "/tmp/test.txt",
      "file_hash": 123456789,
      "has_original_entropy": true,
      "has_final_entropy": true,
      "original_entropy": 5.2,
      "final_entropy": 6.8,
      "entropy_change": 1.6,
      "file_size": 1024,
      "open_time": 1640995200,
      "close_time": 1640995210
    }
  ]
}
```

## 熵值计算算法

### Shannon熵值计算
```cpp
double computeEntropy(const std::vector<size_t>& frequencies, size_t total_bytes) {
    double entropy = 0.0;
    for (size_t freq : frequencies) {
        if (freq > 0) {
            double probability = static_cast<double>(freq) / total_bytes;
            entropy -= probability * log2(probability);
        }
    }
    return entropy;
}
```

### 分块读取算法
```cpp
double calculateFileEntropyInChunks(std::ifstream& file, size_t total_size) {
    const size_t CHUNK_SIZE = 1024 * 1024; // 1MB
    std::vector<char> buffer(CHUNK_SIZE);
    std::vector<size_t> frequencies(256, 0);
    size_t total_bytes_read = 0;
    
    while (total_bytes_read < total_size && !file.eof()) {
        size_t remaining = total_size - total_bytes_read;
        size_t current_chunk_size = std::min(CHUNK_SIZE, remaining);
        
        file.read(buffer.data(), current_chunk_size);
        size_t bytes_read = static_cast<size_t>(file.gcount());
        
        // 计算当前块的字节频率
        for (size_t i = 0; i < bytes_read; ++i) {
            frequencies[static_cast<unsigned char>(buffer[i])]++;
        }
        
        total_bytes_read += bytes_read;
    }
    
    return computeEntropy(frequencies, total_bytes_read);
}
```

### 文件大小限制
```cpp
static constexpr size_t MAX_FILE_SIZE_FOR_ENTROPY = 100 * 1024 * 1024; // 100MB
```

## 使用示例

### 1. 启动程序
```bash
./bin/psfsmon -c config/psfsmon.conf
```

### 2. 设置重点进程
```bash
curl -X POST "http://127.0.0.1:8080/api/focus-processes" \
     -H "Content-Type: application/json" \
     -d '{"pid": 12345}'
```

### 3. 进行文件操作
重点进程进行文件读写操作时，系统会自动计算和记录熵值。

### 4. 获取熵值信息
```bash
# 获取所有重点进程熵值统计
curl "http://127.0.0.1:8080/api/focus-processes/entropy"

# 获取特定进程详细熵值信息
curl "http://127.0.0.1:8080/api/focus-processes/entropy?pid=12345"
```

## 测试

### 运行测试脚本
```bash
# 完整测试
./test/test_entropy_calculation.sh

# 简化测试
./test/test_entropy_simple.sh

# 大文件分块读取测试
./test/test_large_file_entropy.sh
```

### 测试验证点
1. 文件打开时是否计算了原始熵值
2. 文件关闭写入时是否计算了最终熵值
3. 大文件是否被跳过熵值计算
4. 熵值变化量是否正确计算
5. 文件路径哈希值是否正确生成

## 性能优化

### 1. 文件大小限制
- 避免大文件导致的性能问题
- 只计算前100MB的熵值
- 使用分块读取避免内存不足

### 2. 分块读取
- 每次读取1MB的数据块
- 避免一次性将大文件加载到内存
- 支持处理任意大小的文件

### 3. 事件过滤
- 只处理FAN_OPEN和FAN_CLOSE_WRITE事件
- 跳过FAN_ACCESS和FAN_MODIFY事件

### 4. 哈希值缓存
- 使用文件路径哈希值作为唯一标识符
- 避免重复计算相同文件的熵值

### 5. 线程安全
- 使用互斥锁保护共享数据结构
- 支持多线程并发访问

## 监控指标

### 关键指标
- **原始熵值**: 文件打开时的熵值
- **最终熵值**: 文件关闭写入时的熵值
- **熵值变化量**: 最终熵值 - 原始熵值
- **文件数量**: 重点进程操作的文件总数
- **计算次数**: 熵值计算的总次数

### 安全意义
- **熵值增加**: 可能表示文件被加密或压缩
- **熵值减少**: 可能表示文件被解密或解压
- **熵值变化**: 反映文件内容的变化程度

## 配置选项

### 相关配置项
```ini
enable_focus_process_monitoring = true
calculate_file_entropy = true
focus_process_list = "bash,cat,echo"
```

### 常量配置
```cpp
static constexpr size_t MAX_FILE_SIZE_FOR_ENTROPY = 100 * 1024 * 1024; // 100MB
```

## 注意事项

1. **权限要求**: 需要root权限才能监控文件系统事件
2. **内核要求**: 需要Linux 4.19+内核支持fanotify
3. **性能影响**: 熵值计算会增加CPU使用率
4. **存储开销**: 每个文件会占用一定的内存空间
5. **文件类型**: 只处理普通文件，跳过目录和特殊文件

## 故障排除

### 常见问题
1. **程序启动失败**: 检查权限和内核版本
2. **熵值计算失败**: 检查文件权限和大小
3. **API无响应**: 检查API服务器是否启动
4. **内存使用过高**: 检查文件数量和大文件处理

### 调试模式
启用调试模式获取详细日志：
```ini
debug_mode = true
log_level = DEBUG
``` 