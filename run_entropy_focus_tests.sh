#!/bin/bash

# 重点进程管理和熵值检测自动化测试脚本
# 从项目根目录运行此脚本

LOG_DIR="./test/test_results_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$LOG_DIR"

echo "=== PSFSMON-L 重点进程管理和熵值检测自动化测试 ==="
echo "测试结果将保存到: $LOG_DIR"
echo "开始时间: $(date)"
echo ""

# 测试选项列表
declare -a TEST_OPTIONS=(
    "focus-process"
    "focus-process-entropy" 
    "entropy-test"
    "entropy-api-internal"
    "complete-behavior"
)

# 测试描述
declare -A TEST_DESCRIPTIONS=(
    ["focus-process"]="重点进程文件监控功能专项测试"
    ["focus-process-entropy"]="重点进程熵值API死锁检测测试(高风险)"
    ["entropy-test"]="重点进程熵值功能专项测试(使用专门测试程序)"
    ["entropy-api-internal"]="内部熵值API完整测试(详细验证)"
    ["complete-behavior"]="完整重点进程行为模拟器(推荐)"
)

# 运行单个测试
run_single_test() {
    local test_option="$1"
    local description="${TEST_DESCRIPTIONS[$test_option]}"
    local log_file="$LOG_DIR/${test_option}_test.log"
    local result_file="$LOG_DIR/${test_option}_result.txt"
    
    echo "==================================================================================="
    echo "🧪 运行测试: $test_option"
    echo "📝 描述: $description"
    echo "📁 日志文件: $log_file"
    echo "⏰ 开始时间: $(date)"
    echo "==================================================================================="
    
    # 运行测试并记录输出
    if timeout 300 ./test/test_api_comprehensive --test "$test_option" > "$log_file" 2>&1; then
        local exit_code=$?
        echo "✅ 测试完成: $test_option (退出码: $exit_code)" | tee "$result_file"
        
        # 提取关键信息
        echo "=== 测试结果摘要 ===" >> "$result_file"
        grep -E "(总测试数|通过|失败|跳过|✓|✗|⚠️)" "$log_file" | tail -10 >> "$result_file" 2>/dev/null || echo "无测试结果摘要" >> "$result_file"
        
        # 检查是否有错误或警告
        echo "=== 错误和警告 ===" >> "$result_file"
        grep -i -E "(error|warn|fail|timeout|dead|lock)" "$log_file" >> "$result_file" 2>/dev/null || echo "无错误或警告" >> "$result_file"
        
        # 检查熵值相关的关键信息
        echo "=== 熵值相关信息 ===" >> "$result_file"
        grep -i -E "(entropy|熵值|original_entropy|entropy_change|entropy_calculations)" "$log_file" >> "$result_file" 2>/dev/null || echo "无熵值信息" >> "$result_file"
        
    else
        local exit_code=$?
        echo "❌ 测试失败或超时: $test_option (退出码: $exit_code)" | tee "$result_file"
        echo "可能的原因: 测试超时(5分钟), 程序崩溃, 或服务不可用" >> "$result_file"
    fi
    
    # 确保结果文件存在
    if [[ ! -f "$result_file" ]]; then
        echo "⚠️ 结果文件创建失败: $result_file" > "$result_file"
    fi
    
    echo "⏰ 结束时间: $(date)"
    echo ""
    
    # 显示简要结果
    echo "--- 测试结果预览: $test_option ---"
    cat "$result_file"
    echo ""
}

# 检查服务状态
check_service_status() {
    echo "🔍 检查PSFSMON服务状态..."
    if curl -s --connect-timeout 3 http://127.0.0.1:8080/api/health > /dev/null 2>&1; then
        echo "✅ PSFSMON服务正在运行"
        return 0
    else
        echo "❌ PSFSMON服务未运行或不可访问"
        echo "请先启动服务: sudo ./bin/psfsmon --api-port 8080"
        return 1
    fi
}

# 生成最终报告
generate_final_report() {
    local report_file="$LOG_DIR/final_report.md"
    
    echo "# PSFSMON-L 重点进程管理和熵值检测测试报告" > "$report_file"
    echo "" >> "$report_file"
    echo "**测试时间:** $(date)" >> "$report_file"
    echo "**测试目录:** $LOG_DIR" >> "$report_file"
    echo "" >> "$report_file"
    
    echo "## 测试结果总览" >> "$report_file"
    echo "" >> "$report_file"
    echo "| 测试选项 | 描述 | 状态 |" >> "$report_file"
    echo "|----------|------|------|" >> "$report_file"
    
    for test_option in "${TEST_OPTIONS[@]}"; do
        local description="${TEST_DESCRIPTIONS[$test_option]}"
        local result_file="$LOG_DIR/${test_option}_result.txt"
        
        if [[ -f "$result_file" ]]; then
            if grep -q "✅" "$result_file"; then
                echo "| $test_option | $description | ✅ 通过 |" >> "$report_file"
            else
                echo "| $test_option | $description | ❌ 失败 |" >> "$report_file"
            fi
        else
            echo "| $test_option | $description | ⚠️ 未运行 |" >> "$report_file"
        fi
    done
    
    echo "" >> "$report_file"
    echo "## 详细分析" >> "$report_file"
    echo "" >> "$report_file"
    
    for test_option in "${TEST_OPTIONS[@]}"; do
        local result_file="$LOG_DIR/${test_option}_result.txt"
        if [[ -f "$result_file" ]]; then
            echo "### $test_option" >> "$report_file"
            echo "**描述:** ${TEST_DESCRIPTIONS[$test_option]}" >> "$report_file"
            echo "" >> "$report_file"
            echo '```' >> "$report_file"
            if ! cat "$result_file" >> "$report_file" 2>/dev/null; then
                echo "无法读取结果文件" >> "$report_file"
            fi
            echo '```' >> "$report_file"
            echo "" >> "$report_file"
        else
            echo "### $test_option" >> "$report_file"
            echo "**描述:** ${TEST_DESCRIPTIONS[$test_option]}" >> "$report_file"
            echo "" >> "$report_file"
            echo '```' >> "$report_file"
            echo "⚠️ 测试结果文件不存在: $result_file" >> "$report_file"
            echo '```' >> "$report_file"
            echo "" >> "$report_file"
        fi
    done
    
    echo "📊 最终测试报告已生成: $report_file"
}

# 主函数
main() {
    # 检查当前目录是否为项目根目录
    if [[ ! -f "./CMakeLists.txt" || ! -d "./test" || ! -d "./src" ]]; then
        echo "❌ 请从项目根目录运行此脚本"
        echo "当前目录: $(pwd)"
        echo "应该包含: CMakeLists.txt, test/, src/ 目录"
        exit 1
    fi
    
    # 检查测试程序是否存在
    if [[ ! -f "./test/test_api_comprehensive" ]]; then
        echo "❌ 测试程序不存在: ./test/test_api_comprehensive"
        echo "请先编译: cd test && make test_api_comprehensive"
        exit 1
    fi
    
    # 检查服务状态
    if ! check_service_status; then
        exit 1
    fi
    
    echo "开始运行 ${#TEST_OPTIONS[@]} 个重点进程和熵值相关的测试..."
    echo ""
    
    # 运行所有测试
    for test_option in "${TEST_OPTIONS[@]}"; do
        run_single_test "$test_option"
        
        # 测试间间隔，让系统稳定
        echo "⏳ 等待5秒让系统稳定..."
        sleep 5
    done
    
    # 生成最终报告
    generate_final_report
    
    echo "==================================================================================="
    echo "🎉 所有测试完成！"
    echo "📁 详细结果请查看: $LOG_DIR/"
    echo "📊 最终报告: $LOG_DIR/final_report.md"
    echo "⏰ 完成时间: $(date)"
    echo "==================================================================================="
}

# 运行主函数
main "$@" 